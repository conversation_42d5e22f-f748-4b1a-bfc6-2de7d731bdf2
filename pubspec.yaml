name: nextsportz_v2
description: "NextSportz - Connecting Soccer Players, Venues, and Tournament Creators"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  dio: ^5.8.0+1
  flutter_riverpod: ^2.6.1
  shared_preferences: ^2.5.2
  pretty_dio_logger: ^1.4.0
  get_it: ^8.0.3
  fpdart: ^1.1.1
  go_router: ^14.8.0
  animated_notch_bottom_bar: ^1.0.3
  flutter_native_splash: ^2.4.4
  responsive_framework: ^1.5.1
  responsive_builder: ^0.7.1
  auto_size_text: ^3.0.0
  share_plus: ^11.0.0
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  geolocator: ^13.0.1
  permission_handler: ^12.0.1
  google_fonts: ^6.2.1
  palette_generator: ^0.3.3
  provider: ^6.1.2
  flutter_screenutil: ^5.9.0
  google_maps_flutter: ^2.10.0
  page_transition: any
  animated_bottom_navigation_bar: ^1.0.3
  confetti: ^0.7.0
  json_annotation: ^4.9.0
  freezed_annotation: ^2.4.4
  image_picker: ^1.1.2
  mime: ^1.0.6
  uuid: ^4.5.1

dependency_overrides:
  archive: ^4.0.2

flutter_web_plugins:
  sdk: flutter

flutter_native_splash:
  android: true
  ios: true
  web: true
  image: assets/images/nextsportz.png
  color: "#FFFFFF"
  android_12:
    image: assets/images/nextsportz.png
    color: "#FFFFFF"

dev_dependencies:
  integration_test:
    sdk: flutter
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  mocktail: ^1.0.4
  test: ^1.25.7
  flutter_flavorizr: ^2.2.3
  # dio_adapter: ^2.1.1  # Temporarily commented out due to SDK version compatibility
  freezed: ^2.4.7
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  mockito: ^5.4.6
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
