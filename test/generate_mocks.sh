#!/bin/bash

# <PERSON>ript to generate all mock files for tests
# This script uses build_runner to generate mock files for all test files

echo "Generating mock files for tests..."

# Navigate to the project root
cd "$(dirname "$0")/.."

# Clean previous generated files
echo "Cleaning previous generated files..."
fvm flutter packages pub run build_runner clean

# Generate mock files
echo "Generating mock files..."
fvm flutter packages pub run build_runner build --delete-conflicting-outputs

# Check if generation was successful
if [ $? -eq 0 ]; then
    echo "✅ Mock files generated successfully!"
    echo "Generated files:"
    find test -name "*.mocks.dart" -type f
else
    echo "❌ Failed to generate mock files"
    exit 1
fi

echo "Done!"
