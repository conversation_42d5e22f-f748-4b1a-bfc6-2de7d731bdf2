# Test Suite Documentation

This directory contains comprehensive test cases for the NextSportz v2 application, following clean architecture principles and inspired by the `references_architecture` directory.

## Test Structure

The test suite is organized following the same structure as the main application:

```
test/
├── core/                           # Core functionality tests
│   ├── local/                      # Local storage tests
│   │   ├── key_value_storage_test.dart
│   │   └── token_storage_test.dart
│   ├── networking/                 # Network layer tests
│   │   ├── api_client_test.dart
│   │   └── auth_interceptor_test.dart
│   └── widgets/                    # Core widget tests
│       └── blaze_text_form_field_test.dart
├── features/                       # Feature-specific tests
│   ├── auth/                       # Authentication feature tests
│   │   ├── data/                   # Data layer tests
│   │   │   ├── datasources/
│   │   │   │   └── auth_remote_datasource_test.dart
│   │   │   └── repositories/
│   │   │       └── auth_repository_impl_test.dart
│   │   ├── domain/                 # Domain layer tests
│   │   │   └── usecases/
│   │   │       └── auth_usecases_test.dart
│   │   └── presentation/           # Presentation layer tests
│   │       ├── logic/
│   │       │   └── controller_test.dart
│   │       └── screens/
│   │           └── login_screen_test.dart
│   └── teams/                      # Teams feature tests (existing)
│       └── domain/
│           └── usecases/
│               └── teams_usecases_test.dart
├── utils/                          # Utility tests
│   ├── color_test.dart
│   └── color_notifire_test.dart
├── test_helpers.dart               # Common test utilities
├── generate_mocks.sh               # Script to generate mock files
└── README.md                       # This file
```

## Test Categories

### 1. Core Tests

- **Local Storage**: Tests for key-value storage and token management
- **Networking**: Tests for API client and authentication interceptor
- **Widgets**: Tests for reusable UI components

### 2. Feature Tests

- **Authentication**: Complete test coverage for auth feature
  - Data layer: Remote datasource and repository implementation
  - Domain layer: Use cases and business logic
  - Presentation layer: Controllers and UI screens

### 3. Utility Tests

- **Color Management**: Tests for color utilities and theme management

## Running Tests

### Prerequisites

Make sure you have the following dependencies in your `pubspec.yaml`:

```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.4
  build_runner: ^2.4.7
  fpdart: ^1.1.0
```

### Generate Mock Files

Before running tests, generate the required mock files:

```bash
# Make the script executable (first time only)
chmod +x test/generate_mocks.sh

# Generate all mock files
./test/generate_mocks.sh
```

### Run All Tests

```bash
flutter test
```

### Run Specific Test Categories

```bash
# Run core tests only
flutter test test/core/

# Run auth feature tests only
flutter test test/features/auth/

# Run utility tests only
flutter test test/utils/

# Run a specific test file
flutter test test/features/auth/presentation/screens/login_screen_test.dart
```

### Run Tests with Coverage

```bash
flutter test --coverage
```

## Test Patterns

### 1. Unit Tests

Unit tests focus on testing individual components in isolation:

```dart
group('ComponentName', () {
  late MockDependency mockDependency;
  late ComponentUnderTest component;

  setUp(() {
    mockDependency = MockDependency();
    component = ComponentUnderTest(mockDependency);
  });

  test('should perform action successfully', () async {
    // Arrange
    when(mockDependency.method()).thenAnswer((_) async => result);

    // Act
    final result = await component.performAction();

    // Assert
    expect(result, equals(expectedResult));
    verify(mockDependency.method()).called(1);
  });
});
```

### 2. Widget Tests

Widget tests verify UI components work correctly:

```dart
testWidgets('should render widget correctly', (WidgetTester tester) async {
  // Arrange
  await tester.pumpWidget(TestHelpers.createTestWidget(
    child: WidgetUnderTest(),
    overrides: [mockProvider],
  ));

  // Act
  await tester.tap(find.byType(ElevatedButton));
  await TestHelpers.waitForAsync(tester);

  // Assert
  expect(find.text('Expected Text'), findsOneWidget);
});
```

### 3. Integration Tests

Integration tests verify multiple components work together:

```dart
test('should complete user flow successfully', () async {
  // Arrange
  final repository = MockRepository();
  final useCase = UseCase(repository);
  final controller = Controller(useCase);

  // Act
  final result = await controller.performAction();

  // Assert
  expect(result.isRight(), isTrue);
  verify(repository.method()).called(1);
});
```

## Test Utilities

### TestHelpers

The `test_helpers.dart` file provides common utilities:

- `TestHelpers.createTestWidget()`: Creates test widgets with ProviderScope
- `TestHelpers.waitForAsync()`: Waits for async operations to complete
- `TestHelpers.tapAndWait()`: Taps a widget and waits for animations
- `TestHelpers.enterTextAndWait()`: Enters text and waits

### TestDataFactory

Provides factory methods for creating test data:

```dart
final user = TestDataFactory.createTestUser(
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
);
```

### TestMatchers

Custom matchers for common assertions:

```dart
expect(widget, TestMatchers.hasText('Expected Text'));
expect(widget, TestMatchers.isEnabled());
```

## Mock Generation

The test suite uses Mockito for creating mocks. To generate mock files:

1. Add `@GenerateMocks([Class1, Class2])` to your test file
2. Import the generated mock file: `import 'test_file.mocks.dart';`
3. Run the generation script: `./test/generate_mocks.sh`

Example:

```dart
@GenerateMocks([Repository, UseCase])
void main() {
  // Test implementation
}
```

## Best Practices

### 1. Test Organization

- Group related tests using `group()`
- Use descriptive test names that explain the scenario
- Follow the Arrange-Act-Assert pattern

### 2. Mocking

- Mock external dependencies (APIs, databases, etc.)
- Verify that mocked methods are called with correct parameters
- Use `verifyNever()` to ensure methods are not called

### 3. Error Handling

- Test both success and failure scenarios
- Verify error messages and error states
- Test edge cases and boundary conditions

### 4. Async Testing

- Use `await` for async operations
- Use `TestHelpers.waitForAsync()` for UI updates
- Test loading states and error states

### 5. Widget Testing

- Test user interactions (tap, scroll, text input)
- Verify UI state changes
- Test navigation and routing

## Coverage Goals

The test suite aims for:

- **Unit Tests**: 90%+ coverage for business logic
- **Widget Tests**: 80%+ coverage for UI components
- **Integration Tests**: 70%+ coverage for feature workflows

## Continuous Integration

Tests are automatically run in CI/CD pipelines:

- All tests must pass before merging
- Coverage reports are generated
- Test results are reported in pull requests

## Troubleshooting

### Common Issues

1. **Mock files not found**: Run `./test/generate_mocks.sh`
2. **Test timeout**: Increase timeout duration or optimize test performance
3. **Widget not found**: Use `TestHelpers.waitForAsync()` for async operations
4. **Provider not found**: Ensure proper provider overrides in test setup

### Debugging Tests

```bash
# Run tests with verbose output
flutter test --verbose

# Run a specific test with debugging
flutter test test/path/to/test.dart --verbose

# Run tests and stop on first failure
flutter test --stop-on-first-failure
```

## Contributing

When adding new tests:

1. Follow the existing test structure and patterns
2. Use the provided test utilities and helpers
3. Ensure proper mock generation
4. Add comprehensive test coverage
5. Update this documentation if needed

## References

- [Flutter Testing Documentation](https://docs.flutter.dev/testing)
- [Mockito Documentation](https://pub.dev/packages/mockito)
- [Riverpod Testing](https://riverpod.dev/docs/cookbooks/testing)
- [Clean Architecture Testing](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
