import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:nextsportz_v2/core/local/key_value_storage.dart';

import 'key_value_storage_test.mocks.dart';

@GenerateMocks([SharedPreferences])
void main() {
  group('KeyValueStorageServiceImpl', () {
    late MockSharedPreferences mockPrefs;
    late KeyValueStorageServiceImpl storage;

    setUp(() {
      mockPrefs = MockSharedPreferences();
      storage = KeyValueStorageServiceImpl(mockPrefs);
    });

    group('getValue', () {
      test('should return string value', () {
        // Arrange
        const key = 'test_key';
        const value = 'test_value';
        when(mockPrefs.getString(key)).thenReturn(value);

        // Act
        final result = storage.getValue<String>(key);

        // Assert
        expect(result, equals(value));
        verify(mockPrefs.getString(key)).called(1);
      });

      test('should return int value', () {
        // Arrange
        const key = 'test_key';
        const value = 42;
        when(mockPrefs.getInt(key)).thenReturn(value);

        // Act
        final result = storage.getValue<int>(key);

        // Assert
        expect(result, equals(value));
        verify(mockPrefs.getInt(key)).called(1);
      });

      test('should return double value', () {
        // Arrange
        const key = 'test_key';
        const value = 3.14;
        when(mockPrefs.getDouble(key)).thenReturn(value);

        // Act
        final result = storage.getValue<double>(key);

        // Assert
        expect(result, equals(value));
        verify(mockPrefs.getDouble(key)).called(1);
      });

      test('should return bool value', () {
        // Arrange
        const key = 'test_key';
        const value = true;
        when(mockPrefs.getBool(key)).thenReturn(value);

        // Act
        final result = storage.getValue<bool>(key);

        // Assert
        expect(result, equals(value));
        verify(mockPrefs.getBool(key)).called(1);
      });

      test('should return list of strings', () {
        // Arrange
        const key = 'test_key';
        const value = ['item1', 'item2'];
        when(mockPrefs.getStringList(key)).thenReturn(value);

        // Act
        final result = storage.getValue<List<String>>(key);

        // Assert
        expect(result, equals(value));
        verify(mockPrefs.getStringList(key)).called(1);
      });

      test('should return map from json string', () {
        // Arrange
        const key = 'test_key';
        const jsonString = '{"name": "John", "age": 30}';
        const expectedMap = {'name': 'John', 'age': 30};
        when(mockPrefs.getString(key)).thenReturn(jsonString);

        // Act
        final result = storage.getValue<Map>(key);

        // Assert
        expect(result, equals(expectedMap));
        verify(mockPrefs.getString(key)).called(1);
      });

      test('should return null when key does not exist', () {
        // Arrange
        const key = 'nonexistent_key';
        when(mockPrefs.getString(key)).thenReturn(null);

        // Act
        final result = storage.getValue<String>(key);

        // Assert
        expect(result, isNull);
      });

      test('should return null when json is invalid', () {
        // Arrange
        const key = 'test_key';
        const invalidJson = 'invalid json';
        when(mockPrefs.getString(key)).thenReturn(invalidJson);

        // Act
        final result = storage.getValue<Map>(key);

        // Assert
        expect(result, isNull);
      });
    });

    group('setKeyValue', () {
      test('should set string value', () async {
        // Arrange
        const key = 'test_key';
        const value = 'test_value';
        when(mockPrefs.setString(key, value)).thenAnswer((_) async => true);

        // Act
        await storage.setKeyValue(key, value);

        // Assert
        verify(mockPrefs.setString(key, value)).called(1);
      });

      test('should set int value', () async {
        // Arrange
        const key = 'test_key';
        const value = 42;
        when(mockPrefs.setInt(key, value)).thenAnswer((_) async => true);

        // Act
        await storage.setKeyValue(key, value);

        // Assert
        verify(mockPrefs.setInt(key, value)).called(1);
      });

      test('should set double value', () async {
        // Arrange
        const key = 'test_key';
        const value = 3.14;
        when(mockPrefs.setDouble(key, value)).thenAnswer((_) async => true);

        // Act
        await storage.setKeyValue(key, value);

        // Assert
        verify(mockPrefs.setDouble(key, value)).called(1);
      });

      test('should set bool value', () async {
        // Arrange
        const key = 'test_key';
        const value = true;
        when(mockPrefs.setBool(key, value)).thenAnswer((_) async => true);

        // Act
        await storage.setKeyValue(key, value);

        // Assert
        verify(mockPrefs.setBool(key, value)).called(1);
      });

      test('should set list of strings', () async {
        // Arrange
        const key = 'test_key';
        const value = ['item1', 'item2'];
        when(mockPrefs.setStringList(key, value)).thenAnswer((_) async => true);

        // Act
        await storage.setKeyValue(key, value);

        // Assert
        verify(mockPrefs.setStringList(key, value)).called(1);
      });

      test('should remove key when value is null', () async {
        // Arrange
        const key = 'test_key';
        when(mockPrefs.remove(key)).thenAnswer((_) async => true);

        // Act
        await storage.setKeyValue<String>(key, null);

        // Assert
        verify(mockPrefs.remove(key)).called(1);
      });

    });

    group('removeKey', () {
      test('should remove key successfully', () async {
        // Arrange
        const key = 'test_key';
        when(mockPrefs.remove(key)).thenAnswer((_) async => true);

        // Act
        final result = await storage.removeKey(key);

        // Assert
        expect(result, isTrue);
        verify(mockPrefs.remove(key)).called(1);
      });

      test('should return false when removal fails', () async {
        // Arrange
        const key = 'test_key';
        when(mockPrefs.remove(key)).thenAnswer((_) async => false);

        // Act
        final result = await storage.removeKey(key);

        // Assert
        expect(result, isFalse);
        verify(mockPrefs.remove(key)).called(1);
      });
    });

    group('clearAll', () {
      test('should clear all preferences', () async {
        // Arrange
        when(mockPrefs.clear()).thenAnswer((_) async {return true;});

        // Act
        await storage.clearAll();

        // Assert
        verify(mockPrefs.clear()).called(1);
      });
    });
  });
}
