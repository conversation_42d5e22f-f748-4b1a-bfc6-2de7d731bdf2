import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:nextsportz_v2/core/local/token_storage.dart';
import 'package:nextsportz_v2/core/local/key_value_storage.dart';

import 'token_storage_test.mocks.dart';

@GenerateMocks([KeyValueStorageService])
void main() {
  group('TokenStorageServiceImpl', () {
    late MockKeyValueStorageService mockStorage;
    late TokenStorageServiceImpl tokenStorage;

    setUp(() {
      mockStorage = MockKeyValueStorageService();
      tokenStorage = TokenStorageServiceImpl(mockStorage);
    });

    group('saveTokens', () {
      test('should save access and refresh tokens', () async {
        // Arrange
        const accessToken = 'test_access_token';
        const refreshToken = 'test_refresh_token';

        when(
          mockStorage.setKeyValue('access_token', accessToken),
        ).thenAnswer((_) async {});
        when(
          mockStorage.setKeyValue('refresh_token', refreshToken),
        ).thenAnswer((_) async {});

        // Act
        await tokenStorage.saveTokens(
          accessToken: accessToken,
          refreshToken: refreshToken,
        );

        // Assert
        verify(mockStorage.setKeyValue('access_token', accessToken)).called(1);
        verify(
          mockStorage.setKeyValue('refresh_token', refreshToken),
        ).called(1);
      });

      test('should handle storage errors gracefully', () async {
        // Arrange
        const accessToken = 'test_access_token';
        const refreshToken = 'test_refresh_token';

        when(
          mockStorage.setKeyValue('access_token', accessToken),
        ).thenThrow(Exception('Storage error'));

        // Act & Assert
        expect(
          () => tokenStorage.saveTokens(
            accessToken: accessToken,
            refreshToken: refreshToken,
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('getTokens', () {
      test('should return tokens when both exist', () async {
        // Arrange
        const accessToken = 'test_access_token';
        const refreshToken = 'test_refresh_token';

        when(
          mockStorage.getValue<String>('access_token'),
        ).thenReturn(accessToken);
        when(
          mockStorage.getValue<String>('refresh_token'),
        ).thenReturn(refreshToken);

        // Act
        final result = await tokenStorage.getTokens();

        // Assert
        expect(result, isNotNull);
        expect(result!['accessToken'], equals(accessToken));
        expect(result['refreshToken'], equals(refreshToken));
        verify(mockStorage.getValue<String>('access_token')).called(1);
        verify(mockStorage.getValue<String>('refresh_token')).called(1);
      });

      test('should return null when access token is missing', () async {
        // Arrange
        const refreshToken = 'test_refresh_token';

        when(mockStorage.getValue<String>('access_token')).thenReturn(null);
        when(
          mockStorage.getValue<String>('refresh_token'),
        ).thenReturn(refreshToken);

        // Act
        final result = await tokenStorage.getTokens();

        // Assert
        expect(result, isNull);
        verify(mockStorage.getValue<String>('access_token')).called(1);
        verify(mockStorage.getValue<String>('refresh_token')).called(1);
      });

      test('should return null when refresh token is missing', () async {
        // Arrange
        const accessToken = 'test_access_token';

        when(
          mockStorage.getValue<String>('access_token'),
        ).thenReturn(accessToken);
        when(mockStorage.getValue<String>('refresh_token')).thenReturn(null);

        // Act
        final result = await tokenStorage.getTokens();

        // Assert
        expect(result, isNull);
        verify(mockStorage.getValue<String>('access_token')).called(1);
        verify(mockStorage.getValue<String>('refresh_token')).called(1);
      });

      test('should return null when both tokens are missing', () async {
        // Arrange
        when(mockStorage.getValue<String>('access_token')).thenReturn(null);
        when(mockStorage.getValue<String>('refresh_token')).thenReturn(null);

        // Act
        final result = await tokenStorage.getTokens();

        // Assert
        expect(result, isNull);
        verify(mockStorage.getValue<String>('access_token')).called(1);
        verify(mockStorage.getValue<String>('refresh_token')).called(1);
      });
    });

    group('clearTokens', () {
      test('should remove both tokens', () async {
        // Arrange
        when(
          mockStorage.removeKey('access_token'),
        ).thenAnswer((_) async => true);
        when(
          mockStorage.removeKey('refresh_token'),
        ).thenAnswer((_) async => true);

        // Act
        await tokenStorage.clearTokens();

        // Assert
        verify(mockStorage.removeKey('access_token')).called(1);
        verify(mockStorage.removeKey('refresh_token')).called(1);
      });

      test('should handle removal errors gracefully', () async {
        // Arrange
        when(
          mockStorage.removeKey('access_token'),
        ).thenThrow(Exception('Removal error'));

        // Act & Assert
        expect(() => tokenStorage.clearTokens(), throwsA(isA<Exception>()));
      });
    });

    group('hasTokens', () {
      test('should return true when tokens exist', () async {
        // Arrange
        const accessToken = 'test_access_token';
        const refreshToken = 'test_refresh_token';

        when(
          mockStorage.getValue<String>('access_token'),
        ).thenReturn(accessToken);
        when(
          mockStorage.getValue<String>('refresh_token'),
        ).thenReturn(refreshToken);

        // Act
        final result = await tokenStorage.hasTokens();

        // Assert
        expect(result, isTrue);
      });

      test('should return false when tokens do not exist', () async {
        // Arrange
        when(mockStorage.getValue<String>('access_token')).thenReturn(null);
        when(mockStorage.getValue<String>('refresh_token')).thenReturn(null);

        // Act
        final result = await tokenStorage.hasTokens();

        // Assert
        expect(result, isFalse);
      });

      test('should return false when only access token exists', () async {
        // Arrange
        const accessToken = 'test_access_token';

        when(
          mockStorage.getValue<String>('access_token'),
        ).thenReturn(accessToken);
        when(mockStorage.getValue<String>('refresh_token')).thenReturn(null);

        // Act
        final result = await tokenStorage.hasTokens();

        // Assert
        expect(result, isFalse);
      });

      test('should return false when only refresh token exists', () async {
        // Arrange
        const refreshToken = 'test_refresh_token';

        when(mockStorage.getValue<String>('access_token')).thenReturn(null);
        when(
          mockStorage.getValue<String>('refresh_token'),
        ).thenReturn(refreshToken);

        // Act
        final result = await tokenStorage.hasTokens();

        // Assert
        expect(result, isFalse);
      });
    });
  });
}
