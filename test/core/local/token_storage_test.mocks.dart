// Mocks generated by Mockito 5.4.6 from annotations
// in nextsportz_v2/test/core/local/token_storage_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:nextsportz_v2/core/local/key_value_storage.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [KeyValueStorageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockKeyValueStorageService extends _i1.Mock
    implements _i2.KeyValueStorageService {
  MockKeyValueStorageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  T? getValue<T>(String? key) =>
      (super.noSuchMethod(Invocation.method(#getValue, [key])) as T?);

  @override
  _i3.Future<void> setKeyValue<T>(String? key, T? value) =>
      (super.noSuchMethod(
            Invocation.method(#setKeyValue, [key, value]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<bool> removeKey(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#removeKey, [key]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<void> clearAll() =>
      (super.noSuchMethod(
            Invocation.method(#clearAll, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);
}
