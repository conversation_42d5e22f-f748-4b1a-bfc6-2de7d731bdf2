import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:go_router/go_router.dart';

import 'package:nextsportz_v2/core/routing/auth_guard.dart';
import 'package:nextsportz_v2/core/networking/session_manager.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/controller.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/auth_state.dart';
import 'package:nextsportz_v2/features/auth/domain/entities/user.dart';

import 'auth_guard_test.mocks.dart';

@GenerateMocks([SessionManager, GoRouter])
void main() {
  group('AuthGuardService', () {
    late ProviderContainer container;
    late MockSessionManager mockSessionManager;
    late MockGoRouter mockRouter;
    late AuthGuardService authGuard;

    setUp(() {
      mockSessionManager = MockSessionManager();
      mockRouter = MockGoRouter();

      container = ProviderContainer(
        overrides: [
          sessionManagerProvider.overrideWithValue(mockSessionManager),
          authNotifierProvider.overrideWith((ref) => MockAuthNotifier()),
        ],
      );

      authGuard = container.read(authGuardServiceProvider);
      authGuard.initialize(mockRouter);
    });

    tearDown(() {
      container.dispose();
    });

    group('isAuthenticated', () {
      test('returns true when auth state is authenticated', () async {
        // Arrange
        final mockNotifier =
            container.read(authNotifierProvider.notifier) as MockAuthNotifier;
        mockNotifier.setAuthState(
          AuthState.authenticated(
            User(
              id: '1',
              name: 'Test User',
              email: '<EMAIL>',
              phoneNumber: '+**********',
              role: 'PLAYER',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              isActive: true,
            ),
          ),
        );

        // Act
        final result = await authGuard.isAuthenticated();

        // Assert
        expect(result, true);
      });

      test('returns false when auth state is unauthenticated', () async {
        // Arrange
        final mockNotifier =
            container.read(authNotifierProvider.notifier) as MockAuthNotifier;
        mockNotifier.setAuthState(AuthState.unauthenticated());

        // Act
        final result = await authGuard.isAuthenticated();

        // Assert
        expect(result, false);
      });

      test('checks session manager when auth state is initial', () async {
        // Arrange
        final mockNotifier =
            container.read(authNotifierProvider.notifier) as MockAuthNotifier;
        mockNotifier.setAuthState(AuthState.initial());
        when(
          mockSessionManager.isAuthenticated(),
        ).thenAnswer((_) async => true);

        // Act
        final result = await authGuard.isAuthenticated();

        // Assert
        expect(result, true);
        verify(mockSessionManager.isAuthenticated()).called(1);
      });
    });

    group('getRedirectPath', () {
      test(
        'redirects to login for protected routes when unauthenticated',
        () async {
          // Arrange
          final mockNotifier =
              container.read(authNotifierProvider.notifier) as MockAuthNotifier;
          mockNotifier.setAuthState(AuthState.unauthenticated());

          // Act
          final result = await authGuard.getRedirectPath('/home');

          // Assert
          expect(result, '/login');
        },
      );

      test('stores intended destination when redirecting to login', () async {
        // Arrange
        final mockNotifier =
            container.read(authNotifierProvider.notifier) as MockAuthNotifier;
        mockNotifier.setAuthState(AuthState.unauthenticated());

        // Act
        await authGuard.getRedirectPath('/teams/123');
        final storedDestination = authGuard.getAndClearIntendedDestination();

        // Assert
        expect(storedDestination, '/teams/123');
      });

      test('allows access to public routes when unauthenticated', () async {
        // Arrange
        final mockNotifier =
            container.read(authNotifierProvider.notifier) as MockAuthNotifier;
        mockNotifier.setAuthState(AuthState.unauthenticated());

        // Act & Assert
        expect(await authGuard.getRedirectPath('/'), null);
        expect(await authGuard.getRedirectPath('/login'), null);
        expect(await authGuard.getRedirectPath('/register'), null);
        expect(await authGuard.getRedirectPath('/forgot-password'), null);
        expect(await authGuard.getRedirectPath('/onboarding'), null);
        expect(
          await authGuard.getRedirectPath('/otp-verification?phone=123'),
          null,
        );
      });

      test(
        'redirects to home when authenticated user visits auth pages',
        () async {
          // Arrange
          final mockNotifier =
              container.read(authNotifierProvider.notifier) as MockAuthNotifier;
          mockNotifier.setAuthState(
            AuthState.authenticated(
              User(
                id: '1',
                name: 'Test User',
                email: '<EMAIL>',
                phoneNumber: '+**********',
                role: 'PLAYER',
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                isActive: true,
              ),
            ),
          );

          // Act & Assert
          expect(await authGuard.getRedirectPath('/login'), '/home');
          expect(await authGuard.getRedirectPath('/register'), '/home');
        },
      );

      test('allows access to protected routes when authenticated', () async {
        // Arrange
        final mockNotifier =
            container.read(authNotifierProvider.notifier) as MockAuthNotifier;
        mockNotifier.setAuthState(
          AuthState.authenticated(
            User(
              id: '1',
              name: 'Test User',
              email: '<EMAIL>',
              phoneNumber: '+**********',
              role: 'PLAYER',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              isActive: true,
            ),
          ),
        );

        // Act & Assert
        expect(await authGuard.getRedirectPath('/home'), null);
        expect(await authGuard.getRedirectPath('/teams'), null);
        expect(await authGuard.getRedirectPath('/challenges'), null);
      });
    });

    group('getRedirectPathForUri', () {
      test('handles deep links with query parameters', () async {
        // Arrange
        final mockNotifier =
            container.read(authNotifierProvider.notifier) as MockAuthNotifier;
        mockNotifier.setAuthState(AuthState.unauthenticated());
        final uri = Uri.parse('/teams/123?tab=members&sort=name');

        // Act
        final result = await authGuard.getRedirectPathForUri(uri);
        final storedDestination = authGuard.getAndClearIntendedDestination();

        // Assert
        expect(result, '/login');
        expect(storedDestination, '/teams/123?tab=members&sort=name');
      });
    });

    group('intended destination management', () {
      test('clears intended destination after retrieval', () async {
        // Arrange
        final mockNotifier =
            container.read(authNotifierProvider.notifier) as MockAuthNotifier;
        mockNotifier.setAuthState(AuthState.unauthenticated());

        // Act
        await authGuard.getRedirectPath('/teams/123');
        final firstRetrieval = authGuard.getAndClearIntendedDestination();
        final secondRetrieval = authGuard.getAndClearIntendedDestination();

        // Assert
        expect(firstRetrieval, '/teams/123');
        expect(secondRetrieval, null);
      });

      test('does not store auth pages as intended destinations', () async {
        // Arrange
        final mockNotifier =
            container.read(authNotifierProvider.notifier) as MockAuthNotifier;
        mockNotifier.setAuthState(AuthState.unauthenticated());

        // Act
        await authGuard.getRedirectPath('/login');
        await authGuard.getRedirectPath('/register');
        await authGuard.getRedirectPath('/');
        final storedDestination = authGuard.getAndClearIntendedDestination();

        // Assert
        expect(storedDestination, null);
      });
    });

    group('session event handling', () {
      test('handles token refresh failed event', () async {
        // Arrange
        final mockNotifier =
            container.read(authNotifierProvider.notifier) as MockAuthNotifier;
        mockNotifier.setAuthState(
          AuthState.authenticated(
            User(
              id: '1',
              name: 'Test User',
              email: '<EMAIL>',
              phoneNumber: '+**********',
              role: 'PLAYER',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              isActive: true,
            ),
          ),
        );

        // Act
        // Simulate token refresh failed event
        final sessionEvent = SessionEvent(
          type: SessionEventType.tokenRefreshFailed,
          reason: SessionClearReason.tokenRefreshFailed,
          timestamp: DateTime.now(),
        );

        // Trigger the session event handler directly
        authGuard.dispose(); // Clean up first
        authGuard = container.read(authGuardServiceProvider);
        authGuard.initialize(mockRouter);

        // Verify that the auth state is updated to unauthenticated
        expect(mockNotifier.state.status, AuthStatus.authenticated);

        // Simulate the session event by calling logout directly
        await mockNotifier.logout();

        // Assert
        expect(mockNotifier.state.status, AuthStatus.unauthenticated);
      });

      test('clears intended destination on forced logout', () async {
        // Arrange
        final mockNotifier =
            container.read(authNotifierProvider.notifier) as MockAuthNotifier;
        mockNotifier.setAuthState(AuthState.unauthenticated());

        // Store an intended destination first
        await authGuard.getRedirectPath('/teams/123');
        expect(authGuard.getAndClearIntendedDestination(), '/teams/123');

        // Store another destination
        await authGuard.getRedirectPath('/challenges');

        // Act - simulate forced logout
        await mockNotifier.logout();

        // Assert - intended destination should be cleared
        final storedDestination = authGuard.getAndClearIntendedDestination();
        expect(storedDestination, null);
      });
    });
  });
}

/// Mock AuthNotifier for testing
class MockAuthNotifier extends StateNotifier<AuthState> {
  MockAuthNotifier() : super(AuthState.initial());

  void setAuthState(AuthState newState) {
    state = newState;
  }

  @override
  Future<bool> loginWithPhone({
    required String phoneNumber,
    required String password,
    required String role,
  }) async {
    return true;
  }

  @override
  Future<void> logout() async {
    state = AuthState.unauthenticated();
  }

  @override
  Future<void> checkTokenStatus() async {
    // Mock implementation
  }
}
