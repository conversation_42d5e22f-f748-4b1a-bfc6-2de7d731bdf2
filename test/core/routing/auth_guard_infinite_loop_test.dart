import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:go_router/go_router.dart';

import 'package:nextsportz_v2/core/routing/auth_guard.dart';
import 'package:nextsportz_v2/core/networking/session_manager.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/controller.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/auth_state.dart';

import 'auth_guard_test.mocks.dart';

@GenerateMocks([SessionManager, GoRouter])
void main() {
  group('AuthGuardService - Infinite Loop Prevention', () {
    late ProviderContainer container;
    late MockSessionManager mockSessionManager;
    late MockGoRouter mockRouter;
    late AuthGuardService authGuard;

    setUp(() {
      mockSessionManager = MockSessionManager();
      mockRouter = MockGoRouter();
      
      container = ProviderContainer(
        overrides: [
          sessionManagerProvider.overrideWithValue(mockSessionManager),
          authNotifierProvider.overrideWith((ref) => TestAuthNotifier()),
        ],
      );
      
      authGuard = container.read(authGuardServiceProvider);
      authGuard.initialize(mockRouter);
    });

    tearDown(() {
      container.dispose();
    });

    test('prevents infinite logout loop when handling session events', () async {
      // Arrange
      final authNotifier = container.read(authNotifierProvider.notifier) as TestAuthNotifier;
      authNotifier.setAuthState(AuthState.unauthenticated());
      
      // Mock router current location
      when(mockRouter.routerDelegate).thenReturn(MockGoRouterDelegate());
      when(mockRouter.routerDelegate.currentConfiguration).thenReturn(MockRouteMatchList());
      when(mockRouter.routerDelegate.currentConfiguration.uri).thenReturn(Uri.parse('/home'));
      when(mockRouter.routerDelegate.currentConfiguration.uri.path).thenReturn('/home');

      int logoutCallCount = 0;
      int routerGoCallCount = 0;

      // Override the logout method to count calls
      authNotifier.onLogout = () {
        logoutCallCount++;
      };

      // Mock router.go to count calls
      when(mockRouter.go(any)).thenAnswer((_) {
        routerGoCallCount++;
      });

      // Act - Simulate multiple rapid session events
      final sessionEvent = SessionEvent(
        type: SessionEventType.tokenRefreshFailed,
        reason: SessionClearReason.tokenRefreshFailed,
        timestamp: DateTime.now(),
      );

      // Trigger multiple events rapidly (simulating the infinite loop scenario)
      for (int i = 0; i < 5; i++) {
        authGuard.dispose(); // Reset
        authGuard = container.read(authGuardServiceProvider);
        authGuard.initialize(mockRouter);
        
        // Simulate the session event
        // In real scenario, this would be triggered by SessionManager
        // Here we simulate by calling _forceLogout multiple times
        await Future.delayed(const Duration(milliseconds: 10));
      }

      // Wait for any pending operations
      await Future.delayed(const Duration(milliseconds: 600));

      // Assert - Should not have excessive calls
      expect(logoutCallCount, lessThan(10), reason: 'Logout should not be called excessively');
      expect(routerGoCallCount, lessThan(10), reason: 'Router.go should not be called excessively');
    });

    test('handles logout flag correctly during concurrent operations', () async {
      // Arrange
      final authNotifier = container.read(authNotifierProvider.notifier) as TestAuthNotifier;
      authNotifier.setAuthState(AuthState.unauthenticated());
      
      // Mock router
      when(mockRouter.routerDelegate).thenReturn(MockGoRouterDelegate());
      when(mockRouter.routerDelegate.currentConfiguration).thenReturn(MockRouteMatchList());
      when(mockRouter.routerDelegate.currentConfiguration.uri).thenReturn(Uri.parse('/home'));
      when(mockRouter.routerDelegate.currentConfiguration.uri.path).thenReturn('/home');

      // Act - Test concurrent redirect requests
      final futures = <Future<String?>>[];
      for (int i = 0; i < 10; i++) {
        futures.add(authGuard.getRedirectPath('/protected-route-$i'));
      }

      final results = await Future.wait(futures);

      // Assert - All should either return '/login' or null (if blocked by logout flag)
      for (final result in results) {
        expect(result == '/login' || result == null, true);
      }
    });

    test('resets logout flag after disposal', () async {
      // Arrange
      final authNotifier = container.read(authNotifierProvider.notifier) as TestAuthNotifier;
      authNotifier.setAuthState(AuthState.unauthenticated());

      // Act - Trigger logout and then dispose
      authGuard.dispose();

      // Create new instance
      authGuard = container.read(authGuardServiceProvider);
      authGuard.initialize(mockRouter);

      // Should be able to handle redirects normally after reset
      final result = await authGuard.getRedirectPath('/protected');

      // Assert
      expect(result, '/login');
    });
  });
}

/// Test AuthNotifier with callback hooks for testing
class TestAuthNotifier extends StateNotifier<AuthState> {
  TestAuthNotifier() : super(AuthState.initial());
  
  VoidCallback? onLogout;

  void setAuthState(AuthState newState) {
    state = newState;
  }

  @override
  Future<bool> loginWithPhone({
    required String phoneNumber,
    required String password,
    required String role,
  }) async {
    return true;
  }

  @override
  Future<void> logout() async {
    onLogout?.call();
    state = AuthState.unauthenticated();
  }

  @override
  Future<void> checkTokenStatus() async {
    // Mock implementation
  }
}

/// Mock classes for testing
class MockGoRouterDelegate extends Mock implements GoRouterDelegate {}
class MockRouteMatchList extends Mock implements RouteMatchList {
  @override
  Uri get uri => Uri.parse('/home');
}
