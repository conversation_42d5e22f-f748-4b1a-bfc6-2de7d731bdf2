import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:nextsportz_v2/core/routing/app_router.dart';
import 'package:nextsportz_v2/core/routing/auth_guard.dart';
import 'package:nextsportz_v2/core/networking/session_manager.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/controller.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/auth_state.dart';
import 'package:nextsportz_v2/features/auth/domain/entities/user.dart';
import 'package:nextsportz_v2/features/home/<USER>/screens/splash_screen.dart';
import 'package:nextsportz_v2/features/auth/presentation/screens/login_screen.dart';
import 'package:nextsportz_v2/features/home/<USER>/screens/home_screen.dart';

import 'app_router_test.mocks.dart';

@GenerateMocks([SessionManager])
void main() {
  group('App Router Integration Tests', () {
    late ProviderContainer container;
    late MockSessionManager mockSessionManager;
    late GoRouter router;

    setUp(() {
      mockSessionManager = MockSessionManager();
      
      container = ProviderContainer(
        overrides: [
          sessionManagerProvider.overrideWithValue(mockSessionManager),
          authNotifierProvider.overrideWith((ref) => TestAuthNotifier()),
        ],
      );
      
      router = createAppRouter(container);
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('redirects unauthenticated users from protected routes to login', (tester) async {
      // Arrange
      final authNotifier = container.read(authNotifierProvider.notifier) as TestAuthNotifier;
      authNotifier.setAuthState(AuthState.unauthenticated());
      when(mockSessionManager.isAuthenticated()).thenAnswer((_) async => false);

      // Act
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Navigate to protected route
      router.go('/home');
      await tester.pumpAndSettle();

      // Assert
      expect(router.routerDelegate.currentConfiguration.uri.path, '/login');
      expect(find.byType(LoginScreen), findsOneWidget);
    });

    testWidgets('allows authenticated users to access protected routes', (tester) async {
      // Arrange
      final authNotifier = container.read(authNotifierProvider.notifier) as TestAuthNotifier;
      authNotifier.setAuthState(AuthState.authenticated(
        User(
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          phoneNumber: '+**********',
          role: 'PLAYER',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
        ),
      ));

      // Act
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Navigate to protected route
      router.go('/home');
      await tester.pumpAndSettle();

      // Assert
      expect(router.routerDelegate.currentConfiguration.uri.path, '/home');
      expect(find.byType(HomeScreen), findsOneWidget);
    });

    testWidgets('redirects authenticated users from auth pages to home', (tester) async {
      // Arrange
      final authNotifier = container.read(authNotifierProvider.notifier) as TestAuthNotifier;
      authNotifier.setAuthState(AuthState.authenticated(
        User(
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          phoneNumber: '+**********',
          role: 'PLAYER',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
        ),
      ));

      // Act
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Navigate to login page
      router.go('/login');
      await tester.pumpAndSettle();

      // Assert
      expect(router.routerDelegate.currentConfiguration.uri.path, '/home');
      expect(find.byType(HomeScreen), findsOneWidget);
    });

    testWidgets('allows unauthenticated users to access public routes', (tester) async {
      // Arrange
      final authNotifier = container.read(authNotifierProvider.notifier) as TestAuthNotifier;
      authNotifier.setAuthState(AuthState.unauthenticated());
      when(mockSessionManager.isAuthenticated()).thenAnswer((_) async => false);

      // Act
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Navigate to splash (public route)
      router.go('/');
      await tester.pumpAndSettle();

      // Assert
      expect(router.routerDelegate.currentConfiguration.uri.path, '/');
      expect(find.byType(SplashScreen), findsOneWidget);
    });

    testWidgets('stores intended destination when redirecting to login', (tester) async {
      // Arrange
      final authNotifier = container.read(authNotifierProvider.notifier) as TestAuthNotifier;
      authNotifier.setAuthState(AuthState.unauthenticated());
      when(mockSessionManager.isAuthenticated()).thenAnswer((_) async => false);

      // Act
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Navigate to protected route
      router.go('/teams/123');
      await tester.pumpAndSettle();

      // Assert
      expect(router.routerDelegate.currentConfiguration.uri.path, '/login');
      
      // Check that intended destination is stored
      final authGuard = container.read(authGuardServiceProvider);
      final intendedDestination = authGuard.getAndClearIntendedDestination();
      expect(intendedDestination, '/teams/123');
    });

    group('Deep Linking Tests', () {
      testWidgets('handles deep links with query parameters', (tester) async {
        // Arrange
        final authNotifier = container.read(authNotifierProvider.notifier) as TestAuthNotifier;
        authNotifier.setAuthState(AuthState.unauthenticated());
        when(mockSessionManager.isAuthenticated()).thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(
          ProviderScope(
            parent: container,
            child: MaterialApp.router(
              routerConfig: router,
            ),
          ),
        );

        // Navigate to deep link with query parameters
        router.go('/teams/123?tab=members&sort=name');
        await tester.pumpAndSettle();

        // Assert
        expect(router.routerDelegate.currentConfiguration.uri.path, '/login');
        
        // Check that full URI is stored as intended destination
        final authGuard = container.read(authGuardServiceProvider);
        final intendedDestination = authGuard.getAndClearIntendedDestination();
        expect(intendedDestination, contains('/teams/123'));
        expect(intendedDestination, contains('tab=members'));
        expect(intendedDestination, contains('sort=name'));
      });
    });
  });
}

/// Test AuthNotifier for integration tests
class TestAuthNotifier extends StateNotifier<AuthState> {
  TestAuthNotifier() : super(AuthState.initial());

  void setAuthState(AuthState newState) {
    state = newState;
  }

  @override
  Future<bool> loginWithPhone({
    required String phoneNumber,
    required String password,
    required String role,
  }) async {
    return true;
  }

  @override
  Future<void> logout() async {
    state = AuthState.unauthenticated();
  }

  @override
  Future<void> checkTokenStatus() async {
    // Mock implementation
  }
}
