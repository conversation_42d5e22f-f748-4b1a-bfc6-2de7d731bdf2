import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/core/models/image_upload_models.dart';

void main() {
  group('ImageType', () {
    test('should have correct string values', () {
      expect(ImageType.userProfile.value, 'UserProfile');
      expect(ImageType.teamLogo.value, 'TeamLogo');
      expect(ImageType.advertisementBanner.value, 'AdvertisementBanner');
      expect(ImageType.tournamentBanner.value, 'TournamentBanner');
    });

    test('should convert from string correctly', () {
      expect(ImageType.fromString('UserProfile'), ImageType.userProfile);
      expect(ImageType.fromString('TeamLogo'), ImageType.teamLogo);
      expect(
        ImageType.fromString('AdvertisementBanner'),
        ImageType.advertisementBanner,
      );
      expect(
        ImageType.fromString('TournamentBanner'),
        ImageType.tournamentBanner,
      );
    });

    test('should throw error for unknown string', () {
      expect(() => ImageType.fromString('Unknown'), throwsArgumentError);
    });
  });

  group('ImageUploadRequest', () {
    test('should create instance with required fields', () {
      const request = ImageUploadRequest(type: ImageType.userProfile);

      expect(request.type, ImageType.userProfile);
      expect(request.fileName, isNull);
      expect(request.entityId, isNull);
    });

    test('should create instance with all fields', () {
      const request = ImageUploadRequest(
        type: ImageType.teamLogo,
        fileName: 'logo.png',
        entityId: 'team-123',
      );

      expect(request.type, ImageType.teamLogo);
      expect(request.fileName, 'logo.png');
      expect(request.entityId, 'team-123');
    });

    test('should convert to JSON correctly', () {
      const request = ImageUploadRequest(
        type: ImageType.userProfile,
        fileName: 'profile.jpg',
        entityId: 'user-456',
      );

      final json = request.toJson();

      expect(json, {
        'type': 'UserProfile',
        'fileName': 'profile.jpg',
        'entityId': 'user-456',
      });
    });

    test('should convert to JSON with only required fields', () {
      const request = ImageUploadRequest(type: ImageType.tournamentBanner);

      final json = request.toJson();

      expect(json, {'type': 'TournamentBanner'});
    });

    test('should create from JSON correctly', () {
      final json = {
        'type': 'TeamLogo',
        'fileName': 'team.png',
        'entityId': 'team-789',
      };

      final request = ImageUploadRequest.fromJson(json);

      expect(request.type, ImageType.teamLogo);
      expect(request.fileName, 'team.png');
      expect(request.entityId, 'team-789');
    });

    test('should handle equality correctly', () {
      const request1 = ImageUploadRequest(
        type: ImageType.userProfile,
        fileName: 'test.jpg',
        entityId: 'user-1',
      );

      const request2 = ImageUploadRequest(
        type: ImageType.userProfile,
        fileName: 'test.jpg',
        entityId: 'user-1',
      );

      const request3 = ImageUploadRequest(
        type: ImageType.teamLogo,
        fileName: 'test.jpg',
        entityId: 'user-1',
      );

      expect(request1, equals(request2));
      expect(request1, isNot(equals(request3)));
    });
  });

  group('ImageUploadResponse', () {
    test('should create instance with null values', () {
      const response = ImageUploadResponse();

      expect(response.uploadUrl, isNull);
      expect(response.downloadUrl, isNull);
    });

    test('should create instance with values', () {
      const response = ImageUploadResponse(
        uploadUrl: 'https://upload.example.com/123',
        downloadUrl: 'https://cdn.example.com/image.jpg',
      );

      expect(response.uploadUrl, 'https://upload.example.com/123');
      expect(response.downloadUrl, 'https://cdn.example.com/image.jpg');
    });

    test('should convert to JSON correctly', () {
      const response = ImageUploadResponse(
        uploadUrl: 'https://upload.example.com/456',
        downloadUrl: 'https://cdn.example.com/photo.png',
      );

      final json = response.toJson();

      expect(json, {
        'uploadUrl': 'https://upload.example.com/456',
        'downloadUrl': 'https://cdn.example.com/photo.png',
      });
    });

    test('should create from JSON correctly', () {
      final json = {
        'uploadUrl': 'https://upload.example.com/789',
        'downloadUrl': 'https://cdn.example.com/banner.gif',
      };

      final response = ImageUploadResponse.fromJson(json);

      expect(response.uploadUrl, 'https://upload.example.com/789');
      expect(response.downloadUrl, 'https://cdn.example.com/banner.gif');
    });

    test('should handle equality correctly', () {
      const response1 = ImageUploadResponse(
        uploadUrl: 'https://upload.example.com/test',
        downloadUrl: 'https://cdn.example.com/test.jpg',
      );

      const response2 = ImageUploadResponse(
        uploadUrl: 'https://upload.example.com/test',
        downloadUrl: 'https://cdn.example.com/test.jpg',
      );

      const response3 = ImageUploadResponse(
        uploadUrl: 'https://upload.example.com/different',
        downloadUrl: 'https://cdn.example.com/test.jpg',
      );

      expect(response1, equals(response2));
      expect(response1, isNot(equals(response3)));
    });
  });
}
