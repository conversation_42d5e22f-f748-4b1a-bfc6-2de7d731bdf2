import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';

/// Test model for testing PaginatedResponse
class TestItem {
  final String id;
  final String name;

  const TestItem({required this.id, required this.name});

  factory TestItem.fromJson(Map<String, dynamic> json) {
    return TestItem(
      id: json['id'] as String,
      name: json['name'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TestItem && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

void main() {
  group('PaginatedResponse', () {
    late List<TestItem> testItems;
    late Map<String, dynamic> testJson;

    setUp(() {
      testItems = [
        const TestItem(id: '1', name: 'Item 1'),
        const TestItem(id: '2', name: 'Item 2'),
        const TestItem(id: '3', name: 'Item 3'),
      ];

      testJson = {
        'message': 'Success',
        'data': {
          'items': [
            {'id': '1', 'name': 'Item 1'},
            {'id': '2', 'name': 'Item 2'},
            {'id': '3', 'name': 'Item 3'},
          ],
          'total': 10,
          'page': 1,
          'pageSize': 3,
          'totalPages': 4,
        },
        'error': null,
        'validationErrors': null,
      };
    });

    group('fromJson', () {
      test('should create PaginatedResponse from valid JSON', () {
        // Act
        final result = PaginatedResponse<TestItem>.fromJson(
          testJson,
          (json) => TestItem.fromJson(json),
        );

        // Assert
        expect(result.message, equals('Success'));
        expect(result.error, isNull);
        expect(result.validationErrors, isNull);
        expect(result.data.items.length, equals(3));
        expect(result.data.total, equals(10));
        expect(result.data.page, equals(1));
        expect(result.data.pageSize, equals(3));
        expect(result.data.totalPages, equals(4));
        expect(result.data.items.first.id, equals('1'));
        expect(result.data.items.first.name, equals('Item 1'));
      });

      test('should handle empty items array', () {
        // Arrange
        final emptyJson = {
          'message': 'Success',
          'data': {
            'items': [],
            'total': 0,
            'page': 1,
            'pageSize': 20,
            'totalPages': 0,
          },
          'error': null,
          'validationErrors': null,
        };

        // Act
        final result = PaginatedResponse<TestItem>.fromJson(
          emptyJson,
          (json) => TestItem.fromJson(json),
        );

        // Assert
        expect(result.data.items.isEmpty, isTrue);
        expect(result.data.total, equals(0));
        expect(result.data.totalPages, equals(0));
      });

      test('should handle missing optional fields', () {
        // Arrange
        final minimalJson = {
          'data': {
            'items': [{'id': '1', 'name': 'Item 1'}],
          },
        };

        // Act
        final result = PaginatedResponse<TestItem>.fromJson(
          minimalJson,
          (json) => TestItem.fromJson(json),
        );

        // Assert
        expect(result.message, equals(''));
        expect(result.error, isNull);
        expect(result.data.items.length, equals(1));
        expect(result.data.total, equals(0)); // Default value
        expect(result.data.page, equals(1)); // Default value
        expect(result.data.pageSize, equals(20)); // Default value
        expect(result.data.totalPages, equals(0)); // Default value
      });

      test('should handle error response', () {
        // Arrange
        final errorJson = {
          'message': 'Error',
          'data': {
            'items': [],
            'total': 0,
            'page': 1,
            'pageSize': 20,
            'totalPages': 0,
          },
          'error': 'Something went wrong',
          'validationErrors': {'field': ['error message']},
        };

        // Act
        final result = PaginatedResponse<TestItem>.fromJson(
          errorJson,
          (json) => TestItem.fromJson(json),
        );

        // Assert
        expect(result.message, equals('Error'));
        expect(result.error, equals('Something went wrong'));
        expect(result.validationErrors, isNotNull);
        expect(result.isSuccess, isFalse);
      });
    });

    group('toJson', () {
      test('should convert PaginatedResponse to JSON', () {
        // Arrange
        final response = PaginatedResponse<TestItem>(
          message: 'Success',
          data: PaginatedData<TestItem>(
            items: testItems,
            total: 10,
            page: 1,
            pageSize: 3,
            totalPages: 4,
          ),
        );

        // Act
        final result = response.toJson((item) => item.toJson());

        // Assert
        expect(result['message'], equals('Success'));
        expect(result['error'], isNull);
        expect(result['data']['items'], hasLength(3));
        expect(result['data']['total'], equals(10));
        expect(result['data']['page'], equals(1));
        expect(result['data']['pageSize'], equals(3));
        expect(result['data']['totalPages'], equals(4));
      });
    });

    group('utility methods', () {
      test('isSuccess should return true when error is null', () {
        // Arrange
        final response = PaginatedResponse<TestItem>(
          message: 'Success',
          data: PaginatedData<TestItem>(
            items: testItems,
            total: 10,
            page: 1,
            pageSize: 3,
            totalPages: 4,
          ),
        );

        // Assert
        expect(response.isSuccess, isTrue);
      });

      test('isSuccess should return false when error is not null', () {
        // Arrange
        final response = PaginatedResponse<TestItem>(
          message: 'Error',
          data: PaginatedData<TestItem>(
            items: [],
            total: 0,
            page: 1,
            pageSize: 20,
            totalPages: 0,
          ),
          error: 'Something went wrong',
        );

        // Assert
        expect(response.isSuccess, isFalse);
      });

      test('hasNextPage should delegate to data', () {
        // Arrange
        final response = PaginatedResponse<TestItem>(
          message: 'Success',
          data: PaginatedData<TestItem>(
            items: testItems,
            total: 10,
            page: 1,
            pageSize: 3,
            totalPages: 4,
          ),
        );

        // Assert
        expect(response.hasNextPage, isTrue);
      });

      test('hasPreviousPage should delegate to data', () {
        // Arrange
        final response = PaginatedResponse<TestItem>(
          message: 'Success',
          data: PaginatedData<TestItem>(
            items: testItems,
            total: 10,
            page: 2,
            pageSize: 3,
            totalPages: 4,
          ),
        );

        // Assert
        expect(response.hasPreviousPage, isTrue);
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        // Arrange
        final original = PaginatedResponse<TestItem>(
          message: 'Success',
          data: PaginatedData<TestItem>(
            items: testItems,
            total: 10,
            page: 1,
            pageSize: 3,
            totalPages: 4,
          ),
        );

        // Act
        final copy = original.copyWith(
          message: 'Updated',
          error: 'New error',
        );

        // Assert
        expect(copy.message, equals('Updated'));
        expect(copy.error, equals('New error'));
        expect(copy.data, equals(original.data));
        expect(copy.validationErrors, equals(original.validationErrors));
      });
    });
  });

  group('PaginatedData', () {
    late List<TestItem> testItems;

    setUp(() {
      testItems = [
        const TestItem(id: '1', name: 'Item 1'),
        const TestItem(id: '2', name: 'Item 2'),
        const TestItem(id: '3', name: 'Item 3'),
      ];
    });

    group('pagination state methods', () {
      test('hasNextPage should return true when page < totalPages', () {
        // Arrange
        final data = PaginatedData<TestItem>(
          items: testItems,
          total: 10,
          page: 1,
          pageSize: 3,
          totalPages: 4,
        );

        // Assert
        expect(data.hasNextPage, isTrue);
      });

      test('hasNextPage should return false when page >= totalPages', () {
        // Arrange
        final data = PaginatedData<TestItem>(
          items: testItems,
          total: 3,
          page: 1,
          pageSize: 3,
          totalPages: 1,
        );

        // Assert
        expect(data.hasNextPage, isFalse);
      });

      test('hasPreviousPage should return true when page > 1', () {
        // Arrange
        final data = PaginatedData<TestItem>(
          items: testItems,
          total: 10,
          page: 2,
          pageSize: 3,
          totalPages: 4,
        );

        // Assert
        expect(data.hasPreviousPage, isTrue);
      });

      test('hasPreviousPage should return false when page <= 1', () {
        // Arrange
        final data = PaginatedData<TestItem>(
          items: testItems,
          total: 10,
          page: 1,
          pageSize: 3,
          totalPages: 4,
        );

        // Assert
        expect(data.hasPreviousPage, isFalse);
      });

      test('isFirstPage should return true when page is 1', () {
        // Arrange
        final data = PaginatedData<TestItem>(
          items: testItems,
          total: 10,
          page: 1,
          pageSize: 3,
          totalPages: 4,
        );

        // Assert
        expect(data.isFirstPage, isTrue);
      });

      test('isLastPage should return true when page equals totalPages', () {
        // Arrange
        final data = PaginatedData<TestItem>(
          items: testItems,
          total: 3,
          page: 1,
          pageSize: 3,
          totalPages: 1,
        );

        // Assert
        expect(data.isLastPage, isTrue);
      });
    });

    group('range calculations', () {
      test('should calculate correct start and end indices', () {
        // Arrange
        final data = PaginatedData<TestItem>(
          items: testItems,
          total: 10,
          page: 2,
          pageSize: 3,
          totalPages: 4,
        );

        // Assert
        expect(data.startIndex, equals(4)); // (2-1) * 3 + 1
        expect(data.endIndex, equals(6)); // 2 * 3
      });

      test('should handle end index when it exceeds total', () {
        // Arrange
        final data = PaginatedData<TestItem>(
          items: testItems,
          total: 5,
          page: 2,
          pageSize: 3,
          totalPages: 2,
        );

        // Assert
        expect(data.startIndex, equals(4));
        expect(data.endIndex, equals(5)); // Should be capped at total
      });

      test('should generate correct range summary', () {
        // Arrange
        final data = PaginatedData<TestItem>(
          items: testItems,
          total: 10,
          page: 1,
          pageSize: 3,
          totalPages: 4,
        );

        // Assert
        expect(data.rangeSummary, equals('1-3 of 10'));
      });

      test('should handle empty data in range summary', () {
        // Arrange
        final data = PaginatedData<TestItem>(
          items: const [],
          total: 0,
          page: 1,
          pageSize: 20,
          totalPages: 0,
        );

        // Assert
        expect(data.rangeSummary, equals('0 of 0'));
      });
    });

    group('appendPage', () {
      test('should append items from next page', () {
        // Arrange
        final firstPage = PaginatedData<TestItem>(
          items: testItems.take(2).toList(),
          total: 10,
          page: 1,
          pageSize: 2,
          totalPages: 5,
        );

        final secondPage = PaginatedData<TestItem>(
          items: [const TestItem(id: '3', name: 'Item 3')],
          total: 10,
          page: 2,
          pageSize: 2,
          totalPages: 5,
        );

        // Act
        final result = firstPage.appendPage(secondPage);

        // Assert
        expect(result.items.length, equals(3));
        expect(result.items.last.id, equals('3'));
        expect(result.page, equals(2));
        expect(result.total, equals(10));
        expect(result.totalPages, equals(5));
      });
    });
  });

  group('EmptyPaginatedResponse', () {
    test('should create empty response with default values', () {
      // Act
      final empty = EmptyPaginatedResponse<TestItem>();

      // Assert
      expect(empty.message, equals(''));
      expect(empty.error, isNull);
      expect(empty.data.items.isEmpty, isTrue);
      expect(empty.data.total, equals(0));
      expect(empty.data.page, equals(1));
      expect(empty.data.pageSize, equals(20));
      expect(empty.data.totalPages, equals(0));
    });
  });
}
