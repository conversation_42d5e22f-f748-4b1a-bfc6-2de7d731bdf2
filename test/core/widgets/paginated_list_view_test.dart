import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/core/widgets/paginated_list_view.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';

/// Test model for testing PaginatedListView
class TestItem {
  final String id;
  final String name;

  const TestItem({required this.id, required this.name});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TestItem && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

void main() {
  group('PaginatedListView', () {
    late List<TestItem> testItems;
    late PaginatedResponse<TestItem> testResponse;

    setUp(() {
      testItems = [
        const TestItem(id: '1', name: 'Item 1'),
        const TestItem(id: '2', name: 'Item 2'),
        const TestItem(id: '3', name: 'Item 3'),
      ];

      testResponse = PaginatedResponse<TestItem>(
        message: 'Success',
        data: PaginatedData<TestItem>(
          items: testItems,
          total: 10,
          page: 1,
          pageSize: 3,
          totalPages: 4,
        ),
      );
    });

    Widget createTestWidget({
      Future<PaginatedResponse<TestItem>> Function(int page)? onLoadPage,
      Widget Function(BuildContext, TestItem, int)? itemBuilder,
      Widget? emptyWidget,
      Widget Function(String, VoidCallback)? errorBuilder,
      Widget? loadingWidget,
      bool enableRefresh = true,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: PaginatedListView<TestItem>(
            onLoadPage: onLoadPage ?? (_) async => testResponse,
            itemBuilder: itemBuilder ?? 
                (context, item, index) => ListTile(
                  key: Key('item_${item.id}'),
                  title: Text(item.name),
                ),
            emptyWidget: emptyWidget,
            errorBuilder: errorBuilder,
            loadingWidget: loadingWidget,
            enableRefresh: enableRefresh,
          ),
        ),
      );
    }

    testWidgets('should display loading widget initially', (tester) async {
      // Arrange
      bool loadingCalled = false;
      final widget = createTestWidget(
        onLoadPage: (_) async {
          loadingCalled = true;
          await Future.delayed(const Duration(milliseconds: 100));
          return testResponse;
        },
        loadingWidget: const Text('Loading...'),
      );

      // Act
      await tester.pumpWidget(widget);

      // Assert
      expect(find.text('Loading...'), findsOneWidget);
      expect(loadingCalled, isTrue);
    });

    testWidgets('should display items after loading', (tester) async {
      // Arrange
      final widget = createTestWidget();

      // Act
      await tester.pumpWidget(widget);
      await tester.pump(); // Allow the future to complete

      // Assert
      expect(find.text('Item 1'), findsOneWidget);
      expect(find.text('Item 2'), findsOneWidget);
      expect(find.text('Item 3'), findsOneWidget);
      expect(find.byKey(const Key('item_1')), findsOneWidget);
      expect(find.byKey(const Key('item_2')), findsOneWidget);
      expect(find.byKey(const Key('item_3')), findsOneWidget);
    });

    testWidgets('should display empty widget when no items', (tester) async {
      // Arrange
      final emptyResponse = PaginatedResponse<TestItem>(
        message: 'Success',
        data: const PaginatedData<TestItem>(
          items: [],
          total: 0,
          page: 1,
          pageSize: 20,
          totalPages: 0,
        ),
      );

      final widget = createTestWidget(
        onLoadPage: (_) async => emptyResponse,
        emptyWidget: const Text('No items found'),
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.pump();

      // Assert
      expect(find.text('No items found'), findsOneWidget);
      expect(find.text('Item 1'), findsNothing);
    });

    testWidgets('should display error widget when loading fails', (tester) async {
      // Arrange
      const errorMessage = 'Network error';
      final widget = createTestWidget(
        onLoadPage: (_) async => throw Exception(errorMessage),
        errorBuilder: (error, onRetry) => Column(
          children: [
            Text('Error: $error'),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('Retry'),
            ),
          ],
        ),
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.pump();

      // Assert
      expect(find.textContaining('Error:'), findsOneWidget);
      expect(find.textContaining(errorMessage), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('should retry loading when retry button is pressed', (tester) async {
      // Arrange
      int loadAttempts = 0;
      final widget = createTestWidget(
        onLoadPage: (_) async {
          loadAttempts++;
          if (loadAttempts == 1) {
            throw Exception('Network error');
          }
          return testResponse;
        },
        errorBuilder: (error, onRetry) => ElevatedButton(
          onPressed: onRetry,
          child: const Text('Retry'),
        ),
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.pump(); // First load fails

      expect(find.text('Retry'), findsOneWidget);
      expect(loadAttempts, equals(1));

      await tester.tap(find.text('Retry'));
      await tester.pump(); // Retry succeeds

      // Assert
      expect(loadAttempts, equals(2));
      expect(find.text('Item 1'), findsOneWidget);
      expect(find.text('Retry'), findsNothing);
    });

    testWidgets('should load more items when scrolling to bottom', (tester) async {
      // Arrange
      int currentPage = 1;
      final widget = createTestWidget(
        onLoadPage: (page) async {
          currentPage = page;
          if (page == 1) {
            return testResponse;
          } else {
            // Second page
            final moreItems = [
              const TestItem(id: '4', name: 'Item 4'),
              const TestItem(id: '5', name: 'Item 5'),
            ];
            return PaginatedResponse<TestItem>(
              message: 'Success',
              data: PaginatedData<TestItem>(
                items: moreItems,
                total: 10,
                page: 2,
                pageSize: 3,
                totalPages: 4,
              ),
            );
          }
        },
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.pump(); // Load first page

      // Verify first page is loaded
      expect(find.text('Item 1'), findsOneWidget);
      expect(find.text('Item 4'), findsNothing);
      expect(currentPage, equals(1));

      // Scroll to bottom to trigger loading more
      await tester.drag(find.byType(ListView), const Offset(0, -500));
      await tester.pump();
      await tester.pump(); // Allow loading more to complete

      // Assert
      expect(currentPage, equals(2));
      expect(find.text('Item 1'), findsOneWidget); // Original items still there
      expect(find.text('Item 4'), findsOneWidget); // New items loaded
      expect(find.text('Item 5'), findsOneWidget);
    });

    testWidgets('should handle pull to refresh', (tester) async {
      // Arrange
      int refreshCount = 0;
      final widget = createTestWidget(
        onLoadPage: (page) async {
          if (page == 1) refreshCount++;
          return testResponse;
        },
        enableRefresh: true,
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.pump(); // Initial load

      expect(refreshCount, equals(1));

      // Pull to refresh
      await tester.fling(find.byType(ListView), const Offset(0, 300), 1000);
      await tester.pump();
      await tester.pump(const Duration(seconds: 1)); // Wait for refresh

      // Assert
      expect(refreshCount, equals(2));
    });

    testWidgets('should not enable refresh when disabled', (tester) async {
      // Arrange
      final widget = createTestWidget(enableRefresh: false);

      // Act
      await tester.pumpWidget(widget);
      await tester.pump();

      // Assert
      expect(find.byType(RefreshIndicator), findsNothing);
      expect(find.byType(ListView), findsOneWidget);
    });

    testWidgets('should display loading more indicator when loading next page', (tester) async {
      // Arrange
      bool isLoadingMore = false;
      final widget = createTestWidget(
        onLoadPage: (page) async {
          if (page > 1) {
            isLoadingMore = true;
            await Future.delayed(const Duration(milliseconds: 100));
          }
          return testResponse;
        },
        loadingMoreWidget: const Text('Loading more...'),
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.pump(); // Load first page

      // Trigger loading more
      await tester.drag(find.byType(ListView), const Offset(0, -500));
      await tester.pump(); // Start loading more

      // Assert
      expect(find.text('Loading more...'), findsOneWidget);
      expect(isLoadingMore, isTrue);
    });

    testWidgets('should use custom scroll controller when provided', (tester) async {
      // Arrange
      final scrollController = ScrollController();
      final widget = MaterialApp(
        home: Scaffold(
          body: PaginatedListView<TestItem>(
            onLoadPage: (_) async => testResponse,
            itemBuilder: (context, item, index) => ListTile(title: Text(item.name)),
            scrollController: scrollController,
          ),
        ),
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.pump();

      // Assert
      expect(scrollController.hasClients, isTrue);
      
      // Cleanup
      scrollController.dispose();
    });

    testWidgets('should show separators when enabled', (tester) async {
      // Arrange
      final widget = createTestWidget();
      final widgetWithSeparators = MaterialApp(
        home: Scaffold(
          body: PaginatedListView<TestItem>(
            onLoadPage: (_) async => testResponse,
            itemBuilder: (context, item, index) => ListTile(title: Text(item.name)),
            showSeparator: true,
            separator: const Divider(key: Key('separator')),
          ),
        ),
      );

      // Act
      await tester.pumpWidget(widgetWithSeparators);
      await tester.pump();

      // Assert
      expect(find.byKey(const Key('separator')), findsWidgets);
    });

    testWidgets('should handle empty state with refresh enabled', (tester) async {
      // Arrange
      final emptyResponse = PaginatedResponse<TestItem>(
        message: 'Success',
        data: const PaginatedData<TestItem>(
          items: [],
          total: 0,
          page: 1,
          pageSize: 20,
          totalPages: 0,
        ),
      );

      final widget = createTestWidget(
        onLoadPage: (_) async => emptyResponse,
        emptyWidget: const Text('No items found'),
        enableRefresh: true,
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.pump();

      // Assert
      expect(find.text('No items found'), findsOneWidget);
      expect(find.byType(RefreshIndicator), findsOneWidget);
    });
  });
}
