import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/core/widgets/blaze_text_form_field.dart';
import 'package:nextsportz_v2/core/config/theme.dart';

void main() {
  group('BlazeTextFormField', () {
    testWidgets('should render with basic properties', (
      WidgetTester tester,
    ) async {
      // Arrange
      const hintText = 'Enter text';
      const prefixIcon = Icons.email;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BlazeTextFormField(
              hintText: hintText,
              prefixIcon: prefixIcon,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(hintText), findsOneWidget);
      expect(find.byIcon(prefixIcon), findsOneWidget);
      expect(find.byType(TextFormField), findsOneWidget);
    });

    testWidgets('should show password toggle when isPassword is true', (
      WidgetTester tester,
    ) async {
      // Arrange
      const hintText = 'Enter password';
      const prefixIcon = Icons.lock;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BlazeTextFormField(
              hintText: hintText,
              prefixIcon: prefixIcon,
              isPassword: true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.remove_red_eye_outlined), findsOneWidget);

      // Tap the eye icon to toggle visibility
      await tester.tap(find.byIcon(Icons.remove_red_eye_outlined));
      await tester.pump();

      expect(find.byIcon(Icons.remove_red_eye), findsOneWidget);
    });

    testWidgets('should show custom suffix icon when provided', (
      WidgetTester tester,
    ) async {
      // Arrange
      const hintText = 'Enter text';
      const prefixIcon = Icons.email;
      const suffixIcon = Icons.search;
      bool suffixTapped = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BlazeTextFormField(
              hintText: hintText,
              prefixIcon: prefixIcon,
              suffixIcon: suffixIcon,
              onSuffixTap: () => suffixTapped = true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(suffixIcon), findsOneWidget);

      // Tap the suffix icon
      await tester.tap(find.byIcon(suffixIcon));
      await tester.pump();

      expect(suffixTapped, isTrue);
    });

    testWidgets('should use provided controller', (WidgetTester tester) async {
      // Arrange
      const hintText = 'Enter text';
      const prefixIcon = Icons.email;
      final controller = TextEditingController();

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BlazeTextFormField(
              hintText: hintText,
              prefixIcon: prefixIcon,
              controller: controller,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(TextFormField), findsOneWidget);

      // Enter text
      await tester.enterText(find.byType(TextFormField), 'test text');
      await tester.pump();

      expect(controller.text, equals('test text'));
    });

    testWidgets('should validate input when validator is provided', (
      WidgetTester tester,
    ) async {
      // Arrange
      const hintText = 'Enter email';
      const prefixIcon = Icons.email;
      String? validationError;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: BlazeTextFormField(
                hintText: hintText,
                prefixIcon: prefixIcon,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Email is required';
                  }
                  return null;
                },
              ),
            ),
          ),
        ),
      );

      // Find the form and validate
      final form = tester.widget<Form>(find.byType(Form));
      final formKey = GlobalKey<FormState>();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              key: formKey,
              child: BlazeTextFormField(
                hintText: hintText,
                prefixIcon: prefixIcon,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Email is required';
                  }
                  return null;
                },
              ),
            ),
          ),
        ),
      );

      // Validate empty form
      final isValid = formKey.currentState?.validate() ?? false;
      expect(isValid, isFalse);
    });

    testWidgets('should respect enabled property', (WidgetTester tester) async {
      // Arrange
      const hintText = 'Enter text';
      const prefixIcon = Icons.email;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BlazeTextFormField(
              hintText: hintText,
              prefixIcon: prefixIcon,
              enabled: false,
            ),
          ),
        ),
      );

      // Assert
      final textField = tester.widget<TextFormField>(
        find.byType(TextFormField),
      );
      expect(textField.enabled, isFalse);
    });

    testWidgets('should respect maxLines property', (
      WidgetTester tester,
    ) async {
      // Arrange
      const hintText = 'Enter text';
      const prefixIcon = Icons.email;
      const maxLines = 3;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BlazeTextFormField(
              hintText: hintText,
              prefixIcon: prefixIcon,
              maxLines: maxLines,
            ),
          ),
        ),
      );

      // Assert
      final textField = tester.widget<TextField>(
        find.byType(TextField),
      );
      expect(textField.maxLines, equals(maxLines));
    });

    testWidgets('should respect maxLength property', (
      WidgetTester tester,
    ) async {
      // Arrange
      const hintText = 'Enter text';
      const prefixIcon = Icons.email;
      const maxLength = 10;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BlazeTextFormField(
              hintText: hintText,
              prefixIcon: prefixIcon,
              maxLength: maxLength,
            ),
          ),
        ),
      );

      // Assert
      final textField = tester.widget<TextField>(
        find.byType(TextField),
      );
      expect(textField.maxLength, equals(maxLength));
    });

    testWidgets('should respect keyboardType property', (
      WidgetTester tester,
    ) async {
      // Arrange
      const hintText = 'Enter number';
      const prefixIcon = Icons.phone;
      const keyboardType = TextInputType.number;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BlazeTextFormField(
              hintText: hintText,
              prefixIcon: prefixIcon,
              keyboardType: keyboardType,
            ),
          ),
        ),
      );

      // Assert
      final textField = tester.widget<TextField>(
        find.byType(TextField),
      );
      expect(textField.keyboardType, equals(keyboardType));
    });

    testWidgets('should have correct styling', (WidgetTester tester) async {
      // Arrange
      const hintText = 'Enter text';
      const prefixIcon = Icons.email;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BlazeTextFormField(
              hintText: hintText,
              prefixIcon: prefixIcon,
            ),
          ),
        ),
      );

      // Assert
      final textField = tester.widget<TextField>(
        find.byType(TextField),
      );
      expect(textField.style?.color, equals(Colors.white));
      expect(textField.style?.fontFamily, equals('Gilroy_Medium'));

      final decoration = textField.decoration!;
      expect(decoration.fillColor, equals(NextSportzTheme.lightBlue));
      expect(decoration.hintText, equals(hintText));
      expect(decoration.hintStyle?.color, equals(NextSportzTheme.grey));
      expect(decoration.hintStyle?.fontFamily, equals('Gilroy_Medium'));
      expect(decoration.hintStyle?.fontSize, equals(12));
    });

    testWidgets('should have correct border styling', (
      WidgetTester tester,
    ) async {
      // Arrange
      const hintText = 'Enter text';
      const prefixIcon = Icons.email;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BlazeTextFormField(
              hintText: hintText,
              prefixIcon: prefixIcon,
            ),
          ),
        ),
      );

      // Assert
      final textField = tester.widget<TextField>(
        find.byType(TextField),
      );
      final decoration = textField.decoration!;

      // Check border properties
      expect(decoration.border.runtimeType, equals(OutlineInputBorder));
      expect(decoration.enabledBorder.runtimeType, equals(OutlineInputBorder));
      expect(decoration.focusedBorder.runtimeType, equals(OutlineInputBorder));
      expect(decoration.errorBorder.runtimeType, equals(OutlineInputBorder));
      expect(
        decoration.focusedErrorBorder.runtimeType,
        equals(OutlineInputBorder),
      );
    });

    testWidgets('should have correct content padding', (
      WidgetTester tester,
    ) async {
      // Arrange
      const hintText = 'Enter text';
      const prefixIcon = Icons.email;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BlazeTextFormField(
              hintText: hintText,
              prefixIcon: prefixIcon,
            ),
          ),
        ),
      );

      // Assert
      final textField = tester.widget<TextField>(
        find.byType(TextField),
      );
      final decoration = textField.decoration!;
      expect(
        decoration.contentPadding,
        equals(const EdgeInsets.symmetric(horizontal: 20, vertical: 16)),
      );
    });

    testWidgets('should have correct icon styling', (
      WidgetTester tester,
    ) async {
      // Arrange
      const hintText = 'Enter text';
      const prefixIcon = Icons.email;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BlazeTextFormField(
              hintText: hintText,
              prefixIcon: prefixIcon,
            ),
          ),
        ),
      );

      // Assert
      final textField = tester.widget<TextField>(
        find.byType(TextField),
      );
      final decoration = textField.decoration!;

      // Check prefix icon
      final prefixIconWidget = decoration.prefixIcon as Icon;
      expect(prefixIconWidget.icon, equals(prefixIcon));
      expect(prefixIconWidget.color, equals(Colors.white));
      expect(prefixIconWidget.size, equals(20));
    });
  });
}
