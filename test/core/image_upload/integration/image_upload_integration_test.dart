import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';

import 'package:nextsportz_v2/core/image_upload/image_upload_providers.dart';
import 'package:nextsportz_v2/core/image_upload/data/datasources/image_upload_mock_datasource.dart';
import 'package:nextsportz_v2/core/models/image_upload_models.dart';
import 'package:nextsportz_v2/features/profile/profile_providers.dart';
import 'package:nextsportz_v2/features/profile/domain/entities/profile.dart';
import 'package:nextsportz_v2/features/profile/data/datasources/profile_remote_datasource.dart';

import 'image_upload_integration_test.mocks.dart';

@GenerateMocks([ProfileRemoteDataSource, File])
void main() {
  group('Image Upload Integration Tests', () {
    late ProviderContainer container;
    late MockProfileRemoteDataSource mockProfileDatasource;
    late MockFile mockFile;

    setUp(() {
      mockProfileDatasource = MockProfileRemoteDataSource();
      mockFile = MockFile();

      container = ProviderContainer(
        overrides: [
          // Use mock datasource for image upload
          imageUploadDatasourceProvider.overrideWithValue(
            const ImageUploadMockDatasource(),
          ),
          // Use mock datasource for profile
          profileDatasourceProvider.overrideWithValue(mockProfileDatasource),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('should complete full image upload and profile update flow', () async {
      // Arrange
      const userId = 'user-123';
      const fileName = 'profile.jpg';
      final bytes = Uint8List.fromList([1, 2, 3, 4, 5]);

      final originalProfile = Profile(
        id: userId,
        name: 'John Doe',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        photoUrl: null,
        role: 'PLAYER',
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
        isActive: true,
      );

      final updatedProfile = originalProfile.copyWith(
        profileImage:
            'https://mock-cdn.example.com/UserProfile/user-123/profile.jpg',
        updatedAt: DateTime.now(),
      );

      // Mock profile datasource responses
      when(
        mockProfileDatasource.getProfile(),
      ).thenAnswer((_) async => originalProfile);
      when(
        mockProfileDatasource.updateProfile(any),
      ).thenAnswer((_) async => updatedProfile);

      // Act
      final imageUploadHelper = container.read(imageUploadHelperProvider);
      final profileRepository = container.read(profileRepositoryProvider);

      // Step 1: Upload image
      final uploadResult = await imageUploadHelper.uploadUserProfileImage(
        bytes: bytes,
        userId: userId,
        fileName: fileName,
      );

      // Step 2: Update profile with new photo URL
      final profileUpdateResult = uploadResult.fold((error) => Left(error), (
        downloadUrl,
      ) async {
        return await profileRepository.updateProfilePhoto(
          userId: userId,
          bytes: bytes,
          fileName: fileName,
        );
      });

      // Assert
      expect(uploadResult, isA<Right<String, String>>());

      uploadResult.fold(
        (error) => fail('Expected successful upload but got error: $error'),
        (downloadUrl) {
          expect(downloadUrl, contains('UserProfile'));
          expect(downloadUrl, contains(userId));
          expect(downloadUrl, contains(fileName));
        },
      );

      if (profileUpdateResult is Future) {
        final result = await profileUpdateResult;
        expect(result, isA<Right<String, Profile>>());

        result.fold(
          (error) =>
              fail('Expected successful profile update but got error: $error'),
          (profile) {
            expect(profile.photoUrl, isNotNull);
            expect(profile.photoUrl, contains('UserProfile'));
            expect(profile.photoUrl, contains(userId));
          },
        );
      }

      // Verify interactions
      verify(mockProfileDatasource.getProfile()).called(1);
      verify(mockProfileDatasource.updateProfile(any)).called(1);
    });

    test('should handle upload failure gracefully', () async {
      // Arrange
      const userId = 'user-456';
      final bytes = Uint8List.fromList([1, 2, 3, 4, 5]);

      // Override with failing mock datasource
      final failingContainer = ProviderContainer(
        overrides: [
          imageUploadDatasourceProvider.overrideWithValue(
            const ImageUploadMockDatasource(
              shouldFail: true,
              errorMessage: 'Network error',
            ),
          ),
          profileDatasourceProvider.overrideWithValue(mockProfileDatasource),
        ],
      );

      // Act
      final imageUploadHelper = failingContainer.read(
        imageUploadHelperProvider,
      );
      final uploadResult = await imageUploadHelper.uploadUserProfileImage(
        bytes: bytes,
        userId: userId,
        fileName: 'profile.jpg',
      );

      // Assert
      expect(uploadResult, isA<Left<String, String>>());
      uploadResult.fold(
        (error) => expect(error, contains('Network error')),
        (url) => fail('Expected error but got success: $url'),
      );

      // Verify no profile operations were called
      verifyNever(mockProfileDatasource.getProfile());
      verifyNever(mockProfileDatasource.updateProfile(any));

      failingContainer.dispose();
    });

    test('should work with different image types', () async {
      // Arrange
      const teamId = 'team-789';
      const fileName = 'logo.png';
      final bytes = Uint8List.fromList([1, 2, 3, 4, 5]);

      // Act
      final imageUploadHelper = container.read(imageUploadHelperProvider);
      final uploadResult = await imageUploadHelper.uploadTeamLogo(
        bytes: bytes,
        teamId: teamId,
        fileName: fileName,
      );

      // Assert
      expect(uploadResult, isA<Right<String, String>>());

      uploadResult.fold(
        (error) => fail('Expected successful upload but got error: $error'),
        (downloadUrl) {
          expect(downloadUrl, contains('TeamLogo'));
          expect(downloadUrl, contains(teamId));
          expect(downloadUrl, contains(fileName));
        },
      );
    });

    test('should validate input parameters correctly', () async {
      // Arrange
      const userId = 'user-123';

      // Act
      final imageUploadHelper = container.read(imageUploadHelperProvider);

      // Test with no file or bytes
      final result1 = await imageUploadHelper.uploadUserProfileImage(
        userId: userId,
      );

      // Test with bytes but no fileName
      final result2 = await imageUploadHelper.uploadUserProfileImage(
        bytes: Uint8List.fromList([1, 2, 3]),
        userId: userId,
      );

      // Assert
      expect(result1, isA<Left<String, String>>());
      expect(result2, isA<Left<String, String>>());

      result1.fold(
        (error) => expect(error, 'Either file or bytes must be provided'),
        (url) => fail('Expected error but got success: $url'),
      );

      result2.fold(
        (error) => expect(error, 'fileName is required when uploading bytes'),
        (url) => fail('Expected error but got success: $url'),
      );
    });
  });
}
