import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';

import 'package:nextsportz_v2/core/image_upload/domain/helpers/image_upload_helper.dart';
import 'package:nextsportz_v2/core/image_upload/domain/repositories/image_upload_repository.dart';
import 'package:nextsportz_v2/core/models/image_upload_models.dart';

import 'image_upload_helper_test.mocks.dart';

@GenerateMocks([ImageUploadRepository, File])
void main() {
  // Provide dummy values for Either types
  provideDummy<Either<String, String>>(const Left('dummy error'));
  provideDummy<Either<String, ImageUploadResponse>>(const Left('dummy error'));
  group('ImageUploadHelper Tests', () {
    late ImageUploadHelper helper;
    late MockImageUploadRepository mockRepository;
    late MockFile mockFile;

    setUp(() {
      mockRepository = MockImageUploadRepository();
      helper = ImageUploadHelper(mockRepository);
      mockFile = MockFile();
    });

    group('uploadUserProfileImage', () {
      test('should upload user profile image with file', () async {
        // Arrange
        const userId = 'user-123';
        const downloadUrl = 'https://cdn.example.com/profile.jpg';

        when(
          mockRepository.uploadImageFile(
            imageType: ImageType.userProfile,
            file: mockFile,
            entityId: userId,
            fileName: null,
          ),
        ).thenAnswer((_) async => const Right(downloadUrl));

        // Act
        final result = await helper.uploadUserProfileImage(
          file: mockFile,
          userId: userId,
        );

        // Assert
        expect(result, isA<Right<String, String>>());
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (url) => expect(url, downloadUrl),
        );

        verify(
          mockRepository.uploadImageFile(
            imageType: ImageType.userProfile,
            file: mockFile,
            entityId: userId,
            fileName: null,
          ),
        ).called(1);
      });

      test('should upload user profile image with bytes', () async {
        // Arrange
        const userId = 'user-456';
        const fileName = 'profile.png';
        const downloadUrl = 'https://cdn.example.com/profile.png';
        final bytes = Uint8List.fromList([1, 2, 3, 4]);

        when(
          mockRepository.uploadImageBytes(
            imageType: ImageType.userProfile,
            bytes: bytes,
            entityId: userId,
            fileName: fileName,
          ),
        ).thenAnswer((_) async => const Right(downloadUrl));

        // Act
        final result = await helper.uploadUserProfileImage(
          bytes: bytes,
          userId: userId,
          fileName: fileName,
        );

        // Assert
        expect(result, isA<Right<String, String>>());
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (url) => expect(url, downloadUrl),
        );

        verify(
          mockRepository.uploadImageBytes(
            imageType: ImageType.userProfile,
            bytes: bytes,
            entityId: userId,
            fileName: fileName,
          ),
        ).called(1);
      });
    });

    group('uploadTeamLogo', () {
      test('should upload team logo with file', () async {
        // Arrange
        const teamId = 'team-789';
        const downloadUrl = 'https://cdn.example.com/logo.jpg';

        when(
          mockRepository.uploadImageFile(
            imageType: ImageType.teamLogo,
            file: mockFile,
            entityId: teamId,
            fileName: null,
          ),
        ).thenAnswer((_) async => const Right(downloadUrl));

        // Act
        final result = await helper.uploadTeamLogo(
          file: mockFile,
          teamId: teamId,
        );

        // Assert
        expect(result, isA<Right<String, String>>());
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (url) => expect(url, downloadUrl),
        );
      });
    });

    group('uploadImage (generic)', () {
      test(
        'should return error when neither file nor bytes provided',
        () async {
          // Act
          final result = await helper.uploadImage(
            imageType: ImageType.userProfile,
            entityId: 'user-123',
          );

          // Assert
          expect(result, isA<Left<String, String>>());
          result.fold(
            (error) => expect(error, 'Either file or bytes must be provided'),
            (url) => fail('Expected error but got success: $url'),
          );
        },
      );

      test('should return error when both file and bytes provided', () async {
        // Arrange
        final bytes = Uint8List.fromList([1, 2, 3, 4]);

        // Act
        final result = await helper.uploadImage(
          imageType: ImageType.userProfile,
          file: mockFile,
          bytes: bytes,
          entityId: 'user-123',
        );

        // Assert
        expect(result, isA<Left<String, String>>());
        result.fold(
          (error) => expect(error, 'Cannot provide both file and bytes'),
          (url) => fail('Expected error but got success: $url'),
        );
      });

      test(
        'should return error when bytes provided without fileName',
        () async {
          // Arrange
          final bytes = Uint8List.fromList([1, 2, 3, 4]);

          // Act
          final result = await helper.uploadImage(
            imageType: ImageType.userProfile,
            bytes: bytes,
            entityId: 'user-123',
          );

          // Assert
          expect(result, isA<Left<String, String>>());
          result.fold(
            (error) =>
                expect(error, 'fileName is required when uploading bytes'),
            (url) => fail('Expected error but got success: $url'),
          );
        },
      );

      test('should upload successfully with valid file', () async {
        // Arrange
        const entityId = 'entity-123';
        const downloadUrl = 'https://cdn.example.com/image.jpg';

        when(
          mockRepository.uploadImageFile(
            imageType: ImageType.advertisementBanner,
            file: mockFile,
            entityId: entityId,
            fileName: null,
          ),
        ).thenAnswer((_) async => const Right(downloadUrl));

        // Act
        final result = await helper.uploadImage(
          imageType: ImageType.advertisementBanner,
          file: mockFile,
          entityId: entityId,
        );

        // Assert
        expect(result, isA<Right<String, String>>());
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (url) => expect(url, downloadUrl),
        );
      });

      test('should upload successfully with valid bytes', () async {
        // Arrange
        const entityId = 'entity-456';
        const fileName = 'banner.png';
        const downloadUrl = 'https://cdn.example.com/banner.png';
        final bytes = Uint8List.fromList([1, 2, 3, 4]);

        when(
          mockRepository.uploadImageBytes(
            imageType: ImageType.tournamentBanner,
            bytes: bytes,
            entityId: entityId,
            fileName: fileName,
          ),
        ).thenAnswer((_) async => const Right(downloadUrl));

        // Act
        final result = await helper.uploadImage(
          imageType: ImageType.tournamentBanner,
          bytes: bytes,
          entityId: entityId,
          fileName: fileName,
        );

        // Assert
        expect(result, isA<Right<String, String>>());
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (url) => expect(url, downloadUrl),
        );
      });
    });

    group('getUploadUrls', () {
      test('should return upload URLs successfully', () async {
        // Arrange
        const imageType = ImageType.userProfile;
        const entityId = 'user-123';
        const fileName = 'profile.jpg';

        final uploadResponse = ImageUploadResponse(
          uploadUrl: 'https://upload.example.com/123',
          downloadUrl: 'https://cdn.example.com/profile.jpg',
        );

        when(
          mockRepository.getUploadUrls(
            imageType: imageType,
            entityId: entityId,
            fileName: fileName,
          ),
        ).thenAnswer((_) async => Right(uploadResponse));

        // Act
        final result = await helper.getUploadUrls(
          imageType: imageType,
          entityId: entityId,
          fileName: fileName,
        );

        // Assert
        expect(result, isA<Right<String, ImageUploadResponse>>());
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (response) => expect(response, uploadResponse),
        );
      });
    });
  });
}
