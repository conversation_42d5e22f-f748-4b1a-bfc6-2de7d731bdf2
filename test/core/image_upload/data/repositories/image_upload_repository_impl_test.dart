import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';

import 'package:nextsportz_v2/core/image_upload/data/repositories/image_upload_repository_impl.dart';
import 'package:nextsportz_v2/core/image_upload/data/datasources/image_upload_datasource.dart';
import 'package:nextsportz_v2/core/models/image_upload_models.dart';

import 'image_upload_repository_impl_test.mocks.dart';

@GenerateMocks([ImageUploadDatasource, File])
void main() {
  group('ImageUploadRepositoryImpl Tests', () {
    late ImageUploadRepositoryImpl repository;
    late MockImageUploadDatasource mockDatasource;
    late MockFile mockFile;

    setUp(() {
      mockDatasource = MockImageUploadDatasource();
      repository = ImageUploadRepositoryImpl(mockDatasource);
      mockFile = MockFile();
    });

    group('uploadImageFile', () {
      test('should return download URL on successful upload', () async {
        // Arrange
        const imageType = ImageType.userProfile;
        const entityId = 'user-123';
        const fileName = 'profile.jpg';
        const uploadUrl = 'https://upload.example.com/123';
        const downloadUrl = 'https://cdn.example.com/profile.jpg';

        when(mockFile.path).thenReturn('/path/to/profile.jpg');

        final uploadResponse = ImageUploadResponse(
          uploadUrl: uploadUrl,
          downloadUrl: downloadUrl,
        );

        when(
          mockDatasource.getUploadUrls(any),
        ).thenAnswer((_) async => uploadResponse);
        when(
          mockDatasource.uploadFileToUrl(uploadUrl, mockFile),
        ).thenAnswer((_) async => true);

        // Act
        final result = await repository.uploadImageFile(
          imageType: imageType,
          file: mockFile,
          entityId: entityId,
          fileName: fileName,
        );

        // Assert
        expect(result, isA<Right<String, String>>());
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (url) => expect(url, downloadUrl),
        );

        verify(mockDatasource.getUploadUrls(any)).called(1);
        verify(mockDatasource.uploadFileToUrl(uploadUrl, mockFile)).called(1);
      });

      test('should return error when upload URLs are null', () async {
        // Arrange
        const imageType = ImageType.teamLogo;
        const entityId = 'team-456';

        when(mockFile.path).thenReturn('/path/to/logo.png');

        final uploadResponse = ImageUploadResponse(
          uploadUrl: null,
          downloadUrl: 'https://cdn.example.com/logo.png',
        );

        when(
          mockDatasource.getUploadUrls(any),
        ).thenAnswer((_) async => uploadResponse);

        // Act
        final result = await repository.uploadImageFile(
          imageType: imageType,
          file: mockFile,
          entityId: entityId,
        );

        // Assert
        expect(result, isA<Left<String, String>>());
        result.fold(
          (error) => expect(error, 'Failed to get upload URLs from server'),
          (url) => fail('Expected error but got success: $url'),
        );
      });

      test('should return error when file upload fails', () async {
        // Arrange
        const imageType = ImageType.userProfile;
        const entityId = 'user-789';
        const uploadUrl = 'https://upload.example.com/789';
        const downloadUrl = 'https://cdn.example.com/user.jpg';

        when(mockFile.path).thenReturn('/path/to/user.jpg');

        final uploadResponse = ImageUploadResponse(
          uploadUrl: uploadUrl,
          downloadUrl: downloadUrl,
        );

        when(
          mockDatasource.getUploadUrls(any),
        ).thenAnswer((_) async => uploadResponse);
        when(
          mockDatasource.uploadFileToUrl(uploadUrl, mockFile),
        ).thenAnswer((_) async => false);

        // Act
        final result = await repository.uploadImageFile(
          imageType: imageType,
          file: mockFile,
          entityId: entityId,
        );

        // Assert
        expect(result, isA<Left<String, String>>());
        result.fold(
          (error) => expect(error, 'Failed to upload file to storage'),
          (url) => fail('Expected error but got success: $url'),
        );
      });

      test('should handle exceptions gracefully', () async {
        // Arrange
        const imageType = ImageType.tournamentBanner;
        const entityId = 'tournament-123';

        when(mockFile.path).thenReturn('/path/to/banner.jpg');
        when(
          mockDatasource.getUploadUrls(any),
        ).thenThrow(Exception('Network error'));

        // Act
        final result = await repository.uploadImageFile(
          imageType: imageType,
          file: mockFile,
          entityId: entityId,
        );

        // Assert
        expect(result, isA<Left<String, String>>());
        result.fold(
          (error) => expect(error, contains('Image upload failed')),
          (url) => fail('Expected error but got success: $url'),
        );
      });
    });

    group('uploadImageBytes', () {
      test('should return download URL on successful upload', () async {
        // Arrange
        const imageType = ImageType.teamLogo;
        const entityId = 'team-456';
        const fileName = 'logo.png';
        const uploadUrl = 'https://upload.example.com/456';
        const downloadUrl = 'https://cdn.example.com/logo.png';
        final bytes = Uint8List.fromList([1, 2, 3, 4]);

        final uploadResponse = ImageUploadResponse(
          uploadUrl: uploadUrl,
          downloadUrl: downloadUrl,
        );

        when(
          mockDatasource.getUploadUrls(any),
        ).thenAnswer((_) async => uploadResponse);
        when(
          mockDatasource.uploadBytesToUrl(uploadUrl, bytes, fileName),
        ).thenAnswer((_) async => true);

        // Act
        final result = await repository.uploadImageBytes(
          imageType: imageType,
          bytes: bytes,
          entityId: entityId,
          fileName: fileName,
        );

        // Assert
        expect(result, isA<Right<String, String>>());
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (url) => expect(url, downloadUrl),
        );

        verify(mockDatasource.getUploadUrls(any)).called(1);
        verify(
          mockDatasource.uploadBytesToUrl(uploadUrl, bytes, fileName),
        ).called(1);
      });
    });

    group('getUploadUrls', () {
      test('should return upload response on success', () async {
        // Arrange
        const imageType = ImageType.advertisementBanner;
        const entityId = 'ad-789';
        const fileName = 'banner.gif';

        final uploadResponse = ImageUploadResponse(
          uploadUrl: 'https://upload.example.com/789',
          downloadUrl: 'https://cdn.example.com/banner.gif',
        );

        when(
          mockDatasource.getUploadUrls(any),
        ).thenAnswer((_) async => uploadResponse);

        // Act
        final result = await repository.getUploadUrls(
          imageType: imageType,
          entityId: entityId,
          fileName: fileName,
        );

        // Assert
        expect(result, isA<Right<String, ImageUploadResponse>>());
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (response) => expect(response, uploadResponse),
        );
      });

      test('should return error on exception', () async {
        // Arrange
        const imageType = ImageType.userProfile;
        const entityId = 'user-123';

        when(
          mockDatasource.getUploadUrls(any),
        ).thenThrow(Exception('API error'));

        // Act
        final result = await repository.getUploadUrls(
          imageType: imageType,
          entityId: entityId,
        );

        // Assert
        expect(result, isA<Left<String, ImageUploadResponse>>());
        result.fold(
          (error) => expect(error, contains('Failed to get upload URLs')),
          (response) => fail('Expected error but got success: $response'),
        );
      });
    });
  });
}
