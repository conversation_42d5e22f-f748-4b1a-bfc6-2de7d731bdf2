import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nextsportz_v2/core/networking/api_client.dart';
import 'package:nextsportz_v2/core/networking/exception.dart';

import 'api_client_test.mocks.dart';

@GenerateMocks([Dio, Ref, Response])
void main() {
  group('ApiClient', () {
    late MockDio mockDio;
    late MockRef mockRef;
    late ApiClient apiClient;

    setUp(() {
      mockDio = MockDio();
      mockRef = MockRef();
      // Stub interceptors getter so _ensureAuthInterceptor can add interceptors without MissingStubError
      when(mockDio.interceptors).thenReturn(Dio().interceptors);
      apiClient = ApiClient(mockRef, dio: mockDio);
    });

    group('get', () {
      test('should make successful GET request', () async {
        // Arrange
        const path = '/test';
        const query = {'param': 'value'};
        const responseData = {'result': 'success'};
        final response = MockResponse();
        when(response.data).thenReturn(responseData);
        when(
          mockDio.get(path, queryParameters: query),
        ).thenAnswer((_) async => response);

        // Act
        final result = await apiClient.get(path, query: query);

        // Assert
        expect(result, equals(responseData));
        verify(mockDio.get(path, queryParameters: query)).called(1);
      });

      test('should throw DioExceptionHandle on DioException', () async {
        // Arrange
        const path = '/test';
        final dioException = DioException(
          requestOptions: RequestOptions(path: path),
          message: 'Network error',
          response: Response(
            requestOptions: RequestOptions(path: path),
            statusCode: 500,
          ),
        );
        when(mockDio.get(path, queryParameters: null)).thenThrow(dioException);

        // Act & Assert
        expect(() => apiClient.get(path), throwsA(isA<DioExceptionHandle>()));
      });

      test('should add auth interceptor only once', () async {
        // Arrange
        const path = '/test';
        final response = MockResponse();
        when(response.data).thenReturn({'result': 'success'});
        when(
          mockDio.get(path, queryParameters: null),
        ).thenAnswer((_) async => response);

        // Act
        await apiClient.get(path);
        await apiClient.get(path);

        // Assert
        verify(mockDio.get(path, queryParameters: null)).called(2);
        // Note: We can't directly verify interceptor addition due to private method
      });
    });

    group('post', () {
      test('should make successful POST request with JSON data', () async {
        // Arrange
        const path = '/test';
        const data = {'key': 'value'};
        const responseData = {'result': 'success'};
        final response = MockResponse();
        when(response.data).thenReturn(responseData);
        when(
          mockDio.post(path, data: anyNamed('data')),
        ).thenAnswer((_) async => response);

        // Act
        final result = await apiClient.post(path, data: data);

        // Assert
        expect(result, equals(responseData));
        verify(mockDio.post(path, data: anyNamed('data'))).called(1);
      });

      test('should make successful POST request with FormData', () async {
        // Arrange
        const path = '/test';
        const data = {'key': 'value'};
        const responseData = {'result': 'success'};
        final response = MockResponse();
        when(response.data).thenReturn(responseData);
        when(
          mockDio.post(path, data: anyNamed('data')),
        ).thenAnswer((_) async => response);

        // Act
        final result = await apiClient.post(path, data: data, isFormData: true);

        // Assert
        expect(result, equals(responseData));
        verify(mockDio.post(path, data: anyNamed('data'))).called(1);
      });

      test('should throw AppError on DioException', () async {
        // Arrange
        const path = '/test';
        const data = {'key': 'value'};
        final dioException = DioException(
          requestOptions: RequestOptions(path: path),
          message: 'Network error',
        );
        when(
          mockDio.post(path, data: anyNamed('data')),
        ).thenThrow(dioException);

        // Act & Assert
        expect(
          () => apiClient.post(path, data: data),
          throwsA(isA<DioExceptionHandle>()),
        );
      });
    });

    group('put', () {
      test('should make successful PUT request', () async {
        // Arrange
        const path = '/test';
        const data = {'key': 'value'};
        const responseData = {'result': 'success'};
        final response = MockResponse();
        when(response.data).thenReturn(responseData);
        when(mockDio.put(path, data: data)).thenAnswer((_) async => response);

        // Act
        final result = await apiClient.put(path, data: data);

        // Assert
        expect(result, equals(responseData));
        verify(mockDio.put(path, data: data)).called(1);
      });

      test('should throw AppError on DioException', () async {
        // Arrange
        const path = '/test';
        const data = {'key': 'value'};
        final dioException = DioException(
          requestOptions: RequestOptions(path: path),
          message: 'Network error',
        );
        when(mockDio.put(path, data: data)).thenThrow(dioException);

        // Act & Assert
        expect(() => apiClient.put(path, data: data),
            throwsA(isA<DioExceptionHandle>()));
      });
    });

    group('patch', () {
      test('should make successful PATCH request with JSON data', () async {
        // Arrange
        const path = '/test';
        const data = {'key': 'value'};
        const responseData = {'result': 'success'};
        final response = MockResponse();
        when(response.data).thenReturn(responseData);
        when(mockDio.patch(path, data: data)).thenAnswer((_) async => response);

        // Act
        final result = await apiClient.patch(path, data: data);

        // Assert
        expect(result, equals(responseData));
        verify(mockDio.patch(path, data: data)).called(1);
      });

      test('should make successful PATCH request with FormData', () async {
        // Arrange
        const path = '/test';
        const data = {'key': 'value'};
        const responseData = {'result': 'success'};
        final response = MockResponse();
        when(response.data).thenReturn(responseData);
        when(
          mockDio.patch(path, data: anyNamed('data')),
        ).thenAnswer((_) async => response);

        // Act
        final result = await apiClient.patch(
          path,
          data: data,
          isFormData: true,
        );

        // Assert
        expect(result, equals(responseData));
        verify(mockDio.patch(path, data: anyNamed('data'))).called(1);
      });

      test('should throw AppError on DioException', () async {
        // Arrange
        const path = '/test';
        const data = {'key': 'value'};
        final dioException = DioException(
          requestOptions: RequestOptions(path: path),
          message: 'Network error',
        );
        when(mockDio.patch(path, data: data)).thenThrow(dioException);

        // Act & Assert
        expect(
          () => apiClient.patch(path, data: data),
          throwsA(isA<DioExceptionHandle>()),
        );
      });
    });

    group('delete', () {
      test('should make successful DELETE request', () async {
        // Arrange
        const path = '/test';
        const responseData = {'result': 'success'};
        final response = MockResponse();
        when(response.data).thenReturn(responseData);
        when(mockDio.delete(path)).thenAnswer((_) async => response);

        // Act
        final result = await apiClient.delete(path);

        // Assert
        expect(result, equals(responseData));
        verify(mockDio.delete(path)).called(1);
      });

      test('should throw AppError on DioException', () async {
        // Arrange
        const path = '/test';
        final dioException = DioException(
          requestOptions: RequestOptions(path: path),
          message: 'Network error',
        );
        when(mockDio.delete(path)).thenThrow(dioException);

        // Act & Assert
        expect(
            () => apiClient.delete(path), throwsA(isA<DioExceptionHandle>()));
      });
    });
  });
}
