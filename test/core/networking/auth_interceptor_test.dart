import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:nextsportz_v2/core/networking/auth_interceptor.dart';
import 'package:nextsportz_v2/core/networking/session_manager.dart';
import 'package:nextsportz_v2/core/local/token_storage.dart';
import 'package:nextsportz_v2/core/networking/api_const.dart';

import 'auth_interceptor_test.mocks.dart';

@GenerateMocks([TokenStorageService, SessionManager, Dio])
void main() {
  group('AuthInterceptor', () {
    late MockTokenStorageService mockTokenStorage;
    late MockSessionManager mockSessionManager;
    late AuthInterceptor authInterceptor;

    setUp(() {
      mockTokenStorage = MockTokenStorageService();
      mockSessionManager = MockSessionManager();

      // Create a simple mock Ref
      final mockRef = _SimpleMockRef(mockTokenStorage, mockSessionManager);
      authInterceptor = AuthInterceptor(mockRef);
    });

    group('onRequest', () {
      test('should add Authorization header when access token exists',
          () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/test');
        final handler = MockRequestInterceptorHandler();

        when(mockTokenStorage.getTokens()).thenAnswer(
          (_) async => {
            'accessToken': 'test_access_token',
            'refreshToken': 'test_refresh_token',
          },
        );

        // Act
        authInterceptor.onRequest(requestOptions, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        expect(
          requestOptions.headers['Authorization'],
          'Bearer test_access_token',
        );
        verify(handler.next(requestOptions)).called(1);
      });

      test('should continue without token when no tokens exist', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/test');
        final handler = MockRequestInterceptorHandler();

        when(mockTokenStorage.getTokens()).thenAnswer((_) async => null);

        // Act
        authInterceptor.onRequest(requestOptions, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        expect(requestOptions.headers['Authorization'], isNull);
        verify(handler.next(requestOptions)).called(1);
      });

      test('should continue without token when token retrieval throws',
          () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/test');
        final handler = MockRequestInterceptorHandler();

        when(mockTokenStorage.getTokens())
            .thenThrow(Exception('Storage error'));

        // Act
        authInterceptor.onRequest(requestOptions, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        expect(requestOptions.headers['Authorization'], isNull);
        verify(handler.next(requestOptions)).called(1);
      });

      test('should continue without token when access token is empty',
          () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/test');
        final handler = MockRequestInterceptorHandler();

        // Since the real implementation returns null if either token is missing,
        // we need to mock it to return null to simulate missing access token
        when(mockTokenStorage.getTokens()).thenAnswer((_) async => null);

        // Act
        authInterceptor.onRequest(requestOptions, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert - no tokens available, so no header
        expect(requestOptions.headers['Authorization'], isNull);
        verify(handler.next(requestOptions)).called(1);
      });
    });

    group('onError - 401 handling', () {
      test('should clear session when max retries reached', () async {
        // Arrange
        final requestOptions = RequestOptions(
          path: '/test',
          extra: {'retry_count': 3}, // Already at max retries
        );
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(requestOptions: requestOptions, statusCode: 401),
        );
        final handler = MockErrorInterceptorHandler();

        // Act
        authInterceptor.onError(dioException, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(
          mockSessionManager.clearSession(
            reason: SessionClearReason.tokenRefreshFailed,
          ),
        ).called(1);
        verify(mockSessionManager.notifyTokenRefreshFailed()).called(1);
        verify(handler.next(dioException)).called(1);
      });

      test('should clear session when no refresh token available', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/test');
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(requestOptions: requestOptions, statusCode: 401),
        );
        final handler = MockErrorInterceptorHandler();

        when(mockTokenStorage.getTokens()).thenAnswer((_) async => null);

        // Act
        authInterceptor.onError(dioException, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(
          mockSessionManager.clearSession(
            reason: SessionClearReason.tokenRefreshFailed,
          ),
        ).called(1);
        verify(mockSessionManager.notifyTokenRefreshFailed()).called(1);
        verify(handler.next(dioException)).called(1);
      });

      test('should clear session when refresh token is empty', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/test');
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(requestOptions: requestOptions, statusCode: 401),
        );
        final handler = MockErrorInterceptorHandler();

        // Since the real implementation returns null if either token is missing,
        // we need to mock it to return null to simulate missing refresh token
        when(mockTokenStorage.getTokens()).thenAnswer((_) async => null);

        // Act
        authInterceptor.onError(dioException, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(
          mockSessionManager.clearSession(
            reason: SessionClearReason.tokenRefreshFailed,
          ),
        ).called(1);
        verify(mockSessionManager.notifyTokenRefreshFailed()).called(1);
        verify(handler.next(dioException)).called(1);
      });

      test(
          'should clear session when token refresh fails due to missing tokens',
          () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/test');
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(requestOptions: requestOptions, statusCode: 401),
        );
        final handler = MockErrorInterceptorHandler();

        // Mock token storage to return null (no tokens available)
        when(mockTokenStorage.getTokens()).thenAnswer((_) async => null);

        // Act
        authInterceptor.onError(dioException, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert - refresh should fail due to missing tokens, causing session clear
        verify(
          mockSessionManager.clearSession(
            reason: SessionClearReason.tokenRefreshFailed,
          ),
        ).called(1);
        verify(mockSessionManager.notifyTokenRefreshFailed()).called(1);
        verify(handler.next(dioException)).called(1);
      });
    });

    group('onError - non-401 handling', () {
      test('should pass through non-401 errors', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/test');
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(requestOptions: requestOptions, statusCode: 500),
        );
        final handler = MockErrorInterceptorHandler();

        // Act
        authInterceptor.onError(dioException, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(handler.next(dioException)).called(1);
        verifyNever(mockTokenStorage.getTokens());
        verifyNever(
          mockSessionManager.clearSession(reason: anyNamed('reason')),
        );
      });

      test('should pass through 404 errors', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/test');
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(requestOptions: requestOptions, statusCode: 404),
        );
        final handler = MockErrorInterceptorHandler();

        // Act
        authInterceptor.onError(dioException, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(handler.next(dioException)).called(1);
        verifyNever(mockTokenStorage.getTokens());
        verifyNever(
          mockSessionManager.clearSession(reason: anyNamed('reason')),
        );
      });

      test('should pass through network errors', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/test');
        final dioException = DioException(
          requestOptions: requestOptions,
          type: DioExceptionType.connectionTimeout,
        );
        final handler = MockErrorInterceptorHandler();

        // Act
        authInterceptor.onError(dioException, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(handler.next(dioException)).called(1);
        verifyNever(mockTokenStorage.getTokens());
        verifyNever(
          mockSessionManager.clearSession(reason: anyNamed('reason')),
        );
      });
    });

    group('retry logic', () {
      test('should increment retry count on each attempt', () async {
        final requestOptions = RequestOptions(path: '/test');

        // First request (retry_count should be null initially)
        expect(requestOptions.extra['retry_count'], isNull);

        // After first retry
        requestOptions.extra['retry_count'] = 1;
        expect(requestOptions.extra['retry_count'], 1);

        // After second retry
        requestOptions.extra['retry_count'] = 2;
        expect(requestOptions.extra['retry_count'], 2);

        // After third retry (should trigger session clear)
        requestOptions.extra['retry_count'] = 3;
        expect(requestOptions.extra['retry_count'], 3);
      });

      test('should handle retry count when not set', () async {
        final requestOptions = RequestOptions(path: '/test');
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(requestOptions: requestOptions, statusCode: 401),
        );
        final handler = MockErrorInterceptorHandler();

        when(mockTokenStorage.getTokens()).thenAnswer((_) async => null);

        // Act
        authInterceptor.onError(dioException, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert - should handle null retry count gracefully
        verify(
          mockSessionManager.clearSession(
            reason: SessionClearReason.tokenRefreshFailed,
          ),
        ).called(1);
      });
    });

    group('error handling robustness', () {
      test('should handle session manager errors gracefully', () async {
        // Arrange
        final requestOptions = RequestOptions(
          path: '/test',
          extra: {'retry_count': 3},
        );
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(requestOptions: requestOptions, statusCode: 401),
        );
        final handler = MockErrorInterceptorHandler();

        // Mock session manager to throw error
        when(
          mockSessionManager.clearSession(
            reason: SessionClearReason.tokenRefreshFailed,
          ),
        ).thenThrow(Exception('Session clear failed'));

        when(mockSessionManager.notifyTokenRefreshFailed())
            .thenThrow(Exception('Notification failed'));

        // Act
        authInterceptor.onError(dioException, handler);

        // Wait for async operations to complete
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert - should still call handler.next even if session manager fails
        verify(handler.next(dioException)).called(1);
      });
    });
  });
}

// Mock classes for the test
class MockRequestInterceptorHandler extends Mock
    implements RequestInterceptorHandler {}

class MockErrorInterceptorHandler extends Mock
    implements ErrorInterceptorHandler {}

// Simple mock Ref class that doesn't use Mockito
class _SimpleMockRef implements Ref {
  final TokenStorageService _tokenStorage;
  final SessionManager _sessionManager;

  _SimpleMockRef(this._tokenStorage, this._sessionManager);

  @override
  T read<T>(ProviderListenable<T> provider) {
    if (provider == tokenStorageProvider) {
      return _tokenStorage as T;
    }
    if (provider == sessionManagerProvider) {
      return _sessionManager as T;
    }
    throw UnimplementedError('Provider not mocked: $provider');
  }

  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}
