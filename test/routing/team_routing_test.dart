import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:nextsportz_v2/features/teams/presentation/screens/team_details_screen.dart';
import 'package:nextsportz_v2/features/teams/presentation/screens/team_list_screen.dart';
import 'package:nextsportz_v2/features/teams/presentation/screens/my_teams_screen.dart';

void main() {
  group('Team Routing Tests', () {
    late GoRouter router;

    setUp(() {
      router = GoRouter(
        initialLocation: '/',
        routes: [
          GoRoute(
            path: '/',
            name: 'home',
            builder: (context, state) => const Scaffold(body: Text('Home')),
          ),
          GoRoute(
            path: '/teams',
            name: 'teams',
            builder: (context, state) => const TeamListScreen(),
          ),
          GoRoute(
            path: '/my-teams',
            name: 'my-teams',
            builder: (context, state) => const MyTeamsScreen(),
          ),
          GoRoute(
            path: '/teams/:teamId',
            name: 'team-detail',
            builder: (context, state) {
              final teamId = state.pathParameters['teamId']!;
              return TeamDetailsScreen(teamId: teamId, isPublicView: true);
            },
          ),
        ],
      );
    });

    testWidgets('should navigate to team details with correct URL', (
      tester,
    ) async {
      await tester.pumpWidget(MaterialApp.router(routerConfig: router));

      // Navigate to team details
      router.push('/teams/test-team-123');
      await tester.pumpAndSettle();

      // Verify the current location
      expect(
        router.routerDelegate.currentConfiguration.uri.toString(),
        equals('/teams/test-team-123'),
      );

      // Verify the correct screen is displayed
      expect(find.byType(TeamDetailsScreen), findsOneWidget);
    });

    testWidgets('should extract teamId parameter correctly', (tester) async {
      await tester.pumpWidget(MaterialApp.router(routerConfig: router));

      const testTeamId = 'abc-123-def';
      router.push('/teams/$testTeamId');
      await tester.pumpAndSettle();

      // Find the TeamDetailsScreen widget
      final teamDetailsWidget = tester.widget<TeamDetailsScreen>(
        find.byType(TeamDetailsScreen),
      );

      // Verify the teamId was passed correctly
      expect(teamDetailsWidget.teamId, equals(testTeamId));
      expect(teamDetailsWidget.isPublicView, isTrue);
    });

    testWidgets('should navigate to my teams with correct URL', (tester) async {
      await tester.pumpWidget(MaterialApp.router(routerConfig: router));

      router.push('/my-teams');
      await tester.pumpAndSettle();

      expect(
        router.routerDelegate.currentConfiguration.uri.toString(),
        equals('/my-teams'),
      );
      expect(find.byType(MyTeamsScreen), findsOneWidget);
    });

    testWidgets('should navigate to teams list with correct URL', (
      tester,
    ) async {
      await tester.pumpWidget(MaterialApp.router(routerConfig: router));

      router.push('/teams');
      await tester.pumpAndSettle();

      expect(
        router.routerDelegate.currentConfiguration.uri.toString(),
        equals('/teams'),
      );
      expect(find.byType(TeamListScreen), findsOneWidget);
    });

    test('should handle browser refresh on team details page', () {
      // Simulate browser refresh by creating router with team details URL
      final refreshRouter = GoRouter(
        initialLocation: '/teams/refresh-test-123',
        routes: [
          GoRoute(
            path: '/teams/:teamId',
            name: 'team-detail',
            builder: (context, state) {
              final teamId = state.pathParameters['teamId']!;
              return TeamDetailsScreen(teamId: teamId, isPublicView: true);
            },
          ),
        ],
      );

      // Verify the initial location is set correctly
      expect(
        refreshRouter.routerDelegate.currentConfiguration.uri.toString(),
        equals('/teams/refresh-test-123'),
      );
    });

    group('URL Parameter Validation', () {
      test('should handle special characters in team ID', () {
        const specialTeamId = 'team-123_abc.def';
        final uri = Uri.parse('/teams/$specialTeamId');
        expect(uri.pathSegments.last, equals(specialTeamId));
      });

      test('should handle encoded team ID', () {
        const encodedTeamId = 'team%20with%20spaces';
        final uri = Uri.parse('/teams/$encodedTeamId');
        expect(uri.pathSegments.last, equals(encodedTeamId));
      });
    });
  });
}
