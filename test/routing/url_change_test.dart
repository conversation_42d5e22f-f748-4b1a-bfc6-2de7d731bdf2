import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';

void main() {
  group('URL Change Verification Tests', () {
    testWidgets('should demonstrate context.go() URL change behavior', (
      tester,
    ) async {
      final router = GoRouter(
        initialLocation: '/',
        routes: [
          GoRoute(
            path: '/',
            name: 'home',
            builder: (context, state) {
              return Scaffold(
                body: Column(
                  children: [
                    const Text('Home Page'),
                    ElevatedButton(
                      onPressed: () {
                        context.go('/teams/test-456');
                      },
                      child: const Text('Go to Team (replace)'),
                    ),
                  ],
                ),
              );
            },
          ),
          GoRoute(
            path: '/teams/:teamId',
            name: 'team-detail',
            builder: (context, state) {
              final teamId = state.pathParameters['teamId']!;
              return Scaffold(
                body: Column(
                  children: [
                    Text('Team Details: $teamId'),
                    ElevatedButton(
                      onPressed: () {
                        context.go('/');
                      },
                      child: const Text('Go Home'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      );

      await tester.pumpWidget(MaterialApp.router(routerConfig: router));

      // Initial state
      expect(
        router.routerDelegate.currentConfiguration.uri.toString(),
        equals('/'),
      );
      expect(find.text('Home Page'), findsOneWidget);

      // Test context.go() - should replace current route and change URL
      await tester.tap(find.text('Go to Team (replace)'));
      await tester.pumpAndSettle();

      // Verify URL changed
      expect(
        router.routerDelegate.currentConfiguration.uri.toString(),
        equals('/teams/test-456'),
      );
      expect(find.text('Team Details: test-456'), findsOneWidget);

      // Go back home
      await tester.tap(find.text('Go Home'));
      await tester.pumpAndSettle();

      expect(
        router.routerDelegate.currentConfiguration.uri.toString(),
        equals('/'),
      );
      expect(find.text('Home Page'), findsOneWidget);
    });

    testWidgets('should test browser refresh simulation', (tester) async {
      // Simulate browser refresh by creating router with specific initial location
      final router = GoRouter(
        initialLocation: '/teams/refresh-test',
        routes: [
          GoRoute(
            path: '/',
            name: 'home',
            builder: (context, state) => const Scaffold(body: Text('Home')),
          ),
          GoRoute(
            path: '/teams/:teamId',
            name: 'team-detail',
            builder: (context, state) {
              final teamId = state.pathParameters['teamId']!;
              return Scaffold(body: Text('Team Details: $teamId'));
            },
          ),
        ],
      );

      await tester.pumpWidget(MaterialApp.router(routerConfig: router));

      // Should start directly on team details page
      expect(
        router.routerDelegate.currentConfiguration.uri.toString(),
        equals('/teams/refresh-test'),
      );
      expect(find.text('Team Details: refresh-test'), findsOneWidget);
    });
  });
}
