import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';

import '../../../../../lib/features/profile/profile_providers.dart';
import '../../../../../lib/features/profile/domain/entities/profile.dart';
import '../../../../../lib/features/profile/domain/repositories/profile_repository.dart';

import 'profile_provider_test.mocks.dart';

@GenerateMocks([ProfileRepository])
void main() {
  group('ProfileNotifier Tests', () {
    late ProviderContainer container;
    late MockProfileRepository mockRepository;
    late Profile testProfile;

    setUp(() {
      mockRepository = MockProfileRepository();
      testProfile = Profile(
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        profileImage: 'https://example.com/profile.jpg',
        role: 'PLAYER',
        preferences: {'theme': 'dark'},
        dateOfBirth: DateTime(1990, 1, 1),
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 2),
        isActive: true,
      );

      container = ProviderContainer(
        overrides: [
          profileRepositoryProvider.overrideWith((ref) => mockRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('loadProfile', () {
      test('initial state is loading', () {
        // Act
        final notifier = container.read(profileProvider.notifier);
        final state = container.read(profileProvider);

        // Assert
        expect(state, isA<AsyncLoading<Profile?>>());
      });

      test('sets data state when loadProfile succeeds', () async {
        // Arrange
        when(mockRepository.getProfile('current_user'))
            .thenAnswer((_) async => Right(testProfile));

        // Act
        final notifier = container.read(profileProvider.notifier);
        await notifier.loadProfile();

        // Assert
        final state = container.read(profileProvider);
        expect(state, isA<AsyncData<Profile?>>());
        expect(state.value, testProfile);
        verify(mockRepository.getProfile('current_user')).called(1);
      });

      test('sets error state when loadProfile fails', () async {
        // Arrange
        const errorMessage = 'Failed to load profile';
        when(mockRepository.getProfile('current_user'))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final notifier = container.read(profileProvider.notifier);
        await notifier.loadProfile();

        // Assert
        final state = container.read(profileProvider);
        expect(state, isA<AsyncError<Profile?>>());
        expect(state.error.toString(), errorMessage);
        verify(mockRepository.getProfile('current_user')).called(1);
      });

      test('handles exception during loadProfile', () async {
        // Arrange
        when(mockRepository.getProfile('current_user'))
            .thenThrow(Exception('Network error'));

        // Act
        final notifier = container.read(profileProvider.notifier);
        await notifier.loadProfile();

        // Assert
        final state = container.read(profileProvider);
        expect(state, isA<AsyncError<Profile?>>());
        expect(state.error, isA<Exception>());
      });

      test('sets loading state during loadProfile operation', () async {
        // Arrange
        when(mockRepository.getProfile('current_user')).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return Right(testProfile);
        });

        // Act
        final notifier = container.read(profileProvider.notifier);
        final future = notifier.loadProfile();

        // Assert loading state
        final loadingState = container.read(profileProvider);
        expect(loadingState, isA<AsyncLoading<Profile?>>());

        // Wait for completion
        await future;
        final finalState = container.read(profileProvider);
        expect(finalState, isA<AsyncData<Profile?>>());
        expect(finalState.value, testProfile);
      });
    });

    group('updateProfile', () {
      test('updates profile successfully', () async {
        // Arrange
        final updatedProfile = testProfile.copyWith(
          name: 'Jane Doe',
          updatedAt: DateTime(2023, 1, 3),
        );
        when(mockRepository.updateProfile(testProfile))
            .thenAnswer((_) async => Right(updatedProfile));

        // Act
        final notifier = container.read(profileProvider.notifier);
        await notifier.updateProfile(testProfile);

        // Assert
        final state = container.read(profileProvider);
        expect(state, isA<AsyncData<Profile?>>());
        expect(state.value, updatedProfile);
        expect(state.value!.name, 'Jane Doe');
        verify(mockRepository.updateProfile(testProfile)).called(1);
      });

      test('sets error state when updateProfile fails', () async {
        // Arrange
        const errorMessage = 'Update failed';
        when(mockRepository.updateProfile(testProfile))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final notifier = container.read(profileProvider.notifier);
        await notifier.updateProfile(testProfile);

        // Assert
        final state = container.read(profileProvider);
        expect(state, isA<AsyncError<Profile?>>());
        expect(state.error.toString(), errorMessage);
        verify(mockRepository.updateProfile(testProfile)).called(1);
      });

      test('handles exception during updateProfile', () async {
        // Arrange
        when(mockRepository.updateProfile(testProfile))
            .thenThrow(Exception('Network error'));

        // Act
        final notifier = container.read(profileProvider.notifier);
        await notifier.updateProfile(testProfile);

        // Assert
        final state = container.read(profileProvider);
        expect(state, isA<AsyncError<Profile?>>());
        expect(state.error, isA<Exception>());
      });

      test('sets loading state during updateProfile operation', () async {
        // Arrange
        when(mockRepository.updateProfile(testProfile)).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return Right(testProfile);
        });

        // Act
        final notifier = container.read(profileProvider.notifier);
        final future = notifier.updateProfile(testProfile);

        // Assert loading state
        final loadingState = container.read(profileProvider);
        expect(loadingState, isA<AsyncLoading<Profile?>>());

        // Wait for completion
        await future;
        final finalState = container.read(profileProvider);
        expect(finalState, isA<AsyncData<Profile?>>());
      });
    });

    group('uploadProfileImage', () {
      test('uploads image and reloads profile successfully', () async {
        // Arrange
        const imagePath = '/path/to/image.jpg';
        final updatedProfile = testProfile.copyWith(
          profileImage: 'https://example.com/new-profile.jpg',
        );

        when(mockRepository.updateProfileImage('current_user', imagePath))
            .thenAnswer((_) async => const Right(null));
        when(mockRepository.getProfile('current_user'))
            .thenAnswer((_) async => Right(updatedProfile));

        // Act
        final notifier = container.read(profileProvider.notifier);
        await notifier.uploadProfileImage(imagePath);

        // Assert
        final state = container.read(profileProvider);
        expect(state, isA<AsyncData<Profile?>>());
        expect(
            state.value!.profileImage, 'https://example.com/new-profile.jpg');
        verify(mockRepository.updateProfileImage('current_user', imagePath))
            .called(1);
        verify(mockRepository.getProfile('current_user')).called(1);
      });

      test('sets error state when image upload fails', () async {
        // Arrange
        const imagePath = '/path/to/image.jpg';
        const errorMessage = 'Image upload failed';
        when(mockRepository.updateProfileImage('current_user', imagePath))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final notifier = container.read(profileProvider.notifier);
        await notifier.uploadProfileImage(imagePath);

        // Assert
        final state = container.read(profileProvider);
        expect(state, isA<AsyncError<Profile?>>());
        expect(state.error.toString(), errorMessage);
        verify(mockRepository.updateProfileImage('current_user', imagePath))
            .called(1);
        verifyNever(mockRepository.getProfile(any));
      });

      test('handles exception during image upload', () async {
        // Arrange
        const imagePath = '/path/to/image.jpg';
        when(mockRepository.updateProfileImage('current_user', imagePath))
            .thenThrow(Exception('Network error'));

        // Act
        final notifier = container.read(profileProvider.notifier);
        await notifier.uploadProfileImage(imagePath);

        // Assert
        final state = container.read(profileProvider);
        expect(state, isA<AsyncError<Profile?>>());
        expect(state.error, isA<Exception>());
      });

      test('reloads profile after successful image upload', () async {
        // Arrange
        const imagePath = '/path/to/image.jpg';
        when(mockRepository.updateProfileImage('current_user', imagePath))
            .thenAnswer((_) async => const Right(null));
        when(mockRepository.getProfile('current_user'))
            .thenAnswer((_) async => Right(testProfile));

        // Act
        final notifier = container.read(profileProvider.notifier);
        await notifier.uploadProfileImage(imagePath);

        // Assert
        verify(mockRepository.updateProfileImage('current_user', imagePath))
            .called(1);
        verify(mockRepository.getProfile('current_user')).called(1);
      });
    });

    group('logout', () {
      test('sets profile to null on logout', () async {
        // Arrange
        // First set a profile
        when(mockRepository.getProfile('current_user'))
            .thenAnswer((_) async => Right(testProfile));
        final notifier = container.read(profileProvider.notifier);
        await notifier.loadProfile();

        // Verify profile is loaded
        expect(container.read(profileProvider).value, testProfile);

        // Act - logout
        await notifier.logout();

        // Assert
        final state = container.read(profileProvider);
        expect(state, isA<AsyncData<Profile?>>());
        expect(state.value, null);
      });

      test('handles exception during logout', () async {
        // This test is mainly for code coverage since logout doesn't call repository
        // Act
        final notifier = container.read(profileProvider.notifier);
        await notifier.logout();

        // Assert
        final state = container.read(profileProvider);
        expect(state, isA<AsyncData<Profile?>>());
        expect(state.value, null);
      });
    });

    group('Provider Integration', () {
      test('profileStatsProvider returns correct stats', () {
        // Act
        final stats = container.read(profileStatsProvider);

        // Assert
        expect(stats['matches'], 45);
        expect(stats['wins'], 32);
        expect(stats['winRate'], '71%');
        expect(stats['rating'], '4.8');
        expect(stats['challengesCompleted'], 28);
        expect(stats['teams'], 3);
      });

      test('profileMenuItemsProvider returns correct menu items', () {
        // Act
        final menuItems = container.read(profileMenuItemsProvider);

        // Assert
        expect(menuItems, isA<List<Map<String, dynamic>>>());
        expect(menuItems.length, 7);

        final performanceItem = menuItems.firstWhere(
          (item) => item['title'] == 'Performance Stats',
        );
        expect(performanceItem['icon'], 'analytics');
        expect(performanceItem['route'], '/performance');

        final settingsItem = menuItems.firstWhere(
          (item) => item['title'] == 'Settings',
        );
        expect(settingsItem['icon'], 'settings');
        expect(settingsItem['route'], '/settings');
      });

      test('multiple notifiers work independently', () async {
        // Arrange
        final container2 = ProviderContainer(
          overrides: [
            profileRepositoryProvider.overrideWith((ref) => mockRepository),
          ],
        );

        when(mockRepository.getProfile('current_user'))
            .thenAnswer((_) async => Right(testProfile));

        // Act
        final notifier1 = container.read(profileProvider.notifier);
        final notifier2 = container2.read(profileProvider.notifier);

        await notifier1.loadProfile();
        await notifier2.logout();

        // Assert
        final state1 = container.read(profileProvider);
        final state2 = container2.read(profileProvider);

        expect(state1.value, testProfile);
        expect(state2.value, null);

        container2.dispose();
      });
    });

    group('State Transitions', () {
      test('state transitions correctly during successful operations',
          () async {
        // Arrange
        final states = <AsyncValue<Profile?>>[];
        when(mockRepository.getProfile('current_user')).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
          return Right(testProfile);
        });

        // Act
        final notifier = container.read(profileProvider.notifier);

        // Listen to state changes
        container.listen<AsyncValue<Profile?>>(
          profileProvider,
          (prev, next) => states.add(next),
          fireImmediately: true,
        );

        await notifier.loadProfile();

        // Assert
        expect(states.length, greaterThanOrEqualTo(2));
        expect(states.first, isA<AsyncLoading<Profile?>>());
        expect(states.last, isA<AsyncData<Profile?>>());
        expect(states.last.value, testProfile);
      });

      test('state transitions correctly during failed operations', () async {
        // Arrange
        final states = <AsyncValue<Profile?>>[];
        const errorMessage = 'Load failed';
        when(mockRepository.getProfile('current_user')).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
          return const Left(errorMessage);
        });

        // Act
        final notifier = container.read(profileProvider.notifier);

        // Listen to state changes
        container.listen<AsyncValue<Profile?>>(
          profileProvider,
          (prev, next) => states.add(next),
          fireImmediately: true,
        );

        await notifier.loadProfile();

        // Assert
        expect(states.length, greaterThanOrEqualTo(2));
        expect(states.first, isA<AsyncLoading<Profile?>>());
        expect(states.last, isA<AsyncError<Profile?>>());
        expect(states.last.error.toString(), errorMessage);
      });
    });

    group('Error Recovery', () {
      test('can recover from error state by successful operation', () async {
        // Arrange
        var callCount = 0;
        when(mockRepository.getProfile('current_user')).thenAnswer((_) async {
          callCount++;
          if (callCount == 1) {
            return const Left('Error');
          } else {
            return Right(testProfile);
          }
        });

        final notifier = container.read(profileProvider.notifier);

        // Act - First operation fails
        await notifier.loadProfile();
        expect(container.read(profileProvider), isA<AsyncError<Profile?>>());

        // Act - Second operation succeeds
        await notifier.loadProfile();

        // Assert
        final state = container.read(profileProvider);
        expect(state, isA<AsyncData<Profile?>>());
        expect(state.value, testProfile);
      });

      test('maintains error state between failed operations', () async {
        // Arrange
        var callCount = 0;
        when(mockRepository.getProfile('current_user')).thenAnswer((_) async {
          callCount++;
          if (callCount == 1) {
            return const Left('Error 1');
          } else {
            return const Left('Error 2');
          }
        });

        final notifier = container.read(profileProvider.notifier);

        // Act
        await notifier.loadProfile();
        final firstErrorState = container.read(profileProvider);

        await notifier.loadProfile();
        final secondErrorState = container.read(profileProvider);

        // Assert
        expect(firstErrorState, isA<AsyncError<Profile?>>());
        expect(secondErrorState, isA<AsyncError<Profile?>>());
        expect(firstErrorState.error.toString(), 'Error 1');
        expect(secondErrorState.error.toString(), 'Error 2');
      });
    });
  });
}
