import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';

import '../../../../../lib/features/profile/domain/usecases/get_profile_usecase.dart';
import '../../../../../lib/features/profile/domain/repositories/profile_repository.dart';
import '../../../../../lib/features/profile/domain/entities/profile.dart';

import 'get_profile_usecase_test.mocks.dart';

@GenerateMocks([ProfileRepository])
void main() {
  group('GetProfileUseCase Tests', () {
    late GetProfileUseCase useCase;
    late MockProfileRepository mockRepository;
    late Profile testProfile;

    setUp(() {
      mockRepository = MockProfileRepository();
      useCase = GetProfileUseCase(mockRepository);

      testProfile = Profile(
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        phoneNumber: '+1234567890',
        profileImage: 'https://example.com/profile.jpg',
        role: 'PLAYER',
        preferences: {'theme': 'dark'},
        dateOfBirth: DateTime(1990, 1, 1),
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 2),
        isActive: true,
      );
    });

    group('call method', () {
      test('returns profile when repository succeeds', () async {
        // Arrange
        const userId = 'test_user_id';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => Right(testProfile));

        // Act
        final result = await useCase.call(userId);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (profile) {
            expect(profile.id, testProfile.id);
            expect(profile.name, testProfile.name);
            expect(profile.email, testProfile.email);
            expect(profile.phoneNumber, testProfile.phoneNumber);
          },
        );
        verify(mockRepository.getProfile(userId)).called(1);
      });

      test('returns error when repository fails', () async {
        // Arrange
        const userId = 'test_user_id';
        const errorMessage = 'Profile not found';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (profile) => fail('Expected error but got profile'),
        );
        verify(mockRepository.getProfile(userId)).called(1);
      });

      test('calls repository with correct user ID', () async {
        // Arrange
        const userId = 'specific_user_123';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => Right(testProfile));

        // Act
        await useCase.call(userId);

        // Assert
        verify(mockRepository.getProfile(userId)).called(1);
        verifyNoMoreInteractions(mockRepository);
      });

      test('handles different user IDs correctly', () async {
        // Arrange
        const userId1 = 'user1';
        const userId2 = 'user2';
        when(mockRepository.getProfile(any))
            .thenAnswer((_) async => Right(testProfile));

        // Act
        await useCase.call(userId1);
        await useCase.call(userId2);

        // Assert
        verify(mockRepository.getProfile(userId1)).called(1);
        verify(mockRepository.getProfile(userId2)).called(1);
      });

      test('handles empty user ID', () async {
        // Arrange
        const userId = '';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => const Left('Invalid user ID'));

        // Act
        final result = await useCase.call(userId);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, 'Invalid user ID'),
          (profile) => fail('Expected error for empty user ID'),
        );
      });

      test('returns same profile object from repository', () async {
        // Arrange
        const userId = 'test_user';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => Right(testProfile));

        // Act
        final result = await useCase.call(userId);

        // Assert
        result.fold(
          (error) => fail('Expected success'),
          (profile) {
            expect(profile, equals(testProfile));
            expect(profile.id, testProfile.id);
            expect(profile.name, testProfile.name);
            expect(profile.email, testProfile.email);
            expect(profile.phoneNumber, testProfile.phoneNumber);
            expect(profile.profileImage, testProfile.profileImage);
            expect(profile.role, testProfile.role);
            expect(profile.preferences, testProfile.preferences);
            expect(profile.dateOfBirth, testProfile.dateOfBirth);
            expect(profile.createdAt, testProfile.createdAt);
            expect(profile.updatedAt, testProfile.updatedAt);
            expect(profile.isActive, testProfile.isActive);
          },
        );
      });
    });

    group('Error Scenarios', () {
      test('handles network errors', () async {
        // Arrange
        const userId = 'test_user';
        const errorMessage = 'Network connection failed';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (profile) => fail('Expected error'),
        );
      });

      test('handles authentication errors', () async {
        // Arrange
        const userId = 'test_user';
        const errorMessage = 'User not authenticated';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (profile) => fail('Expected error'),
        );
      });

      test('handles server errors', () async {
        // Arrange
        const userId = 'test_user';
        const errorMessage = 'Internal server error';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (profile) => fail('Expected error'),
        );
      });
    });

    group('Edge Cases', () {
      test('handles special characters in user ID', () async {
        // Arrange
        const userId = 'user@#\$%^&*()';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => Right(testProfile));

        // Act
        final result = await useCase.call(userId);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.getProfile(userId)).called(1);
      });

      test('handles very long user ID', () async {
        // Arrange
        final userId = 'user${'a' * 1000}';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => Right(testProfile));

        // Act
        final result = await useCase.call(userId);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.getProfile(userId)).called(1);
      });

      test('handles null-like string values', () async {
        // Arrange
        const userId = 'null';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => Right(testProfile));

        // Act
        final result = await useCase.call(userId);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.getProfile(userId)).called(1);
      });
    });

    group('Performance Tests', () {
      test('handles multiple concurrent calls', () async {
        // Arrange
        const userId = 'test_user';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => Right(testProfile));

        // Act
        final futures = List.generate(
          10,
          (index) => useCase.call(userId),
        );
        final results = await Future.wait(futures);

        // Assert
        for (final result in results) {
          expect(result.isRight(), true);
        }
        verify(mockRepository.getProfile(userId)).called(10);
      });

      test('handles sequential calls efficiently', () async {
        // Arrange
        const userId = 'test_user';
        when(mockRepository.getProfile(userId))
            .thenAnswer((_) async => Right(testProfile));

        // Act
        for (int i = 0; i < 5; i++) {
          final result = await useCase.call(userId);
          expect(result.isRight(), true);
        }

        // Assert
        verify(mockRepository.getProfile(userId)).called(5);
      });
    });
  });
}
