import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';

import '../../../../../lib/features/profile/domain/usecases/update_profile_usecase.dart';
import '../../../../../lib/features/profile/domain/repositories/profile_repository.dart';
import '../../../../../lib/features/profile/domain/entities/profile.dart';

import 'update_profile_usecase_test.mocks.dart';

@GenerateMocks([ProfileRepository])
void main() {
  group('UpdateProfileUseCase Tests', () {
    late UpdateProfileUseCase useCase;
    late MockProfileRepository mockRepository;
    late Profile testProfile;
    late Profile updatedProfile;

    setUp(() {
      mockRepository = MockProfileRepository();
      useCase = UpdateProfileUseCase(mockRepository);

      testProfile = Profile(
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        phoneNumber: '+1234567890',
        profileImage: 'https://example.com/profile.jpg',
        role: 'PLAYER',
        preferences: {'theme': 'dark'},
        dateOfBirth: DateTime(1990, 1, 1),
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 2),
        isActive: true,
      );

      updatedProfile = testProfile.copyWith(
        name: 'Jane Doe',
        email: '<EMAIL>',
        updatedAt: DateTime(2023, 1, 3),
      );
    });

    group('call method', () {
      test('returns updated profile when repository succeeds', () async {
        // Arrange
        when(mockRepository.updateProfile(testProfile))
            .thenAnswer((_) async => Right(updatedProfile));

        // Act
        final result = await useCase.call(testProfile);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (profile) {
            expect(profile.id, updatedProfile.id);
            expect(profile.name, updatedProfile.name);
            expect(profile.email, updatedProfile.email);
            expect(profile.updatedAt, updatedProfile.updatedAt);
          },
        );
        verify(mockRepository.updateProfile(testProfile)).called(1);
      });

      test('returns error when repository fails', () async {
        // Arrange
        const errorMessage = 'Update failed';
        when(mockRepository.updateProfile(testProfile))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(testProfile);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (profile) => fail('Expected error but got profile'),
        );
        verify(mockRepository.updateProfile(testProfile)).called(1);
      });

      test('calls repository with correct profile object', () async {
        // Arrange
        when(mockRepository.updateProfile(any))
            .thenAnswer((_) async => Right(updatedProfile));

        // Act
        await useCase.call(testProfile);

        // Assert
        final captured =
            verify(mockRepository.updateProfile(captureAny)).captured;
        final capturedProfile = captured.first as Profile;
        expect(capturedProfile.id, testProfile.id);
        expect(capturedProfile.name, testProfile.name);
        expect(capturedProfile.email, testProfile.email);
      });

      test('handles profile with minimal data', () async {
        // Arrange
        final minimalProfile = Profile(
          id: '2',
          name: 'Test User',
          email: '<EMAIL>',
          phoneNumber: '+1111111111',
          role: 'PLAYER',
          createdAt: DateTime(2023, 1, 1),
          updatedAt: DateTime(2023, 1, 1),
          isActive: true,
        );
        when(mockRepository.updateProfile(minimalProfile))
            .thenAnswer((_) async => Right(minimalProfile));

        // Act
        final result = await useCase.call(minimalProfile);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updateProfile(minimalProfile)).called(1);
      });

      test('handles profile with all optional fields', () async {
        // Arrange
        final fullProfile = Profile(
          id: '3',
          name: 'Full Profile User',
          email: '<EMAIL>',
          phoneNumber: '+2222222222',
          profileImage: 'https://example.com/full.jpg',
          role: 'COACH',
          preferences: {
            'theme': 'light',
            'notifications': true,
            'language': 'en',
          },
          dateOfBirth: DateTime(1985, 5, 15),
          createdAt: DateTime(2023, 1, 1),
          updatedAt: DateTime(2023, 1, 2),
          isActive: true,
        );
        when(mockRepository.updateProfile(fullProfile))
            .thenAnswer((_) async => Right(fullProfile));

        // Act
        final result = await useCase.call(fullProfile);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updateProfile(fullProfile)).called(1);
      });
    });

    group('Error Scenarios', () {
      test('handles validation errors', () async {
        // Arrange
        const errorMessage = 'Invalid profile data';
        when(mockRepository.updateProfile(testProfile))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(testProfile);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (profile) => fail('Expected error'),
        );
      });

      test('handles network errors', () async {
        // Arrange
        const errorMessage = 'Network connection failed';
        when(mockRepository.updateProfile(testProfile))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(testProfile);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (profile) => fail('Expected error'),
        );
      });

      test('handles server errors', () async {
        // Arrange
        const errorMessage = 'Internal server error';
        when(mockRepository.updateProfile(testProfile))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(testProfile);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (profile) => fail('Expected error'),
        );
      });

      test('handles permission errors', () async {
        // Arrange
        const errorMessage = 'Permission denied';
        when(mockRepository.updateProfile(testProfile))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(testProfile);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (profile) => fail('Expected error'),
        );
      });
    });

    group('Edge Cases', () {
      test('handles profile with empty string values', () async {
        // Arrange
        final emptyProfile = testProfile.copyWith(
          name: '',
          email: '',
          profileImage: '',
        );
        when(mockRepository.updateProfile(emptyProfile))
            .thenAnswer((_) async => Right(emptyProfile));

        // Act
        final result = await useCase.call(emptyProfile);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updateProfile(emptyProfile)).called(1);
      });

      test('handles profile with null optional values', () async {
        // Arrange
        final nullProfile = testProfile.copyWith(
          profileImage: null,
          preferences: null,
          dateOfBirth: null,
        );
        when(mockRepository.updateProfile(nullProfile))
            .thenAnswer((_) async => Right(nullProfile));

        // Act
        final result = await useCase.call(nullProfile);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updateProfile(nullProfile)).called(1);
      });

      test('handles profile with complex preferences', () async {
        // Arrange
        final complexProfile = testProfile.copyWith(
          preferences: {
            'theme': 'dark',
            'notifications': {
              'email': true,
              'push': false,
              'sms': true,
            },
            'privacy': {
              'showProfile': true,
              'showStats': false,
            },
            'language': 'en',
            'timezone': 'UTC',
          },
        );
        when(mockRepository.updateProfile(complexProfile))
            .thenAnswer((_) async => Right(complexProfile));

        // Act
        final result = await useCase.call(complexProfile);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updateProfile(complexProfile)).called(1);
      });
    });

    group('Performance Tests', () {
      test('handles multiple sequential updates', () async {
        // Arrange
        when(mockRepository.updateProfile(any))
            .thenAnswer((_) async => Right(updatedProfile));

        // Act
        for (int i = 0; i < 5; i++) {
          final result = await useCase.call(testProfile);
          expect(result.isRight(), true);
        }

        // Assert
        verify(mockRepository.updateProfile(testProfile)).called(5);
      });

      test('handles concurrent updates', () async {
        // Arrange
        when(mockRepository.updateProfile(any))
            .thenAnswer((_) async => Right(updatedProfile));

        // Act
        final futures = List.generate(
          3,
          (index) => useCase.call(testProfile),
        );
        final results = await Future.wait(futures);

        // Assert
        for (final result in results) {
          expect(result.isRight(), true);
        }
        verify(mockRepository.updateProfile(testProfile)).called(3);
      });
    });
  });
}
