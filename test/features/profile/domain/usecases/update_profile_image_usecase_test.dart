import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';

import '../../../../../lib/features/profile/domain/usecases/update_profile_image_usecase.dart';
import '../../../../../lib/features/profile/domain/repositories/profile_repository.dart';

import 'update_profile_image_usecase_test.mocks.dart';

@GenerateMocks([ProfileRepository])
void main() {
  group('UpdateProfileImageUseCase Tests', () {
    late UpdateProfileImageUseCase useCase;
    late MockProfileRepository mockRepository;

    setUp(() {
      mockRepository = MockProfileRepository();
      useCase = UpdateProfileImageUseCase(mockRepository);
    });

    group('call method', () {
      test('returns success when repository succeeds', () async {
        // Arrange
        const userId = 'user123';
        const imagePath = '/path/to/image.jpg';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (value) => expect(value, null),
        );
        verify(mockRepository.updateProfileImage(userId, imagePath)).called(1);
      });

      test('returns error when repository fails', () async {
        // Arrange
        const userId = 'user123';
        const imagePath = '/path/to/image.jpg';
        const errorMessage = 'Image upload failed';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error but got success'),
        );
        verify(mockRepository.updateProfileImage(userId, imagePath)).called(1);
      });

      test('calls repository with correct parameters', () async {
        // Arrange
        const userId = 'specific_user_456';
        const imagePath = '/specific/path/to/image.png';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Right(null));

        // Act
        await useCase.call(userId, imagePath);

        // Assert
        verify(mockRepository.updateProfileImage(userId, imagePath)).called(1);
        verifyNoMoreInteractions(mockRepository);
      });

      test('handles different image file types', () async {
        // Arrange
        const userId = 'user123';
        const imagePaths = [
          '/path/to/image.jpg',
          '/path/to/image.jpeg',
          '/path/to/image.png',
          '/path/to/image.gif',
          '/path/to/image.webp',
        ];
        when(mockRepository.updateProfileImage(any, any))
            .thenAnswer((_) async => const Right(null));

        // Act & Assert
        for (final imagePath in imagePaths) {
          final result = await useCase.call(userId, imagePath);
          expect(result.isRight(), true);
          verify(mockRepository.updateProfileImage(userId, imagePath))
              .called(1);
        }
      });

      test('handles different user IDs', () async {
        // Arrange
        const imagePath = '/path/to/image.jpg';
        const userIds = ['user1', 'user2', 'user3'];
        when(mockRepository.updateProfileImage(any, any))
            .thenAnswer((_) async => const Right(null));

        // Act & Assert
        for (final userId in userIds) {
          final result = await useCase.call(userId, imagePath);
          expect(result.isRight(), true);
          verify(mockRepository.updateProfileImage(userId, imagePath))
              .called(1);
        }
      });
    });

    group('Error Scenarios', () {
      test('handles file not found errors', () async {
        // Arrange
        const userId = 'user123';
        const imagePath = '/nonexistent/path/image.jpg';
        const errorMessage = 'File not found';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error'),
        );
      });

      test('handles permission errors', () async {
        // Arrange
        const userId = 'user123';
        const imagePath = '/restricted/path/image.jpg';
        const errorMessage = 'Permission denied';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error'),
        );
      });

      test('handles network errors', () async {
        // Arrange
        const userId = 'user123';
        const imagePath = '/path/to/image.jpg';
        const errorMessage = 'Network connection failed';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error'),
        );
      });

      test('handles file size errors', () async {
        // Arrange
        const userId = 'user123';
        const imagePath = '/path/to/large_image.jpg';
        const errorMessage = 'File size too large';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error'),
        );
      });

      test('handles invalid file format errors', () async {
        // Arrange
        const userId = 'user123';
        const imagePath = '/path/to/document.pdf';
        const errorMessage = 'Invalid file format';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error'),
        );
      });
    });

    group('Edge Cases', () {
      test('handles empty user ID', () async {
        // Arrange
        const userId = '';
        const imagePath = '/path/to/image.jpg';
        const errorMessage = 'Invalid user ID';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error for empty user ID'),
        );
      });

      test('handles empty image path', () async {
        // Arrange
        const userId = 'user123';
        const imagePath = '';
        const errorMessage = 'Invalid image path';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error for empty image path'),
        );
      });

      test('handles very long file paths', () async {
        // Arrange
        const userId = 'user123';
        final imagePath = '/very/long/path/${'a' * 500}/image.jpg';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updateProfileImage(userId, imagePath)).called(1);
      });

      test('handles special characters in paths', () async {
        // Arrange
        const userId = 'user@#\$%';
        const imagePath = '/path/with spaces/special-chars_123.jpg';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updateProfileImage(userId, imagePath)).called(1);
      });

      test('handles relative paths', () async {
        // Arrange
        const userId = 'user123';
        const imagePath = '../images/profile.jpg';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updateProfileImage(userId, imagePath)).called(1);
      });

      test('handles URL-like paths', () async {
        // Arrange
        const userId = 'user123';
        const imagePath = 'https://example.com/images/profile.jpg';
        when(mockRepository.updateProfileImage(userId, imagePath))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, imagePath);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updateProfileImage(userId, imagePath)).called(1);
      });
    });

    group('Performance Tests', () {
      test('handles multiple sequential image uploads', () async {
        // Arrange
        const userId = 'user123';
        const imagePaths = [
          '/path/to/image1.jpg',
          '/path/to/image2.jpg',
          '/path/to/image3.jpg',
        ];
        when(mockRepository.updateProfileImage(any, any))
            .thenAnswer((_) async => const Right(null));

        // Act
        for (final imagePath in imagePaths) {
          final result = await useCase.call(userId, imagePath);
          expect(result.isRight(), true);
        }

        // Assert
        for (final imagePath in imagePaths) {
          verify(mockRepository.updateProfileImage(userId, imagePath))
              .called(1);
        }
      });

      test('handles concurrent image uploads for different users', () async {
        // Arrange
        const userIds = ['user1', 'user2', 'user3'];
        const imagePath = '/path/to/image.jpg';
        when(mockRepository.updateProfileImage(any, any))
            .thenAnswer((_) async => const Right(null));

        // Act
        final futures = userIds.map(
          (userId) => useCase.call(userId, imagePath),
        );
        final results = await Future.wait(futures);

        // Assert
        for (final result in results) {
          expect(result.isRight(), true);
        }
        for (final userId in userIds) {
          verify(mockRepository.updateProfileImage(userId, imagePath))
              .called(1);
        }
      });
    });
  });
}
