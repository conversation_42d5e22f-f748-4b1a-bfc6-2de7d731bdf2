import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';

import '../../../../../lib/features/profile/domain/usecases/update_preferences_usecase.dart';
import '../../../../../lib/features/profile/domain/repositories/profile_repository.dart';

import 'update_preferences_usecase_test.mocks.dart';

@GenerateMocks([ProfileRepository])
void main() {
  group('UpdatePreferencesUseCase Tests', () {
    late UpdatePreferencesUseCase useCase;
    late MockProfileRepository mockRepository;

    setUp(() {
      mockRepository = MockProfileRepository();
      useCase = UpdatePreferencesUseCase(mockRepository);
    });

    group('call method', () {
      test('returns success when repository succeeds', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {'theme': 'dark', 'notifications': true};
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (value) => expect(value, null),
        );
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('returns error when repository fails', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {'theme': 'dark'};
        const errorMessage = 'Preferences update failed';
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error but got success'),
        );
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('calls repository with correct parameters', () async {
        // Arrange
        const userId = 'specific_user_789';
        final preferences = {
          'theme': 'light',
          'language': 'en',
          'timezone': 'UTC',
        };
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        await useCase.call(userId, preferences);

        // Assert
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
        verifyNoMoreInteractions(mockRepository);
      });

      test('handles empty preferences map', () async {
        // Arrange
        const userId = 'user123';
        final preferences = <String, dynamic>{};
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('handles single preference', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {'theme': 'dark'};
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('handles multiple preferences', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {
          'theme': 'dark',
          'notifications': true,
          'language': 'en',
          'timezone': 'EST',
          'autoSave': false,
        };
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });
    });

    group('Different Data Types', () {
      test('handles string preferences', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {
          'theme': 'dark',
          'language': 'english',
          'currency': 'USD',
        };
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('handles boolean preferences', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {
          'notifications': true,
          'autoSave': false,
          'darkMode': true,
        };
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('handles number preferences', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {
          'volume': 75,
          'fontSize': 14.5,
          'timeout': 30,
        };
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('handles list preferences', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {
          'favoriteColors': ['blue', 'green', 'red'],
          'recentSearches': ['flutter', 'dart', 'mobile'],
          'blockedUsers': ['user1', 'user2'],
        };
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('handles nested map preferences', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {
          'notifications': {
            'email': true,
            'push': false,
            'sms': true,
          },
          'privacy': {
            'showProfile': true,
            'showStats': false,
            'allowMessages': true,
          },
        };
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('handles mixed data type preferences', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {
          'theme': 'dark',
          'notifications': true,
          'volume': 85,
          'fontSize': 16.0,
          'favorites': ['item1', 'item2'],
          'settings': {
            'autoSync': true,
            'syncInterval': 300,
          },
        };
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('handles null values in preferences', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {
          'theme': 'dark',
          'customBackground': null,
          'lastLoginTime': null,
        };
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });
    });

    group('Error Scenarios', () {
      test('handles validation errors', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {'theme': 'invalid_theme'};
        const errorMessage = 'Invalid preference value';
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error'),
        );
      });

      test('handles network errors', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {'theme': 'dark'};
        const errorMessage = 'Network connection failed';
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error'),
        );
      });

      test('handles permission errors', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {'adminSettings': true};
        const errorMessage = 'Permission denied';
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error'),
        );
      });

      test('handles server errors', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {'theme': 'dark'};
        const errorMessage = 'Internal server error';
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error'),
        );
      });
    });

    group('Edge Cases', () {
      test('handles empty user ID', () async {
        // Arrange
        const userId = '';
        final preferences = {'theme': 'dark'};
        const errorMessage = 'Invalid user ID';
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Left(errorMessage));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (value) => fail('Expected error for empty user ID'),
        );
      });

      test('handles very long preference keys', () async {
        // Arrange
        const userId = 'user123';
        final longKey = 'a' * 500;
        final preferences = {longKey: 'value'};
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('handles very long preference values', () async {
        // Arrange
        const userId = 'user123';
        final longValue = 'value${'x' * 1000}';
        final preferences = {'key': longValue};
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('handles special characters in keys and values', () async {
        // Arrange
        const userId = 'user@#\$%';
        final preferences = {
          'key@#\$%': 'value@#\$%',
          'unicode_key_🚀': 'unicode_value_🌟',
          'spaces in key': 'spaces in value',
        };
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });

      test('handles deeply nested preferences', () async {
        // Arrange
        const userId = 'user123';
        final preferences = {
          'level1': {
            'level2': {
              'level3': {
                'level4': {'level5': 'deep_value'}
              }
            }
          }
        };
        when(mockRepository.updatePreferences(userId, preferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, preferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, preferences)).called(1);
      });
    });

    group('Performance Tests', () {
      test('handles multiple sequential preference updates', () async {
        // Arrange
        const userId = 'user123';
        final preferencesList = [
          {'theme': 'dark'},
          {'language': 'en'},
          {'notifications': true},
        ];
        when(mockRepository.updatePreferences(any, any))
            .thenAnswer((_) async => const Right(null));

        // Act
        for (final preferences in preferencesList) {
          final result = await useCase.call(userId, preferences);
          expect(result.isRight(), true);
        }

        // Assert
        for (final preferences in preferencesList) {
          verify(mockRepository.updatePreferences(userId, preferences))
              .called(1);
        }
      });

      test('handles concurrent preference updates for different users',
          () async {
        // Arrange
        const userIds = ['user1', 'user2', 'user3'];
        final preferences = {'theme': 'dark'};
        when(mockRepository.updatePreferences(any, any))
            .thenAnswer((_) async => const Right(null));

        // Act
        final futures = userIds.map(
          (userId) => useCase.call(userId, preferences),
        );
        final results = await Future.wait(futures);

        // Assert
        for (final result in results) {
          expect(result.isRight(), true);
        }
        for (final userId in userIds) {
          verify(mockRepository.updatePreferences(userId, preferences))
              .called(1);
        }
      });

      test('handles large preference maps', () async {
        // Arrange
        const userId = 'user123';
        final largePreferences = {
          for (int i = 0; i < 100; i++) 'key$i': 'value$i'
        };
        when(mockRepository.updatePreferences(userId, largePreferences))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await useCase.call(userId, largePreferences);

        // Assert
        expect(result.isRight(), true);
        verify(mockRepository.updatePreferences(userId, largePreferences))
            .called(1);
      });
    });
  });
}
