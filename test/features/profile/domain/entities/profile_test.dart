import 'package:flutter_test/flutter_test.dart';
import '../../../../../lib/features/profile/domain/entities/profile.dart';

void main() {
  group('Profile Entity Tests', () {
    late Profile testProfile;
    late Map<String, dynamic> testJson;

    setUp(() {
      testProfile = Profile(
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        phoneNumber: '+1234567890',
        profileImage: 'https://example.com/profile.jpg',
        role: 'PLAYER',
        preferences: {'theme': 'dark', 'notifications': true},
        dateOfBirth: DateTime(1990, 1, 1),
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 2),
        isActive: true,
      );

      testJson = {
        'id': '1',
        'name': '<PERSON>',
        'email': '<EMAIL>',
        'phone_number': '+1234567890',
        'profile_image': 'https://example.com/profile.jpg',
        'role': 'PLAYER',
        'preferences': {'theme': 'dark', 'notifications': true},
        'date_of_birth': '1990-01-01T00:00:00.000',
        'created_at': '2023-01-01T00:00:00.000',
        'updated_at': '2023-01-02T00:00:00.000',
        'is_active': true,
      };
    });

    group('Profile Creation', () {
      test('creates profile with all required fields', () {
        final profile = Profile(
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          phoneNumber: '+1234567890',
          role: 'PLAYER',
          createdAt: DateTime(2023, 1, 1),
          updatedAt: DateTime(2023, 1, 2),
          isActive: true,
        );

        expect(profile.id, '1');
        expect(profile.name, 'John Doe');
        expect(profile.email, '<EMAIL>');
        expect(profile.phoneNumber, '+1234567890');
        expect(profile.role, 'PLAYER');
        expect(profile.isActive, true);
      });

      test('creates profile with optional fields as null', () {
        final profile = Profile(
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          phoneNumber: '+1234567890',
          role: 'PLAYER',
          createdAt: DateTime(2023, 1, 1),
          updatedAt: DateTime(2023, 1, 2),
          isActive: true,
        );

        expect(profile.profileImage, null);
        expect(profile.preferences, null);
        expect(profile.dateOfBirth, null);
      });
    });

    group('JSON Serialization', () {
      test('fromJson creates correct Profile instance', () {
        final profile = Profile.fromJson(testJson);

        expect(profile.id, '1');
        expect(profile.name, 'John Doe');
        expect(profile.email, '<EMAIL>');
        expect(profile.phoneNumber, '+1234567890');
        expect(profile.profileImage, 'https://example.com/profile.jpg');
        expect(profile.role, 'PLAYER');
        expect(profile.preferences, {'theme': 'dark', 'notifications': true});
        expect(profile.dateOfBirth, DateTime(1990, 1, 1));
        expect(profile.createdAt, DateTime(2023, 1, 1));
        expect(profile.updatedAt, DateTime(2023, 1, 2));
        expect(profile.isActive, true);
      });

      test('fromJson handles null optional fields', () {
        final jsonWithNulls = {
          'id': '1',
          'name': 'John Doe',
          'email': '<EMAIL>',
          'phone_number': '+1234567890',
          'profile_image': null,
          'role': 'PLAYER',
          'preferences': null,
          'date_of_birth': null,
          'created_at': '2023-01-01T00:00:00.000',
          'updated_at': '2023-01-02T00:00:00.000',
          'is_active': true,
        };

        final profile = Profile.fromJson(jsonWithNulls);

        expect(profile.profileImage, null);
        expect(profile.preferences, null);
        expect(profile.dateOfBirth, null);
      });

      test('toJson creates correct JSON map', () {
        final json = testProfile.toJson();

        expect(json['id'], '1');
        expect(json['name'], 'John Doe');
        expect(json['email'], '<EMAIL>');
        expect(json['phone_number'], '+1234567890');
        expect(json['profile_image'], 'https://example.com/profile.jpg');
        expect(json['role'], 'PLAYER');
        expect(json['preferences'], {'theme': 'dark', 'notifications': true});
        expect(json['date_of_birth'], '1990-01-01T00:00:00.000');
        expect(json['created_at'], '2023-01-01T00:00:00.000');
        expect(json['updated_at'], '2023-01-02T00:00:00.000');
        expect(json['is_active'], true);
      });

      test('toJson handles null optional fields', () {
        final profileWithNulls = Profile(
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          phoneNumber: '+1234567890',
          role: 'PLAYER',
          createdAt: DateTime(2023, 1, 1),
          updatedAt: DateTime(2023, 1, 2),
          isActive: true,
        );

        final json = profileWithNulls.toJson();

        expect(json['profile_image'], null);
        expect(json['preferences'], null);
        expect(json['date_of_birth'], null);
      });
    });

    group('copyWith Method', () {
      test('copyWith returns new instance with updated fields', () {
        final updatedProfile = testProfile.copyWith(
          name: 'Jane Doe',
          email: '<EMAIL>',
          isActive: false,
        );

        expect(updatedProfile.id, testProfile.id);
        expect(updatedProfile.name, 'Jane Doe');
        expect(updatedProfile.email, '<EMAIL>');
        expect(updatedProfile.phoneNumber, testProfile.phoneNumber);
        expect(updatedProfile.isActive, false);
        expect(updatedProfile.role, testProfile.role);
      });

      test('copyWith returns same instance when no fields are updated', () {
        final copiedProfile = testProfile.copyWith();

        expect(copiedProfile.id, testProfile.id);
        expect(copiedProfile.name, testProfile.name);
        expect(copiedProfile.email, testProfile.email);
        expect(copiedProfile.phoneNumber, testProfile.phoneNumber);
        expect(copiedProfile.profileImage, testProfile.profileImage);
        expect(copiedProfile.role, testProfile.role);
        expect(copiedProfile.preferences, testProfile.preferences);
        expect(copiedProfile.dateOfBirth, testProfile.dateOfBirth);
        expect(copiedProfile.createdAt, testProfile.createdAt);
        expect(copiedProfile.updatedAt, testProfile.updatedAt);
        expect(copiedProfile.isActive, testProfile.isActive);
      });

      test('copyWith can update nullable fields to null', () {
        final updatedProfile = testProfile.copyWith(
          profileImage: null,
          preferences: null,
          dateOfBirth: null,
        );

        expect(updatedProfile.profileImage, null);
        expect(updatedProfile.preferences, null);
        expect(updatedProfile.dateOfBirth, null);
        expect(updatedProfile.id, testProfile.id);
        expect(updatedProfile.name, testProfile.name);
      });

      test('copyWith can update all fields', () {
        final newDate = DateTime(1995, 5, 15);
        final newCreatedAt = DateTime(2023, 6, 1);
        final newUpdatedAt = DateTime(2023, 6, 2);
        final newPreferences = {'theme': 'light', 'language': 'en'};

        final updatedProfile = testProfile.copyWith(
          id: '2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          phoneNumber: '+9876543210',
          profileImage: 'https://example.com/new-profile.jpg',
          role: 'COACH',
          preferences: newPreferences,
          dateOfBirth: newDate,
          createdAt: newCreatedAt,
          updatedAt: newUpdatedAt,
          isActive: false,
        );

        expect(updatedProfile.id, '2');
        expect(updatedProfile.name, 'Jane Smith');
        expect(updatedProfile.email, '<EMAIL>');
        expect(updatedProfile.phoneNumber, '+9876543210');
        expect(
            updatedProfile.profileImage, 'https://example.com/new-profile.jpg');
        expect(updatedProfile.role, 'COACH');
        expect(updatedProfile.preferences, newPreferences);
        expect(updatedProfile.dateOfBirth, newDate);
        expect(updatedProfile.createdAt, newCreatedAt);
        expect(updatedProfile.updatedAt, newUpdatedAt);
        expect(updatedProfile.isActive, false);
      });
    });

    group('Edge Cases', () {
      test('handles empty string values', () {
        final profileJson = {
          'id': '',
          'name': '',
          'email': '',
          'phone_number': '',
          'profile_image': '',
          'role': '',
          'preferences': null,
          'date_of_birth': null,
          'created_at': '2023-01-01T00:00:00.000',
          'updated_at': '2023-01-02T00:00:00.000',
          'is_active': false,
        };

        final profile = Profile.fromJson(profileJson);

        expect(profile.id, '');
        expect(profile.name, '');
        expect(profile.email, '');
        expect(profile.phoneNumber, '');
        expect(profile.profileImage, '');
        expect(profile.role, '');
      });

      test('handles complex preferences map', () {
        final complexPreferences = {
          'theme': 'dark',
          'notifications': {
            'email': true,
            'push': false,
            'sms': true,
          },
          'privacy': {
            'showProfile': true,
            'showStats': false,
          },
          'language': 'en',
          'timezone': 'UTC',
        };

        final profile = testProfile.copyWith(preferences: complexPreferences);
        final json = profile.toJson();
        final deserializedProfile = Profile.fromJson(json);

        expect(deserializedProfile.preferences, complexPreferences);
      });
    });
  });
}
