// Mocks generated by Mockito 5.4.6 from annotations
// in nextsportz_v2/test/features/profile/data/repositories/profile_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:nextsportz_v2/features/profile/data/datasources/profile_datasource.dart'
    as _i3;
import 'package:nextsportz_v2/features/profile/domain/entities/profile.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeProfile_0 extends _i1.SmartFake implements _i2.Profile {
  _FakeProfile_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ProfileDatasource].
///
/// See the documentation for Mockito's code generation for more information.
class MockProfileDatasource extends _i1.Mock implements _i3.ProfileDatasource {
  MockProfileDatasource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Profile> getProfile() =>
      (super.noSuchMethod(
            Invocation.method(#getProfile, []),
            returnValue: _i4.Future<_i2.Profile>.value(
              _FakeProfile_0(this, Invocation.method(#getProfile, [])),
            ),
          )
          as _i4.Future<_i2.Profile>);

  @override
  _i4.Future<_i2.Profile> updateProfile(_i2.Profile? profile) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [profile]),
            returnValue: _i4.Future<_i2.Profile>.value(
              _FakeProfile_0(
                this,
                Invocation.method(#updateProfile, [profile]),
              ),
            ),
          )
          as _i4.Future<_i2.Profile>);

  @override
  _i4.Future<void> uploadProfileImage(String? imagePath) =>
      (super.noSuchMethod(
            Invocation.method(#uploadProfileImage, [imagePath]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}
