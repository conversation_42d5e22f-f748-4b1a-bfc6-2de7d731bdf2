import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';

import '../../../../../lib/features/profile/data/repositories/profile_repository_impl.dart';
import '../../../../../lib/features/profile/data/datasources/profile_datasource.dart';
import '../../../../../lib/features/profile/domain/entities/profile.dart';

import 'profile_repository_impl_test.mocks.dart';

@GenerateMocks([ProfileDatasource])
void main() {
  group('ProfileRepositoryImpl Tests', () {
    late ProfileRepositoryImpl repository;
    late MockProfileDatasource mockDatasource;
    late Profile testProfile;

    setUp(() {
      mockDatasource = MockProfileDatasource();
      repository = ProfileRepositoryImpl(mockDatasource);

      testProfile = Profile(
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        phoneNumber: '+1234567890',
        profileImage: 'https://example.com/profile.jpg',
        role: 'PLAYER',
        preferences: {'theme': 'dark'},
        dateOfBirth: DateTime(1990, 1, 1),
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 2),
        isActive: true,
      );
    });

    group('getProfile', () {
      test('returns profile when datasource succeeds', () async {
        // Arrange
        when(mockDatasource.getProfile()).thenAnswer((_) async => testProfile);

        // Act
        final result = await repository.getProfile('1');

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (profile) {
            expect(profile.id, testProfile.id);
            expect(profile.name, testProfile.name);
            expect(profile.email, testProfile.email);
          },
        );
        verify(mockDatasource.getProfile()).called(1);
      });

      test('returns error when datasource throws exception', () async {
        // Arrange
        const errorMessage = 'Network error';
        when(mockDatasource.getProfile()).thenThrow(Exception(errorMessage));

        // Act
        final result = await repository.getProfile('1');

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, contains(errorMessage)),
          (profile) => fail('Expected error but got profile'),
        );
        verify(mockDatasource.getProfile()).called(1);
      });

      test('returns error when datasource throws string', () async {
        // Arrange
        const errorMessage = 'Profile not found';
        when(mockDatasource.getProfile()).thenThrow(errorMessage);

        // Act
        final result = await repository.getProfile('1');

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, errorMessage),
          (profile) => fail('Expected error but got profile'),
        );
      });
    });

    group('updateProfile', () {
      test('returns updated profile when datasource succeeds', () async {
        // Arrange
        final updatedProfile = testProfile.copyWith(
          name: 'Jane Doe',
          updatedAt: DateTime(2023, 1, 3),
        );
        when(mockDatasource.updateProfile(any))
            .thenAnswer((_) async => updatedProfile);

        // Act
        final result = await repository.updateProfile(testProfile);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (profile) {
            expect(profile.name, 'Jane Doe');
            expect(profile.id, testProfile.id);
          },
        );
        verify(mockDatasource.updateProfile(testProfile)).called(1);
      });

      test('returns error when datasource throws exception', () async {
        // Arrange
        const errorMessage = 'Update failed';
        when(mockDatasource.updateProfile(any))
            .thenThrow(Exception(errorMessage));

        // Act
        final result = await repository.updateProfile(testProfile);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, contains(errorMessage)),
          (profile) => fail('Expected error but got profile'),
        );
        verify(mockDatasource.updateProfile(testProfile)).called(1);
      });

      test('calls datasource with correct profile object', () async {
        // Arrange
        when(mockDatasource.updateProfile(any))
            .thenAnswer((_) async => testProfile);

        // Act
        await repository.updateProfile(testProfile);

        // Assert
        final captured =
            verify(mockDatasource.updateProfile(captureAny)).captured;
        final capturedProfile = captured.first as Profile;
        expect(capturedProfile.id, testProfile.id);
        expect(capturedProfile.name, testProfile.name);
        expect(capturedProfile.email, testProfile.email);
      });
    });

    group('updateProfileImage', () {
      test('returns success when datasource succeeds', () async {
        // Arrange
        const imagePath = '/path/to/image.jpg';
        when(mockDatasource.uploadProfileImage(any))
            .thenAnswer((_) async => {});

        // Act
        final result = await repository.updateProfileImage('1', imagePath);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (value) => expect(value, null),
        );
        verify(mockDatasource.uploadProfileImage(imagePath)).called(1);
      });

      test('returns error when datasource throws exception', () async {
        // Arrange
        const imagePath = '/path/to/image.jpg';
        const errorMessage = 'Upload failed';
        when(mockDatasource.uploadProfileImage(any))
            .thenThrow(Exception(errorMessage));

        // Act
        final result = await repository.updateProfileImage('1', imagePath);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error, contains(errorMessage)),
          (value) => fail('Expected error but got success'),
        );
        verify(mockDatasource.uploadProfileImage(imagePath)).called(1);
      });

      test('calls datasource with correct image path', () async {
        // Arrange
        const imagePath = '/path/to/image.jpg';
        when(mockDatasource.uploadProfileImage(any))
            .thenAnswer((_) async => {});

        // Act
        await repository.updateProfileImage('1', imagePath);

        // Assert
        verify(mockDatasource.uploadProfileImage(imagePath)).called(1);
      });
    });

    group('updatePreferences', () {
      test('returns success for preferences update', () async {
        // Arrange
        final preferences = {'theme': 'light', 'notifications': true};

        // Act
        final result = await repository.updatePreferences('1', preferences);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (value) => expect(value, null),
        );
      });

      test('handles empty preferences map', () async {
        // Arrange
        final preferences = <String, dynamic>{};

        // Act
        final result = await repository.updatePreferences('1', preferences);

        // Assert
        expect(result.isRight(), true);
      });

      test('handles complex preferences structure', () async {
        // Arrange
        final preferences = {
          'theme': 'dark',
          'notifications': {
            'email': true,
            'push': false,
          },
          'privacy': {
            'showProfile': true,
          },
        };

        // Act
        final result = await repository.updatePreferences('1', preferences);

        // Assert
        expect(result.isRight(), true);
      });
    });

    group('deleteProfile', () {
      test('returns success for profile deletion', () async {
        // Act
        final result = await repository.deleteProfile('1');

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (value) => expect(value, null),
        );
      });

      test('handles different user IDs', () async {
        // Act
        final result1 = await repository.deleteProfile('user1');
        final result2 = await repository.deleteProfile('user2');

        // Assert
        expect(result1.isRight(), true);
        expect(result2.isRight(), true);
      });
    });

    group('Error Handling', () {
      test('handles different types of exceptions consistently', () async {
        // Test Exception
        when(mockDatasource.getProfile()).thenThrow(Exception('Network error'));
        final result1 = await repository.getProfile('1');
        expect(result1.isLeft(), true);

        // Test String error
        when(mockDatasource.getProfile()).thenThrow('String error');
        final result2 = await repository.getProfile('1');
        expect(result2.isLeft(), true);

        // Test custom error
        when(mockDatasource.getProfile())
            .thenThrow(ArgumentError('Invalid argument'));
        final result3 = await repository.getProfile('1');
        expect(result3.isLeft(), true);
      });

      test('preserves original error messages', () async {
        // Arrange
        const originalError = 'Specific network timeout error';
        when(mockDatasource.getProfile()).thenThrow(Exception(originalError));

        // Act
        final result = await repository.getProfile('1');

        // Assert
        result.fold(
          (error) => expect(error, contains(originalError)),
          (profile) => fail('Expected error'),
        );
      });
    });

    group('Integration Scenarios', () {
      test('handles multiple sequential operations', () async {
        // Arrange
        when(mockDatasource.getProfile()).thenAnswer((_) async => testProfile);
        when(mockDatasource.updateProfile(any)).thenAnswer(
            (_) async => testProfile.copyWith(name: 'Updated Name'));

        // Act
        final getResult = await repository.getProfile('1');
        final updateResult = await repository.updateProfile(testProfile);

        // Assert
        expect(getResult.isRight(), true);
        expect(updateResult.isRight(), true);
        verify(mockDatasource.getProfile()).called(1);
        verify(mockDatasource.updateProfile(any)).called(1);
      });

      test('handles mixed success and failure operations', () async {
        // Arrange
        when(mockDatasource.getProfile()).thenAnswer((_) async => testProfile);
        when(mockDatasource.updateProfile(any))
            .thenThrow(Exception('Update failed'));

        // Act
        final getResult = await repository.getProfile('1');
        final updateResult = await repository.updateProfile(testProfile);

        // Assert
        expect(getResult.isRight(), true);
        expect(updateResult.isLeft(), true);
      });
    });
  });
}
