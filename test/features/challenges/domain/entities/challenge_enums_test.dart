import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/challenges/domain/entities/challenge_enums.dart';

void main() {
  group('AgeGroup enum', () {
    test('should have correct values', () {
      expect(AgeGroup.u12.value, 'U12');
      expect(AgeGroup.u14.value, 'U14');
      expect(AgeGroup.u16.value, 'U16');
      expect(AgeGroup.u18.value, 'U18');
      expect(AgeGroup.u21.value, 'U21');
      expect(AgeGroup.senior.value, 'Senior');
      expect(AgeGroup.open.value, 'Open');
    });

    test('fromString should return correct enum', () {
      expect(AgeGroup.fromString('U12'), AgeGroup.u12);
      expect(AgeGroup.fromString('U14'), AgeGroup.u14);
      expect(AgeGroup.fromString('U16'), AgeGroup.u16);
      expect(AgeGroup.fromString('U18'), AgeGroup.u18);
      expect(AgeGroup.fromString('U21'), AgeGroup.u21);
      expect(AgeGroup.fromString('Senior'), AgeGroup.senior);
      expect(AgeGroup.fromString('Open'), AgeGroup.open);
    });

    test('fromString should return default for invalid value', () {
      expect(AgeGroup.fromString('Invalid'), AgeGroup.open);
      expect(AgeGroup.fromString(null), null);
    });

    test('toString should return correct value', () {
      expect(AgeGroup.u12.toString(), 'U12');
      expect(AgeGroup.senior.toString(), 'Senior');
      expect(AgeGroup.open.toString(), 'Open');
    });
  });

  group('WagerType enum', () {
    test('should have correct values', () {
      expect(WagerType.money.value, 'Money');
      expect(WagerType.trophy.value, 'Trophy');
      expect(WagerType.braggingRights.value, 'Bragging Rights');
    });

    test('fromString should return correct enum', () {
      expect(WagerType.fromString('Money'), WagerType.money);
      expect(WagerType.fromString('Trophy'), WagerType.trophy);
      expect(WagerType.fromString('Bragging Rights'), WagerType.braggingRights);
    });

    test('fromString should return default for invalid value', () {
      expect(WagerType.fromString('Invalid'), WagerType.braggingRights);
      expect(WagerType.fromString(null), null);
    });

    test('toString should return correct value', () {
      expect(WagerType.money.toString(), 'Money');
      expect(WagerType.trophy.toString(), 'Trophy');
      expect(WagerType.braggingRights.toString(), 'Bragging Rights');
    });
  });
}
