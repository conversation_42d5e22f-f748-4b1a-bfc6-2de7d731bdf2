import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/challenges/data/dto/challenge_dto.dart';

void main() {
  group('CreateChallengeRequestDto', () {
    test('should create DTO with all fields', () {
      final dto = CreateChallengeRequestDto(
        matchType: '5v5',
        ageGroup: '18-25',
        skillLevel: 'intermediate',
        location: '<PERSON><PERSON><PERSON><PERSON>',
        venueId: 'venue-123',
        proposedDateTime: DateTime(2024, 1, 15, 18, 0),
        alternativeDateTime1: DateTime(2024, 1, 16, 18, 0),
        alternativeDateTime2: DateTime(2024, 1, 17, 18, 0),
        wagerAmount: 500.0,
        wagerType: 'NPR',
        description: 'Looking for a competitive match',
        rules: 'Standard futsal rules',
        specificOpponentId: 'opponent-123',
        specificOpponentTeamId: 'team-123',
        challengerTeamId: 'my-team-123',
        expirationHours: 24,
      );

      expect(dto.matchType, '5v5');
      expect(dto.ageGroup, '18-25');
      expect(dto.skillLevel, 'intermediate');
      expect(dto.location, 'Dhum<PERSON><PERSON>al');
      expect(dto.venueId, 'venue-123');
      expect(dto.proposedDateTime, DateTime(2024, 1, 15, 18, 0));
      expect(dto.wagerAmount, 500.0);
      expect(dto.expirationHours, 24);
    });

    test('should convert to JSON correctly', () {
      final dto = CreateChallengeRequestDto(
        matchType: '5v5',
        location: 'Dhumbarahi Futsal',
        proposedDateTime: DateTime(2024, 1, 15, 18, 0),
        expirationHours: 24,
      );

      final json = dto.toJson();

      expect(json['matchType'], '5v5');
      expect(json['location'], 'Dhumbarahi Futsal');
      expect(json['proposedDateTime'], '2024-01-15T18:00:00.000');
      expect(json['expirationHours'], 24);
    });
  });

  group('ChallengeResponseDto', () {
    test('should create DTO from JSON', () {
      final json = {
        'id': 'challenge-123',
        'challengerId': 'user-123',
        'challengerName': 'John Doe',
        'challengerTeamId': 'team-123',
        'challengerTeamName': 'FC Barcelona',
        'opponentId': 'user-456',
        'opponentName': 'Jane Smith',
        'opponentTeamId': 'team-456',
        'opponentTeamName': 'Real Madrid',
        'matchType': '5v5',
        'ageGroup': '18-25',
        'skillLevel': 'intermediate',
        'location': 'Dhumbarahi Futsal',
        'venueId': 'venue-123',
        'venueName': 'Dhumbarahi Futsal Center',
        'proposedDateTime': '2024-01-15T18:00:00.000Z',
        'alternativeDateTime1': '2024-01-16T18:00:00.000Z',
        'alternativeDateTime2': '2024-01-17T18:00:00.000Z',
        'wagerAmount': 500.0,
        'wagerType': 'NPR',
        'description': 'Looking for a competitive match',
        'rules': 'Standard futsal rules',
        'status': 'open',
        'expiresAt': '2024-01-16T18:00:00.000Z',
        'acceptedAt': null,
        'completedAt': null,
        'winnerId': null,
        'winnerName': null,
        'winnerTeamId': null,
        'winnerTeamName': null,
        'matchResult': null,
        'isResultDisputed': false,
        'responseCount': 0,
        'created': '2024-01-15T10:00:00.000Z',
      };

      final dto = ChallengeResponseDto.fromJson(json);

      expect(dto.id, 'challenge-123');
      expect(dto.challengerId, 'user-123');
      expect(dto.challengerName, 'John Doe');
      expect(dto.challengerTeamName, 'FC Barcelona');
      expect(dto.opponentId, 'user-456');
      expect(dto.opponentName, 'Jane Smith');
      expect(dto.matchType, '5v5');
      expect(dto.location, 'Dhumbarahi Futsal');
      expect(dto.wagerAmount, 500.0);
      expect(dto.status, 'open');
      expect(dto.isResultDisputed, false);
      expect(dto.responseCount, 0);
    });

    test('should convert to JSON correctly', () {
      final dto = ChallengeResponseDto(
        id: 'challenge-123',
        challengerId: 'user-123',
        challengerName: 'John Doe',
        matchType: '5v5',
        location: 'Dhumbarahi Futsal',
        proposedDateTime: DateTime(2024, 1, 15, 18, 0),
        expiresAt: DateTime(2024, 1, 16, 18, 0),
        isResultDisputed: false,
        responseCount: 0,
        created: DateTime(2024, 1, 15, 10, 0),
      );

      final json = dto.toJson();

      expect(json['id'], 'challenge-123');
      expect(json['challengerId'], 'user-123');
      expect(json['challengerName'], 'John Doe');
      expect(json['matchType'], '5v5');
      expect(json['location'], 'Dhumbarahi Futsal');
      expect(json['proposedDateTime'], '2024-01-15T18:00:00.000');
      expect(json['expiresAt'], '2024-01-16T18:00:00.000');
      expect(json['isResultDisputed'], false);
      expect(json['responseCount'], 0);
    });
  });

  group('PagedChallengesResponseDto', () {
    test('should create DTO from JSON', () {
      final json = {
        'items': [
          {
            'id': 'challenge-123',
            'challengerId': 'user-123',
            'challengerName': 'John Doe',
            'matchType': '5v5',
            'location': 'Dhumbarahi Futsal',
            'proposedDateTime': '2024-01-15T18:00:00.000Z',
            'expiresAt': '2024-01-16T18:00:00.000Z',
            'isResultDisputed': false,
            'responseCount': 0,
            'created': '2024-01-15T10:00:00.000Z',
          }
        ],
        'total': 1,
        'page': 1,
        'pageSize': 20,
        'totalPages': 1,
      };

      final dto = PagedChallengesResponseDto.fromJson(json);

      expect(dto.items?.length, 1);
      expect(dto.total, 1);
      expect(dto.page, 1);
      expect(dto.pageSize, 20);
      expect(dto.totalPages, 1);
      expect(dto.items?.first.id, 'challenge-123');
    });
  });

  group('ChallengeStatsResponseDto', () {
    test('should create DTO from JSON', () {
      final json = {
        'totalChallenges': 10,
        'openChallenges': 5,
        'acceptedChallenges': 3,
        'completedChallenges': 2,
        'wonChallenges': 1,
        'lostChallenges': 1,
        'winRate': 0.5,
        'disputedChallenges': 0,
      };

      final dto = ChallengeStatsResponseDto.fromJson(json);

      expect(dto.totalChallenges, 10);
      expect(dto.openChallenges, 5);
      expect(dto.acceptedChallenges, 3);
      expect(dto.completedChallenges, 2);
      expect(dto.wonChallenges, 1);
      expect(dto.lostChallenges, 1);
      expect(dto.winRate, 0.5);
      expect(dto.disputedChallenges, 0);
    });
  });

  group('MatchSuggestionResponseDto', () {
    test('should create DTO from JSON', () {
      final json = {
        'id': 'suggestion-123',
        'type': 'challenge',
        'matchType': '5v5',
        'location': 'Dhumbarahi Futsal',
        'proposedDateTime': '2024-01-15T18:00:00.000Z',
        'opponentName': 'Jane Smith',
        'teamName': 'Real Madrid',
        'wagerAmount': 500.0,
        'compatibilityScore': 85,
        'description': 'Great match opportunity',
      };

      final dto = MatchSuggestionResponseDto.fromJson(json);

      expect(dto.id, 'suggestion-123');
      expect(dto.type, 'challenge');
      expect(dto.matchType, '5v5');
      expect(dto.location, 'Dhumbarahi Futsal');
      expect(dto.opponentName, 'Jane Smith');
      expect(dto.teamName, 'Real Madrid');
      expect(dto.wagerAmount, 500.0);
      expect(dto.compatibilityScore, 85);
      expect(dto.description, 'Great match opportunity');
    });
  });

  group('RespondToChallengeRequestDto', () {
    test('should convert to JSON correctly', () {
      final dto = RespondToChallengeRequestDto(
        challengeId: 'challenge-123',
        responseType: 'accepted',
        preferredDateTime: DateTime(2024, 1, 15, 18, 0),
        message: 'I accept the challenge!',
        responderTeamId: 'team-123',
      );

      final json = dto.toJson();

      expect(json['challengeId'], 'challenge-123');
      expect(json['responseType'], 'accepted');
      expect(json['preferredDateTime'], '2024-01-15T18:00:00.000');
      expect(json['message'], 'I accept the challenge!');
      expect(json['responderTeamId'], 'team-123');
    });
  });

  group('SubmitMatchResultRequestDto', () {
    test('should convert to JSON correctly', () {
      final dto = SubmitMatchResultRequestDto(
        challengeId: 'challenge-123',
        winnerId: 'user-123',
        winnerTeamId: 'team-123',
        matchResult: '3-2',
        comments: 'Great game!',
      );

      final json = dto.toJson();

      expect(json['challengeId'], 'challenge-123');
      expect(json['winnerId'], 'user-123');
      expect(json['winnerTeamId'], 'team-123');
      expect(json['matchResult'], '3-2');
      expect(json['comments'], 'Great game!');
    });
  });

  group('DisputeMatchResultRequestDto', () {
    test('should convert to JSON correctly', () {
      final dto = DisputeMatchResultRequestDto(
        challengeId: 'challenge-123',
        disputeReason: 'Incorrect score reported',
        evidence: 'Video evidence available',
        comments: 'Please review the match footage',
      );

      final json = dto.toJson();

      expect(json['challengeId'], 'challenge-123');
      expect(json['disputeReason'], 'Incorrect score reported');
      expect(json['evidence'], 'Video evidence available');
      expect(json['comments'], 'Please review the match footage');
    });
  });
}
