import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/challenges/presentation/screens/challenges_screen.dart';

void main() {
  group('AcceptChallengeBottomSheet Tests', () {
    Widget createTestWidget() {
      return MaterialApp(
        home: Builder(
          builder:
              (context) => Scaffold(
                body: Center(
                  child: ElevatedButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder:
                            (context) => const _AcceptChallengeBottomSheet(
                              teamName: 'FC Barcelona',
                              matchType: '5v5',
                              location: 'Dhumbarahi',
                              timePref: 'Tonight 7-9 PM',
                              wager: 'Losers pay',
                              streakCount: 3,
                              eloRating: 1500,
                            ),
                      );
                    },
                    child: const Text('Show Bottomsheet'),
                  ),
                ),
              ),
        ),
      );
    }

    testWidgets('should display bottomsheet with correct content', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Tap to show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Check if bottomsheet is displayed
      expect(find.byType(DraggableScrollableSheet), findsOneWidget);

      // Check header content
      expect(find.text('Accept Challenge'), findsOneWidget);
      expect(find.text('Review details and confirm'), findsOneWidget);

      // Check team information
      expect(find.text('FC Barcelona'), findsOneWidget);
      expect(find.text('Looking for Opponent'), findsOneWidget);

      // Check challenge details
      expect(find.text('Match Type'), findsOneWidget);
      expect(find.text('5v5'), findsOneWidget);
      expect(find.text('Location'), findsOneWidget);
      expect(find.text('Dhumbarahi'), findsOneWidget);
      expect(find.text('Time'), findsOneWidget);
      expect(find.text('Tonight 7-9 PM'), findsOneWidget);
      expect(find.text('Wager'), findsOneWidget);
      expect(find.text('Losers pay'), findsOneWidget);

      // Check badges
      expect(find.text('Streak x3'), findsOneWidget);
      expect(find.text('1500 Elo'), findsOneWidget);

      // Check action buttons
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Accept Challenge'), findsOneWidget);

      // Check terms and warnings
      expect(
        find.textContaining(
          'By accepting this challenge, you agree to the match terms',
        ),
        findsOneWidget,
      );

      // Check optional message section
      expect(find.text('Message (Optional)'), findsOneWidget);
      expect(
        find.widgetWithText(
          TextField,
          'Add a message to the challenger (optional)...',
        ),
        findsOneWidget,
      );
    });

    testWidgets('should close bottomsheet when Cancel is pressed', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Verify bottomsheet is open
      expect(find.byType(DraggableScrollableSheet), findsOneWidget);

      // Tap Cancel button
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Verify bottomsheet is closed
      expect(find.byType(DraggableScrollableSheet), findsNothing);
    });

    testWidgets('should handle message input correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Find message input field
      final messageField = find.widgetWithText(
        TextField,
        'Add a message to the challenger (optional)...',
      );
      expect(messageField, findsOneWidget);

      // Enter text in message field
      await tester.enterText(messageField, 'Looking forward to the match!');
      await tester.pumpAndSettle();

      // Verify text was entered
      expect(find.text('Looking forward to the match!'), findsOneWidget);
    });

    testWidgets('should show loading state when accepting challenge', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Tap Accept Challenge button
      await tester.tap(find.text('Accept Challenge'));
      await tester.pump(); // Don't settle, we want to see loading state

      // Check for loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Cancel button should be disabled during loading
      final cancelButton = tester.widget<OutlinedButton>(
        find.widgetWithText(OutlinedButton, 'Cancel'),
      );
      expect(cancelButton.onPressed, isNull);
    });

    testWidgets('should show success message after accepting challenge', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Tap Accept Challenge button
      await tester.tap(find.text('Accept Challenge'));
      await tester.pumpAndSettle();

      // Wait for the async operation to complete
      await tester.pump(const Duration(seconds: 3));

      // Check for success snackbar
      expect(find.text('Challenge Accepted!'), findsOneWidget);
      expect(
        find.text('You will be notified with match confirmation details.'),
        findsOneWidget,
      );

      // Bottomsheet should be closed
      expect(find.byType(DraggableScrollableSheet), findsNothing);
    });

    testWidgets('should have correct grid layout for challenge details', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Check if GridView is present
      expect(find.byType(GridView), findsOneWidget);

      // Check if all detail items are present
      final detailItems = ['Match Type', 'Location', 'Time', 'Wager'];

      for (final item in detailItems) {
        expect(find.text(item), findsOneWidget);
      }
    });

    testWidgets('should display badges with correct styling', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Check for fire department icon (streak badge)
      expect(find.byIcon(Icons.local_fire_department), findsAtLeastNWidgets(1));

      // Check for shield icon (ELO badge)
      expect(find.byIcon(Icons.shield), findsAtLeastNWidgets(1));

      // Check badge text
      expect(find.text('Streak x3'), findsOneWidget);
      expect(find.text('1500 Elo'), findsOneWidget);
    });

    testWidgets('should handle keyboard appearance correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Find and focus on message input
      final messageField = find.widgetWithText(
        TextField,
        'Add a message to the challenger (optional)...',
      );

      await tester.tap(messageField);
      await tester.pumpAndSettle();

      // The bottomsheet should still be fully visible and scrollable
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      expect(find.byType(DraggableScrollableSheet), findsOneWidget);
    });

    testWidgets('should display warning section correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Check warning icon
      expect(find.byIcon(Icons.warning_amber_rounded), findsOneWidget);

      // Check warning text
      expect(
        find.textContaining(
          'By accepting this challenge, you agree to the match terms',
        ),
        findsOneWidget,
      );

      // Check that warning container has orange styling
      final warningContainer = tester.widget<Container>(
        find.ancestor(
          of: find.byIcon(Icons.warning_amber_rounded),
          matching: find.byType(Container),
        ),
      );

      expect(warningContainer.decoration, isA<BoxDecoration>());
    });

    testWidgets('should have proper button styling and behavior', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Check Cancel button styling
      final cancelButton = tester.widget<OutlinedButton>(
        find.widgetWithText(OutlinedButton, 'Cancel'),
      );
      expect(cancelButton.onPressed, isNotNull);

      // Check Accept button styling
      final acceptButton = tester.widget<ElevatedButton>(
        find.widgetWithText(ElevatedButton, 'Accept Challenge'),
      );
      expect(acceptButton.onPressed, isNotNull);
      expect(acceptButton.style?.backgroundColor?.resolve({}), isNotNull);
    });

    testWidgets('should handle drag scrolling correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Find the DraggableScrollableSheet
      final draggableSheet = find.byType(DraggableScrollableSheet);
      expect(draggableSheet, findsOneWidget);

      // Test dragging behavior (simplified test)
      await tester.drag(draggableSheet, const Offset(0, -100));
      await tester.pumpAndSettle();

      // Sheet should still be visible
      expect(find.byType(DraggableScrollableSheet), findsOneWidget);
    });

    testWidgets('should display team avatar correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Check for team avatar
      expect(find.byIcon(Icons.group), findsAtLeastNWidgets(1));

      // Check for challenge action avatar
      expect(find.byIcon(Icons.sports_kabaddi), findsOneWidget);
    });
  });

  group('AcceptChallengeBottomSheet Edge Cases', () {
    testWidgets('should handle very long team names', (
      WidgetTester tester,
    ) async {
      const longTeamName =
          'Very Long Team Name That Might Cause Overflow Issues in UI Testing';

      final widget = MaterialApp(
        home: Builder(
          builder:
              (context) => Scaffold(
                body: Center(
                  child: ElevatedButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder:
                            (context) => const _AcceptChallengeBottomSheet(
                              teamName: longTeamName,
                              matchType: '5v5',
                              location: 'Dhumbarahi',
                              timePref: 'Tonight 7-9 PM',
                              wager: 'Losers pay',
                              streakCount: 3,
                              eloRating: 1500,
                            ),
                      );
                    },
                    child: const Text('Show Bottomsheet'),
                  ),
                ),
              ),
        ),
      );

      await tester.pumpWidget(widget);

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Should display long team name without overflow
      expect(find.text(longTeamName), findsOneWidget);
    });

    testWidgets('should handle very long location names', (
      WidgetTester tester,
    ) async {
      const longLocation = 'Very Long Location Name That Could Cause Issues';

      final widget = MaterialApp(
        home: Builder(
          builder:
              (context) => Scaffold(
                body: Center(
                  child: ElevatedButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder:
                            (context) => const _AcceptChallengeBottomSheet(
                              teamName: 'FC Barcelona',
                              matchType: '5v5',
                              location: longLocation,
                              timePref: 'Tonight 7-9 PM',
                              wager: 'Losers pay',
                              streakCount: 3,
                              eloRating: 1500,
                            ),
                      );
                    },
                    child: const Text('Show Bottomsheet'),
                  ),
                ),
              ),
        ),
      );

      await tester.pumpWidget(widget);

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Should display long location name with ellipsis if needed
      expect(
        find.textContaining(longLocation.substring(0, 20)),
        findsOneWidget,
      );
    });

    testWidgets('should handle zero streak count', (WidgetTester tester) async {
      final widget = MaterialApp(
        home: Builder(
          builder:
              (context) => Scaffold(
                body: Center(
                  child: ElevatedButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder:
                            (context) => const _AcceptChallengeBottomSheet(
                              teamName: 'FC Barcelona',
                              matchType: '5v5',
                              location: 'Dhumbarahi',
                              timePref: 'Tonight 7-9 PM',
                              wager: 'Losers pay',
                              streakCount: 0,
                              eloRating: 1500,
                            ),
                      );
                    },
                    child: const Text('Show Bottomsheet'),
                  ),
                ),
              ),
        ),
      );

      await tester.pumpWidget(widget);

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Should display zero streak
      expect(find.text('Streak x0'), findsOneWidget);
    });

    testWidgets('should handle high ELO ratings', (WidgetTester tester) async {
      final widget = MaterialApp(
        home: Builder(
          builder:
              (context) => Scaffold(
                body: Center(
                  child: ElevatedButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder:
                            (context) => const _AcceptChallengeBottomSheet(
                              teamName: 'FC Barcelona',
                              matchType: '5v5',
                              location: 'Dhumbarahi',
                              timePref: 'Tonight 7-9 PM',
                              wager: 'Losers pay',
                              streakCount: 3,
                              eloRating: 999999,
                            ),
                      );
                    },
                    child: const Text('Show Bottomsheet'),
                  ),
                ),
              ),
        ),
      );

      await tester.pumpWidget(widget);

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Should display high ELO rating
      expect(find.text('999999 Elo'), findsOneWidget);
    });

    testWidgets('should handle multiple rapid taps on Accept button', (
      WidgetTester tester,
    ) async {
      final widget = MaterialApp(
        home: Builder(
          builder:
              (context) => Scaffold(
                body: Center(
                  child: ElevatedButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder:
                            (context) => const _AcceptChallengeBottomSheet(
                              teamName: 'FC Barcelona',
                              matchType: '5v5',
                              location: 'Dhumbarahi',
                              timePref: 'Tonight 7-9 PM',
                              wager: 'Losers pay',
                              streakCount: 3,
                              eloRating: 1500,
                            ),
                      );
                    },
                    child: const Text('Show Bottomsheet'),
                  ),
                ),
              ),
        ),
      );

      await tester.pumpWidget(widget);

      // Show bottomsheet
      await tester.tap(find.text('Show Bottomsheet'));
      await tester.pumpAndSettle();

      // Tap Accept Challenge button multiple times rapidly
      await tester.tap(find.text('Accept Challenge'));
      await tester.tap(find.text('Accept Challenge'));
      await tester.tap(find.text('Accept Challenge'));
      await tester.pump();

      // Should only show one loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
  });
}
