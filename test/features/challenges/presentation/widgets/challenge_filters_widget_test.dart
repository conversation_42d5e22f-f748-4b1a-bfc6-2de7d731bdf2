import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nextsportz_v2/features/challenges/domain/entities/challenge.dart';
import 'package:nextsportz_v2/features/challenges/presentation/widgets/challenge_filters_widget.dart';
import 'package:nextsportz_v2/features/challenges/presentation/logic/challenge_state.dart';
import 'package:nextsportz_v2/features/challenges/challenges_providers.dart';

void main() {
  group('ChallengeFiltersWidget Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    Widget createTestWidget({Widget? child}) {
      return ProviderScope(
        parent: container,
        child: MaterialApp(
          home: Scaffold(body: child ?? const ChallengeFiltersWidget()),
        ),
      );
    }

    testWidgets('should display filter widget with initial state', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Check if the main filter sections are present
      expect(find.text('Filters'), findsOneWidget);
      expect(find.text('Match Type'), findsOneWidget);
      expect(find.text('Location'), findsOneWidget);
      expect(find.text('Wager Range'), findsOneWidget);
      expect(find.text('Apply Filters'), findsOneWidget);
      expect(find.text('Clear All'), findsOneWidget);
    });

    testWidgets('should show match type chips', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Check for match type chips
      expect(find.text('All Types'), findsOneWidget);
      expect(find.text('5v5'), findsOneWidget);
      expect(find.text('7v7'), findsOneWidget);
      expect(find.text('11v11'), findsOneWidget);
    });

    testWidgets('should select and deselect match type filters', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Initially no match type should be selected
      final initialState = container.read(challengeFiltersProvider);
      expect(initialState.matchType, isNull);

      // Tap on 5v5 match type
      await tester.tap(find.text('5v5'));
      await tester.pumpAndSettle();

      // Check if 5v5 is selected
      final selectedState = container.read(challengeFiltersProvider);
      expect(selectedState.matchType, ChallengeType.fiveVsFive);

      // Tap again to deselect
      await tester.tap(find.text('5v5'));
      await tester.pumpAndSettle();

      // Check if 5v5 is deselected
      final deselectedState = container.read(challengeFiltersProvider);
      expect(deselectedState.matchType, isNull);
    });

    testWidgets('should update wager range using slider', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Find the range slider
      final rangeSlider = find.byType(RangeSlider);
      expect(rangeSlider, findsOneWidget);

      // Initial state should have default range
      final initialState = container.read(challengeFiltersProvider);
      expect(initialState.minWager, isNull);
      expect(initialState.maxWager, isNull);

      // Test range slider interaction (Note: This is a simplified test)
      // In reality, you'd need to simulate drag gestures for proper testing
      await tester.tap(rangeSlider);
      await tester.pumpAndSettle();
    });

    testWidgets('should clear all filters when Clear All is pressed', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Set some filters first
      final notifier = container.read(challengeFiltersProvider.notifier);
      notifier.updateMatchType(ChallengeType.fiveVsFive);
      notifier.updateLocation('Test Location');
      notifier.updateWagerRange(minWager: 100, maxWager: 1000);

      await tester.pumpAndSettle();

      // Verify filters are set
      var currentState = container.read(challengeFiltersProvider);
      expect(currentState.matchType, ChallengeType.fiveVsFive);
      expect(currentState.location, 'Test Location');
      expect(currentState.minWager, 100);

      // Tap Clear All button
      await tester.tap(find.text('Clear All'));
      await tester.pumpAndSettle();

      // Verify all filters are cleared (but location defaults to 'Nearby')
      currentState = container.read(challengeFiltersProvider);
      expect(currentState.matchType, isNull);
      expect(currentState.location, 'Nearby');
      expect(currentState.minWager, isNull);
      expect(currentState.maxWager, isNull);
      expect(currentState.tags, isEmpty);
    });

    testWidgets('should apply filters and close modal', (
      WidgetTester tester,
    ) async {
      bool onFiltersAppliedCalled = false;

      await tester.pumpWidget(
        createTestWidget(
          child: ChallengeFiltersWidget(
            onFiltersApplied: () {
              onFiltersAppliedCalled = true;
            },
          ),
        ),
      );

      // Set a filter
      await tester.tap(find.text('5v5'));
      await tester.pumpAndSettle();

      // Tap Apply Filters
      await tester.tap(find.text('Apply Filters'));
      await tester.pumpAndSettle();

      // Verify callback was called
      expect(onFiltersAppliedCalled, isTrue);

      // Verify filter was applied
      final appliedState = container.read(challengeFiltersProvider);
      expect(appliedState.matchType, ChallengeType.fiveVsFive);
    });

    testWidgets('should update location filter', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find location text field
      final locationField = find.widgetWithText(
        TextField,
        'Search location...',
      );
      expect(locationField, findsOneWidget);

      // Enter location text
      await tester.enterText(locationField, 'Kathmandu');
      await tester.pumpAndSettle();

      // Apply filters
      await tester.tap(find.text('Apply Filters'));
      await tester.pumpAndSettle();

      // Verify location was set
      final state = container.read(challengeFiltersProvider);
      expect(state.location, 'Kathmandu');
    });

    testWidgets('should handle tag selection', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find and tap a tag
      await tester.tap(find.text('competitive'));
      await tester.pumpAndSettle();

      // Apply filters
      await tester.tap(find.text('Apply Filters'));
      await tester.pumpAndSettle();

      // Verify tag was added
      final state = container.read(challengeFiltersProvider);
      expect(state.tags, contains('competitive'));

      // Tap the same tag again to deselect
      await tester.tap(find.text('competitive'));
      await tester.pumpAndSettle();

      // Apply filters again
      await tester.tap(find.text('Apply Filters'));
      await tester.pumpAndSettle();

      // Verify tag was removed
      final newState = container.read(challengeFiltersProvider);
      expect(newState.tags, isNot(contains('competitive')));
    });
  });

  group('QuickFiltersWidget Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    Widget createTestWidget({VoidCallback? onFilterTap}) {
      return ProviderScope(
        parent: container,
        child: MaterialApp(
          home: Scaffold(body: QuickFiltersWidget(onFilterTap: onFilterTap)),
        ),
      );
    }

    testWidgets('should display quick filter chips', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Check if quick filter chips are present
      expect(find.text('Filters'), findsOneWidget);
      expect(find.text('5v5'), findsOneWidget);
      expect(find.text('Nearby'), findsOneWidget);
      expect(find.text('High Stakes'), findsOneWidget);
      expect(find.text('Tonight'), findsOneWidget);
    });

    testWidgets('should toggle 5v5 filter correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Initially 5v5 should not be active
      final initialState = container.read(challengeFiltersProvider);
      expect(initialState.matchType, isNull);

      // Tap 5v5 filter
      await tester.tap(find.text('5v5'));
      await tester.pumpAndSettle();

      // Verify 5v5 is now active
      final activeState = container.read(challengeFiltersProvider);
      expect(activeState.matchType, ChallengeType.fiveVsFive);

      // Tap 5v5 again to deselect
      await tester.tap(find.text('5v5'));
      await tester.pumpAndSettle();

      // Verify 5v5 is deselected
      final deselectedState = container.read(challengeFiltersProvider);
      expect(deselectedState.matchType, isNull);
    });

    testWidgets('should toggle Nearby filter correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Initially location should be 'Nearby' (default)
      final initialState = container.read(challengeFiltersProvider);
      expect(initialState.location, 'Nearby');

      // Tap Nearby filter to deselect
      await tester.tap(find.text('Nearby'));
      await tester.pumpAndSettle();

      // Verify location is now cleared
      final deselectedState = container.read(challengeFiltersProvider);
      expect(deselectedState.location, isNull);

      // Tap Nearby again to select
      await tester.tap(find.text('Nearby'));
      await tester.pumpAndSettle();

      // Verify location is set back to 'Nearby'
      final reselectedState = container.read(challengeFiltersProvider);
      expect(reselectedState.location, 'Nearby');
    });

    testWidgets('should toggle High Stakes filter correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Initially high stakes should not be active
      final initialState = container.read(challengeFiltersProvider);
      expect(initialState.minWager, isNull);

      // Tap High Stakes filter
      await tester.tap(find.text('High Stakes'));
      await tester.pumpAndSettle();

      // Verify high stakes is now active
      final activeState = container.read(challengeFiltersProvider);
      expect(activeState.minWager, greaterThanOrEqualTo(1000));

      // Tap High Stakes again to deselect
      await tester.tap(find.text('High Stakes'));
      await tester.pumpAndSettle();

      // Verify high stakes is deselected
      final deselectedState = container.read(challengeFiltersProvider);
      expect(deselectedState.minWager, isNull);
    });

    testWidgets('should toggle Tonight filter correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Initially evening tag should not be present
      final initialState = container.read(challengeFiltersProvider);
      expect(initialState.tags, isNot(contains('evening')));

      // Tap Tonight filter
      await tester.tap(find.text('Tonight'));
      await tester.pumpAndSettle();

      // Verify evening tag is now present
      final activeState = container.read(challengeFiltersProvider);
      expect(activeState.tags, contains('evening'));

      // Tap Tonight again to deselect
      await tester.tap(find.text('Tonight'));
      await tester.pumpAndSettle();

      // Verify evening tag is removed
      final deselectedState = container.read(challengeFiltersProvider);
      expect(deselectedState.tags, isNot(contains('evening')));
    });

    testWidgets('should show active filter count', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Initially should show just "Filters"
      expect(find.text('Filters'), findsOneWidget);

      // Apply some filters
      final notifier = container.read(challengeFiltersProvider.notifier);
      notifier.updateMatchType(ChallengeType.fiveVsFive);
      notifier.updateLocation('Test Location');

      await tester.pumpAndSettle();

      // Should now show filter count
      expect(find.textContaining('Filters ('), findsOneWidget);
    });

    testWidgets('should call onFilterTap when main filter button is tapped', (
      WidgetTester tester,
    ) async {
      bool onFilterTapCalled = false;

      await tester.pumpWidget(
        createTestWidget(
          onFilterTap: () {
            onFilterTapCalled = true;
          },
        ),
      );

      // Tap the main Filters button
      await tester.tap(find.text('Filters'));
      await tester.pumpAndSettle();

      // Verify callback was called
      expect(onFilterTapCalled, isTrue);
    });
  });

  group('ChallengeFilters State Tests', () {
    test('should create filters with default values', () {
      const filters = ChallengeFilters();

      expect(filters.matchType, isNull);
      expect(filters.location, isNull);
      expect(filters.minWager, isNull);
      expect(filters.maxWager, isNull);
      expect(filters.latitude, isNull);
      expect(filters.longitude, isNull);
      expect(filters.radiusKm, isNull);
      expect(filters.tags, isEmpty);
      expect(filters.hasFilters, isFalse);
    });

    test('should create filters with nearby default when specified', () {
      const filters = ChallengeFilters(location: 'Nearby');

      expect(filters.matchType, isNull);
      expect(filters.location, 'Nearby');
      expect(filters.minWager, isNull);
      expect(filters.maxWager, isNull);
      expect(filters.latitude, isNull);
      expect(filters.longitude, isNull);
      expect(filters.radiusKm, isNull);
      expect(filters.tags, isEmpty);
      expect(
        filters.hasFilters,
        isTrue,
      ); // hasFilters should be true when location is set
    });

    test('should update filters using copyWith', () {
      const initialFilters = ChallengeFilters();

      final updatedFilters = initialFilters.copyWith(
        matchType: ChallengeType.fiveVsFive,
        location: 'Kathmandu',
        minWager: 100.0,
        tags: ['competitive'],
      );

      expect(updatedFilters.matchType, ChallengeType.fiveVsFive);
      expect(updatedFilters.location, 'Kathmandu');
      expect(updatedFilters.minWager, 100.0);
      expect(updatedFilters.tags, ['competitive']);
      expect(updatedFilters.hasFilters, isTrue);
    });

    test('should clear individual filters using clear flags', () {
      final filters = const ChallengeFilters().copyWith(
        matchType: ChallengeType.fiveVsFive,
        location: 'Kathmandu',
        minWager: 100.0,
      );

      final clearedFilters = filters.copyWith(
        clearMatchType: true,
        clearLocation: true,
        clearMinWager: true,
      );

      expect(clearedFilters.matchType, isNull);
      expect(clearedFilters.location, isNull);
      expect(clearedFilters.minWager, isNull);
      expect(clearedFilters.hasFilters, isFalse);
    });

    test('should detect when filters are applied', () {
      const emptyFilters = ChallengeFilters();
      expect(emptyFilters.hasFilters, isFalse);

      final filtersWithMatchType = emptyFilters.copyWith(
        matchType: ChallengeType.fiveVsFive,
      );
      expect(filtersWithMatchType.hasFilters, isTrue);

      final filtersWithLocation = emptyFilters.copyWith(location: 'Kathmandu');
      expect(filtersWithLocation.hasFilters, isTrue);

      final filtersWithWager = emptyFilters.copyWith(minWager: 100.0);
      expect(filtersWithWager.hasFilters, isTrue);

      final filtersWithTags = emptyFilters.copyWith(tags: ['competitive']);
      expect(filtersWithTags.hasFilters, isTrue);
    });

    test('should clear all filters', () {
      final filters = const ChallengeFilters().copyWith(
        matchType: ChallengeType.fiveVsFive,
        location: 'Kathmandu',
        minWager: 100.0,
        maxWager: 1000.0,
        tags: ['competitive', 'evening'],
      );

      expect(filters.hasFilters, isTrue);

      final clearedFilters = filters.clear();
      expect(clearedFilters.matchType, isNull);
      expect(
        clearedFilters.location,
        'Nearby',
      ); // clear() now returns default state with 'Nearby'
      expect(clearedFilters.minWager, isNull);
      expect(clearedFilters.maxWager, isNull);
      expect(clearedFilters.tags, isEmpty);
      expect(
        clearedFilters.hasFilters,
        isTrue,
      ); // hasFilters is true because location is set
    });
  });

  group('ChallengeFiltersNotifier Tests', () {
    late ChallengeFiltersNotifier notifier;

    setUp(() {
      notifier = ChallengeFiltersNotifier();
    });

    test('should start with default nearby filter', () {
      expect(
        notifier.state.hasFilters,
        isTrue,
      ); // hasFilters is true because of default location
      expect(notifier.state.matchType, isNull);
      expect(
        notifier.state.location,
        'Nearby',
      ); // default location is now 'Nearby'
    });

    test('should update match type', () {
      notifier.updateMatchType(ChallengeType.fiveVsFive);
      expect(notifier.state.matchType, ChallengeType.fiveVsFive);

      notifier.updateMatchType(null);
      expect(notifier.state.matchType, isNull);
    });

    test('should update location', () {
      notifier.updateLocation('Kathmandu');
      expect(notifier.state.location, 'Kathmandu');

      notifier.updateLocation(null);
      expect(notifier.state.location, isNull);
    });

    test('should update wager range', () {
      notifier.updateWagerRange(minWager: 100.0, maxWager: 1000.0);
      expect(notifier.state.minWager, 100.0);
      expect(notifier.state.maxWager, 1000.0);

      notifier.updateWagerRange(minWager: null, maxWager: null);
      expect(notifier.state.minWager, isNull);
      expect(notifier.state.maxWager, isNull);
    });

    test('should update tags', () {
      notifier.updateTags(['competitive']);
      expect(notifier.state.tags, ['competitive']);

      notifier.updateTags(['competitive', 'evening']);
      expect(notifier.state.tags, ['competitive', 'evening']);

      notifier.updateTags([]);
      expect(notifier.state.tags, isEmpty);
    });

    test('should clear all filters', () {
      // Set some filters first
      notifier.updateMatchType(ChallengeType.fiveVsFive);
      notifier.updateLocation('Kathmandu');
      notifier.updateWagerRange(minWager: 100.0);
      notifier.updateTags(['competitive']);

      expect(notifier.state.hasFilters, isTrue);

      // Clear all filters
      notifier.clearFilters();

      expect(
        notifier.state.hasFilters,
        isTrue,
      ); // hasFilters is true because of default location
      expect(notifier.state.matchType, isNull);
      expect(
        notifier.state.location,
        'Nearby',
      ); // clearFilters() now resets to default state with 'Nearby'
      expect(notifier.state.minWager, isNull);
      expect(notifier.state.tags, isEmpty);
    });
  });
}
