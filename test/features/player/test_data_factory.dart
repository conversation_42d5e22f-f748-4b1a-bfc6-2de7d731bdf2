import 'package:nextsportz_v2/core/models/paginated_response.dart';
import 'package:nextsportz_v2/features/player/domain/entities/player_search_item.dart';
import 'package:nextsportz_v2/features/player/data/dto/player_search_item_dto.dart';

/// Factory class for creating test data for player-related tests
class PlayerTestDataFactory {
  static const List<String> _firstNames = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  ];

  static const List<String> _lastNames = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  ];

  static const List<String> _positions = [
    'Goalkeeper', 'Defender', 'Midfielder', 'Forward', 'Striker',
    'Center Back', 'Full Back', '<PERSON> Back', 'Defensive Midfielder',
    '<PERSON> Midfielder', 'Attacking Midfielder', 'Winger', 'Left Back', 'Right Back',
  ];

  static const List<String> _preferredFeet = ['Left', 'Right', 'Both'];

  static const List<String> _descriptions = [
    'Experienced player with great leadership skills',
    'Young talent with excellent technical abilities',
    'Versatile player who can play multiple positions',
    'Strong defensive player with good aerial ability',
    'Creative midfielder with excellent passing range',
    'Fast winger with good crossing ability',
    'Clinical finisher with great positioning',
    'Reliable goalkeeper with excellent reflexes',
    'Hardworking player with great stamina',
    'Skillful player with good dribbling ability',
  ];

  /// Creates a single PlayerSearchItem entity for testing
  static PlayerSearchItem createPlayerSearchItem({
    String? id,
    String? fullName,
    int? age,
    int? heightCm,
    int? weightKg,
    String? email,
    String? phone,
    String? description,
    String? photoUrl,
    bool? scoutingEnabled,
    Map<String, dynamic>? gameInfo,
  }) {
    final playerId = id ?? 'player_${DateTime.now().millisecondsSinceEpoch}';
    final firstName = _firstNames[playerId.hashCode % _firstNames.length];
    final lastName = _lastNames[playerId.hashCode % _lastNames.length];
    final playerFullName = fullName ?? '$firstName $lastName';
    final playerAge = age ?? (18 + (playerId.hashCode % 20)); // 18-38 years old
    final playerHeight = heightCm ?? (160 + (playerId.hashCode % 30)); // 160-190 cm
    final playerWeight = weightKg ?? (60 + (playerId.hashCode % 40)); // 60-100 kg
    final playerEmail = email ?? '${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com';
    final playerPhone = phone ?? '+1${(playerId.hashCode % 900 + 100)}${(playerId.hashCode % 900 + 100)}${(playerId.hashCode % 9000 + 1000)}';
    final playerDescription = description ?? _descriptions[playerId.hashCode % _descriptions.length];
    final playerPhotoUrl = photoUrl ?? (playerId.hashCode % 2 == 0 ? 'https://example.com/avatar_$playerId.jpg' : null);
    final playerScoutingEnabled = scoutingEnabled ?? (playerId.hashCode % 2 == 0);
    final playerGameInfo = gameInfo ?? {
      'position': _positions[playerId.hashCode % _positions.length],
      'preferredFoot': _preferredFeet[playerId.hashCode % _preferredFeet.length],
      'experience': '${playerId.hashCode % 10 + 1} years',
    };

    return PlayerSearchItem(
      id: playerId,
      fullName: playerFullName,
      age: playerAge,
      heightCm: playerHeight,
      weightKg: playerWeight,
      email: playerEmail,
      phone: playerPhone,
      description: playerDescription,
      photoUrl: playerPhotoUrl,
      scoutingEnabled: playerScoutingEnabled,
      gameInfo: playerGameInfo,
    );
  }

  /// Creates a single PlayerSearchItemDto for testing
  static PlayerSearchItemDto createPlayerSearchItemDto({
    String? id,
    String? fullName,
    int? age,
    int? heightCm,
    int? weightKg,
    String? email,
    String? phone,
    String? description,
    String? photoUrl,
    bool? scoutingEnabled,
    Map<String, dynamic>? gameInfo,
  }) {
    final entity = createPlayerSearchItem(
      id: id,
      fullName: fullName,
      age: age,
      heightCm: heightCm,
      weightKg: weightKg,
      email: email,
      phone: phone,
      description: description,
      photoUrl: photoUrl,
      scoutingEnabled: scoutingEnabled,
      gameInfo: gameInfo,
    );

    return PlayerSearchItemDto.fromEntity(entity);
  }

  /// Creates a list of PlayerSearchItem entities for testing
  static List<PlayerSearchItem> createPlayerSearchItemList({
    int count = 10,
    String? namePattern,
    String? emailPattern,
    String? phonePattern,
  }) {
    return List.generate(count, (index) {
      final baseId = 'player_${index + 1}';
      String? fullName;
      String? email;
      String? phone;

      if (namePattern != null) {
        fullName = '$namePattern Player $index';
      }

      if (emailPattern != null) {
        email = '${namePattern?.toLowerCase() ?? 'player'}$index$emailPattern';
      }

      if (phonePattern != null) {
        phone = '$phonePattern${index.toString().padLeft(3, '0')}';
      }

      return createPlayerSearchItem(
        id: baseId,
        fullName: fullName,
        email: email,
        phone: phone,
      );
    });
  }

  /// Creates a list of PlayerSearchItemDto for testing
  static List<PlayerSearchItemDto> createPlayerSearchItemDtoList({
    int count = 10,
    String? namePattern,
    String? emailPattern,
    String? phonePattern,
  }) {
    final entities = createPlayerSearchItemList(
      count: count,
      namePattern: namePattern,
      emailPattern: emailPattern,
      phonePattern: phonePattern,
    );

    return entities.map((entity) => PlayerSearchItemDto.fromEntity(entity)).toList();
  }

  /// Creates a paginated response of PlayerSearchItem entities for testing
  static PaginatedResponse<PlayerSearchItem> createPaginatedPlayerSearchResponse({
    int itemCount = 10,
    int page = 1,
    int pageSize = 20,
    int? totalItems,
    String? namePattern,
    String? emailPattern,
    String? phonePattern,
    String message = 'Success',
  }) {
    final items = createPlayerSearchItemList(
      count: itemCount,
      namePattern: namePattern,
      emailPattern: emailPattern,
      phonePattern: phonePattern,
    );

    final total = totalItems ?? itemCount;
    final totalPages = (total / pageSize).ceil();

    return PaginatedResponse<PlayerSearchItem>(
      message: message,
      data: PaginatedData<PlayerSearchItem>(
        items: items,
        total: total,
        page: page,
        pageSize: pageSize,
        totalPages: totalPages,
      ),
    );
  }

  /// Creates a paginated response of PlayerSearchItemDto for testing
  static PaginatedResponse<PlayerSearchItemDto> createPaginatedPlayerSearchDtoResponse({
    int itemCount = 10,
    int page = 1,
    int pageSize = 20,
    int? totalItems,
    String? namePattern,
    String? emailPattern,
    String? phonePattern,
    String message = 'Success',
  }) {
    final items = createPlayerSearchItemDtoList(
      count: itemCount,
      namePattern: namePattern,
      emailPattern: emailPattern,
      phonePattern: phonePattern,
    );

    final total = totalItems ?? itemCount;
    final totalPages = (total / pageSize).ceil();

    return PaginatedResponse<PlayerSearchItemDto>(
      message: message,
      data: PaginatedData<PlayerSearchItemDto>(
        items: items,
        total: total,
        page: page,
        pageSize: pageSize,
        totalPages: totalPages,
      ),
    );
  }

  /// Creates an empty paginated response for testing
  static PaginatedResponse<PlayerSearchItem> createEmptyPaginatedResponse({
    int page = 1,
    int pageSize = 20,
    String message = 'Success',
  }) {
    return PaginatedResponse<PlayerSearchItem>(
      message: message,
      data: PaginatedData<PlayerSearchItem>(
        items: const [],
        total: 0,
        page: page,
        pageSize: pageSize,
        totalPages: 0,
      ),
    );
  }

  /// Creates an empty paginated DTO response for testing
  static PaginatedResponse<PlayerSearchItemDto> createEmptyPaginatedDtoResponse({
    int page = 1,
    int pageSize = 20,
    String message = 'Success',
  }) {
    return PaginatedResponse<PlayerSearchItemDto>(
      message: message,
      data: PaginatedData<PlayerSearchItemDto>(
        items: const [],
        total: 0,
        page: page,
        pageSize: pageSize,
        totalPages: 0,
      ),
    );
  }

  /// Creates players with specific search criteria for testing search functionality
  static List<PlayerSearchItem> createSearchablePlayerList() {
    return [
      createPlayerSearchItem(
        id: 'john_doe_1',
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        description: 'Experienced midfielder',
      ),
      createPlayerSearchItem(
        id: 'john_smith_2',
        fullName: 'John Smith',
        email: '<EMAIL>',
        phone: '+1234567891',
        description: 'Skilled forward',
      ),
      createPlayerSearchItem(
        id: 'jane_doe_3',
        fullName: 'Jane Doe',
        email: '<EMAIL>',
        phone: '+1234567892',
        description: 'Reliable goalkeeper',
      ),
      createPlayerSearchItem(
        id: 'mike_johnson_4',
        fullName: 'Mike Johnson',
        email: '<EMAIL>',
        phone: '+1987654321',
        description: 'Strong defender',
      ),
      createPlayerSearchItem(
        id: 'sarah_wilson_5',
        fullName: 'Sarah Wilson',
        email: '<EMAIL>',
        phone: '+1555123456',
        description: 'Creative midfielder',
      ),
    ];
  }

  /// Creates a JSON representation of a player for API testing
  static Map<String, dynamic> createPlayerJson({
    String? id,
    String? fullName,
    int? age,
    int? heightCm,
    int? weightKg,
    String? email,
    String? phone,
    String? description,
    String? photoUrl,
    bool? scoutingEnabled,
    Map<String, dynamic>? gameInfo,
  }) {
    final dto = createPlayerSearchItemDto(
      id: id,
      fullName: fullName,
      age: age,
      heightCm: heightCm,
      weightKg: weightKg,
      email: email,
      phone: phone,
      description: description,
      photoUrl: photoUrl,
      scoutingEnabled: scoutingEnabled,
      gameInfo: gameInfo,
    );

    return dto.toJson();
  }

  /// Creates a paginated JSON response for API testing
  static Map<String, dynamic> createPaginatedPlayerJsonResponse({
    int itemCount = 10,
    int page = 1,
    int pageSize = 20,
    int? totalItems,
    String? namePattern,
    String message = 'Success',
  }) {
    final items = List.generate(itemCount, (index) {
      return createPlayerJson(
        id: 'player_${index + 1}',
        fullName: namePattern != null ? '$namePattern Player ${index + 1}' : null,
      );
    });

    final total = totalItems ?? itemCount;
    final totalPages = (total / pageSize).ceil();

    return {
      'message': message,
      'data': {
        'items': items,
        'total': total,
        'page': page,
        'pageSize': pageSize,
        'totalPages': totalPages,
      },
    };
  }
}
