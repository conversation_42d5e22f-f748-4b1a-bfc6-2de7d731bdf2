import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';
import 'package:nextsportz_v2/features/player/domain/entities/player_search_item.dart';
import 'package:nextsportz_v2/features/player/domain/repositories/player_repository.dart';
import 'package:nextsportz_v2/features/player/domain/usecases/search_players_usecase.dart';

import 'search_players_usecase_test.mocks.dart';

@GenerateMocks([PlayerRepository])
void main() {
  late MockPlayerRepository mockPlayerRepository;
  late SearchPlayersUseCase searchPlayersUseCase;

  // Provide dummy values for Mockito
  provideDummy<Either<AppError, PaginatedResponse<PlayerSearchItem>>>(
    Right(
      PaginatedResponse<PlayerSearchItem>(
        message: 'Success',
        data: PaginatedData<PlayerSearchItem>(
          items: const [],
          total: 0,
          page: 1,
          pageSize: 20,
          totalPages: 0,
        ),
      ),
    ),
  );

  setUp(() {
    mockPlayerRepository = MockPlayerRepository();
    searchPlayersUseCase = SearchPlayersUseCase(mockPlayerRepository);
  });

  group('SearchPlayersUseCase', () {
    const testQuery = 'john';
    const testPage = 1;
    const testPageSize = 20;

    final testParams = SearchPlayersParams(
      query: testQuery,
      page: testPage,
      pageSize: testPageSize,
    );

    final mockPlayers = [
      const PlayerSearchItem(
        id: 'player_1',
        fullName: 'John Doe',
        age: 25,
        heightCm: 180,
        weightKg: 75,
        email: '<EMAIL>',
        phone: '+1234567890',
        description: 'Experienced midfielder',
        photoUrl: 'https://example.com/john.jpg',
        scoutingEnabled: true,
        gameInfo: {'position': 'Midfielder', 'preferredFoot': 'Right'},
      ),
      const PlayerSearchItem(
        id: 'player_2',
        fullName: 'John Smith',
        age: 28,
        heightCm: 175,
        weightKg: 70,
        email: '<EMAIL>',
        phone: '+1234567891',
        description: 'Skilled forward',
        photoUrl: null,
        scoutingEnabled: false,
        gameInfo: {'position': 'Forward', 'preferredFoot': 'Left'},
      ),
    ];

    final mockPaginatedResponse = PaginatedResponse<PlayerSearchItem>(
      message: 'Success',
      data: PaginatedData<PlayerSearchItem>(
        items: mockPlayers,
        total: 2,
        page: testPage,
        pageSize: testPageSize,
        totalPages: 1,
      ),
    );

    group('Success scenarios', () {
      test('should return paginated players when search is successful', () async {
        // Arrange
        when(mockPlayerRepository.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        )).thenAnswer((_) async => Right(mockPaginatedResponse));

        // Act
        final result = await searchPlayersUseCase(testParams);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: ${error.message}'),
          (response) {
            expect(response.data.items.length, equals(2));
            expect(response.data.items[0].fullName, equals('John Doe'));
            expect(response.data.items[1].fullName, equals('John Smith'));
            expect(response.data.total, equals(2));
            expect(response.data.page, equals(testPage));
            expect(response.data.pageSize, equals(testPageSize));
          },
        );

        verify(mockPlayerRepository.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        )).called(1);
      });

      test('should return empty list when no players match search', () async {
        // Arrange
        final emptyResponse = PaginatedResponse<PlayerSearchItem>(
          message: 'Success',
          data: PaginatedData<PlayerSearchItem>(
            items: const [],
            total: 0,
            page: testPage,
            pageSize: testPageSize,
            totalPages: 0,
          ),
        );

        when(mockPlayerRepository.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        )).thenAnswer((_) async => Right(emptyResponse));

        // Act
        final result = await searchPlayersUseCase(testParams);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: ${error.message}'),
          (response) {
            expect(response.data.items.isEmpty, true);
            expect(response.data.total, equals(0));
          },
        );
      });

      test('should handle search without query parameter', () async {
        // Arrange
        final paramsWithoutQuery = SearchPlayersParams(
          page: testPage,
          pageSize: testPageSize,
        );

        when(mockPlayerRepository.searchPlayers(
          query: null,
          page: testPage,
          pageSize: testPageSize,
        )).thenAnswer((_) async => Right(mockPaginatedResponse));

        // Act
        final result = await searchPlayersUseCase(paramsWithoutQuery);

        // Assert
        expect(result.isRight(), true);
        verify(mockPlayerRepository.searchPlayers(
          query: null,
          page: testPage,
          pageSize: testPageSize,
        )).called(1);
      });
    });

    group('Error scenarios', () {
      test('should return error when repository fails', () async {
        // Arrange
        const errorMessage = 'Network error';
        when(mockPlayerRepository.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        )).thenAnswer((_) async => const Left(AppError(errorMessage)));

        // Act
        final result = await searchPlayersUseCase(testParams);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, equals(errorMessage)),
          (response) => fail('Expected error but got success'),
        );
      });

      test('should handle repository timeout error', () async {
        // Arrange
        const errorMessage = 'Request timeout';
        when(mockPlayerRepository.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        )).thenAnswer((_) async => const Left(AppError(errorMessage)));

        // Act
        final result = await searchPlayersUseCase(testParams);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, equals(errorMessage)),
          (response) => fail('Expected error but got success'),
        );
      });
    });

    group('Parameter validation', () {
      test('should handle default page and pageSize values', () async {
        // Arrange
        const paramsWithDefaults = SearchPlayersParams(query: testQuery);

        when(mockPlayerRepository.searchPlayers(
          query: testQuery,
          page: null,
          pageSize: null,
        )).thenAnswer((_) async => Right(mockPaginatedResponse));

        // Act
        final result = await searchPlayersUseCase(paramsWithDefaults);

        // Assert
        expect(result.isRight(), true);
        verify(mockPlayerRepository.searchPlayers(
          query: testQuery,
          page: null,
          pageSize: null,
        )).called(1);
      });

      test('should handle large page numbers', () async {
        // Arrange
        const largePageParams = SearchPlayersParams(
          query: testQuery,
          page: 999,
          pageSize: testPageSize,
        );

        final emptyResponse = PaginatedResponse<PlayerSearchItem>(
          message: 'Success',
          data: PaginatedData<PlayerSearchItem>(
            items: const [],
            total: 2,
            page: 999,
            pageSize: testPageSize,
            totalPages: 1,
          ),
        );

        when(mockPlayerRepository.searchPlayers(
          query: testQuery,
          page: 999,
          pageSize: testPageSize,
        )).thenAnswer((_) async => Right(emptyResponse));

        // Act
        final result = await searchPlayersUseCase(largePageParams);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: ${error.message}'),
          (response) {
            expect(response.data.items.isEmpty, true);
            expect(response.data.page, equals(999));
          },
        );
      });
    });
  });

  group('SearchPlayersParams', () {
    test('should create params with all values', () {
      // Act
      const params = SearchPlayersParams(
        query: 'test',
        page: 2,
        pageSize: 10,
      );

      // Assert
      expect(params.query, equals('test'));
      expect(params.page, equals(2));
      expect(params.pageSize, equals(10));
    });

    test('should create params with null values', () {
      // Act
      const params = SearchPlayersParams();

      // Assert
      expect(params.query, isNull);
      expect(params.page, isNull);
      expect(params.pageSize, isNull);
    });

    test('should support copyWith method', () {
      // Arrange
      const originalParams = SearchPlayersParams(
        query: 'original',
        page: 1,
        pageSize: 20,
      );

      // Act
      final updatedParams = originalParams.copyWith(
        query: 'updated',
        page: 2,
      );

      // Assert
      expect(updatedParams.query, equals('updated'));
      expect(updatedParams.page, equals(2));
      expect(updatedParams.pageSize, equals(20)); // Should remain unchanged
    });

    test('should support equality comparison', () {
      // Arrange
      const params1 = SearchPlayersParams(
        query: 'test',
        page: 1,
        pageSize: 20,
      );
      const params2 = SearchPlayersParams(
        query: 'test',
        page: 1,
        pageSize: 20,
      );
      const params3 = SearchPlayersParams(
        query: 'different',
        page: 1,
        pageSize: 20,
      );

      // Assert
      expect(params1, equals(params2));
      expect(params1, isNot(equals(params3)));
    });

    test('should have consistent hashCode', () {
      // Arrange
      const params1 = SearchPlayersParams(
        query: 'test',
        page: 1,
        pageSize: 20,
      );
      const params2 = SearchPlayersParams(
        query: 'test',
        page: 1,
        pageSize: 20,
      );

      // Assert
      expect(params1.hashCode, equals(params2.hashCode));
    });

    test('should have meaningful toString', () {
      // Arrange
      const params = SearchPlayersParams(
        query: 'test',
        page: 1,
        pageSize: 20,
      );

      // Act
      final stringRepresentation = params.toString();

      // Assert
      expect(stringRepresentation, contains('test'));
      expect(stringRepresentation, contains('1'));
      expect(stringRepresentation, contains('20'));
    });
  });
}
