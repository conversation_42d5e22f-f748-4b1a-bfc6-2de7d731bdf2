// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in nextsportz_v2/test/features/player/domain/usecases/search_players_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:fpdart/fpdart.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;
import 'package:nextsportz_v2/core/models/paginated_response.dart' as _i10;
import 'package:nextsportz_v2/core/networking/app_error.dart' as _i5;
import 'package:nextsportz_v2/features/player/data/dto/player_request_models.dart'
    as _i8;
import 'package:nextsportz_v2/features/player/domain/entities/player.dart'
    as _i6;
import 'package:nextsportz_v2/features/player/domain/entities/player_rating.dart'
    as _i9;
import 'package:nextsportz_v2/features/player/domain/entities/player_search_item.dart'
    as _i11;
import 'package:nextsportz_v2/features/player/domain/repositories/player_repository.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [PlayerRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockPlayerRepository extends _i1.Mock implements _i2.PlayerRepository {
  MockPlayerRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i4.Either<_i5.AppError, _i6.Player>> getPlayerProfile(
    String? playerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPlayerProfile, [playerId]),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, _i6.Player>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, _i6.Player>>(
                this,
                Invocation.method(#getPlayerProfile, [playerId]),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, _i6.Player>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, _i6.Player>> updatePlayerProfile(
    String? playerId,
    _i8.UpdatePlayerProfileRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updatePlayerProfile, [playerId, request]),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, _i6.Player>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, _i6.Player>>(
                this,
                Invocation.method(#updatePlayerProfile, [playerId, request]),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, _i6.Player>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, _i9.PlayerRating>> getPlayerRating(
    String? playerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPlayerRating, [playerId]),
            returnValue:
                _i3.Future<_i4.Either<_i5.AppError, _i9.PlayerRating>>.value(
                  _i7.dummyValue<_i4.Either<_i5.AppError, _i9.PlayerRating>>(
                    this,
                    Invocation.method(#getPlayerRating, [playerId]),
                  ),
                ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, _i9.PlayerRating>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> submitRating(
    String? playerId,
    _i8.SubmitRatingRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#submitRating, [playerId, request]),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#submitRating, [playerId, request]),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<
    _i4.Either<_i5.AppError, _i10.PaginatedResponse<_i11.PlayerSearchItem>>
  >
  searchPlayers({String? query, int? page, int? pageSize}) =>
      (super.noSuchMethod(
            Invocation.method(#searchPlayers, [], {
              #query: query,
              #page: page,
              #pageSize: pageSize,
            }),
            returnValue: _i3.Future<
              _i4.Either<
                _i5.AppError,
                _i10.PaginatedResponse<_i11.PlayerSearchItem>
              >
            >.value(
              _i7.dummyValue<
                _i4.Either<
                  _i5.AppError,
                  _i10.PaginatedResponse<_i11.PlayerSearchItem>
                >
              >(
                this,
                Invocation.method(#searchPlayers, [], {
                  #query: query,
                  #page: page,
                  #pageSize: pageSize,
                }),
              ),
            ),
          )
          as _i3.Future<
            _i4.Either<
              _i5.AppError,
              _i10.PaginatedResponse<_i11.PlayerSearchItem>
            >
          >);
}
