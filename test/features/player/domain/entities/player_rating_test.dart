import 'package:flutter_test/flutter_test.dart';
import '../../../../../lib/features/player/domain/entities/player_rating.dart';

void main() {
  group('PlayerRatingVote Entity Tests', () {
    late PlayerRatingVote testVote;

    setUp(() {
      testVote = PlayerRatingVote(
        id: 'vote123',
        ratingCategory: 'teammate',
        defense: 8,
        shooting: 7,
        passing: 9,
        pace: 6,
        physicality: 8,
        dribbling: 7,
        comments: 'Great player!',
        voterName: '<PERSON>',
        createdAt: DateTime(2023, 1, 1),
      );
    });

    group('PlayerRatingVote Creation', () {
      test('creates vote with all required fields', () {
        final vote = PlayerRatingVote(
          id: 'test123',
          ratingCategory: 'public',
          defense: 5,
          shooting: 6,
          passing: 7,
          pace: 8,
          physicality: 5,
          dribbling: 6,
          voterName: 'Test Voter',
          createdAt: DateTime(2023, 5, 15),
        );

        expect(vote.id, 'test123');
        expect(vote.ratingCategory, 'public');
        expect(vote.defense, 5);
        expect(vote.shooting, 6);
        expect(vote.passing, 7);
        expect(vote.pace, 8);
        expect(vote.physicality, 5);
        expect(vote.dribbling, 6);
        expect(vote.voterName, 'Test Voter');
        expect(vote.comments, null);
        expect(vote.createdAt, DateTime(2023, 5, 15));
      });

      test('creates vote with comments', () {
        final vote = PlayerRatingVote(
          id: 'test123',
          ratingCategory: 'scout',
          defense: 5,
          shooting: 6,
          passing: 7,
          pace: 8,
          physicality: 5,
          dribbling: 6,
          comments: 'Excellent technique',
          voterName: 'Scout Name',
          createdAt: DateTime(2023, 5, 15),
        );

        expect(vote.comments, 'Excellent technique');
      });
    });

    group('averageRating calculation', () {
      test('calculates correct average from all attributes', () {
        // defense: 8, shooting: 7, passing: 9, pace: 6, physicality: 8, dribbling: 7
        // Total: 45, Average: 45/6 = 7.5
        expect(testVote.averageRating, 7.5);
      });

      test('calculates average with minimum values', () {
        final minVote = testVote.copyWith(
          defense: 1,
          shooting: 1,
          passing: 1,
          pace: 1,
          physicality: 1,
          dribbling: 1,
        );

        expect(minVote.averageRating, 1.0);
      });

      test('calculates average with maximum values', () {
        final maxVote = testVote.copyWith(
          defense: 10,
          shooting: 10,
          passing: 10,
          pace: 10,
          physicality: 10,
          dribbling: 10,
        );

        expect(maxVote.averageRating, 10.0);
      });

      test('calculates average with zero values', () {
        final zeroVote = testVote.copyWith(
          defense: 0,
          shooting: 0,
          passing: 0,
          pace: 0,
          physicality: 0,
          dribbling: 0,
        );

        expect(zeroVote.averageRating, 0.0);
      });

      test('calculates average with mixed values', () {
        final mixedVote = testVote.copyWith(
          defense: 2,
          shooting: 4,
          passing: 6,
          pace: 8,
          physicality: 10,
          dribbling: 6,
        );

        // Total: 36, Average: 36/6 = 6.0
        expect(mixedVote.averageRating, 6.0);
      });
    });

    group('copyWith method', () {
      test('creates new instance with updated fields', () {
        final updatedVote = testVote.copyWith(
          id: 'new123',
          defense: 10,
          comments: 'Updated comment',
        );

        expect(updatedVote.id, 'new123');
        expect(updatedVote.defense, 10);
        expect(updatedVote.comments, 'Updated comment');
        expect(updatedVote.shooting, testVote.shooting);
        expect(updatedVote.passing, testVote.passing);
        expect(updatedVote.ratingCategory, testVote.ratingCategory);
      });

      test('creates identical instance when no fields updated', () {
        final copiedVote = testVote.copyWith();

        expect(copiedVote.id, testVote.id);
        expect(copiedVote.ratingCategory, testVote.ratingCategory);
        expect(copiedVote.defense, testVote.defense);
        expect(copiedVote.shooting, testVote.shooting);
        expect(copiedVote.passing, testVote.passing);
        expect(copiedVote.pace, testVote.pace);
        expect(copiedVote.physicality, testVote.physicality);
        expect(copiedVote.dribbling, testVote.dribbling);
        expect(copiedVote.comments, testVote.comments);
        expect(copiedVote.voterName, testVote.voterName);
        expect(copiedVote.createdAt, testVote.createdAt);
      });

      test('can update all fields', () {
        final newDate = DateTime(2023, 12, 25);
        final fullyUpdated = testVote.copyWith(
          id: 'updated123',
          ratingCategory: 'self',
          defense: 1,
          shooting: 2,
          passing: 3,
          pace: 4,
          physicality: 5,
          dribbling: 6,
          comments: 'New comments',
          voterName: 'New Voter',
          createdAt: newDate,
        );

        expect(fullyUpdated.id, 'updated123');
        expect(fullyUpdated.ratingCategory, 'self');
        expect(fullyUpdated.defense, 1);
        expect(fullyUpdated.shooting, 2);
        expect(fullyUpdated.passing, 3);
        expect(fullyUpdated.pace, 4);
        expect(fullyUpdated.physicality, 5);
        expect(fullyUpdated.dribbling, 6);
        expect(fullyUpdated.comments, 'New comments');
        expect(fullyUpdated.voterName, 'New Voter');
        expect(fullyUpdated.createdAt, newDate);
      });

      test('can set comments to null', () {
        final voteWithoutComments = testVote.copyWith(comments: null);
        expect(voteWithoutComments.comments, null);
      });
    });

    group('Different Rating Categories', () {
      test('handles public rating category', () {
        final publicVote = testVote.copyWith(ratingCategory: 'public');
        expect(publicVote.ratingCategory, 'public');
      });

      test('handles teammate rating category', () {
        final teammateVote = testVote.copyWith(ratingCategory: 'teammate');
        expect(teammateVote.ratingCategory, 'teammate');
      });

      test('handles opponent rating category', () {
        final opponentVote = testVote.copyWith(ratingCategory: 'opponent');
        expect(opponentVote.ratingCategory, 'opponent');
      });

      test('handles scout rating category', () {
        final scoutVote = testVote.copyWith(ratingCategory: 'scout');
        expect(scoutVote.ratingCategory, 'scout');
      });

      test('handles self rating category', () {
        final selfVote = testVote.copyWith(ratingCategory: 'self');
        expect(selfVote.ratingCategory, 'self');
      });
    });
  });

  group('PlayerRating Entity Tests', () {
    late PlayerRating testRating;
    late List<PlayerRatingVote> testVotes;

    setUp(() {
      testVotes = [
        PlayerRatingVote(
          id: 'vote1',
          ratingCategory: 'teammate',
          defense: 8,
          shooting: 7,
          passing: 9,
          pace: 6,
          physicality: 8,
          dribbling: 7,
          voterName: 'Teammate 1',
          createdAt: DateTime(2023, 1, 1),
        ),
        PlayerRatingVote(
          id: 'vote2',
          ratingCategory: 'self',
          defense: 7,
          shooting: 8,
          passing: 8,
          pace: 7,
          physicality: 7,
          dribbling: 8,
          voterName: 'Player Self',
          createdAt: DateTime(2023, 1, 2),
        ),
        PlayerRatingVote(
          id: 'vote3',
          ratingCategory: 'scout',
          defense: 9,
          shooting: 6,
          passing: 10,
          pace: 5,
          physicality: 9,
          dribbling: 6,
          voterName: 'Scout Pro',
          createdAt: DateTime(2023, 1, 3),
        ),
      ];

      testRating = PlayerRating(
        averageRating: 7.5,
        totalRatings: 15,
        categoryAverages: {
          'defense': 8.0,
          'shooting': 7.0,
          'passing': 9.0,
          'pace': 6.0,
          'physicality': 8.0,
          'dribbling': 7.0,
        },
        recentVotes: testVotes,
      );
    });

    group('PlayerRating Creation', () {
      test('creates rating with all required fields', () {
        final rating = PlayerRating(
          averageRating: 8.2,
          totalRatings: 25,
          categoryAverages: {
            'defense': 8.5,
            'shooting': 7.8,
            'passing': 8.9,
            'pace': 7.2,
            'physicality': 8.1,
            'dribbling': 8.3,
          },
          recentVotes: [],
        );

        expect(rating.averageRating, 8.2);
        expect(rating.totalRatings, 25);
        expect(rating.categoryAverages['defense'], 8.5);
        expect(rating.recentVotes, isEmpty);
      });

      test('creates rating with empty category averages', () {
        final rating = PlayerRating(
          averageRating: 0.0,
          totalRatings: 0,
          categoryAverages: {},
          recentVotes: [],
        );

        expect(rating.categoryAverages, isEmpty);
        expect(rating.totalRatings, 0);
      });
    });

    group('hasSelfAssessment', () {
      test('returns true when self assessment exists', () {
        expect(testRating.hasSelfAssessment, true);
      });

      test('returns false when no self assessment exists', () {
        final votesWithoutSelf =
            testVotes.where((vote) => vote.ratingCategory != 'self').toList();
        final ratingWithoutSelf = testRating.copyWith(
          recentVotes: votesWithoutSelf,
        );

        expect(ratingWithoutSelf.hasSelfAssessment, false);
      });

      test('returns true for case insensitive self assessment', () {
        final selfVoteUppercase = testVotes[1].copyWith(ratingCategory: 'SELF');
        final updatedVotes = [testVotes[0], selfVoteUppercase, testVotes[2]];
        final rating = testRating.copyWith(recentVotes: updatedVotes);

        expect(rating.hasSelfAssessment, true);
      });

      test('returns false when recent votes is empty', () {
        final emptyRating = testRating.copyWith(recentVotes: []);
        expect(emptyRating.hasSelfAssessment, false);
      });
    });

    group('latestSelfAssessment', () {
      test('returns most recent self assessment', () {
        // Add another self assessment with later date
        final newerSelfVote = PlayerRatingVote(
          id: 'vote4',
          ratingCategory: 'self',
          defense: 9,
          shooting: 9,
          passing: 9,
          pace: 9,
          physicality: 9,
          dribbling: 9,
          voterName: 'Player Self Updated',
          createdAt: DateTime(2023, 1, 5),
        );

        final updatedVotes = [...testVotes, newerSelfVote];
        final rating = testRating.copyWith(recentVotes: updatedVotes);

        final latest = rating.latestSelfAssessment;
        expect(latest, isNotNull);
        expect(latest!.id, 'vote4');
        expect(latest.voterName, 'Player Self Updated');
        expect(latest.createdAt, DateTime(2023, 1, 5));
      });

      test('returns null when no self assessment exists', () {
        final votesWithoutSelf =
            testVotes.where((vote) => vote.ratingCategory != 'self').toList();
        final ratingWithoutSelf = testRating.copyWith(
          recentVotes: votesWithoutSelf,
        );

        expect(ratingWithoutSelf.latestSelfAssessment, null);
      });

      test('returns single self assessment when only one exists', () {
        final latest = testRating.latestSelfAssessment;
        expect(latest, isNotNull);
        expect(latest!.id, 'vote2');
        expect(latest.ratingCategory, 'self');
      });
    });

    group('getCategoryAverage', () {
      test('returns correct average for existing category', () {
        expect(testRating.getCategoryAverage('defense'), 8.0);
        expect(testRating.getCategoryAverage('shooting'), 7.0);
        expect(testRating.getCategoryAverage('passing'), 9.0);
        expect(testRating.getCategoryAverage('pace'), 6.0);
        expect(testRating.getCategoryAverage('physicality'), 8.0);
        expect(testRating.getCategoryAverage('dribbling'), 7.0);
      });

      test('returns 0.0 for non-existing category', () {
        expect(testRating.getCategoryAverage('nonexistent'), 0.0);
        expect(testRating.getCategoryAverage(''), 0.0);
      });

      test('handles case sensitive category names', () {
        expect(testRating.getCategoryAverage('Defense'), 0.0);
        expect(testRating.getCategoryAverage('DEFENSE'), 0.0);
      });
    });

    group('Category Getters', () {
      test('defense getter returns correct value', () {
        expect(testRating.defense, 8.0);
      });

      test('shooting getter returns correct value', () {
        expect(testRating.shooting, 7.0);
      });

      test('passing getter returns correct value', () {
        expect(testRating.passing, 9.0);
      });

      test('pace getter returns correct value', () {
        expect(testRating.pace, 6.0);
      });

      test('physicality getter returns correct value', () {
        expect(testRating.physicality, 8.0);
      });

      test('dribbling getter returns correct value', () {
        expect(testRating.dribbling, 7.0);
      });

      test('getters return 0.0 when category not found', () {
        final emptyRating = testRating.copyWith(categoryAverages: {});
        expect(emptyRating.defense, 0.0);
        expect(emptyRating.shooting, 0.0);
        expect(emptyRating.passing, 0.0);
        expect(emptyRating.pace, 0.0);
        expect(emptyRating.physicality, 0.0);
        expect(emptyRating.dribbling, 0.0);
      });
    });

    group('copyWith method', () {
      test('creates new instance with updated fields', () {
        final updatedRating = testRating.copyWith(
          averageRating: 8.5,
          totalRatings: 20,
        );

        expect(updatedRating.averageRating, 8.5);
        expect(updatedRating.totalRatings, 20);
        expect(updatedRating.categoryAverages, testRating.categoryAverages);
        expect(updatedRating.recentVotes, testRating.recentVotes);
      });

      test('creates identical instance when no fields updated', () {
        final copiedRating = testRating.copyWith();

        expect(copiedRating.averageRating, testRating.averageRating);
        expect(copiedRating.totalRatings, testRating.totalRatings);
        expect(copiedRating.categoryAverages, testRating.categoryAverages);
        expect(copiedRating.recentVotes, testRating.recentVotes);
      });

      test('can update category averages', () {
        final newAverages = {
          'defense': 9.0,
          'shooting': 8.0,
          'passing': 10.0,
          'pace': 7.0,
          'physicality': 9.0,
          'dribbling': 8.0,
        };

        final updatedRating = testRating.copyWith(
          categoryAverages: newAverages,
        );

        expect(updatedRating.categoryAverages, newAverages);
        expect(updatedRating.defense, 9.0);
        expect(updatedRating.shooting, 8.0);
      });

      test('can update recent votes', () {
        final newVotes = [testVotes[0]]; // Only one vote
        final updatedRating = testRating.copyWith(recentVotes: newVotes);

        expect(updatedRating.recentVotes.length, 1);
        expect(updatedRating.recentVotes[0].id, 'vote1');
      });

      test('can update all fields', () {
        final newAverages = {'new': 5.0};
        final newVotes = <PlayerRatingVote>[];

        final fullyUpdated = testRating.copyWith(
          averageRating: 10.0,
          totalRatings: 100,
          categoryAverages: newAverages,
          recentVotes: newVotes,
        );

        expect(fullyUpdated.averageRating, 10.0);
        expect(fullyUpdated.totalRatings, 100);
        expect(fullyUpdated.categoryAverages, newAverages);
        expect(fullyUpdated.recentVotes, newVotes);
      });
    });

    group('Edge Cases', () {
      test('handles negative ratings', () {
        final negativeRating = testRating.copyWith(
          averageRating: -1.0,
          totalRatings: -5,
          categoryAverages: {
            'defense': -2.0,
            'shooting': -1.5,
          },
        );

        expect(negativeRating.averageRating, -1.0);
        expect(negativeRating.totalRatings, -5);
        expect(negativeRating.defense, -2.0);
        expect(negativeRating.shooting, -1.5);
      });

      test('handles very large numbers', () {
        final largeRating = testRating.copyWith(
          averageRating: 999999.99,
          totalRatings: 999999,
          categoryAverages: {
            'defense': 888888.88,
          },
        );

        expect(largeRating.averageRating, 999999.99);
        expect(largeRating.totalRatings, 999999);
        expect(largeRating.defense, 888888.88);
      });

      test('handles decimal precision', () {
        final preciseRating = testRating.copyWith(
          averageRating: 7.123456789,
          categoryAverages: {
            'defense': 8.987654321,
          },
        );

        expect(preciseRating.averageRating, 7.123456789);
        expect(preciseRating.defense, 8.987654321);
      });

      test('handles empty and null-like strings in vote data', () {
        final emptyVote = PlayerRatingVote(
          id: '',
          ratingCategory: '',
          defense: 0,
          shooting: 0,
          passing: 0,
          pace: 0,
          physicality: 0,
          dribbling: 0,
          voterName: '',
          createdAt: DateTime.now(),
        );

        final ratingWithEmpty = testRating.copyWith(
          recentVotes: [emptyVote],
        );

        expect(ratingWithEmpty.recentVotes[0].id, '');
        expect(ratingWithEmpty.recentVotes[0].ratingCategory, '');
        expect(ratingWithEmpty.recentVotes[0].voterName, '');
      });
    });
  });
}
