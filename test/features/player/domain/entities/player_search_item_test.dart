import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/player/domain/entities/player_search_item.dart';

void main() {
  group('PlayerSearchItem', () {
    const testPlayerSearchItem = PlayerSearchItem(
      id: 'player_123',
      fullName: '<PERSON>',
      age: 25,
      heightCm: 180,
      weightKg: 75,
      email: '<EMAIL>',
      phone: '+1234567890',
      description: 'Experienced midfielder',
      photoUrl: 'https://example.com/john.jpg',
      scoutingEnabled: true,
      gameInfo: {'position': 'Midfielder', 'preferredFoot': 'Right'},
    );

    group('Constructor', () {
      test('should create PlayerSearchItem with all required fields', () {
        // Arrange & Act
        const player = PlayerSearchItem(
          id: 'player_123',
          fullName: '<PERSON>',
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: true,
        );

        // Assert
        expect(player.id, equals('player_123'));
        expect(player.fullName, equals('<PERSON>'));
        expect(player.email, equals('<EMAIL>'));
        expect(player.phone, equals('+1234567890'));
        expect(player.scoutingEnabled, equals(true));
        expect(player.age, isNull);
        expect(player.heightCm, isNull);
        expect(player.weightKg, isNull);
        expect(player.description, isNull);
        expect(player.photoUrl, isNull);
        expect(player.gameInfo, isNull);
      });

      test('should create PlayerSearchItem with all fields', () {
        // Assert
        expect(testPlayerSearchItem.id, equals('player_123'));
        expect(testPlayerSearchItem.fullName, equals('John Doe'));
        expect(testPlayerSearchItem.age, equals(25));
        expect(testPlayerSearchItem.heightCm, equals(180));
        expect(testPlayerSearchItem.weightKg, equals(75));
        expect(testPlayerSearchItem.email, equals('<EMAIL>'));
        expect(testPlayerSearchItem.phone, equals('+1234567890'));
        expect(
          testPlayerSearchItem.description,
          equals('Experienced midfielder'),
        );
        expect(
          testPlayerSearchItem.photoUrl,
          equals('https://example.com/john.jpg'),
        );
        expect(testPlayerSearchItem.scoutingEnabled, equals(true));
        expect(testPlayerSearchItem.gameInfo, isNotNull);
        expect(
          testPlayerSearchItem.gameInfo!['position'],
          equals('Midfielder'),
        );
        expect(
          testPlayerSearchItem.gameInfo!['preferredFoot'],
          equals('Right'),
        );
      });

      test('should handle null optional fields', () {
        // Arrange & Act
        const player = PlayerSearchItem(
          id: 'player_456',
          fullName: 'Jane Smith',
          email: '<EMAIL>',
          phone: '+1234567891',
          scoutingEnabled: false,
          age: null,
          heightCm: null,
          weightKg: null,
          description: null,
          photoUrl: null,
          gameInfo: null,
        );

        // Assert
        expect(player.age, isNull);
        expect(player.heightCm, isNull);
        expect(player.weightKg, isNull);
        expect(player.description, isNull);
        expect(player.photoUrl, isNull);
        expect(player.gameInfo, isNull);
      });

      test('should handle empty strings for optional fields', () {
        // Arrange & Act
        const player = PlayerSearchItem(
          id: 'player_789',
          fullName: 'Bob Johnson',
          email: '<EMAIL>',
          phone: '',
          description: '',
          photoUrl: '',
          scoutingEnabled: true,
        );

        // Assert
        expect(player.phone, equals(''));
        expect(player.description, equals(''));
        expect(player.photoUrl, equals(''));
      });
    });

    group('copyWith', () {
      test('should create new instance with updated fields', () {
        // Act
        final updatedPlayer = testPlayerSearchItem.copyWith(
          fullName: 'John Smith',
          age: 26,
          email: '<EMAIL>',
        );

        // Assert
        expect(updatedPlayer.id, equals('player_123')); // Unchanged
        expect(updatedPlayer.fullName, equals('John Smith')); // Changed
        expect(updatedPlayer.age, equals(26)); // Changed
        expect(
          updatedPlayer.email,
          equals('<EMAIL>'),
        ); // Changed
        expect(updatedPlayer.phone, equals('+1234567890')); // Unchanged
        expect(updatedPlayer.heightCm, equals(180)); // Unchanged
        expect(updatedPlayer.weightKg, equals(75)); // Unchanged
        expect(
          updatedPlayer.description,
          equals('Experienced midfielder'),
        ); // Unchanged
        expect(
          updatedPlayer.photoUrl,
          equals('https://example.com/john.jpg'),
        ); // Unchanged
        expect(updatedPlayer.scoutingEnabled, equals(true)); // Unchanged
        expect(
          updatedPlayer.gameInfo,
          equals(testPlayerSearchItem.gameInfo),
        ); // Unchanged
      });

      test('should create new instance with updated values', () {
        // Create a player with null values first
        const playerWithNulls = PlayerSearchItem(
          id: 'player_null',
          fullName: 'Null Player',
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: false,
        );

        // Act - update with new values
        final updatedPlayer = playerWithNulls.copyWith(
          age: 30,
          heightCm: 185,
          weightKg: 80,
          description: 'Updated description',
          photoUrl: 'https://example.com/updated.jpg',
          gameInfo: {'position': 'Updated Position'},
        );

        // Assert
        expect(updatedPlayer.age, equals(30));
        expect(updatedPlayer.heightCm, equals(185));
        expect(updatedPlayer.weightKg, equals(80));
        expect(updatedPlayer.description, equals('Updated description'));
        expect(
          updatedPlayer.photoUrl,
          equals('https://example.com/updated.jpg'),
        );
        expect(updatedPlayer.gameInfo, isNotNull);
        expect(updatedPlayer.gameInfo!['position'], equals('Updated Position'));
        // Required fields should remain unchanged
        expect(updatedPlayer.id, equals('player_null'));
        expect(updatedPlayer.fullName, equals('Null Player'));
        expect(updatedPlayer.email, equals('<EMAIL>'));
        expect(updatedPlayer.phone, equals('+1234567890'));
        expect(updatedPlayer.scoutingEnabled, equals(false));
      });

      test('should return identical instance when no changes', () {
        // Act
        final copiedPlayer = testPlayerSearchItem.copyWith();

        // Assert
        expect(copiedPlayer.id, equals(testPlayerSearchItem.id));
        expect(copiedPlayer.fullName, equals(testPlayerSearchItem.fullName));
        expect(copiedPlayer.age, equals(testPlayerSearchItem.age));
        expect(copiedPlayer.heightCm, equals(testPlayerSearchItem.heightCm));
        expect(copiedPlayer.weightKg, equals(testPlayerSearchItem.weightKg));
        expect(copiedPlayer.email, equals(testPlayerSearchItem.email));
        expect(copiedPlayer.phone, equals(testPlayerSearchItem.phone));
        expect(
          copiedPlayer.description,
          equals(testPlayerSearchItem.description),
        );
        expect(copiedPlayer.photoUrl, equals(testPlayerSearchItem.photoUrl));
        expect(
          copiedPlayer.scoutingEnabled,
          equals(testPlayerSearchItem.scoutingEnabled),
        );
        expect(copiedPlayer.gameInfo, equals(testPlayerSearchItem.gameInfo));
      });
    });

    group('Equality', () {
      test('should be equal when ids are the same', () {
        // Arrange
        const player1 = PlayerSearchItem(
          id: 'player_123',
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: true,
        );

        const player2 = PlayerSearchItem(
          id: 'player_123',
          fullName: 'Jane Smith', // Different name
          email: '<EMAIL>', // Different email
          phone: '+1234567891', // Different phone
          scoutingEnabled: false, // Different scouting
        );

        // Assert
        expect(player1, equals(player2));
        expect(player1.hashCode, equals(player2.hashCode));
      });

      test('should not be equal when ids are different', () {
        // Arrange
        const player1 = PlayerSearchItem(
          id: 'player_123',
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: true,
        );

        const player2 = PlayerSearchItem(
          id: 'player_456',
          fullName: 'John Doe', // Same name
          email: '<EMAIL>', // Same email
          phone: '+1234567890', // Same phone
          scoutingEnabled: true, // Same scouting
        );

        // Assert
        expect(player1, isNot(equals(player2)));
        expect(player1.hashCode, isNot(equals(player2.hashCode)));
      });

      test('should be equal to itself', () {
        // Assert
        expect(testPlayerSearchItem, equals(testPlayerSearchItem));
        expect(
          testPlayerSearchItem.hashCode,
          equals(testPlayerSearchItem.hashCode),
        );
      });

      test('should not be equal to different type', () {
        // Assert
        expect(testPlayerSearchItem, isNot(equals('not a player')));
        expect(testPlayerSearchItem, isNot(equals(123)));
        expect(testPlayerSearchItem, isNot(equals(null)));
      });
    });

    group('toString', () {
      test('should return meaningful string representation', () {
        // Act
        final stringRepresentation = testPlayerSearchItem.toString();

        // Assert
        expect(stringRepresentation, contains('PlayerSearchItem'));
        expect(stringRepresentation, contains('player_123'));
        expect(stringRepresentation, contains('John Doe'));
        expect(stringRepresentation, contains('<EMAIL>'));
      });

      test('should handle null values in toString', () {
        // Arrange
        const playerWithNulls = PlayerSearchItem(
          id: 'player_null',
          fullName: 'Null Player',
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: false,
        );

        // Act
        final stringRepresentation = playerWithNulls.toString();

        // Assert
        expect(stringRepresentation, contains('PlayerSearchItem'));
        expect(stringRepresentation, contains('player_null'));
        expect(stringRepresentation, contains('Null Player'));
        expect(stringRepresentation, contains('<EMAIL>'));
      });
    });

    group('Edge Cases', () {
      test('should handle very long names', () {
        // Arrange
        final longName = 'A' * 1000;
        final player = PlayerSearchItem(
          id: 'player_long',
          fullName: longName,
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: true,
        );

        // Assert
        expect(player.fullName, equals(longName));
        expect(player.fullName.length, equals(1000));
      });

      test('should handle special characters in fields', () {
        // Arrange
        const player = PlayerSearchItem(
          id: 'player_special',
          fullName: 'João José María',
          email: 'joão.josé@example.com',
          phone: '+55 11 99999-9999',
          description: 'Player with special chars: àáâãäåæçèéêë',
          scoutingEnabled: true,
        );

        // Assert
        expect(player.fullName, equals('João José María'));
        expect(player.email, equals('joão.josé@example.com'));
        expect(player.phone, equals('+55 11 99999-9999'));
        expect(player.description, contains('àáâãäåæçèéêë'));
      });

      test('should handle extreme age values', () {
        // Arrange
        const youngPlayer = PlayerSearchItem(
          id: 'player_young',
          fullName: 'Young Player',
          age: 16,
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: true,
        );

        const oldPlayer = PlayerSearchItem(
          id: 'player_old',
          fullName: 'Old Player',
          age: 45,
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: true,
        );

        // Assert
        expect(youngPlayer.age, equals(16));
        expect(oldPlayer.age, equals(45));
      });

      test('should handle extreme height and weight values', () {
        // Arrange
        const extremePlayer = PlayerSearchItem(
          id: 'player_extreme',
          fullName: 'Extreme Player',
          heightCm: 150, // Very short
          weightKg: 50, // Very light
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: true,
        );

        const tallPlayer = PlayerSearchItem(
          id: 'player_tall',
          fullName: 'Tall Player',
          heightCm: 220, // Very tall
          weightKg: 120, // Very heavy
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: true,
        );

        // Assert
        expect(extremePlayer.heightCm, equals(150));
        expect(extremePlayer.weightKg, equals(50));
        expect(tallPlayer.heightCm, equals(220));
        expect(tallPlayer.weightKg, equals(120));
      });

      test('should handle complex gameInfo data', () {
        // Arrange
        const complexGameInfo = {
          'position': 'Midfielder',
          'preferredFoot': 'Right',
          'skills': ['Passing', 'Shooting', 'Dribbling'],
          'stats': {'goals': 15, 'assists': 8, 'matches': 25},
          'isInjured': false,
          'contractExpiry': '2025-12-31',
        };

        const player = PlayerSearchItem(
          id: 'player_complex',
          fullName: 'Complex Player',
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: true,
          gameInfo: complexGameInfo,
        );

        // Assert
        expect(player.gameInfo, equals(complexGameInfo));
        expect(player.gameInfo!['position'], equals('Midfielder'));
        expect(player.gameInfo!['skills'], isA<List>());
        expect(player.gameInfo!['stats'], isA<Map>());
        expect(player.gameInfo!['stats']['goals'], equals(15));
      });
    });
  });
}
