// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in nextsportz_v2/test/features/player/data/repositories/player_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:mockito/mockito.dart' as _i1;
import 'package:nextsportz_v2/core/models/paginated_response.dart' as _i3;
import 'package:nextsportz_v2/features/player/data/datasources/player_datasource.dart'
    as _i4;
import 'package:nextsportz_v2/features/player/data/dto/player_request_models.dart'
    as _i6;
import 'package:nextsportz_v2/features/player/data/dto/player_response_models.dart'
    as _i2;
import 'package:nextsportz_v2/features/player/data/dto/player_search_item_dto.dart'
    as _i7;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakePlayerProfileResponse_0 extends _i1.SmartFake
    implements _i2.PlayerProfileResponse {
  _FakePlayerProfileResponse_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePlayerRatingResponse_1 extends _i1.SmartFake
    implements _i2.PlayerRatingResponse {
  _FakePlayerRatingResponse_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePaginatedResponse_2<T> extends _i1.SmartFake
    implements _i3.PaginatedResponse<T> {
  _FakePaginatedResponse_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [PlayerDatasource].
///
/// See the documentation for Mockito's code generation for more information.
class MockPlayerDatasource extends _i1.Mock implements _i4.PlayerDatasource {
  MockPlayerDatasource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.PlayerProfileResponse> getPlayerProfile(String? playerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPlayerProfile, [playerId]),
            returnValue: _i5.Future<_i2.PlayerProfileResponse>.value(
              _FakePlayerProfileResponse_0(
                this,
                Invocation.method(#getPlayerProfile, [playerId]),
              ),
            ),
          )
          as _i5.Future<_i2.PlayerProfileResponse>);

  @override
  _i5.Future<_i2.PlayerProfileResponse> updatePlayerProfile(
    String? playerId,
    _i6.UpdatePlayerProfileRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updatePlayerProfile, [playerId, request]),
            returnValue: _i5.Future<_i2.PlayerProfileResponse>.value(
              _FakePlayerProfileResponse_0(
                this,
                Invocation.method(#updatePlayerProfile, [playerId, request]),
              ),
            ),
          )
          as _i5.Future<_i2.PlayerProfileResponse>);

  @override
  _i5.Future<_i2.PlayerRatingResponse> getPlayerRating(String? playerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPlayerRating, [playerId]),
            returnValue: _i5.Future<_i2.PlayerRatingResponse>.value(
              _FakePlayerRatingResponse_1(
                this,
                Invocation.method(#getPlayerRating, [playerId]),
              ),
            ),
          )
          as _i5.Future<_i2.PlayerRatingResponse>);

  @override
  _i5.Future<void> submitRating(
    String? playerId,
    _i6.SubmitRatingRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#submitRating, [playerId, request]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i3.PaginatedResponse<_i7.PlayerSearchItemDto>> searchPlayers({
    String? query,
    int? page,
    int? pageSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#searchPlayers, [], {
              #query: query,
              #page: page,
              #pageSize: pageSize,
            }),
            returnValue: _i5.Future<
              _i3.PaginatedResponse<_i7.PlayerSearchItemDto>
            >.value(
              _FakePaginatedResponse_2<_i7.PlayerSearchItemDto>(
                this,
                Invocation.method(#searchPlayers, [], {
                  #query: query,
                  #page: page,
                  #pageSize: pageSize,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.PaginatedResponse<_i7.PlayerSearchItemDto>>);
}
