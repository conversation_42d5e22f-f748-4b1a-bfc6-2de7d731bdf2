import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/core/networking/exception.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';
import 'package:nextsportz_v2/features/player/data/datasources/player_datasource.dart';
import 'package:nextsportz_v2/features/player/data/dto/player_search_item_dto.dart';
import 'package:nextsportz_v2/features/player/data/repositories/player_repository_impl.dart';
import 'package:nextsportz_v2/features/player/domain/entities/player_search_item.dart';

import 'player_repository_impl_test.mocks.dart';

@GenerateMocks([PlayerDatasource])
void main() {
  late MockPlayerDatasource mockPlayerDatasource;
  late PlayerRepositoryImpl playerRepository;

  // Provide dummy values for Mockito
  provideDummy<PaginatedResponse<PlayerSearchItemDto>>(
    PaginatedResponse<PlayerSearchItemDto>(
      message: 'Success',
      data: PaginatedData<PlayerSearchItemDto>(
        items: const [],
        total: 0,
        page: 1,
        pageSize: 20,
        totalPages: 0,
      ),
    ),
  );

  setUp(() {
    mockPlayerDatasource = MockPlayerDatasource();
    playerRepository = PlayerRepositoryImpl(mockPlayerDatasource);
  });

  group('PlayerRepositoryImpl - searchPlayers', () {
    const testQuery = 'john';
    const testPage = 1;
    const testPageSize = 20;

    final mockPlayerDtos = [
      const PlayerSearchItemDto(
        id: 'player_1',
        fullName: 'John Doe',
        age: 25,
        heightCm: 180,
        weightKg: 75,
        email: '<EMAIL>',
        phone: '+1234567890',
        description: 'Experienced midfielder',
        photoUrl: 'https://example.com/john.jpg',
        scoutingEnabled: true,
        gameInfo: {'position': 'Midfielder', 'preferredFoot': 'Right'},
      ),
      const PlayerSearchItemDto(
        id: 'player_2',
        fullName: 'John Smith',
        age: 28,
        heightCm: 175,
        weightKg: 70,
        email: '<EMAIL>',
        phone: '+1234567891',
        description: 'Skilled forward',
        photoUrl: null,
        scoutingEnabled: false,
        gameInfo: {'position': 'Forward', 'preferredFoot': 'Left'},
      ),
    ];

    final mockDtoResponse = PaginatedResponse<PlayerSearchItemDto>(
      message: 'Success',
      data: PaginatedData<PlayerSearchItemDto>(
        items: mockPlayerDtos,
        total: 2,
        page: testPage,
        pageSize: testPageSize,
        totalPages: 1,
      ),
    );

    group('Success scenarios', () {
      test(
        'should return paginated players when datasource succeeds',
        () async {
          // Arrange
          when(
            mockPlayerDatasource.searchPlayers(
              query: testQuery,
              page: testPage,
              pageSize: testPageSize,
            ),
          ).thenAnswer((_) async => mockDtoResponse);

          // Act
          final result = await playerRepository.searchPlayers(
            query: testQuery,
            page: testPage,
            pageSize: testPageSize,
          );

          // Assert
          expect(result.isRight(), true);
          result.fold(
            (error) => fail('Expected success but got error: ${error.message}'),
            (response) {
              expect(response.data.items.length, equals(2));
              expect(response.data.items[0], isA<PlayerSearchItem>());
              expect(response.data.items[0].fullName, equals('John Doe'));
              expect(response.data.items[1].fullName, equals('John Smith'));
              expect(response.data.total, equals(2));
              expect(response.data.page, equals(testPage));
              expect(response.data.pageSize, equals(testPageSize));
              expect(response.message, equals('Success'));
            },
          );

          verify(
            mockPlayerDatasource.searchPlayers(
              query: testQuery,
              page: testPage,
              pageSize: testPageSize,
            ),
          ).called(1);
        },
      );

      test('should return empty list when no players found', () async {
        // Arrange
        final emptyDtoResponse = PaginatedResponse<PlayerSearchItemDto>(
          message: 'Success',
          data: PaginatedData<PlayerSearchItemDto>(
            items: const [],
            total: 0,
            page: testPage,
            pageSize: testPageSize,
            totalPages: 0,
          ),
        );

        when(
          mockPlayerDatasource.searchPlayers(
            query: testQuery,
            page: testPage,
            pageSize: testPageSize,
          ),
        ).thenAnswer((_) async => emptyDtoResponse);

        // Act
        final result = await playerRepository.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: ${error.message}'),
          (response) {
            expect(response.data.items.isEmpty, true);
            expect(response.data.total, equals(0));
          },
        );
      });

      test('should handle null query parameter', () async {
        // Arrange
        when(
          mockPlayerDatasource.searchPlayers(
            query: null,
            page: testPage,
            pageSize: testPageSize,
          ),
        ).thenAnswer((_) async => mockDtoResponse);

        // Act
        final result = await playerRepository.searchPlayers(
          query: null,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result.isRight(), true);
        verify(
          mockPlayerDatasource.searchPlayers(
            query: null,
            page: testPage,
            pageSize: testPageSize,
          ),
        ).called(1);
      });

      test('should handle null page and pageSize parameters', () async {
        // Arrange
        when(
          mockPlayerDatasource.searchPlayers(
            query: testQuery,
            page: null,
            pageSize: null,
          ),
        ).thenAnswer((_) async => mockDtoResponse);

        // Act
        final result = await playerRepository.searchPlayers(
          query: testQuery,
          page: null,
          pageSize: null,
        );

        // Assert
        expect(result.isRight(), true);
        verify(
          mockPlayerDatasource.searchPlayers(
            query: testQuery,
            page: null,
            pageSize: null,
          ),
        ).called(1);
      });

      test('should properly convert DTOs to entities', () async {
        // Arrange
        when(
          mockPlayerDatasource.searchPlayers(
            query: testQuery,
            page: testPage,
            pageSize: testPageSize,
          ),
        ).thenAnswer((_) async => mockDtoResponse);

        // Act
        final result = await playerRepository.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: ${error.message}'),
          (response) {
            final firstPlayer = response.data.items[0];
            expect(firstPlayer.id, equals('player_1'));
            expect(firstPlayer.fullName, equals('John Doe'));
            expect(firstPlayer.age, equals(25));
            expect(firstPlayer.heightCm, equals(180));
            expect(firstPlayer.weightKg, equals(75));
            expect(firstPlayer.email, equals('<EMAIL>'));
            expect(firstPlayer.phone, equals('+1234567890'));
            expect(firstPlayer.description, equals('Experienced midfielder'));
            expect(
              firstPlayer.photoUrl,
              equals('https://example.com/john.jpg'),
            );
            expect(firstPlayer.scoutingEnabled, equals(true));
            expect(firstPlayer.gameInfo, isNotNull);
            expect(firstPlayer.gameInfo!['position'], equals('Midfielder'));
          },
        );
      });
    });

    group('Error scenarios', () {
      test(
        'should return AppError when datasource throws DioExceptionHandle',
        () async {
          // Arrange
          final dioException = DioExceptionHandle.fromDioError(
            DioException(
              requestOptions: RequestOptions(path: '/test'),
              type: DioExceptionType.connectionError,
            ),
          );

          when(
            mockPlayerDatasource.searchPlayers(
              query: testQuery,
              page: testPage,
              pageSize: testPageSize,
            ),
          ).thenThrow(dioException);

          // Act
          final result = await playerRepository.searchPlayers(
            query: testQuery,
            page: testPage,
            pageSize: testPageSize,
          );

          // Assert
          expect(result.isLeft(), true);
          result.fold(
            (error) => expect(error.message, contains('Connection failed')),
            (response) => fail('Expected error but got success'),
          );
        },
      );

      test(
        'should return AppError when datasource throws generic exception',
        () async {
          // Arrange
          const errorMessage = 'Unexpected error';
          final exception = Exception(errorMessage);

          when(
            mockPlayerDatasource.searchPlayers(
              query: testQuery,
              page: testPage,
              pageSize: testPageSize,
            ),
          ).thenThrow(exception);

          // Act
          final result = await playerRepository.searchPlayers(
            query: testQuery,
            page: testPage,
            pageSize: testPageSize,
          );

          // Assert
          expect(result.isLeft(), true);
          result.fold(
            (error) => expect(error.message, contains(errorMessage)),
            (response) => fail('Expected error but got success'),
          );
        },
      );

      test('should handle timeout errors', () async {
        // Arrange
        final timeoutException = DioExceptionHandle.fromDioError(
          DioException(
            requestOptions: RequestOptions(path: '/test'),
            type: DioExceptionType.connectionTimeout,
          ),
        );

        when(
          mockPlayerDatasource.searchPlayers(
            query: testQuery,
            page: testPage,
            pageSize: testPageSize,
          ),
        ).thenThrow(timeoutException);

        // Act
        final result = await playerRepository.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, contains('Something went wrong')),
          (response) => fail('Expected error but got success'),
        );
      });

      test('should handle server errors', () async {
        // Arrange
        final serverException = DioExceptionHandle.fromDioError(
          DioException(
            requestOptions: RequestOptions(path: '/test'),
            type: DioExceptionType.badResponse,
            response: Response(
              requestOptions: RequestOptions(path: '/test'),
              statusCode: 500,
            ),
          ),
        );

        when(
          mockPlayerDatasource.searchPlayers(
            query: testQuery,
            page: testPage,
            pageSize: testPageSize,
          ),
        ).thenThrow(serverException);

        // Act
        final result = await playerRepository.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, contains('Internal server error')),
          (response) => fail('Expected error but got success'),
        );
      });
    });

    group('Edge cases', () {
      test('should handle large datasets', () async {
        // Arrange
        final largeDtoResponse = PaginatedResponse<PlayerSearchItemDto>(
          message: 'Success',
          data: PaginatedData<PlayerSearchItemDto>(
            items: List.generate(
              100,
              (index) => PlayerSearchItemDto(
                id: 'player_$index',
                fullName: 'Player $index',
                age: 20 + (index % 20),
                heightCm: 170 + (index % 30),
                weightKg: 60 + (index % 40),
                email: 'player$<EMAIL>',
                phone: '+123456789$index',
                description: 'Player description $index',
                photoUrl: 'https://example.com/player$index.jpg',
                scoutingEnabled: index % 2 == 0,
                gameInfo: {'position': 'Position $index'},
              ),
            ),
            total: 1000,
            page: testPage,
            pageSize: 100,
            totalPages: 10,
          ),
        );

        when(
          mockPlayerDatasource.searchPlayers(
            query: testQuery,
            page: testPage,
            pageSize: 100,
          ),
        ).thenAnswer((_) async => largeDtoResponse);

        // Act
        final result = await playerRepository.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: 100,
        );

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: ${error.message}'),
          (response) {
            expect(response.data.items.length, equals(100));
            expect(response.data.total, equals(1000));
            expect(response.data.totalPages, equals(10));
          },
        );
      });

      test('should handle special characters in query', () async {
        // Arrange
        const specialQuery = 'João@special';

        when(
          mockPlayerDatasource.searchPlayers(
            query: specialQuery,
            page: testPage,
            pageSize: testPageSize,
          ),
        ).thenAnswer((_) async => mockDtoResponse);

        // Act
        final result = await playerRepository.searchPlayers(
          query: specialQuery,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result.isRight(), true);
        verify(
          mockPlayerDatasource.searchPlayers(
            query: specialQuery,
            page: testPage,
            pageSize: testPageSize,
          ),
        ).called(1);
      });

      test('should handle empty string query', () async {
        // Arrange
        const emptyQuery = '';

        when(
          mockPlayerDatasource.searchPlayers(
            query: emptyQuery,
            page: testPage,
            pageSize: testPageSize,
          ),
        ).thenAnswer((_) async => mockDtoResponse);

        // Act
        final result = await playerRepository.searchPlayers(
          query: emptyQuery,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result.isRight(), true);
        verify(
          mockPlayerDatasource.searchPlayers(
            query: emptyQuery,
            page: testPage,
            pageSize: testPageSize,
          ),
        ).called(1);
      });
    });
  });
}
