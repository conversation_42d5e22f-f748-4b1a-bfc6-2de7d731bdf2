import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dio/dio.dart';
import 'package:nextsportz_v2/core/networking/api_client.dart';
import 'package:nextsportz_v2/core/networking/api_const.dart';
import 'package:nextsportz_v2/core/networking/exception.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';
import 'package:nextsportz_v2/features/player/data/datasources/player_remote_datasource.dart';
import 'package:nextsportz_v2/features/player/data/dto/player_search_item_dto.dart';

import 'player_remote_datasource_test.mocks.dart';

@GenerateMocks([ApiClient])
void main() {
  late MockApiClient mockApiClient;
  late PlayerRemoteDataSource playerRemoteDataSource;

  setUp(() {
    mockApiClient = MockApiClient();
    playerRemoteDataSource = PlayerRemoteDataSource(mockApiClient);
  });

  group('PlayerRemoteDataSource - searchPlayers', () {
    const testQuery = 'john';
    const testPage = 1;
    const testPageSize = 20;

    final mockApiResponse = {
      'message': 'Success',
      'data': {
        'items': [
          {
            'id': 'player_1',
            'fullName': 'John Doe',
            'age': 25,
            'heightCm': 180,
            'weightKg': 75,
            'email': '<EMAIL>',
            'phone': '+1234567890',
            'description': 'Experienced midfielder',
            'photoUrl': 'https://example.com/john.jpg',
            'scoutingEnabled': true,
            'gameInfo': {'position': 'Midfielder', 'preferredFoot': 'Right'},
          },
          {
            'id': 'player_2',
            'fullName': 'John Smith',
            'age': 28,
            'heightCm': 175,
            'weightKg': 70,
            'email': '<EMAIL>',
            'phone': '+1234567891',
            'description': 'Skilled forward',
            'photoUrl': null,
            'scoutingEnabled': false,
            'gameInfo': {'position': 'Forward', 'preferredFoot': 'Left'},
          },
        ],
        'total': 2,
        'page': 1,
        'pageSize': 20,
        'totalPages': 1,
      },
    };

    group('Success scenarios', () {
      test('should return paginated players when API call succeeds', () async {
        // Arrange
        when(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {
              'search': testQuery,
              'page': testPage,
              'pageSize': testPageSize,
            },
          ),
        ).thenAnswer((_) async => mockApiResponse);

        // Act
        final result = await playerRemoteDataSource.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result, isA<PaginatedResponse<PlayerSearchItemDto>>());
        expect(result.data.items.length, equals(2));
        expect(result.data.items[0].fullName, equals('John Doe'));
        expect(result.data.items[1].fullName, equals('John Smith'));
        expect(result.data.total, equals(2));
        expect(result.data.page, equals(1));
        expect(result.data.pageSize, equals(20));
        expect(result.message, equals('Success'));

        verify(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {
              'search': testQuery,
              'page': testPage,
              'pageSize': testPageSize,
            },
          ),
        ).called(1);
      });

      test('should handle null query parameter', () async {
        // Arrange
        when(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {'page': testPage, 'pageSize': testPageSize},
          ),
        ).thenAnswer((_) async => mockApiResponse);

        // Act
        final result = await playerRemoteDataSource.searchPlayers(
          query: null,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result, isA<PaginatedResponse<PlayerSearchItemDto>>());
        verify(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {'page': testPage, 'pageSize': testPageSize},
          ),
        ).called(1);
      });

      test('should handle empty query parameter', () async {
        // Arrange
        when(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {'page': testPage, 'pageSize': testPageSize},
          ),
        ).thenAnswer((_) async => mockApiResponse);

        // Act
        final result = await playerRemoteDataSource.searchPlayers(
          query: '',
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result, isA<PaginatedResponse<PlayerSearchItemDto>>());
        verify(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {'page': testPage, 'pageSize': testPageSize},
          ),
        ).called(1);
      });

      test('should handle null page and pageSize parameters', () async {
        // Arrange
        when(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {'search': testQuery},
          ),
        ).thenAnswer((_) async => mockApiResponse);

        // Act
        final result = await playerRemoteDataSource.searchPlayers(
          query: testQuery,
          page: null,
          pageSize: null,
        );

        // Assert
        expect(result, isA<PaginatedResponse<PlayerSearchItemDto>>());
        verify(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {'search': testQuery},
          ),
        ).called(1);
      });

      test('should handle empty response', () async {
        // Arrange
        final emptyApiResponse = {
          'message': 'Success',
          'data': {
            'items': <Map<String, dynamic>>[],
            'total': 0,
            'page': 1,
            'pageSize': 20,
            'totalPages': 0,
          },
        };

        when(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {
              'search': testQuery,
              'page': testPage,
              'pageSize': testPageSize,
            },
          ),
        ).thenAnswer((_) async => emptyApiResponse);

        // Act
        final result = await playerRemoteDataSource.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result, isA<PaginatedResponse<PlayerSearchItemDto>>());
        expect(result.data.items.isEmpty, true);
        expect(result.data.total, equals(0));
      });

      test('should properly parse player data with all fields', () async {
        // Arrange
        when(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {
              'search': testQuery,
              'page': testPage,
              'pageSize': testPageSize,
            },
          ),
        ).thenAnswer((_) async => mockApiResponse);

        // Act
        final result = await playerRemoteDataSource.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        final firstPlayer = result.data.items[0];
        expect(firstPlayer.id, equals('player_1'));
        expect(firstPlayer.fullName, equals('John Doe'));
        expect(firstPlayer.age, equals(25));
        expect(firstPlayer.heightCm, equals(180));
        expect(firstPlayer.weightKg, equals(75));
        expect(firstPlayer.email, equals('<EMAIL>'));
        expect(firstPlayer.phone, equals('+1234567890'));
        expect(firstPlayer.description, equals('Experienced midfielder'));
        expect(firstPlayer.photoUrl, equals('https://example.com/john.jpg'));
        expect(firstPlayer.scoutingEnabled, equals(true));
        expect(firstPlayer.gameInfo, isNotNull);
        expect(firstPlayer.gameInfo!['position'], equals('Midfielder'));
      });

      test('should handle player data with null optional fields', () async {
        // Arrange
        when(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {
              'search': testQuery,
              'page': testPage,
              'pageSize': testPageSize,
            },
          ),
        ).thenAnswer((_) async => mockApiResponse);

        // Act
        final result = await playerRemoteDataSource.searchPlayers(
          query: testQuery,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        final secondPlayer = result.data.items[1];
        expect(secondPlayer.photoUrl, isNull);
        expect(secondPlayer.scoutingEnabled, equals(false));
      });
    });

    group('Error scenarios', () {
      test(
        'should throw DioExceptionHandle when DioException occurs',
        () async {
          // Arrange
          final dioException = DioException(
            requestOptions: RequestOptions(path: ApiConst.playersEndpoint),
            type: DioExceptionType.connectionTimeout,
            message: 'Connection timeout',
          );

          when(
            mockApiClient.get(
              ApiConst.playersEndpoint,
              query: {
                'search': testQuery,
                'page': testPage,
                'pageSize': testPageSize,
              },
            ),
          ).thenThrow(dioException);

          // Act & Assert
          expect(
            () => playerRemoteDataSource.searchPlayers(
              query: testQuery,
              page: testPage,
              pageSize: testPageSize,
            ),
            throwsA(isA<DioExceptionHandle>()),
          );
        },
      );

      test('should rethrow DioExceptionHandle when it occurs', () async {
        // Arrange
        final dioExceptionHandle = DioExceptionHandle.fromDioError(
          DioException(
            requestOptions: RequestOptions(path: ApiConst.playersEndpoint),
            type: DioExceptionType.connectionError,
          ),
        );

        when(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {
              'search': testQuery,
              'page': testPage,
              'pageSize': testPageSize,
            },
          ),
        ).thenThrow(dioExceptionHandle);

        // Act & Assert
        expect(
          () => playerRemoteDataSource.searchPlayers(
            query: testQuery,
            page: testPage,
            pageSize: testPageSize,
          ),
          throwsA(same(dioExceptionHandle)),
        );
      });

      test('should throw Exception when generic error occurs', () async {
        // Arrange
        const errorMessage = 'Unexpected error';
        final genericException = Exception(errorMessage);

        when(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {
              'search': testQuery,
              'page': testPage,
              'pageSize': testPageSize,
            },
          ),
        ).thenThrow(genericException);

        // Act & Assert
        expect(
          () => playerRemoteDataSource.searchPlayers(
            query: testQuery,
            page: testPage,
            pageSize: testPageSize,
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle malformed API response', () async {
        // Arrange
        final malformedResponse = {'invalid': 'response'};

        when(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {
              'search': testQuery,
              'page': testPage,
              'pageSize': testPageSize,
            },
          ),
        ).thenAnswer((_) async => malformedResponse);

        // Act & Assert
        expect(
          () => playerRemoteDataSource.searchPlayers(
            query: testQuery,
            page: testPage,
            pageSize: testPageSize,
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Edge cases', () {
      test('should handle special characters in query', () async {
        // Arrange
        const specialQuery = 'João@special';

        when(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {
              'search': specialQuery,
              'page': testPage,
              'pageSize': testPageSize,
            },
          ),
        ).thenAnswer((_) async => mockApiResponse);

        // Act
        final result = await playerRemoteDataSource.searchPlayers(
          query: specialQuery,
          page: testPage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result, isA<PaginatedResponse<PlayerSearchItemDto>>());
        verify(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {
              'search': specialQuery,
              'page': testPage,
              'pageSize': testPageSize,
            },
          ),
        ).called(1);
      });

      test('should handle large page numbers', () async {
        // Arrange
        const largePage = 999;

        when(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {
              'search': testQuery,
              'page': largePage,
              'pageSize': testPageSize,
            },
          ),
        ).thenAnswer((_) async => mockApiResponse);

        // Act
        final result = await playerRemoteDataSource.searchPlayers(
          query: testQuery,
          page: largePage,
          pageSize: testPageSize,
        );

        // Assert
        expect(result, isA<PaginatedResponse<PlayerSearchItemDto>>());
        verify(
          mockApiClient.get(
            ApiConst.playersEndpoint,
            query: {
              'search': testQuery,
              'page': largePage,
              'pageSize': testPageSize,
            },
          ),
        ).called(1);
      });
    });
  });
}
