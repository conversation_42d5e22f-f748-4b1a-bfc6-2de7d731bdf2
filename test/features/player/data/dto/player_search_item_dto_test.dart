import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/player/data/dto/player_search_item_dto.dart';
import 'package:nextsportz_v2/features/player/domain/entities/player_search_item.dart';

void main() {
  group('PlayerSearchItemDto', () {
    const testDto = PlayerSearchItemDto(
      id: 'player_123',
      fullName: '<PERSON>',
      age: 25,
      heightCm: 180,
      weightKg: 75,
      email: '<EMAIL>',
      phone: '+1234567890',
      description: 'Experienced midfielder',
      photoUrl: 'https://example.com/john.jpg',
      scoutingEnabled: true,
      gameInfo: {'position': 'Midfielder', 'preferredFoot': 'Right'},
    );

    final testJson = {
      'id': 'player_123',
      'fullName': '<PERSON>',
      'age': 25,
      'heightCm': 180,
      'weightKg': 75,
      'email': '<EMAIL>',
      'phone': '+1234567890',
      'description': 'Experienced midfielder',
      'photoUrl': 'https://example.com/john.jpg',
      'scoutingEnabled': true,
      'gameInfo': {'position': 'Midfielder', 'preferredFoot': 'Right'},
    };

    const testEntity = PlayerSearchItem(
      id: 'player_123',
      fullName: 'John Doe',
      age: 25,
      heightCm: 180,
      weightKg: 75,
      email: '<EMAIL>',
      phone: '+1234567890',
      description: 'Experienced midfielder',
      photoUrl: 'https://example.com/john.jpg',
      scoutingEnabled: true,
      gameInfo: {'position': 'Midfielder', 'preferredFoot': 'Right'},
    );

    group('fromJson', () {
      test('should create DTO from complete JSON', () {
        // Act
        final dto = PlayerSearchItemDto.fromJson(testJson);

        // Assert
        expect(dto.id, equals('player_123'));
        expect(dto.fullName, equals('John Doe'));
        expect(dto.age, equals(25));
        expect(dto.heightCm, equals(180));
        expect(dto.weightKg, equals(75));
        expect(dto.email, equals('<EMAIL>'));
        expect(dto.phone, equals('+1234567890'));
        expect(dto.description, equals('Experienced midfielder'));
        expect(dto.photoUrl, equals('https://example.com/john.jpg'));
        expect(dto.scoutingEnabled, equals(true));
        expect(dto.gameInfo, isNotNull);
        expect(dto.gameInfo!['position'], equals('Midfielder'));
        expect(dto.gameInfo!['preferredFoot'], equals('Right'));
      });

      test('should handle missing optional fields in JSON', () {
        // Arrange
        final minimalJson = {
          'id': 'player_456',
          'fullName': 'Jane Smith',
          'email': '<EMAIL>',
          'phone': '+1234567891',
        };

        // Act
        final dto = PlayerSearchItemDto.fromJson(minimalJson);

        // Assert
        expect(dto.id, equals('player_456'));
        expect(dto.fullName, equals('Jane Smith'));
        expect(dto.email, equals('<EMAIL>'));
        expect(dto.phone, equals('+1234567891'));
        expect(dto.age, isNull);
        expect(dto.heightCm, isNull);
        expect(dto.weightKg, isNull);
        expect(dto.description, isNull);
        expect(dto.photoUrl, isNull);
        expect(dto.scoutingEnabled, equals(false)); // Default value
        expect(dto.gameInfo, isNull);
      });

      test('should handle null values in JSON', () {
        // Arrange
        final jsonWithNulls = {
          'id': 'player_789',
          'fullName': 'Bob Johnson',
          'age': null,
          'heightCm': null,
          'weightKg': null,
          'email': '<EMAIL>',
          'phone': '+1234567892',
          'description': null,
          'photoUrl': null,
          'scoutingEnabled': null,
          'gameInfo': null,
        };

        // Act
        final dto = PlayerSearchItemDto.fromJson(jsonWithNulls);

        // Assert
        expect(dto.id, equals('player_789'));
        expect(dto.fullName, equals('Bob Johnson'));
        expect(dto.age, isNull);
        expect(dto.heightCm, isNull);
        expect(dto.weightKg, isNull);
        expect(dto.email, equals('<EMAIL>'));
        expect(dto.phone, equals('+1234567892'));
        expect(dto.description, isNull);
        expect(dto.photoUrl, isNull);
        expect(dto.scoutingEnabled, equals(false)); // Default when null
        expect(dto.gameInfo, isNull);
      });

      test('should handle empty strings in JSON', () {
        // Arrange
        final jsonWithEmptyStrings = {
          'id': '',
          'fullName': '',
          'email': '',
          'phone': '',
          'description': '',
          'photoUrl': '',
          'scoutingEnabled': false,
        };

        // Act
        final dto = PlayerSearchItemDto.fromJson(jsonWithEmptyStrings);

        // Assert
        expect(dto.id, equals(''));
        expect(dto.fullName, equals(''));
        expect(dto.email, equals(''));
        expect(dto.phone, equals(''));
        expect(dto.description, equals(''));
        expect(dto.photoUrl, equals(''));
        expect(dto.scoutingEnabled, equals(false));
      });

      test('should handle complex gameInfo in JSON', () {
        // Arrange
        final complexGameInfo = {
          'position': 'Goalkeeper',
          'preferredFoot': 'Right',
          'skills': ['Reflexes', 'Distribution', 'Command'],
          'stats': {
            'saves': 45,
            'cleanSheets': 12,
            'matches': 20,
          },
          'isInjured': false,
        };

        final jsonWithComplexGameInfo = {
          'id': 'player_complex',
          'fullName': 'Complex Player',
          'email': '<EMAIL>',
          'phone': '+1234567893',
          'scoutingEnabled': true,
          'gameInfo': complexGameInfo,
        };

        // Act
        final dto = PlayerSearchItemDto.fromJson(jsonWithComplexGameInfo);

        // Assert
        expect(dto.gameInfo, equals(complexGameInfo));
        expect(dto.gameInfo!['position'], equals('Goalkeeper'));
        expect(dto.gameInfo!['skills'], isA<List>());
        expect(dto.gameInfo!['stats'], isA<Map>());
      });
    });

    group('toJson', () {
      test('should convert DTO to complete JSON', () {
        // Act
        final json = testDto.toJson();

        // Assert
        expect(json['id'], equals('player_123'));
        expect(json['fullName'], equals('John Doe'));
        expect(json['age'], equals(25));
        expect(json['heightCm'], equals(180));
        expect(json['weightKg'], equals(75));
        expect(json['email'], equals('<EMAIL>'));
        expect(json['phone'], equals('+1234567890'));
        expect(json['description'], equals('Experienced midfielder'));
        expect(json['photoUrl'], equals('https://example.com/john.jpg'));
        expect(json['scoutingEnabled'], equals(true));
        expect(json['gameInfo'], isNotNull);
        expect(json['gameInfo']['position'], equals('Midfielder'));
        expect(json['gameInfo']['preferredFoot'], equals('Right'));
      });

      test('should handle null values in toJson', () {
        // Arrange
        const dtoWithNulls = PlayerSearchItemDto(
          id: 'player_null',
          fullName: 'Null Player',
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: false,
        );

        // Act
        final json = dtoWithNulls.toJson();

        // Assert
        expect(json['id'], equals('player_null'));
        expect(json['fullName'], equals('Null Player'));
        expect(json['age'], isNull);
        expect(json['heightCm'], isNull);
        expect(json['weightKg'], isNull);
        expect(json['email'], equals('<EMAIL>'));
        expect(json['phone'], equals('+1234567890'));
        expect(json['description'], isNull);
        expect(json['photoUrl'], isNull);
        expect(json['scoutingEnabled'], equals(false));
        expect(json['gameInfo'], isNull);
      });

      test('should preserve complex gameInfo in toJson', () {
        // Arrange
        const complexGameInfo = {
          'position': 'Defender',
          'preferredFoot': 'Left',
          'skills': ['Tackling', 'Heading', 'Positioning'],
          'stats': {
            'tackles': 85,
            'interceptions': 42,
            'clearances': 67,
          },
        };

        const dtoWithComplexGameInfo = PlayerSearchItemDto(
          id: 'player_defender',
          fullName: 'Defender Player',
          email: '<EMAIL>',
          phone: '+1234567894',
          scoutingEnabled: true,
          gameInfo: complexGameInfo,
        );

        // Act
        final json = dtoWithComplexGameInfo.toJson();

        // Assert
        expect(json['gameInfo'], equals(complexGameInfo));
        expect(json['gameInfo']['position'], equals('Defender'));
        expect(json['gameInfo']['skills'], isA<List>());
        expect(json['gameInfo']['stats'], isA<Map>());
      });
    });

    group('toEntity', () {
      test('should convert DTO to entity with all fields', () {
        // Act
        final entity = testDto.toEntity();

        // Assert
        expect(entity, isA<PlayerSearchItem>());
        expect(entity.id, equals(testDto.id));
        expect(entity.fullName, equals(testDto.fullName));
        expect(entity.age, equals(testDto.age));
        expect(entity.heightCm, equals(testDto.heightCm));
        expect(entity.weightKg, equals(testDto.weightKg));
        expect(entity.email, equals(testDto.email));
        expect(entity.phone, equals(testDto.phone));
        expect(entity.description, equals(testDto.description));
        expect(entity.photoUrl, equals(testDto.photoUrl));
        expect(entity.scoutingEnabled, equals(testDto.scoutingEnabled));
        expect(entity.gameInfo, equals(testDto.gameInfo));
      });

      test('should convert DTO with null values to entity', () {
        // Arrange
        const dtoWithNulls = PlayerSearchItemDto(
          id: 'player_null',
          fullName: 'Null Player',
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: false,
        );

        // Act
        final entity = dtoWithNulls.toEntity();

        // Assert
        expect(entity.id, equals('player_null'));
        expect(entity.fullName, equals('Null Player'));
        expect(entity.age, isNull);
        expect(entity.heightCm, isNull);
        expect(entity.weightKg, isNull);
        expect(entity.email, equals('<EMAIL>'));
        expect(entity.phone, equals('+1234567890'));
        expect(entity.description, isNull);
        expect(entity.photoUrl, isNull);
        expect(entity.scoutingEnabled, equals(false));
        expect(entity.gameInfo, isNull);
      });
    });

    group('fromEntity', () {
      test('should create DTO from entity with all fields', () {
        // Act
        final dto = PlayerSearchItemDto.fromEntity(testEntity);

        // Assert
        expect(dto, isA<PlayerSearchItemDto>());
        expect(dto.id, equals(testEntity.id));
        expect(dto.fullName, equals(testEntity.fullName));
        expect(dto.age, equals(testEntity.age));
        expect(dto.heightCm, equals(testEntity.heightCm));
        expect(dto.weightKg, equals(testEntity.weightKg));
        expect(dto.email, equals(testEntity.email));
        expect(dto.phone, equals(testEntity.phone));
        expect(dto.description, equals(testEntity.description));
        expect(dto.photoUrl, equals(testEntity.photoUrl));
        expect(dto.scoutingEnabled, equals(testEntity.scoutingEnabled));
        expect(dto.gameInfo, equals(testEntity.gameInfo));
      });

      test('should create DTO from entity with null values', () {
        // Arrange
        const entityWithNulls = PlayerSearchItem(
          id: 'player_null',
          fullName: 'Null Player',
          email: '<EMAIL>',
          phone: '+1234567890',
          scoutingEnabled: false,
        );

        // Act
        final dto = PlayerSearchItemDto.fromEntity(entityWithNulls);

        // Assert
        expect(dto.id, equals('player_null'));
        expect(dto.fullName, equals('Null Player'));
        expect(dto.age, isNull);
        expect(dto.heightCm, isNull);
        expect(dto.weightKg, isNull);
        expect(dto.email, equals('<EMAIL>'));
        expect(dto.phone, equals('+1234567890'));
        expect(dto.description, isNull);
        expect(dto.photoUrl, isNull);
        expect(dto.scoutingEnabled, equals(false));
        expect(dto.gameInfo, isNull);
      });
    });

    group('Round-trip conversion', () {
      test('should maintain data integrity through JSON round-trip', () {
        // Act
        final json = testDto.toJson();
        final recreatedDto = PlayerSearchItemDto.fromJson(json);

        // Assert
        expect(recreatedDto.id, equals(testDto.id));
        expect(recreatedDto.fullName, equals(testDto.fullName));
        expect(recreatedDto.age, equals(testDto.age));
        expect(recreatedDto.heightCm, equals(testDto.heightCm));
        expect(recreatedDto.weightKg, equals(testDto.weightKg));
        expect(recreatedDto.email, equals(testDto.email));
        expect(recreatedDto.phone, equals(testDto.phone));
        expect(recreatedDto.description, equals(testDto.description));
        expect(recreatedDto.photoUrl, equals(testDto.photoUrl));
        expect(recreatedDto.scoutingEnabled, equals(testDto.scoutingEnabled));
        expect(recreatedDto.gameInfo, equals(testDto.gameInfo));
      });

      test('should maintain data integrity through entity round-trip', () {
        // Act
        final entity = testDto.toEntity();
        final recreatedDto = PlayerSearchItemDto.fromEntity(entity);

        // Assert
        expect(recreatedDto.id, equals(testDto.id));
        expect(recreatedDto.fullName, equals(testDto.fullName));
        expect(recreatedDto.age, equals(testDto.age));
        expect(recreatedDto.heightCm, equals(testDto.heightCm));
        expect(recreatedDto.weightKg, equals(testDto.weightKg));
        expect(recreatedDto.email, equals(testDto.email));
        expect(recreatedDto.phone, equals(testDto.phone));
        expect(recreatedDto.description, equals(testDto.description));
        expect(recreatedDto.photoUrl, equals(testDto.photoUrl));
        expect(recreatedDto.scoutingEnabled, equals(testDto.scoutingEnabled));
        expect(recreatedDto.gameInfo, equals(testDto.gameInfo));
      });

      test('should maintain data integrity through complete round-trip', () {
        // Act
        final json = testDto.toJson();
        final recreatedDto = PlayerSearchItemDto.fromJson(json);
        final entity = recreatedDto.toEntity();
        final finalDto = PlayerSearchItemDto.fromEntity(entity);

        // Assert
        expect(finalDto.id, equals(testDto.id));
        expect(finalDto.fullName, equals(testDto.fullName));
        expect(finalDto.age, equals(testDto.age));
        expect(finalDto.heightCm, equals(testDto.heightCm));
        expect(finalDto.weightKg, equals(testDto.weightKg));
        expect(finalDto.email, equals(testDto.email));
        expect(finalDto.phone, equals(testDto.phone));
        expect(finalDto.description, equals(testDto.description));
        expect(finalDto.photoUrl, equals(testDto.photoUrl));
        expect(finalDto.scoutingEnabled, equals(testDto.scoutingEnabled));
        expect(finalDto.gameInfo, equals(testDto.gameInfo));
      });
    });
  });
}
