import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/tournaments/domain/entities/tournament.dart';

void main() {
  group('Tournament Entity Tests', () {
    late Tournament tournament;

    setUp(() {
      tournament = Tournament(
        id: 'test_tournament_1',
        name: 'Test Championship',
        description: 'A test tournament',
        format: const TournamentFormat(
          type: TournamentType.fiveVsFive,
          playersPerTeam: 5,
          displayName: '5v5',
        ),
        location: 'Test Stadium',
        city: 'Test City',
        startDate: DateTime.now().add(const Duration(days: 30)),
        endDate: DateTime.now().add(const Duration(days: 31)),
        registrationDeadline: DateTime.now().add(const Duration(days: 25)),
        maxParticipants: 16,
        currentParticipants: 8,
        entryFee: 1000.0,
        currency: 'NPR',
        prize: const TournamentPrize(
          totalAmount: 10000.0,
          currency: 'NPR',
          distribution: [
            PrizeDistribution(
              position: 1,
              amount: 5000.0,
              description: 'Winner',
            ),
            PrizeDistribution(
              position: 2,
              amount: 3000.0,
              description: 'Runner-up',
            ),
            PrizeDistribution(
              position: 3,
              amount: 2000.0,
              description: 'Third Place',
            ),
          ],
        ),
        organizerId: 'org_1',
        organizerName: 'Test Organizer',
        status: TournamentStatus.registrationOpen,
        rules: ['Rule 1', 'Rule 2'],
        tags: ['football', '5v5'],
        bannerImage: 'test_banner.jpg',
        participants: [],
        venueDetails: const TournamentLocation(
          name: 'Test Stadium',
          address: 'Test Address',
          latitude: 27.7172,
          longitude: 85.3240,
        ),
        createdAt: DateTime(2024, 5, 1),
        updatedAt: DateTime(2024, 5, 2),
      );
    });

    test('should create tournament with required properties', () {
      expect(tournament.id, 'test_tournament_1');
      expect(tournament.name, 'Test Championship');
      expect(tournament.maxParticipants, 16);
      expect(tournament.currentParticipants, 8);
    });

    test('should calculate available slots correctly', () {
      expect(tournament.availableSlots, 8);
      expect(tournament.hasAvailableSlots, true);
    });

    test(
      'should return false for hasAvailableSlots when tournament is full',
      () {
        final fullTournament = tournament.copyWith(currentParticipants: 16);
        expect(fullTournament.hasAvailableSlots, false);
        expect(fullTournament.availableSlots, 0);
      },
    );

    test('should correctly identify registration status', () {
      // Set current time before registration deadline
      final now = DateTime(2024, 6, 5);
      expect(tournament.status, TournamentStatus.registrationOpen);

      // Registration should be open if deadline hasn't passed
      expect(tournament.registrationDeadline.isAfter(now), true);
    });

    test('should correctly identify tournament phases', () {
      // Tournament starts in June 2024, so it should be upcoming
      expect(tournament.isUpcoming, true);
      expect(tournament.isOngoing, false);
      expect(tournament.isCompleted, false);
    });

    test('should provide correct status display text', () {
      // Since registration deadline is in the future, it should show "Registration Open"
      expect(tournament.statusDisplayText, 'Registration Open');

      final inProgressTournament = tournament.copyWith(
        status: TournamentStatus.inProgress,
      );
      expect(inProgressTournament.statusDisplayText, 'In Progress');
    });

    group('TournamentType Extension Tests', () {
      test('should return correct display names', () {
        expect(TournamentType.oneVsOne.displayName, '1v1');
        expect(TournamentType.fiveVsFive.displayName, '5v5');
        expect(TournamentType.elevenVsEleven.displayName, '11v11');
      });

      test('should return correct players per team', () {
        expect(TournamentType.oneVsOne.playersPerTeam, 1);
        expect(TournamentType.fiveVsFive.playersPerTeam, 5);
        expect(TournamentType.elevenVsEleven.playersPerTeam, 11);
      });
    });

    group('Tournament copyWith Tests', () {
      test('should create new tournament with updated values', () {
        final updatedTournament = tournament.copyWith(
          name: 'Updated Championship',
          currentParticipants: 10,
        );

        expect(updatedTournament.name, 'Updated Championship');
        expect(updatedTournament.currentParticipants, 10);
        expect(updatedTournament.id, tournament.id); // Should remain unchanged
      });
    });
  });

  group('TournamentFormat Tests', () {
    test('should create tournament format correctly', () {
      const format = TournamentFormat(
        type: TournamentType.sevenVsSeven,
        playersPerTeam: 7,
        displayName: '7v7',
      );

      expect(format.type, TournamentType.sevenVsSeven);
      expect(format.playersPerTeam, 7);
      expect(format.displayName, '7v7');
      expect(format.displayName, '7v7');
    });
  });

  group('TournamentPrize Tests', () {
    test('should create tournament prize with distribution', () {
      const prize = TournamentPrize(
        totalAmount: 15000.0,
        currency: 'NPR',
        distribution: [
          PrizeDistribution(position: 1, amount: 7500.0, description: 'Winner'),
          PrizeDistribution(
            position: 2,
            amount: 4500.0,
            description: 'Runner-up',
          ),
          PrizeDistribution(
            position: 3,
            amount: 3000.0,
            description: 'Third Place',
          ),
        ],
      );

      expect(prize.totalAmount, 15000.0);
      expect(prize.distribution.length, 3);
      expect(prize.currency, 'NPR');
    });
  });

  group('TournamentParticipant Tests', () {
    test('should create tournament participant correctly', () {
      final participant = TournamentParticipant(
        playerId: 'player_1',
        playerName: 'John Doe',
        playerAvatar: 'avatar.png',
        teamId: 'team_1',
        teamName: 'Test Team',
        joinedAt: DateTime(2024, 5, 15),
        status: TournamentParticipantStatus.confirmed,
      );

      expect(participant.playerId, 'player_1');
      expect(participant.playerName, 'John Doe');
      expect(participant.status, TournamentParticipantStatus.confirmed);
    });
  });
}
