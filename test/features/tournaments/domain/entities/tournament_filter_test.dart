import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/tournaments/domain/entities/tournament.dart';
import 'package:nextsportz_v2/features/tournaments/domain/entities/tournament_filter.dart';

void main() {
  group('TournamentFilter Tests', () {
    test('should create empty filter by default', () {
      const filter = TournamentFilter();

      expect(filter.formats, isEmpty);
      expect(filter.locations, isEmpty);
      expect(filter.dateRange, isNull);
      expect(filter.entryFeeRange, isNull);
      expect(filter.prizeRange, isNull);
      expect(filter.availableSlotsOnly, false);
      expect(filter.statuses, isEmpty);
      expect(filter.organizers, isEmpty);
      expect(filter.sortBy, TournamentSortBy.soonestFirst);
      expect(filter.sortOrder, SortOrder.ascending);
    });

    test('should identify empty filter correctly', () {
      const emptyFilter = TournamentFilter();
      expect(emptyFilter.isEmpty, true);
      expect(emptyFilter.hasActiveFilters, false);
      expect(emptyFilter.activeFilterCount, 0);
    });

    test('should identify non-empty filter correctly', () {
      const filter = TournamentFilter(
        formats: [TournamentType.fiveVsFive],
        locations: ['Kathmandu'],
        availableSlotsOnly: true,
      );

      expect(filter.isEmpty, false);
      expect(filter.hasActiveFilters, true);
      expect(filter.activeFilterCount, 3);
    });

    test('should clear all filters correctly', () {
      const filter = TournamentFilter(
        formats: [TournamentType.fiveVsFive],
        locations: ['Kathmandu'],
        availableSlotsOnly: true,
      );

      final clearedFilter = filter.clearAll();
      expect(clearedFilter.isEmpty, true);
      expect(clearedFilter.formats, isEmpty);
      expect(clearedFilter.locations, isEmpty);
      expect(clearedFilter.availableSlotsOnly, false);
    });

    test('should clear specific filters correctly', () {
      const filter = TournamentFilter(
        formats: [TournamentType.fiveVsFive],
        locations: ['Kathmandu'],
        availableSlotsOnly: true,
      );

      final clearedFormats = filter.clearFormats();
      expect(clearedFormats.formats, isEmpty);
      expect(clearedFormats.locations, ['Kathmandu']); // Should remain
      expect(clearedFormats.availableSlotsOnly, true); // Should remain

      final clearedLocations = filter.clearLocations();
      expect(clearedLocations.locations, isEmpty);
      expect(clearedLocations.formats, [
        TournamentType.fiveVsFive,
      ]); // Should remain

      final clearedAvailableSlots = filter.clearAvailableSlots();
      expect(clearedAvailableSlots.availableSlotsOnly, false);
    });
  });

  group('DateRangeFilter Tests', () {
    test('should create date range filter', () {
      final startDate = DateTime(2024, 6, 1);
      final endDate = DateTime(2024, 6, 30);

      final dateRange = DateRangeFilter(startDate: startDate, endDate: endDate);

      expect(dateRange.startDate, startDate);
      expect(dateRange.endDate, endDate);
    });

    test('should allow null dates', () {
      const dateRange = DateRangeFilter();

      expect(dateRange.startDate, isNull);
      expect(dateRange.endDate, isNull);
    });
  });

  group('PriceRangeFilter Tests', () {
    test('should create price range filter', () {
      const priceRange = PriceRangeFilter(minAmount: 100.0, maxAmount: 1000.0);

      expect(priceRange.minAmount, 100.0);
      expect(priceRange.maxAmount, 1000.0);
    });

    test('should allow null amounts', () {
      const priceRange = PriceRangeFilter();

      expect(priceRange.minAmount, isNull);
      expect(priceRange.maxAmount, isNull);
    });
  });

  group('TournamentSearchQuery Tests', () {
    test('should create search query with defaults', () {
      const query = TournamentSearchQuery();

      expect(query.searchTerm, '');
      expect(query.filter, const TournamentFilter());
      expect(query.limit, 20);
      expect(query.offset, 0);
    });

    test('should create search query with custom values', () {
      const filter = TournamentFilter(formats: [TournamentType.fiveVsFive]);
      const query = TournamentSearchQuery(
        searchTerm: 'football',
        filter: filter,
        limit: 10,
        offset: 20,
      );

      expect(query.searchTerm, 'football');
      expect(query.filter, filter);
      expect(query.limit, 10);
      expect(query.offset, 20);
    });
  });

  group('TournamentSearchResult Tests', () {
    test('should create search result correctly', () {
      const query = TournamentSearchQuery(searchTerm: 'test');
      final result = TournamentSearchResult(
        tournaments: [],
        totalCount: 50,
        hasMore: true,
        currentPage: 1,
        query: query,
      );

      expect(result.tournaments, isEmpty);
      expect(result.totalCount, 50);
      expect(result.hasMore, true);
      expect(result.currentPage, 1);
      expect(result.query, query);
    });
  });

  group('TournamentSortBy Extension Tests', () {
    test('should return correct display names', () {
      expect(TournamentSortBy.soonestFirst.displayName, 'Soonest First');
      expect(TournamentSortBy.lowestFee.displayName, 'Lowest Fee');
      expect(TournamentSortBy.highestPrize.displayName, 'Highest Prize');
      expect(TournamentSortBy.mostPopular.displayName, 'Most Popular');
    });

    test('should return correct descriptions', () {
      expect(
        TournamentSortBy.soonestFirst.description,
        'Show tournaments starting soon first',
      );
      expect(
        TournamentSortBy.lowestFee.description,
        'Show cheapest tournaments first',
      );
      expect(
        TournamentSortBy.nearestLocation.description,
        'Show nearest tournaments first',
      );
    });
  });
}
