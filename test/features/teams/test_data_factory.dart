import 'package:nextsportz_v2/core/models/paginated_response.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';
import 'package:nextsportz_v2/features/teams/data/dto/team_dto.dart';
import 'package:nextsportz_v2/features/teams/data/dto/team_search_item_dto.dart';

/// Test data factory for creating test objects related to teams
class TeamsTestDataFactory {
  /// Creates a test TeamSearchItem entity
  static TeamSearchItem createTestTeamSearchItem({
    String id = '1',
    String name = 'Test Team',
    int membersCount = 10,
    String winRate = '75.00',
    String logoUrl = 'https://example.com/logo.png',
  }) {
    return TeamSearchItem(
      id: id,
      name: name,
      membersCount: membersCount,
      winRate: winRate,
      logoUrl: logoUrl,
    );
  }

  /// Creates a list of test TeamSearchItem entities
  static List<TeamSearchItem> createTestTeamSearchItems({
    int count = 5,
    String namePrefix = 'Team',
  }) {
    return List.generate(count, (index) {
      return createTestTeamSearchItem(
        id: '${index + 1}',
        name: '$namePrefix ${index + 1}',
        membersCount: 10 + index,
        winRate: '${70 + index * 5}.00',
        logoUrl: index % 2 == 0 ? 'https://example.com/logo$index.png' : '',
      );
    });
  }

  /// Creates a test TeamSearchItemDto
  static TeamSearchItemDto createTestTeamSearchItemDto({
    String id = '1',
    String name = 'Test Team',
    int membersCount = 10,
    String winRate = '75.00',
    String logoUrl = 'https://example.com/logo.png',
  }) {
    return TeamSearchItemDto(
      id: id,
      name: name,
      membersCount: membersCount,
      winRate: winRate,
      logoUrl: logoUrl,
    );
  }

  /// Creates a test Team entity
  static Team createTestTeam({
    String id = '1',
    String teamCode = 'TC001',
    String name = 'Test Team',
    String description = 'A test team',
    String? slogan = 'Test slogan',
    String logoUrl = 'https://example.com/logo.png',
    bool isActive = true,
    List<TeamMember>? members,
    DateTime? created,
    TeamStats? stats,
  }) {
    return Team(
      id: id,
      teamCode: teamCode,
      name: name,
      description: description,
      slogan: slogan,
      logoUrl: logoUrl,
      isActive: isActive,
      members: members ?? [createTestTeamMember()],
      created: created ?? DateTime.parse('2023-01-01T00:00:00Z'),
      stats: stats ?? createTestTeamStats(),
    );
  }

  /// Creates a test TeamMember
  static TeamMember createTestTeamMember({
    String id = 'member1',
    String userId = 'user1',
    String userName = 'John Doe',
    String playerId = 'player1',
    String playerName = 'John Doe',
    String role = 'Player',
    DateTime? joinedAt,
    bool isActive = true,
  }) {
    return TeamMember(
      id: id,
      userId: userId,
      userName: userName,
      playerId: playerId,
      playerName: playerName,
      role: role,
      joinedAt: joinedAt ?? DateTime.parse('2023-01-01T00:00:00Z'),
      isActive: isActive,
    );
  }

  /// Creates a test TeamStats
  static TeamStats createTestTeamStats({
    int wins = 7,
    int losses = 2,
    int draws = 1,
    int scoredGoals = 25,
    int concededGoals = 10,
  }) {
    return TeamStats(
      wins: wins,
      losses: losses,
      draws: draws,
      scoredGoals: scoredGoals,
      concededGoals: concededGoals,
    );
  }

  /// Creates a test PlayerStats
  static PlayerStats createTestPlayerStats({
    int matchesPlayed = 10,
    int goals = 5,
    int assists = 3,
    int cleanSheets = 2,
    double rating = 4.5,
    int yellowCards = 1,
    int redCards = 0,
    int minutesPlayed = 900,
  }) {
    return PlayerStats(
      matchesPlayed: matchesPlayed,
      goals: goals,
      assists: assists,
      cleanSheets: cleanSheets,
      rating: rating,
      yellowCards: yellowCards,
      redCards: redCards,
      minutesPlayed: minutesPlayed,
    );
  }

  /// Creates a test PaginatedResponse for TeamSearchItem
  static PaginatedResponse<TeamSearchItem> createTestPaginatedResponse({
    List<TeamSearchItem>? items,
    int total = 50,
    int page = 1,
    int pageSize = 20,
    int? totalPages,
    String message = 'Success',
  }) {
    final responseItems = items ?? createTestTeamSearchItems(count: pageSize);
    final calculatedTotalPages = totalPages ?? (total / pageSize).ceil();

    return PaginatedResponse<TeamSearchItem>(
      message: message,
      data: PaginatedData<TeamSearchItem>(
        items: responseItems,
        total: total,
        page: page,
        pageSize: pageSize,
        totalPages: calculatedTotalPages,
      ),
    );
  }

  /// Creates test JSON data for API responses
  static Map<String, dynamic> createTestTeamJson({
    String id = '1',
    String name = 'Test Team',
    String description = 'A test team',
    String? logo = 'https://example.com/logo.png',
    String slogan = 'Test slogan',
    String createdBy = 'user1',
    String createdAt = '2023-01-01T00:00:00Z',
    String updatedAt = '2023-01-01T00:00:00Z',
    bool isActive = true,
    List<Map<String, dynamic>>? members,
    List<Map<String, dynamic>>? invitations,
    Map<String, dynamic>? stats,
  }) {
    return {
      'id': id,
      'name': name,
      'description': description,
      'logo': logo,
      'slogan': slogan,
      'created_by': createdBy,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'is_active': isActive,
      'members': members ?? [createTestTeamMemberJson()],
      'invitations': invitations ?? [],
      'stats': stats ?? createTestTeamStatsJson(),
    };
  }

  /// Creates test JSON data for TeamMember
  static Map<String, dynamic> createTestTeamMemberJson({
    String id = 'member1',
    String userId = 'user1',
    String userName = 'John Doe',
    String userEmail = '<EMAIL>',
    String userPhone = '1234567890',
    String role = 'MEMBER',
    String joinedAt = '2023-01-01T00:00:00Z',
  }) {
    return {
      'id': id,
      'user_id': userId,
      'user_name': userName,
      'user_email': userEmail,
      'user_phone': userPhone,
      'role': role,
      'joined_at': joinedAt,
    };
  }

  /// Creates test JSON data for TeamStats
  static Map<String, dynamic> createTestTeamStatsJson({
    int totalMatches = 10,
    int wins = 7,
    int losses = 2,
    int draws = 1,
    double winRate = 0.7,
    int totalGoals = 25,
    int goalsConceded = 10,
    int cleanSheets = 3,
    double averageGoalsPerMatch = 2.5,
  }) {
    return {
      'total_matches': totalMatches,
      'wins': wins,
      'losses': losses,
      'draws': draws,
      'win_rate': winRate,
      'total_goals': totalGoals,
      'goals_conceded': goalsConceded,
      'clean_sheets': cleanSheets,
      'average_goals_per_match': averageGoalsPerMatch,
    };
  }

  /// Creates test JSON data for TeamSearchItem
  static Map<String, dynamic> createTestTeamSearchItemJson({
    String id = '1',
    String name = 'Test Team',
    int membersCount = 10,
    String winRate = '75.00',
    String logoUrl = 'https://example.com/logo.png',
  }) {
    return {
      'id': id,
      'name': name,
      'membersCount': membersCount,
      'winRate': winRate,
      'logoUrl': logoUrl,
    };
  }

  /// Creates test JSON data for paginated response
  static Map<String, dynamic> createTestPaginatedResponseJson({
    List<Map<String, dynamic>>? items,
    int total = 50,
    int page = 1,
    int pageSize = 20,
    int? totalPages,
    String message = 'Success',
  }) {
    final responseItems =
        items ??
        List.generate(
          pageSize,
          (index) => createTestTeamSearchItemJson(
            id: '${index + 1}',
            name: 'Team ${index + 1}',
          ),
        );
    final calculatedTotalPages = totalPages ?? (total / pageSize).ceil();

    return {
      'message': message,
      'data': {
        'items': responseItems,
        'total': total,
        'page': page,
        'pageSize': pageSize,
        'totalPages': calculatedTotalPages,
      },
      'error': null,
      'validationErrors': null,
    };
  }

  /// Creates test error response JSON
  static Map<String, dynamic> createTestErrorResponseJson({
    String message = 'Error',
    String error = 'Something went wrong',
    dynamic validationErrors,
  }) {
    return {
      'message': message,
      'data': null,
      'error': error,
      'validationErrors': validationErrors,
    };
  }
}
