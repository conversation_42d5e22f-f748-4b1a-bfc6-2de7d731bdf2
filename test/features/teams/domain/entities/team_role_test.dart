import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team_role.dart';

void main() {
  group('TeamRole', () {
    test('should have correct values', () {
      expect(TeamRole.player.value, 'Player');
      expect(TeamRole.captain.value, 'Captain');
    });

    test('should create TeamRole from string value', () {
      expect(TeamRole.fromString('Player'), TeamRole.player);
      expect(TeamRole.fromString('Captain'), TeamRole.captain);
    });

    test('should throw ArgumentError for invalid string value', () {
      expect(
        () => TeamRole.fromString('InvalidRole'),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('should convert TeamRole to string value', () {
      expect(TeamRole.player.toString(), 'Player');
      expect(TeamRole.captain.toString(), 'Captain');
    });

    test('should return all available team roles', () {
      final values = TeamRole.values;
      expect(values, contains(TeamRole.player));
      expect(values, contains(TeamRole.captain));
      expect(values.length, 2);
    });

    test('should return display names for all roles', () {
      final displayNames = TeamRole.displayNames;
      expect(displayNames, contains('Player'));
      expect(displayNames, contains('Captain'));
      expect(displayNames.length, 2);
    });
  });
}
