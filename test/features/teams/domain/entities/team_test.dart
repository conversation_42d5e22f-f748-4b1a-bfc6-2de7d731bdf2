import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';

void main() {
  group('Team Entity Tests', () {
    test('should create a Team instance with all required fields', () {
      final team = Team(
        id: '1',
        name: 'Test Team',
        description: 'A test team',
        createdBy: 'user1',
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
        isActive: true,
        members: [],
        invitations: [],
        stats: const TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
      );

      expect(team.id, '1');
      expect(team.name, 'Test Team');
      expect(team.description, 'A test team');
      expect(team.createdBy, 'user1');
      expect(team.isActive, true);
      expect(team.members, isEmpty);
      expect(team.invitations, isEmpty);
    });

    test('should create Team from JSON', () {
      final json = {
        'id': '1',
        'name': 'Test Team',
        'description': 'A test team',
        'logo': null,
        'created_by': 'user1',
        'created_at': '2023-01-01T00:00:00.000Z',
        'updated_at': '2023-01-01T00:00:00.000Z',
        'is_active': true,
        'members': [],
        'invitations': [],
        'stats': {
          'total_matches': 0,
          'wins': 0,
          'losses': 0,
          'draws': 0,
          'win_rate': 0.0,
          'total_goals': 0,
          'goals_conceded': 0,
          'clean_sheets': 0,
          'average_goals_per_match': 0.0,
        },
      };

      final team = Team.fromJson(json);

      expect(team.id, '1');
      expect(team.name, 'Test Team');
      expect(team.description, 'A test team');
      expect(team.createdBy, 'user1');
      expect(team.isActive, true);
    });

    test('should convert Team to JSON', () {
      final team = Team(
        id: '1',
        name: 'Test Team',
        description: 'A test team',
        createdBy: 'user1',
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
        isActive: true,
        members: [],
        invitations: [],
        stats: const TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
      );

      final json = team.toJson();

      expect(json['id'], '1');
      expect(json['name'], 'Test Team');
      expect(json['description'], 'A test team');
      expect(json['created_by'], 'user1');
      expect(json['is_active'], true);
    });

    test('should copy Team with new values', () {
      final originalTeam = Team(
        id: '1',
        name: 'Original Team',
        description: 'Original description',
        createdBy: 'user1',
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
        isActive: true,
        members: [],
        invitations: [],
        stats: const TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
      );

      final updatedTeam = originalTeam.copyWith(
        name: 'Updated Team',
        description: 'Updated description',
      );

      expect(updatedTeam.id, originalTeam.id);
      expect(updatedTeam.name, 'Updated Team');
      expect(updatedTeam.description, 'Updated description');
      expect(updatedTeam.createdBy, originalTeam.createdBy);
      expect(updatedTeam.isActive, originalTeam.isActive);
    });
  });

  group('TeamMember Entity Tests', () {
    test('should create a TeamMember instance', () {
      final member = TeamMember(
        id: '1',
        userId: 'user1',
        name: 'John Doe',
        position: 'Forward',
        role: 'captain',
        joinedAt: DateTime(2023, 1, 1),
        stats: const PlayerStats(
          matchesPlayed: 10,
          goals: 5,
          assists: 3,
          cleanSheets: 0,
          rating: 4.5,
          yellowCards: 1,
          redCards: 0,
          minutesPlayed: 900,
        ),
        isActive: true,
      );

      expect(member.id, '1');
      expect(member.userId, 'user1');
      expect(member.name, 'John Doe');
      expect(member.position, 'Forward');
      expect(member.role, 'captain');
      expect(member.isActive, true);
    });

    test('should create TeamMember from JSON', () {
      final json = {
        'id': '1',
        'user_id': 'user1',
        'name': 'John Doe',
        'profile_image': null,
        'position': 'Forward',
        'role': 'captain',
        'joined_at': '2023-01-01T00:00:00.000Z',
        'stats': {
          'matches_played': 10,
          'goals': 5,
          'assists': 3,
          'clean_sheets': 0,
          'rating': 4.5,
          'yellow_cards': 1,
          'red_cards': 0,
          'minutes_played': 900,
        },
        'is_active': true,
      };

      final member = TeamMember.fromJson(json);

      expect(member.id, '1');
      expect(member.userId, 'user1');
      expect(member.name, 'John Doe');
      expect(member.position, 'Forward');
      expect(member.role, 'captain');
    });
  });

  group('TeamInvitation Entity Tests', () {
    test('should create a TeamInvitation instance', () {
      final invitation = TeamInvitation(
        id: '1',
        teamId: 'team1',
        invitedUserId: 'user2',
        invitedUserName: 'Jane Doe',
        status: 'pending',
        createdAt: DateTime(2023, 1, 1),
      );

      expect(invitation.id, '1');
      expect(invitation.teamId, 'team1');
      expect(invitation.invitedUserId, 'user2');
      expect(invitation.invitedUserName, 'Jane Doe');
      expect(invitation.status, 'pending');
    });

    test('should create TeamInvitation from JSON', () {
      final json = {
        'id': '1',
        'team_id': 'team1',
        'invited_user_id': 'user2',
        'invited_user_name': 'Jane Doe',
        'invited_user_email': '<EMAIL>',
        'invited_user_phone': '+1234567890',
        'status': 'pending',
        'created_at': '2023-01-01T00:00:00.000Z',
        'responded_at': null,
      };

      final invitation = TeamInvitation.fromJson(json);

      expect(invitation.id, '1');
      expect(invitation.teamId, 'team1');
      expect(invitation.invitedUserId, 'user2');
      expect(invitation.invitedUserName, 'Jane Doe');
      expect(invitation.invitedUserEmail, '<EMAIL>');
      expect(invitation.invitedUserPhone, '+1234567890');
      expect(invitation.status, 'pending');
    });
  });

  group('TeamStats Entity Tests', () {
    test('should create a TeamStats instance', () {
      final stats = const TeamStats(
        totalMatches: 20,
        wins: 15,
        losses: 3,
        draws: 2,
        winRate: 75.0,
        totalGoals: 45,
        goalsConceded: 20,
        cleanSheets: 8,
        averageGoalsPerMatch: 2.25,
      );

      expect(stats.totalMatches, 20);
      expect(stats.wins, 15);
      expect(stats.losses, 3);
      expect(stats.draws, 2);
      expect(stats.winRate, 75.0);
      expect(stats.totalGoals, 45);
      expect(stats.goalsConceded, 20);
      expect(stats.cleanSheets, 8);
      expect(stats.averageGoalsPerMatch, 2.25);
    });

    test('should create TeamStats from JSON', () {
      final json = {
        'total_matches': 20,
        'wins': 15,
        'losses': 3,
        'draws': 2,
        'win_rate': 75.0,
        'total_goals': 45,
        'goals_conceded': 20,
        'clean_sheets': 8,
        'average_goals_per_match': 2.25,
      };

      final stats = TeamStats.fromJson(json);

      expect(stats.totalMatches, 20);
      expect(stats.wins, 15);
      expect(stats.losses, 3);
      expect(stats.draws, 2);
      expect(stats.winRate, 75.0);
    });
  });

  group('PlayerStats Entity Tests', () {
    test('should create a PlayerStats instance', () {
      final stats = const PlayerStats(
        matchesPlayed: 15,
        goals: 8,
        assists: 6,
        cleanSheets: 0,
        rating: 4.7,
        yellowCards: 2,
        redCards: 0,
        minutesPlayed: 1350,
      );

      expect(stats.matchesPlayed, 15);
      expect(stats.goals, 8);
      expect(stats.assists, 6);
      expect(stats.cleanSheets, 0);
      expect(stats.rating, 4.7);
      expect(stats.yellowCards, 2);
      expect(stats.redCards, 0);
      expect(stats.minutesPlayed, 1350);
    });

    test('should create PlayerStats from JSON', () {
      final json = {
        'matches_played': 15,
        'goals': 8,
        'assists': 6,
        'clean_sheets': 0,
        'rating': 4.7,
        'yellow_cards': 2,
        'red_cards': 0,
        'minutes_played': 1350,
      };

      final stats = PlayerStats.fromJson(json);

      expect(stats.matchesPlayed, 15);
      expect(stats.goals, 8);
      expect(stats.assists, 6);
      expect(stats.cleanSheets, 0);
      expect(stats.rating, 4.7);
    });
  });
}
