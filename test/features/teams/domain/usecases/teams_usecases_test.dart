import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';
import 'package:nextsportz_v2/features/teams/domain/repositories/teams_repository.dart';
import 'package:nextsportz_v2/features/teams/domain/usecases/teams_usecases.dart';
import '../../test_data_factory.dart';

import 'teams_usecases_test.mocks.dart';

@GenerateMocks([TeamsRepository])
void main() {
  // Provide dummy values for Mockito
  provideDummy<Either<AppError, List<TeamSearchItem>>>(
    const Right(<TeamSearchItem>[]),
  );

  provideDummy<Either<AppError, Team>>(
    Right(
      Team(
        id: 'dummy',
        name: 'Dummy Team',
        description: 'Dummy Description',
        createdBy: 'dummy',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isActive: true,
        stats: const TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
      ),
    ),
  );

  provideDummy<Either<AppError, TeamDetails>>(
    Right(
      TeamDetails(
        id: 'dummy',
        name: 'Dummy Team Details',
        description: 'Dummy Description',
        logoUrl: '',
        members: [],
        created: DateTime.now(),
        stats: const TeamDetailStats(
          wins: 0,
          losses: 0,
          draws: 0,
          scoredGoals: 0,
          concededGoals: 0,
        ),
      ),
    ),
  );

  provideDummy<Either<AppError, void>>(const Right(null));
  late MockTeamsRepository mockRepository;
  late GetMyTeamsUseCase getMyTeamsUseCase;
  late CreateTeamUseCase createTeamUseCase;
  late GetTeamByIdUseCase getTeamByIdUseCase;
  late GetTeamDetailsUseCase getTeamDetailsUseCase;
  late InvitePlayerUseCase invitePlayerUseCase;

  setUp(() {
    mockRepository = MockTeamsRepository();
    getMyTeamsUseCase = GetMyTeamsUseCase(mockRepository);
    createTeamUseCase = CreateTeamUseCase(mockRepository);
    getTeamByIdUseCase = GetTeamByIdUseCase(mockRepository);
    getTeamDetailsUseCase = GetTeamDetailsUseCase(mockRepository);
    invitePlayerUseCase = InvitePlayerUseCase(mockRepository);
  });

  group('GetMyTeamsUseCase', () {
    test('should get list of teams from repository', () async {
      // arrange
      final teams = [
        TeamSearchItem(
          id: '1',
          name: 'Team 1',
          membersCount: 5,
          winRate: '0.70',
          logoUrl: 'https://example.com/logo.png',
        ),
      ];

      when(mockRepository.getMyTeams()).thenAnswer((_) async => Right(teams));

      // act
      final result = await getMyTeamsUseCase();

      // assert
      expect(result.isRight(), true);
      result.fold((error) => fail('Expected success but got error: $error'), (
        teamsList,
      ) {
        expect(teamsList, isA<List<TeamSearchItem>>());
        expect(teamsList.length, equals(1));
        expect(teamsList.first.name, equals('Team 1'));
      });
      verify(mockRepository.getMyTeams()).called(1);
    });
  });

  group('CreateTeamUseCase', () {
    test('should create a team with given parameters', () async {
      // arrange
      const name = 'New Team';
      const description = 'A new team';
      const logo = 'logo_url';

      final createdTeam = Team(
        id: '1',
        name: name,
        description: description,
        logo: logo,
        createdBy: 'user1',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isActive: true,
        stats: const TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
      );

      when(
        mockRepository.createTeam(
          name: name,
          description: description,
          logo: logo,
        ),
      ).thenAnswer((_) async => Right(createdTeam));

      // act
      final result = await createTeamUseCase(
        name: name,
        description: description,
        logo: logo,
      );

      // assert
      expect(result.isRight(), true);
      result.fold((error) => fail('Expected success but got error: $error'), (
        team,
      ) {
        expect(team, isA<Team>());
        expect(team.name, equals(name));
        expect(team.description, equals(description));
        expect(team.logo, equals(logo));
      });
      verify(
        mockRepository.createTeam(
          name: name,
          description: description,
          logo: logo,
        ),
      ).called(1);
    });
  });

  group('GetTeamByIdUseCase', () {
    test('should get team by id from repository', () async {
      // arrange
      const teamId = '1';
      final team = Team(
        id: teamId,
        name: 'Team 1',
        description: 'Description 1',
        createdBy: 'user1',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isActive: true,
        stats: const TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
      );

      when(
        mockRepository.getTeamById(teamId),
      ).thenAnswer((_) async => Right(team));

      // act
      final result = await getTeamByIdUseCase(teamId);

      // assert
      expect(result.isRight(), true);
      result.fold((error) => fail('Expected success but got error: $error'), (
        returnedTeam,
      ) {
        expect(returnedTeam, isA<Team>());
        expect(returnedTeam.id, equals(teamId));
        expect(returnedTeam.name, equals('Team 1'));
      });
      verify(mockRepository.getTeamById(teamId)).called(1);
    });
  });

  group('GetTeamDetailsUseCase', () {
    test('should get team details from repository', () async {
      // arrange
      const teamId = '1';
      final teamDetails = TeamsTestDataFactory.createTestTeamDetails(
        id: teamId,
        name: 'Test Team Details',
        description: 'A detailed test team',
      );

      when(
        mockRepository.getTeamDetails(teamId),
      ).thenAnswer((_) async => Right(teamDetails));

      // act
      final result = await getTeamDetailsUseCase(teamId);

      // assert
      expect(result.isRight(), true);
      result.fold((error) => fail('Expected success but got error: $error'), (
        details,
      ) {
        expect(details, isA<TeamDetails>());
        expect(details.id, equals(teamId));
        expect(details.name, equals('Test Team Details'));
        expect(details.members, isA<List<TeamDetailMember>>());
        expect(details.stats, isA<TeamDetailStats>());
      });
      verify(mockRepository.getTeamDetails(teamId)).called(1);
    });

    test('should return error when repository fails', () async {
      // arrange
      const teamId = '1';
      final error = AppError('Team details not found');

      when(
        mockRepository.getTeamDetails(teamId),
      ).thenAnswer((_) async => Left(error));

      // act
      final result = await getTeamDetailsUseCase(teamId);

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (err) => expect(err.message, equals('Team details not found')),
        (details) => fail('Expected error but got success'),
      );
      verify(mockRepository.getTeamDetails(teamId)).called(1);
    });
  });

  group('InvitePlayerUseCase', () {
    test('should invite player to team', () async {
      // arrange
      const teamId = '1';
      const playerId = 'user2';
      const message = 'Join our team!';

      when(
        mockRepository.invitePlayer(
          teamId: teamId,
          playerId: playerId,
          message: message,
        ),
      ).thenAnswer((_) async => const Right(null));

      // act
      await invitePlayerUseCase(
        teamId: teamId,
        playerId: playerId,
        message: message,
      );

      // assert
      verify(
        mockRepository.invitePlayer(
          teamId: teamId,
          playerId: playerId,
          message: message,
        ),
      ).called(1);
    });

    test('should invite player without message', () async {
      // arrange
      const teamId = '1';
      const playerId = 'user2';

      when(
        mockRepository.invitePlayer(
          teamId: teamId,
          playerId: playerId,
          message: null,
        ),
      ).thenAnswer((_) async => const Right(null));

      // act
      await invitePlayerUseCase(
        teamId: teamId,
        playerId: playerId,
        message: null,
      );

      // assert
      verify(
        mockRepository.invitePlayer(
          teamId: teamId,
          playerId: playerId,
          message: null,
        ),
      ).called(1);
    });
  });
}
