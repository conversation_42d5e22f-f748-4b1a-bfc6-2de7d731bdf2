import 'package:flutter_test/flutter_test.dart';
import 'package:fpdart/fpdart.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team_role.dart';
import 'package:nextsportz_v2/features/teams/domain/repositories/team_invitations_repository.dart';
import 'package:nextsportz_v2/features/teams/domain/usecases/team_invitations_usecases.dart';

import 'team_invitations_usecases_test.mocks.dart';

@GenerateMocks([TeamInvitationsRepository])
void main() {
  late MockTeamInvitationsRepository mockRepository;

  setUpAll(() {
    // Provide dummy values for Either types
    provideDummy<Either<AppError, void>>(const Right(null));
    provideDummy<Either<AppError, List<TeamInvitation>>>(const Right([]));
    provideDummy<Either<AppError, TeamInvitation>>(
      Right(
        TeamInvitation(
          id: 'test-id',
          teamId: 'test-team-id',
          invitedUserId: 'test-user-id',
          invitedUserName: 'Test User',
          invitedUserEmail: '<EMAIL>',
          status: 'pending',
          createdAt: DateTime.now(),
        ),
      ),
    );
    provideDummy<Either<AppError, int>>(const Right(0));
  });

  setUp(() {
    mockRepository = MockTeamInvitationsRepository();
  });

  group('InviteTeamMemberUseCase', () {
    late InviteTeamMemberUseCase useCase;

    setUp(() {
      useCase = InviteTeamMemberUseCase(mockRepository);
    });

    test('should invite team member successfully', () async {
      // Arrange
      const params = InviteTeamMemberParams(
        teamId: 'team-id',
        playerId: 'player-id',
        role: TeamRole.player,
      );

      when(
        mockRepository.inviteTeamMember(
          teamId: anyNamed('teamId'),
          playerId: anyNamed('playerId'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async => const Right(null));

      // Act
      final result = await useCase(params);

      // Assert
      expect(result, isA<Right<AppError, void>>());
      verify(
        mockRepository.inviteTeamMember(
          teamId: 'team-id',
          playerId: 'player-id',
          role: TeamRole.player,
        ),
      ).called(1);
    });

    test('should return error when invitation fails', () async {
      // Arrange
      const params = InviteTeamMemberParams(
        teamId: 'team-id',
        playerId: 'player-id',
        role: TeamRole.captain,
      );

      const error = AppError('Invitation failed');
      when(
        mockRepository.inviteTeamMember(
          teamId: anyNamed('teamId'),
          playerId: anyNamed('playerId'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async => Left(error));

      // Act
      final result = await useCase(params);

      // Assert
      expect(result, isA<Left<AppError, void>>());
      expect(result.fold((l) => l, (r) => null), error);
    });
  });

  group('AcceptInvitationUseCase', () {
    late AcceptInvitationUseCase useCase;

    setUp(() {
      useCase = AcceptInvitationUseCase(mockRepository);
    });

    test('should accept invitation successfully', () async {
      // Arrange
      const params = AcceptInvitationParams(
        teamId: 'team-id',
        invitationId: 'invitation-id',
      );

      when(
        mockRepository.acceptInvitation(
          teamId: anyNamed('teamId'),
          invitationId: anyNamed('invitationId'),
        ),
      ).thenAnswer((_) async => const Right(null));

      // Act
      final result = await useCase(params);

      // Assert
      expect(result, isA<Right<AppError, void>>());
      verify(
        mockRepository.acceptInvitation(
          teamId: 'team-id',
          invitationId: 'invitation-id',
        ),
      ).called(1);
    });

    test('should return error when accepting invitation fails', () async {
      // Arrange
      const params = AcceptInvitationParams(
        teamId: 'team-id',
        invitationId: 'invitation-id',
      );

      const error = AppError('Accept invitation failed');
      when(
        mockRepository.acceptInvitation(
          teamId: anyNamed('teamId'),
          invitationId: anyNamed('invitationId'),
        ),
      ).thenAnswer((_) async => Left(error));

      // Act
      final result = await useCase(params);

      // Assert
      expect(result, isA<Left<AppError, void>>());
      expect(result.fold((l) => l, (r) => null), error);
    });
  });

  group('DeclineInvitationUseCase', () {
    late DeclineInvitationUseCase useCase;

    setUp(() {
      useCase = DeclineInvitationUseCase(mockRepository);
    });

    test('should decline invitation successfully', () async {
      // Arrange
      const params = DeclineInvitationParams(
        teamId: 'team-id',
        invitationId: 'invitation-id',
      );

      when(
        mockRepository.declineInvitation(
          teamId: anyNamed('teamId'),
          invitationId: anyNamed('invitationId'),
        ),
      ).thenAnswer((_) async => const Right(null));

      // Act
      final result = await useCase(params);

      // Assert
      expect(result, isA<Right<AppError, void>>());
      verify(
        mockRepository.declineInvitation(
          teamId: 'team-id',
          invitationId: 'invitation-id',
        ),
      ).called(1);
    });

    test('should return error when declining invitation fails', () async {
      // Arrange
      const params = DeclineInvitationParams(
        teamId: 'team-id',
        invitationId: 'invitation-id',
      );

      const error = AppError('Decline invitation failed');
      when(
        mockRepository.declineInvitation(
          teamId: anyNamed('teamId'),
          invitationId: anyNamed('invitationId'),
        ),
      ).thenAnswer((_) async => Left(error));

      // Act
      final result = await useCase(params);

      // Assert
      expect(result, isA<Left<AppError, void>>());
      expect(result.fold((l) => l, (r) => null), error);
    });
  });

  group('DeleteTeamInviteUseCase', () {
    late DeleteTeamInviteUseCase useCase;

    setUp(() {
      useCase = DeleteTeamInviteUseCase(mockRepository);
    });

    test('should delete team invite successfully', () async {
      // Arrange
      const params = DeleteTeamInviteParams(
        teamId: 'team-id',
        memberId: 'member-id',
      );

      when(
        mockRepository.deleteTeamInvite(
          teamId: anyNamed('teamId'),
          memberId: anyNamed('memberId'),
        ),
      ).thenAnswer((_) async => const Right(null));

      // Act
      final result = await useCase(params);

      // Assert
      expect(result, isA<Right<AppError, void>>());
      verify(
        mockRepository.deleteTeamInvite(
          teamId: 'team-id',
          memberId: 'member-id',
        ),
      ).called(1);
    });

    test('should return error when deleting invite fails', () async {
      // Arrange
      const params = DeleteTeamInviteParams(
        teamId: 'team-id',
        memberId: 'member-id',
      );

      const error = AppError('Delete invite failed');
      when(
        mockRepository.deleteTeamInvite(
          teamId: anyNamed('teamId'),
          memberId: anyNamed('memberId'),
        ),
      ).thenAnswer((_) async => Left(error));

      // Act
      final result = await useCase(params);

      // Assert
      expect(result, isA<Left<AppError, void>>());
      expect(result.fold((l) => l, (r) => null), error);
    });
  });
}
