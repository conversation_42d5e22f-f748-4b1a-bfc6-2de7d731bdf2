// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in nextsportz_v2/test/features/teams/domain/usecases/invite_player_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:fpdart/fpdart.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;
import 'package:nextsportz_v2/core/networking/app_error.dart' as _i5;
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart' as _i6;
import 'package:nextsportz_v2/features/teams/domain/entities/team_role.dart'
    as _i8;
import 'package:nextsportz_v2/features/teams/domain/repositories/team_invitations_repository.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [TeamInvitationsRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockTeamInvitationsRepository extends _i1.Mock
    implements _i2.TeamInvitationsRepository {
  MockTeamInvitationsRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i4.Either<_i5.AppError, List<_i6.TeamInvitation>>>
  getPendingInvitations() =>
      (super.noSuchMethod(
            Invocation.method(#getPendingInvitations, []),
            returnValue: _i3.Future<
              _i4.Either<_i5.AppError, List<_i6.TeamInvitation>>
            >.value(
              _i7.dummyValue<
                _i4.Either<_i5.AppError, List<_i6.TeamInvitation>>
              >(this, Invocation.method(#getPendingInvitations, [])),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, List<_i6.TeamInvitation>>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, List<_i6.TeamInvitation>>>
  getTeamInvitations(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamInvitations, [teamId]),
            returnValue: _i3.Future<
              _i4.Either<_i5.AppError, List<_i6.TeamInvitation>>
            >.value(
              _i7.dummyValue<
                _i4.Either<_i5.AppError, List<_i6.TeamInvitation>>
              >(this, Invocation.method(#getTeamInvitations, [teamId])),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, List<_i6.TeamInvitation>>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, _i6.TeamInvitation>> getInvitation(
    String? invitationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getInvitation, [invitationId]),
            returnValue:
                _i3.Future<_i4.Either<_i5.AppError, _i6.TeamInvitation>>.value(
                  _i7.dummyValue<_i4.Either<_i5.AppError, _i6.TeamInvitation>>(
                    this,
                    Invocation.method(#getInvitation, [invitationId]),
                  ),
                ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, _i6.TeamInvitation>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> invitePlayer({
    required String? teamId,
    required String? playerId,
    String? role = 'Player',
    String? message,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#invitePlayer, [], {
              #teamId: teamId,
              #playerId: playerId,
              #role: role,
              #message: message,
            }),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#invitePlayer, [], {
                  #teamId: teamId,
                  #playerId: playerId,
                  #role: role,
                  #message: message,
                }),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> acceptInvitation({
    required String? teamId,
    required String? invitationId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#acceptInvitation, [], {
              #teamId: teamId,
              #invitationId: invitationId,
            }),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#acceptInvitation, [], {
                  #teamId: teamId,
                  #invitationId: invitationId,
                }),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> declineInvitation({
    required String? teamId,
    required String? invitationId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#declineInvitation, [], {
              #teamId: teamId,
              #invitationId: invitationId,
            }),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#declineInvitation, [], {
                  #teamId: teamId,
                  #invitationId: invitationId,
                }),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, int>> getTeamInvitationsCount(
    String? teamId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamInvitationsCount, [teamId]),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, int>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, int>>(
                this,
                Invocation.method(#getTeamInvitationsCount, [teamId]),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, int>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> inviteTeamMember({
    required String? teamId,
    required String? playerId,
    required _i8.TeamRole? role,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#inviteTeamMember, [], {
              #teamId: teamId,
              #playerId: playerId,
              #role: role,
            }),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#inviteTeamMember, [], {
                  #teamId: teamId,
                  #playerId: playerId,
                  #role: role,
                }),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> deleteTeamInvite({
    required String? teamId,
    required String? memberId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#deleteTeamInvite, [], {
              #teamId: teamId,
              #memberId: memberId,
            }),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#deleteTeamInvite, [], {
                  #teamId: teamId,
                  #memberId: memberId,
                }),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> acceptTeamInvite({
    required String? teamId,
    required String? memberId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#acceptTeamInvite, [], {
              #teamId: teamId,
              #memberId: memberId,
            }),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#acceptTeamInvite, [], {
                  #teamId: teamId,
                  #memberId: memberId,
                }),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);
}
