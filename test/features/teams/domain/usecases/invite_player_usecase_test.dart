import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/features/teams/domain/repositories/team_invitations_repository.dart';
import 'package:nextsportz_v2/features/teams/domain/usecases/invite_player_usecase.dart';

import 'invite_player_usecase_test.mocks.dart';

@GenerateMocks([TeamInvitationsRepository])
void main() {
  late MockTeamInvitationsRepository mockTeamInvitationsRepository;
  late InvitePlayerUseCase invitePlayerUseCase;

  // Provide dummy values for Mockito
  provideDummy<Either<AppError, void>>(const Right(null));

  setUp(() {
    mockTeamInvitationsRepository = MockTeamInvitationsRepository();
    invitePlayerUseCase = InvitePlayerUseCase(mockTeamInvitationsRepository);
  });

  group('InvitePlayerUseCase', () {
    const testTeamId = 'team_123';
    const testPlayerId = 'player_456';
    const testRole = 'Player';

    final testParams = InvitePlayerParams(
      teamId: testTeamId,
      playerId: testPlayerId,
      role: testRole,
    );

    group('Success scenarios', () {
      test(
        'should successfully invite player when repository succeeds',
        () async {
          // Arrange
          when(
            mockTeamInvitationsRepository.invitePlayer(
              teamId: testTeamId,
              playerId: testPlayerId,
              role: testRole,
            ),
          ).thenAnswer((_) async => const Right(null));

          // Act
          final result = await invitePlayerUseCase(testParams);

          // Assert
          expect(result.isRight(), true);
          result.fold(
            (error) => fail('Expected success but got error: ${error.message}'),
            (success) => {}, // Success - void return type
          );

          verify(
            mockTeamInvitationsRepository.invitePlayer(
              teamId: testTeamId,
              playerId: testPlayerId,
              role: testRole,
            ),
          ).called(1);
        },
      );

      test('should use default role when not specified', () async {
        // Arrange
        const paramsWithDefaultRole = InvitePlayerParams(
          teamId: testTeamId,
          playerId: testPlayerId,
        );

        when(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: testPlayerId,
            role: 'Player', // Default role
          ),
        ).thenAnswer((_) async => const Right(null));

        // Act
        final result = await invitePlayerUseCase(paramsWithDefaultRole);

        // Assert
        expect(result.isRight(), true);
        verify(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: testPlayerId,
            role: 'Player',
          ),
        ).called(1);
      });

      test('should handle custom role assignment', () async {
        // Arrange
        const customRole = 'Captain';
        final paramsWithCustomRole = InvitePlayerParams(
          teamId: testTeamId,
          playerId: testPlayerId,
          role: customRole,
        );

        when(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: testPlayerId,
            role: customRole,
          ),
        ).thenAnswer((_) async => const Right(null));

        // Act
        final result = await invitePlayerUseCase(paramsWithCustomRole);

        // Assert
        expect(result.isRight(), true);
        verify(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: testPlayerId,
            role: customRole,
          ),
        ).called(1);
      });
    });

    group('Error scenarios', () {
      test('should return error when repository fails', () async {
        // Arrange
        const errorMessage = 'Failed to send invitation';
        when(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: testPlayerId,
            role: testRole,
          ),
        ).thenAnswer((_) async => const Left(AppError(errorMessage)));

        // Act
        final result = await invitePlayerUseCase(testParams);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, equals(errorMessage)),
          (success) => fail('Expected error but got success'),
        );
      });

      test('should handle network error', () async {
        // Arrange
        const errorMessage = 'Network connection failed';
        when(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: testPlayerId,
            role: testRole,
          ),
        ).thenAnswer((_) async => const Left(AppError(errorMessage)));

        // Act
        final result = await invitePlayerUseCase(testParams);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, equals(errorMessage)),
          (success) => fail('Expected error but got success'),
        );
      });

      test('should handle player already invited error', () async {
        // Arrange
        const errorMessage = 'Player is already invited to this team';
        when(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: testPlayerId,
            role: testRole,
          ),
        ).thenAnswer((_) async => const Left(AppError(errorMessage)));

        // Act
        final result = await invitePlayerUseCase(testParams);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, equals(errorMessage)),
          (success) => fail('Expected error but got success'),
        );
      });

      test('should handle invalid team ID error', () async {
        // Arrange
        const errorMessage = 'Team not found';
        when(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: testPlayerId,
            role: testRole,
          ),
        ).thenAnswer((_) async => const Left(AppError(errorMessage)));

        // Act
        final result = await invitePlayerUseCase(testParams);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, equals(errorMessage)),
          (success) => fail('Expected error but got success'),
        );
      });

      test('should handle invalid player ID error', () async {
        // Arrange
        const errorMessage = 'Player not found';
        when(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: testPlayerId,
            role: testRole,
          ),
        ).thenAnswer((_) async => const Left(AppError(errorMessage)));

        // Act
        final result = await invitePlayerUseCase(testParams);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, equals(errorMessage)),
          (success) => fail('Expected error but got success'),
        );
      });
    });

    group('Multiple invitations', () {
      test('should handle multiple sequential invitations', () async {
        // Arrange
        const player1Params = InvitePlayerParams(
          teamId: testTeamId,
          playerId: 'player_1',
          role: testRole,
        );
        const player2Params = InvitePlayerParams(
          teamId: testTeamId,
          playerId: 'player_2',
          role: testRole,
        );

        when(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: 'player_1',
            role: testRole,
          ),
        ).thenAnswer((_) async => const Right(null));

        when(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: 'player_2',
            role: testRole,
          ),
        ).thenAnswer((_) async => const Right(null));

        // Act
        final result1 = await invitePlayerUseCase(player1Params);
        final result2 = await invitePlayerUseCase(player2Params);

        // Assert
        expect(result1.isRight(), true);
        expect(result2.isRight(), true);

        verify(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: 'player_1',
            role: testRole,
          ),
        ).called(1);

        verify(
          mockTeamInvitationsRepository.invitePlayer(
            teamId: testTeamId,
            playerId: 'player_2',
            role: testRole,
          ),
        ).called(1);
      });
    });
  });

  group('InvitePlayerParams', () {
    test('should create params with all values', () {
      // Act
      const params = InvitePlayerParams(
        teamId: 'team_123',
        playerId: 'player_456',
        role: 'Captain',
      );

      // Assert
      expect(params.teamId, equals('team_123'));
      expect(params.playerId, equals('player_456'));
      expect(params.role, equals('Captain'));
    });

    test('should use default role when not specified', () {
      // Act
      const params = InvitePlayerParams(
        teamId: 'team_123',
        playerId: 'player_456',
      );

      // Assert
      expect(params.teamId, equals('team_123'));
      expect(params.playerId, equals('player_456'));
      expect(params.role, equals('Player')); // Default role
    });

    test('should support copyWith method', () {
      // Arrange
      const originalParams = InvitePlayerParams(
        teamId: 'team_123',
        playerId: 'player_456',
        role: 'Player',
      );

      // Act
      final updatedParams = originalParams.copyWith(role: 'Captain');

      // Assert
      expect(updatedParams.teamId, equals('team_123'));
      expect(updatedParams.playerId, equals('player_456'));
      expect(updatedParams.role, equals('Captain'));
    });

    test('should support equality comparison', () {
      // Arrange
      const params1 = InvitePlayerParams(
        teamId: 'team_123',
        playerId: 'player_456',
        role: 'Player',
      );
      const params2 = InvitePlayerParams(
        teamId: 'team_123',
        playerId: 'player_456',
        role: 'Player',
      );
      const params3 = InvitePlayerParams(
        teamId: 'team_123',
        playerId: 'player_789',
        role: 'Player',
      );

      // Assert
      expect(params1, equals(params2));
      expect(params1, isNot(equals(params3)));
    });

    test('should have consistent hashCode', () {
      // Arrange
      const params1 = InvitePlayerParams(
        teamId: 'team_123',
        playerId: 'player_456',
        role: 'Player',
      );
      const params2 = InvitePlayerParams(
        teamId: 'team_123',
        playerId: 'player_456',
        role: 'Player',
      );

      // Assert
      expect(params1.hashCode, equals(params2.hashCode));
    });

    test('should have meaningful toString', () {
      // Arrange
      const params = InvitePlayerParams(
        teamId: 'team_123',
        playerId: 'player_456',
        role: 'Captain',
      );

      // Act
      final stringRepresentation = params.toString();

      // Assert
      expect(stringRepresentation, contains('team_123'));
      expect(stringRepresentation, contains('player_456'));
      expect(stringRepresentation, contains('Captain'));
    });
  });
}
