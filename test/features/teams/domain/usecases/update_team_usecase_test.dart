import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import '../../../../../lib/features/teams/domain/usecases/teams_usecases.dart';
import '../../../../../lib/features/teams/domain/repositories/teams_repository.dart';
import '../../../../../lib/features/teams/domain/entities/team.dart';

import 'update_team_usecase_test.mocks.dart';

@GenerateMocks([TeamsRepository])
void main() {
  group('UpdateTeamUseCase Tests', () {
    late UpdateTeamUseCase useCase;
    late MockTeamsRepository mockRepository;
    late Team originalTeam;
    late Team updatedTeam;

    setUp(() {
      mockRepository = MockTeamsRepository();
      useCase = UpdateTeamUseCase(mockRepository);

      originalTeam = Team(
        id: '1',
        name: 'Original Team',
        description: 'Original description',
        slogan: 'Original slogan',
        logo: 'https://example.com/original-logo.jpg',
        createdBy: 'owner123',
        updatedAt: DateTime(2023, 1, 1),
        invitations: [],
        stats: TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
        members: [],
        createdAt: DateTime(2023, 1, 1),
        isActive: true,
      );

      updatedTeam = Team(
        id: '1',
        name: 'Updated Team',
        description: 'Updated description',
        slogan: 'Updated slogan',
        logo: 'https://example.com/updated-logo.jpg',
        createdBy: 'owner123',
        updatedAt: DateTime(2023, 1, 1),
        invitations: [],
        stats: TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
        members: [],
        createdAt: DateTime(2023, 1, 1),
        isActive: true,
      );
    });

    group('call method', () {
      test('updates team successfully with all parameters', () async {
        // Arrange
        const teamId = '1';
        const name = 'Updated Team';
        const description = 'Updated description';
        const slogan = 'Updated slogan';
        const logo = 'https://example.com/updated-logo.jpg';

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).thenAnswer((_) async => updatedTeam);

        // Act
        final result = await useCase.call(
          teamId: teamId,
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        );

        // Assert
        expect(result.id, teamId);
        expect(result.name, name);
        expect(result.description, description);
        expect(result.slogan, slogan);
        expect(result.logo, logo);
        verify(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).called(1);
      });

      test('updates team with only team ID and name', () async {
        // Arrange
        const teamId = '1';
        const name = 'Name Only Update';
        final nameOnlyTeam = originalTeam.copyWith(name: name);

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: null,
          slogan: null,
          logo: null,
        )).thenAnswer((_) async => nameOnlyTeam);

        // Act
        final result = await useCase.call(
          teamId: teamId,
          name: name,
        );

        // Assert
        expect(result.name, name);
        verify(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: null,
          slogan: null,
          logo: null,
        )).called(1);
      });

      test('updates team with only team ID and description', () async {
        // Arrange
        const teamId = '1';
        const description = 'Description only update';
        final descOnlyTeam = originalTeam.copyWith(description: description);

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: null,
          description: description,
          slogan: null,
          logo: null,
        )).thenAnswer((_) async => descOnlyTeam);

        // Act
        final result = await useCase.call(
          teamId: teamId,
          description: description,
        );

        // Assert
        expect(result.description, description);
        verify(mockRepository.updateTeam(
          teamId: teamId,
          name: null,
          description: description,
          slogan: null,
          logo: null,
        )).called(1);
      });

      test('updates team with only team ID and slogan', () async {
        // Arrange
        const teamId = '1';
        const slogan = 'Slogan only update';
        final sloganOnlyTeam = originalTeam.copyWith(slogan: slogan);

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: null,
          description: null,
          slogan: slogan,
          logo: null,
        )).thenAnswer((_) async => sloganOnlyTeam);

        // Act
        final result = await useCase.call(
          teamId: teamId,
          slogan: slogan,
        );

        // Assert
        expect(result.slogan, slogan);
        verify(mockRepository.updateTeam(
          teamId: teamId,
          name: null,
          description: null,
          slogan: slogan,
          logo: null,
        )).called(1);
      });

      test('updates team with only team ID and logo', () async {
        // Arrange
        const teamId = '1';
        const logo = 'https://example.com/new-logo.jpg';
        final logoOnlyTeam = originalTeam.copyWith(logo: logo);

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: null,
          description: null,
          slogan: null,
          logo: logo,
        )).thenAnswer((_) async => logoOnlyTeam);

        // Act
        final result = await useCase.call(
          teamId: teamId,
          logo: logo,
        );

        // Assert
        expect(result.logo, logo);
        verify(mockRepository.updateTeam(
          teamId: teamId,
          name: null,
          description: null,
          slogan: null,
          logo: logo,
        )).called(1);
      });

      test('updates team with multiple parameters but not all', () async {
        // Arrange
        const teamId = '1';
        const name = 'Partial Update Team';
        const description = 'Partial update description';
        final partialTeam = originalTeam.copyWith(
          name: name,
          description: description,
        );

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: description,
          slogan: null,
          logo: null,
        )).thenAnswer((_) async => partialTeam);

        // Act
        final result = await useCase.call(
          teamId: teamId,
          name: name,
          description: description,
        );

        // Assert
        expect(result.name, name);
        expect(result.description, description);
        verify(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: description,
          slogan: null,
          logo: null,
        )).called(1);
      });

      test('calls repository with exact parameters provided', () async {
        // Arrange
        const teamId = 'exact123';
        const name = 'Exact Team';
        const description = 'Exact description';
        const slogan = 'Exact slogan';
        const logo = 'https://exact.com/logo.png';

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).thenAnswer((_) async => updatedTeam);

        // Act
        await useCase.call(
          teamId: teamId,
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        );

        // Assert
        verify(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).called(1);
        verifyNoMoreInteractions(mockRepository);
      });

      test('returns updated team with correct properties from repository',
          () async {
        // Arrange
        const teamId = 'prop456';
        const name = 'Property Team';
        final propertyTeam = Team(
          id: teamId,
          name: name,
          description: 'Property description',
          slogan: 'Props for all',
          logo: 'https://props.com/logo.gif',
          createdBy: 'propOwner',
          members: [],
          createdAt: DateTime(2023, 5, 15),
          isActive: true,
        );

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => propertyTeam);

        // Act
        final result = await useCase.call(
          teamId: teamId,
          name: name,
        );

        // Assert
        expect(result.id, teamId);
        expect(result.name, name);
        expect(result.description, 'Property description');
        expect(result.slogan, 'Props for all');
        expect(result.logo, 'https://props.com/logo.gif');
        expect(result.createdBy, 'propOwner');
        expect(result.members, isEmpty);
        expect(result.createdAt, DateTime(2023, 5, 15));
        expect(result.isActive, true);
      });
    });

    group('Parameter Validation', () {
      test('requires teamId parameter', () async {
        // Arrange
        const teamId = 'required123';
        when(mockRepository.updateTeam(
          teamId: teamId,
          name: null,
          description: null,
          slogan: null,
          logo: null,
        )).thenAnswer((_) async => originalTeam);

        // Act
        final result = await useCase.call(teamId: teamId);

        // Assert
        expect(result, originalTeam);
        verify(mockRepository.updateTeam(
          teamId: teamId,
          name: null,
          description: null,
          slogan: null,
          logo: null,
        )).called(1);
      });

      test('handles null values for optional parameters', () async {
        // Arrange
        const teamId = 'null123';
        when(mockRepository.updateTeam(
          teamId: teamId,
          name: null,
          description: null,
          slogan: null,
          logo: null,
        )).thenAnswer((_) async => originalTeam);

        // Act
        final result = await useCase.call(
          teamId: teamId,
          name: null,
          description: null,
          slogan: null,
          logo: null,
        );

        // Assert
        expect(result, originalTeam);
        verify(mockRepository.updateTeam(
          teamId: teamId,
          name: null,
          description: null,
          slogan: null,
          logo: null,
        )).called(1);
      });

      test('passes through all parameter combinations correctly', () async {
        // Arrange
        final testCases = [
          {
            'teamId': 'test1',
            'name': 'Team A',
            'description': null,
            'slogan': null,
            'logo': null,
          },
          {
            'teamId': 'test2',
            'name': null,
            'description': 'Description B',
            'slogan': null,
            'logo': null,
          },
          {
            'teamId': 'test3',
            'name': null,
            'description': null,
            'slogan': 'Slogan C',
            'logo': null,
          },
          {
            'teamId': 'test4',
            'name': null,
            'description': null,
            'slogan': null,
            'logo': 'logo-d.jpg',
          },
          {
            'teamId': 'test5',
            'name': 'Team E',
            'description': 'Description E',
            'slogan': 'Slogan E',
            'logo': 'logo-e.jpg',
          },
        ];

        when(mockRepository.updateTeam(
          teamId: anyNamed('teamId'),
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => updatedTeam);

        // Act & Assert
        for (final testCase in testCases) {
          final result = await useCase.call(
            teamId: testCase['teamId'] as String,
            name: testCase['name'] as String?,
            description: testCase['description'] as String?,
            slogan: testCase['slogan'] as String?,
            logo: testCase['logo'] as String?,
          );

          expect(result, updatedTeam);
          verify(mockRepository.updateTeam(
            teamId: testCase['teamId'] as String,
            name: testCase['name'] as String?,
            description: testCase['description'] as String?,
            slogan: testCase['slogan'] as String?,
            logo: testCase['logo'] as String?,
          )).called(1);
        }
      });
    });

    group('Edge Cases', () {
      test('handles empty string values', () async {
        // Arrange
        const teamId = 'empty123';
        const name = '';
        const description = '';
        const slogan = '';
        const logo = '';

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).thenAnswer((_) async => updatedTeam);

        // Act
        final result = await useCase.call(
          teamId: teamId,
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        );

        // Assert
        expect(result, updatedTeam);
        verify(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).called(1);
      });

      test('handles very long strings', () async {
        // Arrange
        const teamId = 'long123';
        final longName = 'Team ${'A' * 500}';
        final longDescription = 'Description ${'B' * 1000}';
        final longSlogan = 'Slogan ${'C' * 200}';
        final longLogo =
            'https://example.com/very/long/path/${'logo' * 50}.jpg';

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: longName,
          description: longDescription,
          slogan: longSlogan,
          logo: longLogo,
        )).thenAnswer((_) async => updatedTeam);

        // Act
        final result = await useCase.call(
          teamId: teamId,
          name: longName,
          description: longDescription,
          slogan: longSlogan,
          logo: longLogo,
        );

        // Assert
        expect(result, updatedTeam);
        verify(mockRepository.updateTeam(
          teamId: teamId,
          name: longName,
          description: longDescription,
          slogan: longSlogan,
          logo: longLogo,
        )).called(1);
      });

      test('handles special characters and unicode', () async {
        // Arrange
        const teamId = 'special123';
        const name = 'Team 🚀⚽';
        const description = 'Description with émojis and spëcial chars!@#\$%';
        const slogan = 'Victòry & Hòpe 💪';
        const logo = 'https://example.com/tëam-lògo.jpg';

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).thenAnswer((_) async => updatedTeam);

        // Act
        final result = await useCase.call(
          teamId: teamId,
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        );

        // Assert
        expect(result, updatedTeam);
        verify(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).called(1);
      });

      test('handles different team ID formats', () async {
        // Arrange
        final teamIds = [
          'simple123',
          'uuid-12345678-1234-1234-1234-123456789012',
          'team_id_with_underscores',
          'team-id-with-hyphens',
          'TeamIdWithCamelCase',
          '12345',
          'a',
          'very_long_team_id_with_many_characters_to_test_limits',
        ];

        when(mockRepository.updateTeam(
          teamId: anyNamed('teamId'),
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => updatedTeam);

        // Act & Assert
        for (final teamId in teamIds) {
          final result = await useCase.call(teamId: teamId);
          expect(result, updatedTeam);
          verify(mockRepository.updateTeam(
            teamId: teamId,
            name: null,
            description: null,
            slogan: null,
            logo: null,
          )).called(1);
        }
      });

      test('handles different URL formats for logo', () async {
        // Arrange
        const teamId = 'url123';
        final logoUrls = [
          'https://example.com/logo.jpg',
          'http://example.com/logo.png',
          'file:///local/path/logo.gif',
          'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD',
          '../relative/path/logo.svg',
          '/absolute/path/logo.webp',
        ];

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: null,
          description: null,
          slogan: null,
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => updatedTeam);

        // Act & Assert
        for (final logoUrl in logoUrls) {
          final result = await useCase.call(
            teamId: teamId,
            logo: logoUrl,
          );
          expect(result, updatedTeam);
        }

        // Verify all calls were made
        for (final logoUrl in logoUrls) {
          verify(mockRepository.updateTeam(
            teamId: teamId,
            name: null,
            description: null,
            slogan: null,
            logo: logoUrl,
          )).called(1);
        }
      });
    });

    group('Error Handling', () {
      test('propagates repository exceptions', () async {
        // Arrange
        const teamId = 'error123';
        const name = 'Error Team';
        when(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: null,
          slogan: null,
          logo: null,
        )).thenThrow(Exception('Repository error'));

        // Act & Assert
        expect(
          () => useCase.call(teamId: teamId, name: name),
          throwsA(isA<Exception>()),
        );
      });

      test('propagates custom exceptions', () async {
        // Arrange
        const teamId = 'custom_error123';
        const name = 'Custom Error Team';
        when(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: null,
          slogan: null,
          logo: null,
        )).thenThrow(ArgumentError('Invalid team data'));

        // Act & Assert
        expect(
          () => useCase.call(teamId: teamId, name: name),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('propagates string errors', () async {
        // Arrange
        const teamId = 'string_error123';
        const name = 'String Error Team';
        when(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: null,
          slogan: null,
          logo: null,
        )).thenThrow('String error message');

        // Act & Assert
        expect(
          () => useCase.call(teamId: teamId, name: name),
          throwsA(equals('String error message')),
        );
      });
    });

    group('Performance Tests', () {
      test('handles multiple sequential team updates', () async {
        // Arrange
        final updateData = [
          {'teamId': 'seq1', 'name': 'Sequential Team 1'},
          {'teamId': 'seq2', 'name': 'Sequential Team 2'},
          {'teamId': 'seq3', 'name': 'Sequential Team 3'},
        ];

        when(mockRepository.updateTeam(
          teamId: anyNamed('teamId'),
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => updatedTeam);

        // Act
        for (final data in updateData) {
          final result = await useCase.call(
            teamId: data['teamId']! as String,
            name: data['name']! as String,
          );
          expect(result, updatedTeam);
        }

        // Assert
        for (final data in updateData) {
          verify(mockRepository.updateTeam(
            teamId: data['teamId']! as String,
            name: data['name']! as String,
            description: null,
            slogan: null,
            logo: null,
          )).called(1);
        }
      });

      test('handles concurrent team updates', () async {
        // Arrange
        final updateData = [
          {'teamId': 'conc1', 'name': 'Concurrent Team 1'},
          {'teamId': 'conc2', 'name': 'Concurrent Team 2'},
          {'teamId': 'conc3', 'name': 'Concurrent Team 3'},
        ];

        when(mockRepository.updateTeam(
          teamId: anyNamed('teamId'),
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
          return updatedTeam;
        });

        // Act
        final futures = updateData.map((data) => useCase.call(
              teamId: data['teamId']! as String,
              name: data['name']! as String,
            ));
        final results = await Future.wait(futures);

        // Assert
        for (final result in results) {
          expect(result, updatedTeam);
        }
        expect(results.length, updateData.length);
      });

      test('handles large update data efficiently', () async {
        // Arrange
        const teamId = 'large123';
        final largeDescription = 'Large description ${'data ' * 1000}';
        const name = 'Large Data Team';

        when(mockRepository.updateTeam(
          teamId: teamId,
          name: name,
          description: largeDescription,
          slogan: null,
          logo: null,
        )).thenAnswer((_) async => updatedTeam);

        // Act
        final stopwatch = Stopwatch()..start();
        final result = await useCase.call(
          teamId: teamId,
          name: name,
          description: largeDescription,
        );
        stopwatch.stop();

        // Assert
        expect(result, updatedTeam);
        // Performance assertion - should complete within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
      });
    });
  });
}
