import 'package:flutter_test/flutter_test.dart';

// Import all team invitation related tests
import 'data/datasources/team_invitations_remote_datasource_test.dart' as datasource_tests;
import 'data/dto/add_team_member_request_dto_test.dart' as dto_tests;
import 'domain/entities/team_role_test.dart' as entity_tests;
import 'domain/usecases/team_invitations_usecases_test.dart' as usecase_tests;
import 'integration/team_invitations_integration_test.dart' as integration_tests;

/// Comprehensive test suite for team invitations feature
/// 
/// This test suite covers:
/// - Domain entities (TeamRole)
/// - Data DTOs (AddTeamMemberRequestDto)
/// - Data sources (TeamInvitationsRemoteDataSource)
/// - Use cases (All team invitation use cases)
/// - Integration tests (End-to-end workflows)
/// 
/// Run this test suite to ensure all team invitation functionality
/// works correctly with the updated swagger.json API specifications.
void main() {
  group('Team Invitations Test Suite', () {
    group('Domain Layer Tests', () {
      group('Entities', () {
        entity_tests.main();
      });
      
      group('Use Cases', () {
        usecase_tests.main();
      });
    });

    group('Data Layer Tests', () {
      group('DTOs', () {
        dto_tests.main();
      });
      
      group('Data Sources', () {
        datasource_tests.main();
      });
    });

    group('Integration Tests', () {
      integration_tests.main();
    });
  });
}
