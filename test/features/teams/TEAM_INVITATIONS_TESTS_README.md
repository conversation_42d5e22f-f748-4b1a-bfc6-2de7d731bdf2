# Team Invitations Test Suite

This document describes the comprehensive test suite for the team invitations feature, which has been updated to align with the new swagger.json API specifications.

## Overview

The team invitations feature has been updated to match the swagger.json specifications with the following key changes:

1. **API Endpoints Updated**: All endpoints now match the swagger.json definitions
2. **TeamRole Enum**: Proper enum handling for Player/Captain roles
3. **Parameter Structure**: Updated to use named parameters with teamId and invitationId
4. **Request DTOs**: AddTeamMemberRequestDto now properly handles TeamRole enum

## Test Coverage

### Domain Layer Tests

#### 1. TeamRole Entity Tests (`domain/entities/team_role_test.dart`)
- ✅ Enum value validation
- ✅ String conversion (fromString/toString)
- ✅ Error handling for invalid roles
- ✅ Display names functionality

#### 2. Team Invitation Use Cases Tests (`domain/usecases/team_invitations_usecases_test.dart`)
- ✅ InviteTeamMemberUseCase
- ✅ AcceptInvitationUseCase
- ✅ DeclineInvitationUseCase
- ✅ DeleteTeamInviteUseCase
- ✅ AcceptTeamInviteUseCase
- ✅ Error handling for all use cases

### Data Layer Tests

#### 3. AddTeamMemberRequestDto Tests (`data/dto/add_team_member_request_dto_test.dart`)
- ✅ JSON serialization/deserialization
- ✅ TeamRole enum handling
- ✅ Copy functionality
- ✅ Equality comparison
- ✅ Error handling for invalid roles

#### 4. TeamInvitationsRemoteDataSource Tests (`data/datasources/team_invitations_remote_datasource_test.dart`)
- ✅ API endpoint validation
- ✅ Request payload validation
- ✅ HTTP method validation (POST/DELETE)
- ✅ Parameter replacement in URLs
- ✅ TeamRole enum serialization

### Integration Tests

#### 5. Team Invitations Integration Tests (`integration/team_invitations_integration_test.dart`)
- ✅ End-to-end invitation workflow
- ✅ Full stack testing (Use Case → Repository → DataSource → API)
- ✅ Error propagation testing
- ✅ Multiple role scenarios (Player/Captain)
- ✅ All invitation operations (invite, accept, decline, delete)

## API Endpoints Tested

The tests validate the following swagger.json endpoints:

1. **POST** `/api/teams/{teamId}/invitations` - Invite team members
2. **DELETE** `/api/teams/invitations/{invitationId}` - Delete team invite
3. **POST** `/api/teams/{teamId}/invitations/{invitationId}/accept` - Accept invitation
4. **POST** `/api/teams/{teamId}/invitations/{invitationId}/decline` - Decline invitation

## Running the Tests

### Run All Team Invitation Tests
```bash
flutter test test/features/teams/team_invitations_test_suite.dart
```

### Run Individual Test Files
```bash
# Domain tests
flutter test test/features/teams/domain/entities/team_role_test.dart
flutter test test/features/teams/domain/usecases/team_invitations_usecases_test.dart

# Data tests
flutter test test/features/teams/data/dto/add_team_member_request_dto_test.dart
flutter test test/features/teams/data/datasources/team_invitations_remote_datasource_test.dart

# Integration tests
flutter test test/features/teams/integration/team_invitations_integration_test.dart
```

### Generate Test Coverage
```bash
flutter test --coverage test/features/teams/team_invitations_test_suite.dart
genhtml coverage/lcov.info -o coverage/html
```

## Test Data Factory

The tests use mock data and factories to ensure consistent test scenarios. Key test data includes:

- **Team IDs**: `test-team-id`, `team-123`
- **Player IDs**: `test-player-id`, `player-456`
- **Invitation IDs**: `test-invitation-id`, `invitation-789`
- **Roles**: `TeamRole.player`, `TeamRole.captain`

## Known Issues and TODOs

1. **UI Layer**: Some UI components still use placeholder teamIds due to API parameter changes
2. **Mock Generation**: Run `flutter packages pub run build_runner build` to generate missing mocks
3. **Repository Tests**: Existing repository tests may need updates for new parameter structure

## Swagger.json Compliance

All tests validate compliance with the swagger.json specifications:

- ✅ AddTeamMemberRequest schema validation
- ✅ TeamRole enum values (Player, Captain)
- ✅ Correct HTTP methods and endpoints
- ✅ Proper request/response handling
- ✅ Error scenarios and status codes

## Test Metrics

- **Total Test Cases**: 25+
- **Coverage Target**: 80%+
- **Test Types**: Unit, Integration, End-to-End
- **Layers Covered**: Domain, Data, Integration

## Maintenance

When updating the swagger.json or API specifications:

1. Update the corresponding test cases
2. Verify endpoint URLs in test assertions
3. Update request/response validation
4. Run the full test suite to ensure compatibility
5. Update this README with any new test scenarios
