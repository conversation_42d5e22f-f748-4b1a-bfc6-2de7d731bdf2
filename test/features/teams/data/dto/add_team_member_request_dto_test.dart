import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/teams/data/dto/add_team_member_request_dto.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team_role.dart';

void main() {
  group('AddTeamMemberRequestDto', () {
    const testPlayerId = 'test-player-id';
    const testRole = TeamRole.player;

    test('should create instance with required parameters', () {
      const dto = AddTeamMemberRequestDto(
        playerId: testPlayerId,
        role: testRole,
      );

      expect(dto.playerId, testPlayerId);
      expect(dto.role, testRole);
    });

    test('should convert to JSON correctly', () {
      const dto = AddTeamMemberRequestDto(
        playerId: testPlayerId,
        role: testRole,
      );

      final json = dto.toJson();

      expect(json['playerId'], testPlayerId);
      expect(json['role'], 'Player');
    });

    test('should create from JSON correctly', () {
      final json = {
        'playerId': testPlayerId,
        'role': 'Player',
      };

      final dto = AddTeamMemberRequestDto.fromJson(json);

      expect(dto.playerId, testPlayerId);
      expect(dto.role, TeamRole.player);
    });

    test('should create from JSON with Captain role', () {
      final json = {
        'playerId': testPlayerId,
        'role': 'Captain',
      };

      final dto = AddTeamMemberRequestDto.fromJson(json);

      expect(dto.playerId, testPlayerId);
      expect(dto.role, TeamRole.captain);
    });

    test('should throw error when creating from JSON with invalid role', () {
      final json = {
        'playerId': testPlayerId,
        'role': 'InvalidRole',
      };

      expect(
        () => AddTeamMemberRequestDto.fromJson(json),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('should create copy with updated values', () {
      const originalDto = AddTeamMemberRequestDto(
        playerId: testPlayerId,
        role: testRole,
      );

      const newPlayerId = 'new-player-id';
      const newRole = TeamRole.captain;

      final copiedDto = originalDto.copyWith(
        playerId: newPlayerId,
        role: newRole,
      );

      expect(copiedDto.playerId, newPlayerId);
      expect(copiedDto.role, newRole);
      expect(originalDto.playerId, testPlayerId);
      expect(originalDto.role, testRole);
    });

    test('should create copy with partial updates', () {
      const originalDto = AddTeamMemberRequestDto(
        playerId: testPlayerId,
        role: testRole,
      );

      final copiedDto = originalDto.copyWith(role: TeamRole.captain);

      expect(copiedDto.playerId, testPlayerId);
      expect(copiedDto.role, TeamRole.captain);
    });

    test('should support equality comparison', () {
      const dto1 = AddTeamMemberRequestDto(
        playerId: testPlayerId,
        role: testRole,
      );

      const dto2 = AddTeamMemberRequestDto(
        playerId: testPlayerId,
        role: testRole,
      );

      const dto3 = AddTeamMemberRequestDto(
        playerId: 'different-id',
        role: testRole,
      );

      expect(dto1, equals(dto2));
      expect(dto1, isNot(equals(dto3)));
    });
  });
}
