import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:nextsportz_v2/core/networking/api_client.dart';
import 'package:nextsportz_v2/core/networking/api_const.dart';
import 'package:nextsportz_v2/features/teams/data/datasources/team_invitations_remote_datasource.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team_role.dart';

import 'team_invitations_remote_datasource_test.mocks.dart';

@GenerateMocks([ApiClient])
void main() {
  late MockApiClient mockApiClient;
  late TeamInvitationsRemoteDataSource dataSource;

  setUp(() {
    mockApiClient = MockApiClient();
    dataSource = TeamInvitationsRemoteDataSource(mockApiClient);
  });

  group('TeamInvitationsRemoteDataSource', () {
    group('inviteTeamMember', () {
      test('should call API with correct endpoint and data', () async {
        // Arrange
        const teamId = 'test-team-id';
        const playerId = 'test-player-id';
        const role = TeamRole.player;

        when(mockApiClient.post(any, data: anyNamed('data')))
            .thenAnswer((_) async => {});

        // Act
        await dataSource.inviteTeamMember(
          teamId: teamId,
          playerId: playerId,
          role: role,
        );

        // Assert
        final expectedEndpoint = ApiConst.teamInvitePlayerEndpoint
            .replaceAll('{teamId}', teamId);
        
        verify(mockApiClient.post(
          expectedEndpoint,
          data: {
            'playerId': playerId,
            'role': 'Player',
          },
        )).called(1);
      });

      test('should call API with Captain role', () async {
        // Arrange
        const teamId = 'test-team-id';
        const playerId = 'test-player-id';
        const role = TeamRole.captain;

        when(mockApiClient.post(any, data: anyNamed('data')))
            .thenAnswer((_) async => {});

        // Act
        await dataSource.inviteTeamMember(
          teamId: teamId,
          playerId: playerId,
          role: role,
        );

        // Assert
        verify(mockApiClient.post(
          any,
          data: {
            'playerId': playerId,
            'role': 'Captain',
          },
        )).called(1);
      });
    });

    group('acceptInvitation', () {
      test('should call API with correct endpoint', () async {
        // Arrange
        const teamId = 'test-team-id';
        const invitationId = 'test-invitation-id';

        when(mockApiClient.post(any)).thenAnswer((_) async => {});

        // Act
        await dataSource.acceptInvitation(
          teamId: teamId,
          invitationId: invitationId,
        );

        // Assert
        final expectedEndpoint = ApiConst.teamInvitationAcceptEndpoint
            .replaceAll('{teamId}', teamId)
            .replaceAll('{invitationId}', invitationId);
        
        verify(mockApiClient.post(expectedEndpoint)).called(1);
      });
    });

    group('declineInvitation', () {
      test('should call API with correct endpoint', () async {
        // Arrange
        const teamId = 'test-team-id';
        const invitationId = 'test-invitation-id';

        when(mockApiClient.post(any)).thenAnswer((_) async => {});

        // Act
        await dataSource.declineInvitation(
          teamId: teamId,
          invitationId: invitationId,
        );

        // Assert
        final expectedEndpoint = ApiConst.teamInvitationDeclineEndpoint
            .replaceAll('{teamId}', teamId)
            .replaceAll('{invitationId}', invitationId);
        
        verify(mockApiClient.post(expectedEndpoint)).called(1);
      });
    });

    group('deleteTeamInvite', () {
      test('should call API with correct endpoint', () async {
        // Arrange
        const teamId = 'test-team-id';
        const memberId = 'test-member-id';

        when(mockApiClient.delete(any)).thenAnswer((_) async => {});

        // Act
        await dataSource.deleteTeamInvite(
          teamId: teamId,
          memberId: memberId,
        );

        // Assert
        final expectedEndpoint = ApiConst.teamInvitationDeleteEndpoint
            .replaceAll('{invitationId}', memberId);
        
        verify(mockApiClient.delete(expectedEndpoint)).called(1);
      });
    });

    group('acceptTeamInvite', () {
      test('should call API with correct endpoint', () async {
        // Arrange
        const teamId = 'test-team-id';
        const memberId = 'test-member-id';

        when(mockApiClient.post(any)).thenAnswer((_) async => {});

        // Act
        await dataSource.acceptTeamInvite(
          teamId: teamId,
          memberId: memberId,
        );

        // Assert
        final expectedEndpoint = ApiConst.teamInvitationAcceptEndpoint
            .replaceAll('{teamId}', teamId)
            .replaceAll('{invitationId}', memberId);
        
        verify(mockApiClient.post(expectedEndpoint)).called(1);
      });
    });
  });
}
