import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:nextsportz_v2/core/networking/api_client.dart';
import 'package:nextsportz_v2/features/teams/data/datasources/teams_remote_datasource.dart';
import 'package:nextsportz_v2/features/teams/data/dto/team_dto.dart';
import 'package:nextsportz_v2/features/teams/data/dto/team_request_dto.dart';
import 'package:nextsportz_v2/features/teams/data/dto/team_invitation_dto.dart';
import 'package:nextsportz_v2/features/teams/data/dto/team_member_dto.dart';
import 'package:nextsportz_v2/features/teams/data/dto/team_details_dto.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';
import 'package:nextsportz_v2/core/networking/api_const.dart';
import 'package:dio/dio.dart';
import 'package:nextsportz_v2/core/networking/exception.dart';

import 'teams_remote_datasource_test.mocks.dart';
import '../../test_data_factory.dart';

@GenerateMocks([ApiClient])
void main() {
  group('TeamsRemoteDatasource', () {
    late MockApiClient mockApiClient;
    late TeamsRemoteDataSource teamsRemoteDatasource;

    setUp(() {
      mockApiClient = MockApiClient();
      teamsRemoteDatasource = TeamsRemoteDataSource(mockApiClient);
    });

    group('getMyTeams', () {
      test('should get teams successfully', () async {
        // Arrange
        final responseData = {
          'data': [
            {
              'id': '1',
              'name': 'Team Alpha',
              'description': 'A great team',
              'logo': 'https://example.com/logo.png',
              'slogan': 'We are the best',
              'created_by': 'user1',
              'created_at': '2023-01-01T00:00:00Z',
              'updated_at': '2023-01-01T00:00:00Z',
              'is_active': true,
              'member_count': 5,
              'stats': {
                'total_matches': 10,
                'wins': 7,
                'losses': 2,
                'draws': 1,
                'win_rate': 0.7,
                'total_goals': 25,
                'goals_conceded': 10,
                'clean_sheets': 3,
                'average_goals_per_match': 2.5,
              },
            },
          ],
        };

        when(
          mockApiClient.get('/api/teams/me'),
        ).thenAnswer((_) async => responseData);

        // Act
        final result = await teamsRemoteDatasource.getMyTeams();

        // Assert
        expect(result, isA<List<TeamSearchItem>>());
        expect(result.length, equals(1));
        expect(result.first.name, equals('Team Alpha'));
        expect(result.first.membersCount, equals(5));
        expect(result.first.winRate, equals('0.70'));
        verify(mockApiClient.get('/api/teams/me')).called(1);
      });

      test('should bubble DioExceptionHandle on transport error', () async {
        final dioError = DioException(
          requestOptions: RequestOptions(path: '/api/teams/me'),
          response: Response(
            requestOptions: RequestOptions(path: '/api/teams/me'),
            statusCode: 500,
          ),
          type: DioExceptionType.badResponse,
          message: 'Server error',
        );

        when(
          mockApiClient.get('/api/teams/me'),
        ).thenThrow(DioExceptionHandle.fromDioError(dioError));

        expect(
          () => teamsRemoteDatasource.getMyTeams(),
          throwsA(isA<DioExceptionHandle>()),
        );
      });
    });

    group('getTeamById', () {
      test('should get team by id successfully', () async {
        // Arrange
        const teamId = '1';
        final teamData = {
          'id': teamId,
          'name': 'Team Alpha',
          'description': 'A great team',
          'logo': 'logo1.png',
          'slogan': 'We are the best',
          'created_by': 'user1',
          'created_at': '2023-01-01T00:00:00Z',
          'updated_at': '2023-01-01T00:00:00Z',
          'is_active': true,
          'members': [],
          'invitations': [],
          'stats': {
            'total_matches': 10,
            'wins': 7,
            'losses': 2,
            'draws': 1,
            'win_rate': 0.7,
            'total_goals': 25,
            'goals_conceded': 10,
            'clean_sheets': 3,
            'average_goals_per_match': 2.5,
          },
        };

        when(
          mockApiClient.get('/api/teams/$teamId'),
        ).thenAnswer((_) async => teamData);

        // Act
        final result = await teamsRemoteDatasource.getTeamById(teamId);

        // Assert
        expect(result, isA<Team>());
        expect(result.id, equals(teamId));
        expect(result.name, equals('Team Alpha'));
        verify(mockApiClient.get('/api/teams/$teamId')).called(1);
      });
    });

    group('getTeamDetails', () {
      test('should get team details successfully', () async {
        // Arrange
        const teamId = '1';
        final teamDetailsData = TeamsTestDataFactory.createTestTeamDetailsJson(
          id: teamId,
          name: 'Team Alpha',
          description: 'A great team',
          logoUrl: 'https://example.com/logo.png',
        );

        when(
          mockApiClient.get('/api/teams/$teamId'),
        ).thenAnswer((_) async => {'data': teamDetailsData});

        // Act
        final result = await teamsRemoteDatasource.getTeamDetails(teamId);

        // Assert
        expect(result, isA<TeamDetails>());
        expect(result.id, equals(teamId));
        expect(result.name, equals('Team Alpha'));
        expect(result.description, equals('A great team'));
        expect(result.logoUrl, equals('https://example.com/logo.png'));
        expect(result.members, isA<List<TeamDetailMember>>());
        expect(result.stats, isA<TeamDetailStats>());
        verify(mockApiClient.get('/api/teams/$teamId')).called(1);
      });

      test('should handle response without data wrapper', () async {
        // Arrange
        const teamId = '1';
        final teamDetailsData = TeamsTestDataFactory.createTestTeamDetailsJson(
          id: teamId,
          name: 'Team Alpha',
        );

        when(
          mockApiClient.get('/api/teams/$teamId'),
        ).thenAnswer((_) async => teamDetailsData);

        // Act
        final result = await teamsRemoteDatasource.getTeamDetails(teamId);

        // Assert
        expect(result, isA<TeamDetails>());
        expect(result.id, equals(teamId));
        expect(result.name, equals('Team Alpha'));
        verify(mockApiClient.get('/api/teams/$teamId')).called(1);
      });

      test('should bubble DioExceptionHandle on transport error', () async {
        const teamId = '1';
        final dioError = DioException(
          requestOptions: RequestOptions(path: '/api/teams/$teamId'),
          response: Response(
            requestOptions: RequestOptions(path: '/api/teams/$teamId'),
            statusCode: 404,
          ),
          type: DioExceptionType.badResponse,
          message: 'Team not found',
        );

        when(
          mockApiClient.get('/api/teams/$teamId'),
        ).thenThrow(DioExceptionHandle.fromDioError(dioError));

        expect(
          () => teamsRemoteDatasource.getTeamDetails(teamId),
          throwsA(isA<DioExceptionHandle>()),
        );
      });
    });

    group('createTeam', () {
      test('should create team successfully', () async {
        // Arrange
        final requestDto = CreateTeamRequestDto(
          name: 'New Team',
          description: 'A new team',
          logo: 'new_logo.png',
          slogan: 'New slogan',
        );

        final responseData = {
          'id': '2',
          'name': 'New Team',
          'description': 'A new team',
          'logo': 'new_logo.png',
          'slogan': 'New slogan',
          'created_by': 'user1',
          'created_at': '2023-01-01T00:00:00Z',
          'updated_at': '2023-01-01T00:00:00Z',
          'is_active': true,
          'members': [],
          'invitations': [],
          'stats': {
            'total_matches': 0,
            'wins': 0,
            'losses': 0,
            'draws': 0,
            'win_rate': 0.0,
            'total_goals': 0,
            'goals_conceded': 0,
            'clean_sheets': 0,
            'average_goals_per_match': 0.0,
          },
        };

        when(
          mockApiClient.post('/api/teams', data: requestDto.toJson()),
        ).thenAnswer((_) async => responseData);

        // Act
        final result = await teamsRemoteDatasource.createTeam(
          name: 'New Team',
          description: 'A new team',
          logo: 'new_logo.png',
          slogan: 'New slogan',
        );

        // Assert
        expect(result, isA<Team>());
        expect(result.name, equals('New Team'));
        expect(result.description, equals('A new team'));
        verify(
          mockApiClient.post('/api/teams', data: requestDto.toJson()),
        ).called(1);
      });
    });

    group('updateTeam', () {
      test('should update team successfully', () async {
        // Arrange
        const teamId = '1';
        final requestDto = UpdateTeamRequestDto(
          name: 'Updated Team',
          description: 'Updated description',
          logo: 'updated_logo.png',
          slogan: 'Updated slogan',
        );

        final responseData = {
          'id': teamId,
          'name': 'Updated Team',
          'description': 'Updated description',
          'logo': 'updated_logo.png',
          'slogan': 'Updated slogan',
          'created_by': 'user1',
          'created_at': '2023-01-01T00:00:00Z',
          'updated_at': '2023-01-02T00:00:00Z',
          'is_active': true,
          'members': [],
          'invitations': [],
          'stats': {
            'total_matches': 10,
            'wins': 7,
            'losses': 2,
            'draws': 1,
            'win_rate': 0.7,
            'total_goals': 25,
            'goals_conceded': 10,
            'clean_sheets': 3,
            'average_goals_per_match': 2.5,
          },
        };

        when(
          mockApiClient.put('/api/teams/$teamId', data: requestDto.toJson()),
        ).thenAnswer((_) async => responseData);

        // Act
        final result = await teamsRemoteDatasource.updateTeam(
          teamId: teamId,
          name: 'Updated Team',
          description: 'Updated description',
          logo: 'updated_logo.png',
          slogan: 'Updated slogan',
        );

        // Assert
        expect(result, isA<Team>());
        expect(result.name, equals('Updated Team'));
        expect(result.description, equals('Updated description'));
        verify(
          mockApiClient.put('/api/teams/$teamId', data: requestDto.toJson()),
        ).called(1);
      });
    });

    group('deleteTeam', () {
      test('should delete team successfully', () async {
        // Arrange
        const teamId = '1';

        when(
          mockApiClient.delete('/api/teams/$teamId'),
        ).thenAnswer((_) async => {});

        // Act
        await teamsRemoteDatasource.deleteTeam(teamId);

        // Assert
        verify(mockApiClient.delete('/api/teams/$teamId')).called(1);
      });
    });

    group('invitePlayer', () {
      test('should invite player successfully', () async {
        // Arrange
        const teamId = '1';
        const playerId = 'player1';
        const message = 'Join our team!';
        final requestDto = InvitePlayerRequestDto(
          playerId: playerId,
          message: message,
        );

        when(
          mockApiClient.post(
            '/api/teams/$teamId/invite',
            data: requestDto.toJson(),
          ),
        ).thenAnswer((_) async => {});

        // Act
        await teamsRemoteDatasource.invitePlayer(
          teamId: teamId,
          playerId: playerId,
          message: message,
        );

        // Assert
        verify(
          mockApiClient.post(
            '/api/teams/$teamId/invite',
            data: requestDto.toJson(),
          ),
        ).called(1);
      });
    });

    group('acceptInvitation', () {
      test('should accept invitation successfully', () async {
        // Arrange
        const invitationId = 'inv1';
        final requestDto = InvitationResponseRequestDto(action: 'accept');

        when(
          mockApiClient.post(
            '/api/teams/invitations/$invitationId/respond',
            data: requestDto.toJson(),
          ),
        ).thenAnswer((_) async => {});

        // Act
        await teamsRemoteDatasource.acceptInvitation(invitationId);

        // Assert
        verify(
          mockApiClient.post(
            '/api/teams/invitations/$invitationId/respond',
            data: requestDto.toJson(),
          ),
        ).called(1);
      });
    });

    group('declineInvitation', () {
      test('should decline invitation successfully', () async {
        // Arrange
        const invitationId = 'inv1';
        final requestDto = InvitationResponseRequestDto(action: 'decline');

        when(
          mockApiClient.post(
            '/api/teams/invitations/$invitationId/respond',
            data: requestDto.toJson(),
          ),
        ).thenAnswer((_) async => {});

        // Act
        await teamsRemoteDatasource.declineInvitation(invitationId);

        // Assert
        verify(
          mockApiClient.post(
            '/api/teams/invitations/$invitationId/respond',
            data: requestDto.toJson(),
          ),
        ).called(1);
      });
    });

    group('removeMember', () {
      test('should remove member successfully', () async {
        // Arrange
        const teamId = '1';
        const memberId = 'member1';

        when(
          mockApiClient.delete('/api/teams/$teamId/members/$memberId'),
        ).thenAnswer((_) async => {});

        // Act
        await teamsRemoteDatasource.removeMember(
          teamId: teamId,
          memberId: memberId,
        );

        // Assert
        verify(
          mockApiClient.delete('/api/teams/$teamId/members/$memberId'),
        ).called(1);
      });
    });

    group('updateMemberRole', () {
      test('should update member role successfully', () async {
        // Arrange
        const teamId = '1';
        const memberId = 'member1';
        const role = 'CAPTAIN';
        final requestDto = UpdateMemberRoleRequestDto(role: role);

        when(
          mockApiClient.put(
            '/api/teams/$teamId/members/$memberId/role',
            data: requestDto.toJson(),
          ),
        ).thenAnswer((_) async => {});

        // Act
        await teamsRemoteDatasource.updateMemberRole(
          teamId: teamId,
          memberId: memberId,
          role: role,
        );

        // Assert
        verify(
          mockApiClient.put(
            '/api/teams/$teamId/members/$memberId/role',
            data: requestDto.toJson(),
          ),
        ).called(1);
      });
    });

    group('uploadTeamLogo', () {
      test('should upload team logo successfully', () async {
        // Arrange
        final mockFile = MockFile();
        final responseData = {'url': 'https://example.com/logo.png'};

        when(
          mockApiClient.post('/api/upload', data: anyNamed('data')),
        ).thenAnswer((_) async => responseData);

        // Act
        final result = await teamsRemoteDatasource.uploadTeamLogo(mockFile);

        // Assert
        expect(result, equals('https://example.com/logo.png'));
        verify(
          mockApiClient.post('/api/upload', data: anyNamed('data')),
        ).called(1);
      });
    });

    group('searchTeamsPaginated', () {
      test('should search teams with pagination successfully', () async {
        // Arrange
        const query = 'test';
        const page = 1;
        const pageSize = 20;
        final responseData =
            TeamsTestDataFactory.createTestPaginatedResponseJson(
              items: List.generate(
                5,
                (index) => TeamsTestDataFactory.createTestTeamSearchItemJson(
                  id: '${index + 1}',
                  name: 'Test Team ${index + 1}',
                ),
              ),
              total: 5,
              page: page,
              pageSize: pageSize,
              totalPages: 1,
            );

        when(
          mockApiClient.get(
            ApiConst.searchTeamsEndpoint,
            query: {'search': query, 'page': page, 'pageSize': pageSize},
          ),
        ).thenAnswer((_) async => responseData);

        // Act
        final result = await teamsRemoteDatasource.searchTeamsPaginated(
          query: query,
          page: page,
          pageSize: pageSize,
        );

        // Assert
        expect(result, isA<PaginatedResponse<TeamSearchItem>>());
        expect(result.data.items.length, equals(5));
        expect(result.data.page, equals(page));
        expect(result.data.pageSize, equals(pageSize));
        expect(result.data.total, equals(5));
        expect(result.data.totalPages, equals(1));
        expect(result.data.items.first.name, equals('Test Team 1'));
        verify(
          mockApiClient.get(
            ApiConst.searchTeamsEndpoint,
            query: {'search': query, 'page': page, 'pageSize': pageSize},
          ),
        ).called(1);
      });

      test('should search teams without query parameters', () async {
        // Arrange
        final responseData =
            TeamsTestDataFactory.createTestPaginatedResponseJson();

        when(
          mockApiClient.get(ApiConst.searchTeamsEndpoint, query: {}),
        ).thenAnswer((_) async => responseData);

        // Act
        final result = await teamsRemoteDatasource.searchTeamsPaginated();

        // Assert
        expect(result, isA<PaginatedResponse<TeamSearchItem>>());
        expect(result.data.items.length, equals(20));
        verify(
          mockApiClient.get(ApiConst.searchTeamsEndpoint, query: {}),
        ).called(1);
      });

      test(
        'should handle DioException and rethrow DioExceptionHandle',
        () async {
          // Arrange
          final dioError = DioException(
            requestOptions: RequestOptions(path: ApiConst.searchTeamsEndpoint),
            response: Response(
              requestOptions: RequestOptions(
                path: ApiConst.searchTeamsEndpoint,
              ),
              statusCode: 500,
            ),
            type: DioExceptionType.badResponse,
            message: 'Server error',
          );

          when(
            mockApiClient.get(
              ApiConst.searchTeamsEndpoint,
              query: anyNamed('query'),
            ),
          ).thenThrow(dioError);

          // Act & Assert
          expect(
            () => teamsRemoteDatasource.searchTeamsPaginated(),
            throwsA(isA<DioExceptionHandle>()),
          );
        },
      );

      test('should handle generic exception and throw Exception', () async {
        // Arrange
        when(
          mockApiClient.get(
            ApiConst.searchTeamsEndpoint,
            query: anyNamed('query'),
          ),
        ).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () => teamsRemoteDatasource.searchTeamsPaginated(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('getAllTeamsPaginated', () {
      test('should get all teams with pagination successfully', () async {
        // Arrange
        const page = 2;
        const pageSize = 10;
        final responseData =
            TeamsTestDataFactory.createTestPaginatedResponseJson(
              items: List.generate(
                10,
                (index) => TeamsTestDataFactory.createTestTeamSearchItemJson(
                  id: '${index + 11}',
                  name: 'Team ${index + 11}',
                ),
              ),
              total: 25,
              page: page,
              pageSize: pageSize,
              totalPages: 3,
            );

        when(
          mockApiClient.get(
            ApiConst.allTeamsEndpoint,
            query: {'page': page, 'pageSize': pageSize},
          ),
        ).thenAnswer((_) async => responseData);

        // Act
        final result = await teamsRemoteDatasource.getAllTeamsPaginated(
          page: page,
          pageSize: pageSize,
        );

        // Assert
        expect(result, isA<PaginatedResponse<TeamSearchItem>>());
        expect(result.data.items.length, equals(10));
        expect(result.data.page, equals(page));
        expect(result.data.pageSize, equals(pageSize));
        expect(result.data.total, equals(25));
        expect(result.data.totalPages, equals(3));
        expect(result.data.hasNextPage, isTrue);
        expect(result.data.hasPreviousPage, isTrue);
        verify(
          mockApiClient.get(
            ApiConst.allTeamsEndpoint,
            query: {'page': page, 'pageSize': pageSize},
          ),
        ).called(1);
      });

      test('should get all teams without pagination parameters', () async {
        // Arrange
        final responseData =
            TeamsTestDataFactory.createTestPaginatedResponseJson();

        when(
          mockApiClient.get(ApiConst.allTeamsEndpoint, query: {}),
        ).thenAnswer((_) async => responseData);

        // Act
        final result = await teamsRemoteDatasource.getAllTeamsPaginated();

        // Assert
        expect(result, isA<PaginatedResponse<TeamSearchItem>>());
        expect(result.data.items.length, equals(20));
        verify(
          mockApiClient.get(ApiConst.allTeamsEndpoint, query: {}),
        ).called(1);
      });

      test('should handle empty response', () async {
        // Arrange
        final responseData =
            TeamsTestDataFactory.createTestPaginatedResponseJson(
              items: [],
              total: 0,
              totalPages: 0,
            );

        when(
          mockApiClient.get(
            ApiConst.allTeamsEndpoint,
            query: anyNamed('query'),
          ),
        ).thenAnswer((_) async => responseData);

        // Act
        final result = await teamsRemoteDatasource.getAllTeamsPaginated();

        // Assert
        expect(result, isA<PaginatedResponse<TeamSearchItem>>());
        expect(result.data.items.isEmpty, isTrue);
        expect(result.data.total, equals(0));
        expect(result.data.totalPages, equals(0));
        expect(result.data.hasNextPage, isFalse);
      });

      test('should handle DioExceptionHandle and rethrow', () async {
        // Arrange
        final dioExceptionHandle = DioExceptionHandle.fromDioError(
          DioException(
            requestOptions: RequestOptions(path: ApiConst.allTeamsEndpoint),
            type: DioExceptionType.connectionError,
          ),
        );

        when(
          mockApiClient.get(
            ApiConst.allTeamsEndpoint,
            query: anyNamed('query'),
          ),
        ).thenThrow(dioExceptionHandle);

        // Act & Assert
        expect(
          () => teamsRemoteDatasource.getAllTeamsPaginated(),
          throwsA(same(dioExceptionHandle)),
        );
      });
    });
  });
}

class MockFile {
  String get path => '/path/to/file.jpg';
}
