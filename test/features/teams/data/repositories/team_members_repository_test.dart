import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:nextsportz_v2/features/teams/data/repositories/team_members_repository_impl.dart';
import 'package:nextsportz_v2/features/teams/data/datasources/team_members_datasource.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/core/networking/exception.dart';

import 'team_members_repository_test.mocks.dart';

@GenerateMocks([TeamMembersDatasource])
void main() {
  group('TeamMembersRepositoryImpl', () {
    late TeamMembersRepositoryImpl repository;
    late MockTeamMembersDatasource mockDataSource;

    setUp(() {
      mockDataSource = MockTeamMembersDatasource();
      repository = TeamMembersRepositoryImpl(mockDataSource);
    });

    final testTeamId = 'team123';
    final testMemberId = 'member123';
    final testMembers = [
      TeamMember(
        id: 'member1',
        userId: 'user1',
        name: '<PERSON> <PERSON>e',
        position: 'Forward',
        role: 'captain',
        joinedAt: DateTime.now(),
        isActive: true,
        stats: PlayerStats(
          matchesPlayed: 10,
          goals: 5,
          assists: 3,
          cleanSheets: 2,
          rating: 4.5,
          yellowCards: 1,
          redCards: 0,
          minutesPlayed: 900,
        ),
      ),
    ];

    group('getTeamMembers', () {
      test('returns team members from data source successfully', () async {
        // Arrange
        when(mockDataSource.getTeamMembers(testTeamId))
            .thenAnswer((_) async => testMembers);

        // Act
        final result = await repository.getTeamMembers(testTeamId);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (error) => fail('Expected success but got error: $error'),
          (members) => expect(members, equals(testMembers)),
        );
        verify(mockDataSource.getTeamMembers(testTeamId)).called(1);
      });

      test('returns error when data source fails with DioExceptionHandle', () async {
        // Arrange
        when(mockDataSource.getTeamMembers(testTeamId))
            .thenThrow(DioExceptionHandle('Network error'));

        // Act
        final result = await repository.getTeamMembers(testTeamId);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, equals('Network error')),
          (members) => fail('Expected error but got success'),
        );
      });

      test('returns error when unknown exception occurs', () async {
        // Arrange
        when(mockDataSource.getTeamMembers(testTeamId))
            .thenThrow(Exception('Unknown error'));

        // Act
        final result = await repository.getTeamMembers(testTeamId);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, contains('Exception: Unknown error')),
          (members) => fail('Expected error but got success'),
        );
      });
    });

    group('removeMember', () {
      test('removes member successfully', () async {
        // Arrange
        when(mockDataSource.removeMember(
          teamId: testTeamId,
          memberId: testMemberId,
        )).thenAnswer((_) async {});

        // Act
        final result = await repository.removeMember(
          teamId: testTeamId,
          memberId: testMemberId,
        );

        // Assert
        expect(result.isRight(), true);
        verify(mockDataSource.removeMember(
          teamId: testTeamId,
          memberId: testMemberId,
        )).called(1);
      });
    });
  });
}
