// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in nextsportz_v2/test/features/teams/data/repositories/team_members_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:nextsportz_v2/features/teams/data/datasources/team_members_datasource.dart'
    as _i3;
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTeamMember_0 extends _i1.SmartFake implements _i2.TeamMember {
  _FakeTeamMember_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [TeamMembersDatasource].
///
/// See the documentation for Mockito's code generation for more information.
class MockTeamMembersDatasource extends _i1.Mock
    implements _i3.TeamMembersDatasource {
  MockTeamMembersDatasource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i2.TeamMember>> getTeamMembers(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamMembers, [teamId]),
            returnValue: _i4.Future<List<_i2.TeamMember>>.value(
              <_i2.TeamMember>[],
            ),
          )
          as _i4.Future<List<_i2.TeamMember>>);

  @override
  _i4.Future<_i2.TeamMember> getTeamMember(String? teamId, String? memberId) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamMember, [teamId, memberId]),
            returnValue: _i4.Future<_i2.TeamMember>.value(
              _FakeTeamMember_0(
                this,
                Invocation.method(#getTeamMember, [teamId, memberId]),
              ),
            ),
          )
          as _i4.Future<_i2.TeamMember>);

  @override
  _i4.Future<void> removeMember({
    required String? teamId,
    required String? memberId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#removeMember, [], {
              #teamId: teamId,
              #memberId: memberId,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> updateMemberRole({
    required String? teamId,
    required String? memberId,
    required String? role,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateMemberRole, [], {
              #teamId: teamId,
              #memberId: memberId,
              #role: role,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<int> getTeamMembersCount(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamMembersCount, [teamId]),
            returnValue: _i4.Future<int>.value(0),
          )
          as _i4.Future<int>);
}
