import 'package:flutter_test/flutter_test.dart';
import 'package:fpdart/fpdart.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/core/networking/exception.dart';
import 'package:nextsportz_v2/features/teams/data/datasources/team_invitations_datasource.dart';
import 'package:nextsportz_v2/features/teams/data/repositories/team_invitations_repository_impl.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team_role.dart';

import 'team_invitations_repository_impl_test.mocks.dart';

@GenerateMocks([TeamInvitationsDatasource])
void main() {
  late MockTeamInvitationsDatasource mockDataSource;
  late TeamInvitationsRepositoryImpl repository;

  setUp(() {
    mockDataSource = MockTeamInvitationsDatasource();
    repository = TeamInvitationsRepositoryImpl(mockDataSource);
  });

  group('TeamInvitationsRepositoryImpl', () {
    group('inviteTeamMember', () {
      test('should return Right when invitation succeeds', () async {
        // Arrange
        const teamId = 'test-team-id';
        const playerId = 'test-player-id';
        const role = TeamRole.player;

        when(mockDataSource.inviteTeamMember(
          teamId: anyNamed('teamId'),
          playerId: anyNamed('playerId'),
          role: anyNamed('role'),
        )).thenAnswer((_) async => {});

        // Act
        final result = await repository.inviteTeamMember(
          teamId: teamId,
          playerId: playerId,
          role: role,
        );

        // Assert
        expect(result, isA<Right<AppError, void>>());
        verify(mockDataSource.inviteTeamMember(
          teamId: teamId,
          playerId: playerId,
          role: role,
        )).called(1);
      });

      test('should return Left when DioExceptionHandle is thrown', () async {
        // Arrange
        const teamId = 'test-team-id';
        const playerId = 'test-player-id';
        const role = TeamRole.captain;
        const errorMessage = 'Network error';

        when(mockDataSource.inviteTeamMember(
          teamId: anyNamed('teamId'),
          playerId: anyNamed('playerId'),
          role: anyNamed('role'),
        )).thenThrow(const DioExceptionHandle(errorMessage));

        // Act
        final result = await repository.inviteTeamMember(
          teamId: teamId,
          playerId: playerId,
          role: role,
        );

        // Assert
        expect(result, isA<Left<AppError, void>>());
        expect(result.fold((l) => l.message, (r) => null), errorMessage);
      });

      test('should return Left when generic exception is thrown', () async {
        // Arrange
        const teamId = 'test-team-id';
        const playerId = 'test-player-id';
        const role = TeamRole.player;
        const errorMessage = 'Generic error';

        when(mockDataSource.inviteTeamMember(
          teamId: anyNamed('teamId'),
          playerId: anyNamed('playerId'),
          role: anyNamed('role'),
        )).thenThrow(Exception(errorMessage));

        // Act
        final result = await repository.inviteTeamMember(
          teamId: teamId,
          playerId: playerId,
          role: role,
        );

        // Assert
        expect(result, isA<Left<AppError, void>>());
        expect(result.fold((l) => l.message, (r) => null), contains(errorMessage));
      });
    });

    group('acceptInvitation', () {
      test('should return Right when accepting invitation succeeds', () async {
        // Arrange
        const teamId = 'test-team-id';
        const invitationId = 'test-invitation-id';

        when(mockDataSource.acceptInvitation(
          teamId: anyNamed('teamId'),
          invitationId: anyNamed('invitationId'),
        )).thenAnswer((_) async => {});

        // Act
        final result = await repository.acceptInvitation(
          teamId: teamId,
          invitationId: invitationId,
        );

        // Assert
        expect(result, isA<Right<AppError, void>>());
        verify(mockDataSource.acceptInvitation(
          teamId: teamId,
          invitationId: invitationId,
        )).called(1);
      });

      test('should return Left when DioExceptionHandle is thrown', () async {
        // Arrange
        const teamId = 'test-team-id';
        const invitationId = 'test-invitation-id';
        const errorMessage = 'Accept invitation failed';

        when(mockDataSource.acceptInvitation(
          teamId: anyNamed('teamId'),
          invitationId: anyNamed('invitationId'),
        )).thenThrow(const DioExceptionHandle(errorMessage));

        // Act
        final result = await repository.acceptInvitation(
          teamId: teamId,
          invitationId: invitationId,
        );

        // Assert
        expect(result, isA<Left<AppError, void>>());
        expect(result.fold((l) => l.message, (r) => null), errorMessage);
      });
    });

    group('declineInvitation', () {
      test('should return Right when declining invitation succeeds', () async {
        // Arrange
        const teamId = 'test-team-id';
        const invitationId = 'test-invitation-id';

        when(mockDataSource.declineInvitation(
          teamId: anyNamed('teamId'),
          invitationId: anyNamed('invitationId'),
        )).thenAnswer((_) async => {});

        // Act
        final result = await repository.declineInvitation(
          teamId: teamId,
          invitationId: invitationId,
        );

        // Assert
        expect(result, isA<Right<AppError, void>>());
        verify(mockDataSource.declineInvitation(
          teamId: teamId,
          invitationId: invitationId,
        )).called(1);
      });

      test('should return Left when DioExceptionHandle is thrown', () async {
        // Arrange
        const teamId = 'test-team-id';
        const invitationId = 'test-invitation-id';
        const errorMessage = 'Decline invitation failed';

        when(mockDataSource.declineInvitation(
          teamId: anyNamed('teamId'),
          invitationId: anyNamed('invitationId'),
        )).thenThrow(const DioExceptionHandle(errorMessage));

        // Act
        final result = await repository.declineInvitation(
          teamId: teamId,
          invitationId: invitationId,
        );

        // Assert
        expect(result, isA<Left<AppError, void>>());
        expect(result.fold((l) => l.message, (r) => null), errorMessage);
      });
    });

    group('deleteTeamInvite', () {
      test('should return Right when deleting invite succeeds', () async {
        // Arrange
        const teamId = 'test-team-id';
        const memberId = 'test-member-id';

        when(mockDataSource.deleteTeamInvite(
          teamId: anyNamed('teamId'),
          memberId: anyNamed('memberId'),
        )).thenAnswer((_) async => {});

        // Act
        final result = await repository.deleteTeamInvite(
          teamId: teamId,
          memberId: memberId,
        );

        // Assert
        expect(result, isA<Right<AppError, void>>());
        verify(mockDataSource.deleteTeamInvite(
          teamId: teamId,
          memberId: memberId,
        )).called(1);
      });

      test('should return Left when DioExceptionHandle is thrown', () async {
        // Arrange
        const teamId = 'test-team-id';
        const memberId = 'test-member-id';
        const errorMessage = 'Delete invite failed';

        when(mockDataSource.deleteTeamInvite(
          teamId: anyNamed('teamId'),
          memberId: anyNamed('memberId'),
        )).thenThrow(const DioExceptionHandle(errorMessage));

        // Act
        final result = await repository.deleteTeamInvite(
          teamId: teamId,
          memberId: memberId,
        );

        // Assert
        expect(result, isA<Left<AppError, void>>());
        expect(result.fold((l) => l.message, (r) => null), errorMessage);
      });
    });
  });
}
