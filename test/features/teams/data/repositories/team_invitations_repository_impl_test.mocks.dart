// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in nextsportz_v2/test/features/teams/data/repositories/team_invitations_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:nextsportz_v2/features/teams/data/datasources/team_invitations_datasource.dart'
    as _i3;
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart' as _i2;
import 'package:nextsportz_v2/features/teams/domain/entities/team_role.dart'
    as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTeamInvitation_0 extends _i1.SmartFake
    implements _i2.TeamInvitation {
  _FakeTeamInvitation_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [TeamInvitationsDatasource].
///
/// See the documentation for Mockito's code generation for more information.
class MockTeamInvitationsDatasource extends _i1.Mock
    implements _i3.TeamInvitationsDatasource {
  MockTeamInvitationsDatasource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i2.TeamInvitation>> getPendingInvitations() =>
      (super.noSuchMethod(
            Invocation.method(#getPendingInvitations, []),
            returnValue: _i4.Future<List<_i2.TeamInvitation>>.value(
              <_i2.TeamInvitation>[],
            ),
          )
          as _i4.Future<List<_i2.TeamInvitation>>);

  @override
  _i4.Future<List<_i2.TeamInvitation>> getTeamInvitations(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamInvitations, [teamId]),
            returnValue: _i4.Future<List<_i2.TeamInvitation>>.value(
              <_i2.TeamInvitation>[],
            ),
          )
          as _i4.Future<List<_i2.TeamInvitation>>);

  @override
  _i4.Future<_i2.TeamInvitation> getInvitation(String? invitationId) =>
      (super.noSuchMethod(
            Invocation.method(#getInvitation, [invitationId]),
            returnValue: _i4.Future<_i2.TeamInvitation>.value(
              _FakeTeamInvitation_0(
                this,
                Invocation.method(#getInvitation, [invitationId]),
              ),
            ),
          )
          as _i4.Future<_i2.TeamInvitation>);

  @override
  _i4.Future<void> invitePlayer({
    required String? teamId,
    required String? playerId,
    String? role = 'Player',
    String? message,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#invitePlayer, [], {
              #teamId: teamId,
              #playerId: playerId,
              #role: role,
              #message: message,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> acceptInvitation({
    required String? teamId,
    required String? invitationId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#acceptInvitation, [], {
              #teamId: teamId,
              #invitationId: invitationId,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> declineInvitation({
    required String? teamId,
    required String? invitationId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#declineInvitation, [], {
              #teamId: teamId,
              #invitationId: invitationId,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<int> getTeamInvitationsCount(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamInvitationsCount, [teamId]),
            returnValue: _i4.Future<int>.value(0),
          )
          as _i4.Future<int>);

  @override
  _i4.Future<void> inviteTeamMember({
    required String? teamId,
    required String? playerId,
    required _i5.TeamRole? role,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#inviteTeamMember, [], {
              #teamId: teamId,
              #playerId: playerId,
              #role: role,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> deleteTeamInvite({
    required String? teamId,
    required String? memberId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#deleteTeamInvite, [], {
              #teamId: teamId,
              #memberId: memberId,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> acceptTeamInvite({
    required String? teamId,
    required String? memberId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#acceptTeamInvite, [], {
              #teamId: teamId,
              #memberId: memberId,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}
