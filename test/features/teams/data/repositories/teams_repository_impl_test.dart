import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:nextsportz_v2/features/teams/data/repositories/teams_repository_impl.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';
import 'package:nextsportz_v2/features/teams/data/datasources/teams_datasource.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/core/networking/exception.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';
import 'package:fpdart/fpdart.dart';
import 'package:dio/dio.dart';

import 'teams_repository_impl_test.mocks.dart';
import '../../test_data_factory.dart';

@GenerateMocks([TeamsDatasource])
void main() {
  group('TeamsRepositoryImpl', () {
    late TeamsRepositoryImpl repository;
    late MockTeamsDatasource mockDataSource;

    setUp(() {
      mockDataSource = MockTeamsDatasource();
      repository = TeamsRepositoryImpl(mockDataSource);

      // Set up mock data
      final testTeams = [
        TeamsTestDataFactory.createTestTeam(
          id: '1',
          name: 'Test Team',
          description: 'Test Description',
          createdBy: 'user123',
        ),
      ];

      final testTeam = testTeams.first;
      final testInvitations = [
        TeamInvitation(
          id: 'inv1',
          teamId: '1',
          invitedUserId: 'user456',
          invitedUserName: 'John Doe',
          invitedUserEmail: '<EMAIL>',
          status: 'pending',
          createdAt: DateTime.now(),
        ),
        TeamInvitation(
          id: 'inv2',
          teamId: '1',
          invitedUserId: 'user789',
          invitedUserName: 'Jane Smith',
          invitedUserPhone: '+1234567890',
          status: 'pending',
          createdAt: DateTime.now(),
        ),
      ];

      // Mock data source methods
      final testTeamsList = [
        TeamSearchItem(
          id: '1',
          name: 'Test Team',
          membersCount: 5,
          winRate: '0.70',
          logoUrl: 'https://example.com/logo.png',
        ),
      ];
      when(mockDataSource.getMyTeams()).thenAnswer((_) async => testTeamsList);
      when(mockDataSource.getTeamById('1')).thenAnswer((_) async => testTeam);
      when(
        mockDataSource.getTeamById('non-existent-id'),
      ).thenThrow(Exception('Team not found'));
      when(
        mockDataSource.getTeamById('test-team-id'),
      ).thenAnswer((_) async => testTeam);
      when(
        mockDataSource.createTeam(
          name: anyNamed('name'),
          description: anyNamed('description'),
          logo: anyNamed('logo'),
          slogan: anyNamed('slogan'),
        ),
      ).thenAnswer((invocation) async {
        final name = invocation.namedArguments[Symbol('name')] as String;
        final description =
            invocation.namedArguments[Symbol('description')] as String;
        final logo = invocation.namedArguments[Symbol('logo')] as String?;
        final slogan = invocation.namedArguments[Symbol('slogan')] as String?;
        return testTeam.copyWith(
          name: name,
          description: description,
          logo: logo,
          slogan: slogan,
        );
      });
      when(
        mockDataSource.updateTeam(
          teamId: anyNamed('teamId'),
          name: anyNamed('name'),
          description: anyNamed('description'),
          logo: anyNamed('logo'),
          slogan: anyNamed('slogan'),
        ),
      ).thenAnswer((invocation) async {
        final name = invocation.namedArguments[Symbol('name')] as String?;
        final description =
            invocation.namedArguments[Symbol('description')] as String?;
        final logo = invocation.namedArguments[Symbol('logo')] as String?;
        final slogan = invocation.namedArguments[Symbol('slogan')] as String?;
        return testTeam.copyWith(
          name: name,
          description: description,
          logo: logo,
          slogan: slogan,
        );
      });
      when(mockDataSource.deleteTeam(any)).thenAnswer((_) async {});
      when(
        mockDataSource.invitePlayer(
          teamId: anyNamed('teamId'),
          playerId: anyNamed('playerId'),
          message: anyNamed('message'),
        ),
      ).thenAnswer((_) async {});
      when(mockDataSource.acceptInvitation(any)).thenAnswer((_) async {});
      when(mockDataSource.declineInvitation(any)).thenAnswer((_) async {});
      when(
        mockDataSource.removeMember(
          teamId: anyNamed('teamId'),
          memberId: anyNamed('memberId'),
        ),
      ).thenAnswer((_) async {});
      when(
        mockDataSource.updateMemberRole(
          teamId: anyNamed('teamId'),
          memberId: anyNamed('memberId'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async {});
      when(
        mockDataSource.getPendingInvitations(),
      ).thenAnswer((_) async => testInvitations);
    });

    group('getMyTeams', () {
      test('should return list of teams successfully', () async {
        // act
        final result = await repository.getMyTeams();

        // assert
        expect(result.isRight(), true);
        result.fold((error) => fail('Expected success but got error: $error'), (
          teamsList,
        ) {
          expect(teamsList, isA<List<TeamSearchItem>>());
          expect(teamsList.length, greaterThan(0));
          expect(teamsList.first, isA<TeamSearchItem>());
          expect(teamsList.first.name, isNotEmpty);
          expect(teamsList.first.membersCount, greaterThan(0));
        });
        verify(mockDataSource.getMyTeams()).called(1);
      });

      test('should return error when data source fails', () async {
        // arrange
        when(mockDataSource.getMyTeams()).thenThrow(
          DioExceptionHandle.fromDioError(
            DioException(
              requestOptions: RequestOptions(path: '/api/teams'),
              type: DioExceptionType.connectionError,
            ),
          ),
        );

        // act
        final result = await repository.getMyTeams();

        // assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(
            error.message,
            equals('Connection failed due to internet connection'),
          ),
          (teams) => fail('Expected error but got success'),
        );
      });
    });

    group('getTeamById', () {
      test('should return team by id successfully', () async {
        // act
        final result = await repository.getTeamById('1');

        // assert
        expect(result.isRight(), true);
        result.fold((error) => fail('Expected success but got error: $error'), (
          team,
        ) {
          expect(team, isA<Team>());
          expect(team.id, equals('1'));
          expect(team.name, isNotEmpty);
        });
        verify(mockDataSource.getTeamById('1')).called(1);
      });

      test('should return error for non-existent team', () async {
        // act
        final result = await repository.getTeamById('non-existent-id');

        // assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, contains('Team not found')),
          (team) => fail('Expected error but got success'),
        );
      });
    });

    group('getTeamDetails', () {
      test('should return team details successfully', () async {
        // arrange
        final teamDetails = TeamsTestDataFactory.createTestTeamDetails(
          id: '1',
          name: 'Test Team Details',
        );
        when(
          mockDataSource.getTeamDetails('1'),
        ).thenAnswer((_) async => teamDetails);

        // act
        final result = await repository.getTeamDetails('1');

        // assert
        expect(result.isRight(), true);
        result.fold((error) => fail('Expected success but got error: $error'), (
          details,
        ) {
          expect(details, isA<TeamDetails>());
          expect(details.id, equals('1'));
          expect(details.name, equals('Test Team Details'));
          expect(details.members, isA<List<TeamDetailMember>>());
          expect(details.stats, isA<TeamDetailStats>());
        });
        verify(mockDataSource.getTeamDetails('1')).called(1);
      });

      test(
        'should return error when datasource throws DioExceptionHandle',
        () async {
          // arrange
          final dioError = DioException(
            requestOptions: RequestOptions(path: '/api/teams/1'),
            response: Response(
              requestOptions: RequestOptions(path: '/api/teams/1'),
              statusCode: 404,
            ),
            type: DioExceptionType.badResponse,
            message: 'Team details not found',
          );
          when(
            mockDataSource.getTeamDetails('1'),
          ).thenThrow(DioExceptionHandle.fromDioError(dioError));

          // act
          final result = await repository.getTeamDetails('1');

          // assert
          expect(result.isLeft(), true);
          result.fold(
            (error) => expect(error.message, equals('Something went wrong')),
            (details) => fail('Expected error but got success'),
          );
        },
      );

      test(
        'should return error when datasource throws generic exception',
        () async {
          // arrange
          when(
            mockDataSource.getTeamDetails('1'),
          ).thenThrow(Exception('Network error'));

          // act
          final result = await repository.getTeamDetails('1');

          // assert
          expect(result.isLeft(), true);
          result.fold(
            (error) =>
                expect(error.message, contains('Exception: Network error')),
            (details) => fail('Expected error but got success'),
          );
        },
      );
    });

    group('createTeam', () {
      test('should create a new team successfully', () async {
        // arrange
        const name = 'New Test Team';
        const description = 'A new test team description';

        // act
        final result = await repository.createTeam(
          name: name,
          description: description,
        );

        // assert
        expect(result.isRight(), true);
        result.fold((error) => fail('Expected success but got error: $error'), (
          team,
        ) {
          expect(team, isA<Team>());
          expect(team.name, name);
          expect(team.description, description);
        });
        verify(
          mockDataSource.createTeam(
            name: name,
            description: description,
            logo: null,
            slogan: null,
          ),
        ).called(1);
      });

      test('should return error when create team fails', () async {
        // arrange
        when(
          mockDataSource.createTeam(
            name: anyNamed('name'),
            description: anyNamed('description'),
            logo: anyNamed('logo'),
            slogan: anyNamed('slogan'),
          ),
        ).thenThrow(
          DioExceptionHandle.fromDioError(
            DioException(
              requestOptions: RequestOptions(path: '/api/teams'),
              type: DioExceptionType.badResponse,
            ),
          ),
        );

        // act
        final result = await repository.createTeam(
          name: 'Test Team',
          description: 'Test Description',
        );

        // assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, equals('Something went wrong')),
          (team) => fail('Expected error but got success'),
        );
      });
    });

    group('deleteTeam', () {
      test('should delete team successfully', () async {
        // act
        final result = await repository.deleteTeam('test-team-id');

        // assert
        expect(result.isRight(), true);
        verify(mockDataSource.deleteTeam('test-team-id')).called(1);
      });

      test('should return error when delete team fails', () async {
        // arrange
        when(mockDataSource.deleteTeam(any)).thenThrow(
          DioExceptionHandle.fromDioError(
            DioException(
              requestOptions: RequestOptions(path: '/api/teams'),
              type: DioExceptionType.badResponse,
            ),
          ),
        );

        // act
        final result = await repository.deleteTeam('test-team-id');

        // assert
        expect(result.isLeft(), true);
        result.fold(
          (error) => expect(error.message, equals('Something went wrong')),
          (_) => fail('Expected error but got success'),
        );
      });
    });

    group('searchTeamsPaginated', () {
      test(
        'should return paginated teams when datasource call succeeds',
        () async {
          // Arrange
          const query = 'test';
          const page = 1;
          const pageSize = 20;
          final paginatedResponse =
              TeamsTestDataFactory.createTestPaginatedResponse(
                items: TeamsTestDataFactory.createTestTeamSearchItems(count: 5),
                total: 5,
                page: page,
                pageSize: pageSize,
                totalPages: 1,
              );

          when(
            mockDataSource.searchTeamsPaginated(
              query: query,
              page: page,
              pageSize: pageSize,
            ),
          ).thenAnswer((_) async => paginatedResponse);

          // Act
          final result = await repository.searchTeamsPaginated(
            query: query,
            page: page,
            pageSize: pageSize,
          );

          // Assert
          expect(
            result,
            isA<Right<AppError, PaginatedResponse<TeamSearchItem>>>(),
          );
          result.fold(
            (error) => fail(
              'Expected Right but got Left with error: ${error.message}',
            ),
            (response) {
              expect(response.data.items.length, equals(5));
              expect(response.data.page, equals(page));
              expect(response.data.pageSize, equals(pageSize));
              expect(response.data.total, equals(5));
              expect(response.data.totalPages, equals(1));
            },
          );
          verify(
            mockDataSource.searchTeamsPaginated(
              query: query,
              page: page,
              pageSize: pageSize,
            ),
          ).called(1);
        },
      );

      test(
        'should return AppError when datasource throws DioExceptionHandle',
        () async {
          // Arrange
          final dioExceptionHandle = DioExceptionHandle.fromDioError(
            DioException(
              requestOptions: RequestOptions(path: '/api/teams'),
              type: DioExceptionType.connectionError,
            ),
          );

          when(
            mockDataSource.searchTeamsPaginated(
              query: anyNamed('query'),
              page: anyNamed('page'),
              pageSize: anyNamed('pageSize'),
            ),
          ).thenThrow(dioExceptionHandle);

          // Act
          final result = await repository.searchTeamsPaginated(query: 'test');

          // Assert
          expect(
            result,
            isA<Left<AppError, PaginatedResponse<TeamSearchItem>>>(),
          );
          result.fold((error) {
            expect(error, isA<AppError>());
            expect(error.message, equals(dioExceptionHandle.message));
          }, (response) => fail('Expected Left but got Right'));
        },
      );

      test(
        'should return AppError when datasource throws generic exception',
        () async {
          // Arrange
          const errorMessage = 'Network error';
          when(
            mockDataSource.searchTeamsPaginated(
              query: anyNamed('query'),
              page: anyNamed('page'),
              pageSize: anyNamed('pageSize'),
            ),
          ).thenThrow(Exception(errorMessage));

          // Act
          final result = await repository.searchTeamsPaginated(query: 'test');

          // Assert
          expect(
            result,
            isA<Left<AppError, PaginatedResponse<TeamSearchItem>>>(),
          );
          result.fold((error) {
            expect(error, isA<AppError>());
            expect(error.message, contains(errorMessage));
          }, (response) => fail('Expected Left but got Right'));
        },
      );
    });

    group('getAllTeamsPaginated', () {
      test(
        'should return paginated teams when datasource call succeeds',
        () async {
          // Arrange
          const page = 2;
          const pageSize = 10;
          final paginatedResponse =
              TeamsTestDataFactory.createTestPaginatedResponse(
                items: TeamsTestDataFactory.createTestTeamSearchItems(
                  count: 10,
                ),
                total: 25,
                page: page,
                pageSize: pageSize,
                totalPages: 3,
              );

          when(
            mockDataSource.getAllTeamsPaginated(page: page, pageSize: pageSize),
          ).thenAnswer((_) async => paginatedResponse);

          // Act
          final result = await repository.getAllTeamsPaginated(
            page: page,
            pageSize: pageSize,
          );

          // Assert
          expect(
            result,
            isA<Right<AppError, PaginatedResponse<TeamSearchItem>>>(),
          );
          result.fold(
            (error) => fail(
              'Expected Right but got Left with error: ${error.message}',
            ),
            (response) {
              expect(response.data.items.length, equals(10));
              expect(response.data.page, equals(page));
              expect(response.data.pageSize, equals(pageSize));
              expect(response.data.total, equals(25));
              expect(response.data.totalPages, equals(3));
              expect(response.data.hasNextPage, isTrue);
              expect(response.data.hasPreviousPage, isTrue);
            },
          );
          verify(
            mockDataSource.getAllTeamsPaginated(page: page, pageSize: pageSize),
          ).called(1);
        },
      );

      test(
        'should return empty paginated response when no teams found',
        () async {
          // Arrange
          final emptyResponse =
              TeamsTestDataFactory.createTestPaginatedResponse(
                items: [],
                total: 0,
                totalPages: 0,
              );

          when(
            mockDataSource.getAllTeamsPaginated(
              page: anyNamed('page'),
              pageSize: anyNamed('pageSize'),
            ),
          ).thenAnswer((_) async => emptyResponse);

          // Act
          final result = await repository.getAllTeamsPaginated();

          // Assert
          expect(
            result,
            isA<Right<AppError, PaginatedResponse<TeamSearchItem>>>(),
          );
          result.fold(
            (error) => fail(
              'Expected Right but got Left with error: ${error.message}',
            ),
            (response) {
              expect(response.data.items.isEmpty, isTrue);
              expect(response.data.total, equals(0));
              expect(response.data.totalPages, equals(0));
              expect(response.data.hasNextPage, isFalse);
            },
          );
        },
      );

      test('should return AppError when datasource throws exception', () async {
        // Arrange
        final dioExceptionHandle = DioExceptionHandle.fromDioError(
          DioException(
            requestOptions: RequestOptions(path: '/api/teams'),
            response: Response(
              requestOptions: RequestOptions(path: '/api/teams'),
              statusCode: 500,
            ),
            type: DioExceptionType.badResponse,
          ),
        );

        when(
          mockDataSource.getAllTeamsPaginated(
            page: anyNamed('page'),
            pageSize: anyNamed('pageSize'),
          ),
        ).thenThrow(dioExceptionHandle);

        // Act
        final result = await repository.getAllTeamsPaginated();

        // Assert
        expect(
          result,
          isA<Left<AppError, PaginatedResponse<TeamSearchItem>>>(),
        );
        result.fold((error) {
          expect(error, isA<AppError>());
          expect(error.message, equals(dioExceptionHandle.message));
        }, (response) => fail('Expected Left but got Right'));
      });
    });
  });
}
