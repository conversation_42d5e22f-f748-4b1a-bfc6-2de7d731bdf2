import 'package:flutter_test/flutter_test.dart';

// Import all test files for the invite player feature
import '../player/domain/entities/player_search_item_test.dart'
    as player_search_item_test;
import '../player/data/dto/player_search_item_dto_test.dart'
    as player_search_item_dto_test;
import '../player/domain/usecases/search_players_usecase_test.dart'
    as search_players_usecase_test;
import '../player/data/repositories/player_repository_impl_test.dart'
    as player_repository_impl_test;
import '../player/data/datasources/player_remote_datasource_test.dart'
    as player_remote_datasource_test;
import 'domain/usecases/invite_player_usecase_test.dart'
    as invite_player_usecase_test;
import 'presentation/screens/invite_player_screen_test.dart'
    as invite_player_screen_test;

/// Test suite for the complete invite player feature
///
/// This test suite runs all tests related to the invite player functionality,
/// including:
/// - Domain entities and DTOs
/// - Use cases
/// - Repository implementations
/// - Data sources
/// - Widget tests
///
/// Run this test suite with:
/// ```bash
/// flutter test test/features/teams/invite_player_test_suite.dart
/// ```
///
/// For coverage report:
/// ```bash
/// flutter test --coverage test/features/teams/invite_player_test_suite.dart
/// genhtml coverage/lcov.info -o coverage/html
/// ```
void main() {
  group('Invite Player Feature Test Suite', () {
    group('Domain Layer Tests', () {
      group('Entities', () {
        player_search_item_test.main();
      });

      group('Use Cases', () {
        search_players_usecase_test.main();
        invite_player_usecase_test.main();
      });
    });

    group('Data Layer Tests', () {
      group('DTOs', () {
        player_search_item_dto_test.main();
      });

      group('Repositories', () {
        player_repository_impl_test.main();
      });

      group('Data Sources', () {
        player_remote_datasource_test.main();
      });
    });

    group('Presentation Layer Tests', () {
      group('Screens', () {
        invite_player_screen_test.main();
      });
    });
  });
}

/// Helper function to run specific test groups
///
/// Usage:
/// ```dart
/// runTestGroup(TestGroup.domain);
/// runTestGroup(TestGroup.data);
/// runTestGroup(TestGroup.presentation);
/// ```
enum TestGroup { domain, data, presentation, all }

void runTestGroup(TestGroup group) {
  switch (group) {
    case TestGroup.domain:
      group('Domain Layer Tests Only', () {
        player_search_item_test.main();
        search_players_usecase_test.main();
        invite_player_usecase_test.main();
      });
      break;
    case TestGroup.data:
      group('Data Layer Tests Only', () {
        player_search_item_dto_test.main();
        player_repository_impl_test.main();
        player_remote_datasource_test.main();
      });
      break;
    case TestGroup.presentation:
      group('Presentation Layer Tests Only', () {
        invite_player_screen_test.main();
      });
      break;
    case TestGroup.all:
      // Run all tests by calling main()
      break;
  }
}
