import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nextsportz_v2/features/teams/presentation/widgets/team_search_item_card.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';

import '../../test_data_factory.dart';

void main() {
  group('TeamSearchItemCard', () {
    late TeamSearchItem testTeam;

    setUp(() {
      testTeam = TeamsTestDataFactory.createTestTeamSearchItem(
        id: '1',
        name: 'Test Team FC',
        membersCount: 15,
        winRate: '75.50',
        logoUrl: 'https://example.com/logo.png',
      );
    });

    Widget createTestWidget({
      TeamSearchItem? team,
      VoidCallback? onTap,
    }) {
      return ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: TeamSearchItemCard(
              team: team ?? testTeam,
              onTap: onTap,
            ),
          ),
        ),
      );
    }

    testWidgets('should display team information correctly', (tester) async {
      // Arrange
      final widget = createTestWidget();

      // Act
      await tester.pumpWidget(widget);

      // Assert
      expect(find.text('Test Team FC'), findsOneWidget);
      expect(find.text('15 members'), findsOneWidget);
      expect(find.text('76%'), findsOneWidget); // Formatted win rate percentage
      expect(find.byIcon(Icons.people), findsOneWidget);
      expect(find.byIcon(Icons.trending_up), findsOneWidget);
    });

    testWidgets('should display default logo when logoUrl is empty', (tester) async {
      // Arrange
      final teamWithoutLogo = TeamsTestDataFactory.createTestTeamSearchItem(
        logoUrl: '',
      );
      final widget = createTestWidget(team: teamWithoutLogo);

      // Act
      await tester.pumpWidget(widget);

      // Assert
      expect(find.byIcon(Icons.groups), findsOneWidget);
    });

    testWidgets('should handle tap events', (tester) async {
      // Arrange
      bool tapped = false;
      final widget = createTestWidget(
        onTap: () => tapped = true,
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.tap(find.byType(TeamSearchItemCard));

      // Assert
      expect(tapped, isTrue);
    });

    testWidgets('should not handle tap when onTap is null', (tester) async {
      // Arrange
      final widget = createTestWidget(onTap: null);

      // Act
      await tester.pumpWidget(widget);
      await tester.tap(find.byType(TeamSearchItemCard));

      // Assert - Should not throw any errors
      expect(find.byType(TeamSearchItemCard), findsOneWidget);
    });

    testWidgets('should display correct win rate colors', (tester) async {
      // Test high win rate (green)
      final highWinRateTeam = TeamsTestDataFactory.createTestTeamSearchItem(
        winRate: '85.00',
      );
      
      await tester.pumpWidget(createTestWidget(team: highWinRateTeam));
      
      // Find the win rate badge container
      final highWinRateBadge = find.descendant(
        of: find.byType(TeamSearchItemCard),
        matching: find.text('85%'),
      );
      expect(highWinRateBadge, findsOneWidget);

      // Test medium win rate (orange)
      final mediumWinRateTeam = TeamsTestDataFactory.createTestTeamSearchItem(
        winRate: '60.00',
      );
      
      await tester.pumpWidget(createTestWidget(team: mediumWinRateTeam));
      await tester.pump();
      
      final mediumWinRateBadge = find.descendant(
        of: find.byType(TeamSearchItemCard),
        matching: find.text('60%'),
      );
      expect(mediumWinRateBadge, findsOneWidget);

      // Test low win rate (red)
      final lowWinRateTeam = TeamsTestDataFactory.createTestTeamSearchItem(
        winRate: '30.00',
      );
      
      await tester.pumpWidget(createTestWidget(team: lowWinRateTeam));
      await tester.pump();
      
      final lowWinRateBadge = find.descendant(
        of: find.byType(TeamSearchItemCard),
        matching: find.text('30%'),
      );
      expect(lowWinRateBadge, findsOneWidget);
    });

    testWidgets('should handle long team names with ellipsis', (tester) async {
      // Arrange
      final longNameTeam = TeamsTestDataFactory.createTestTeamSearchItem(
        name: 'This is a very long team name that should be truncated with ellipsis',
      );
      final widget = createTestWidget(team: longNameTeam);

      // Act
      await tester.pumpWidget(widget);

      // Assert
      final textWidget = tester.widget<Text>(
        find.descendant(
          of: find.byType(TeamSearchItemCard),
          matching: find.text('This is a very long team name that should be truncated with ellipsis'),
        ),
      );
      expect(textWidget.overflow, equals(TextOverflow.ellipsis));
      expect(textWidget.maxLines, equals(1));
    });

    testWidgets('should display arrow icon', (tester) async {
      // Arrange
      final widget = createTestWidget();

      // Act
      await tester.pumpWidget(widget);

      // Assert
      expect(find.byIcon(Icons.arrow_forward_ios), findsOneWidget);
    });

    testWidgets('should handle zero members count', (tester) async {
      // Arrange
      final teamWithZeroMembers = TeamsTestDataFactory.createTestTeamSearchItem(
        membersCount: 0,
      );
      final widget = createTestWidget(team: teamWithZeroMembers);

      // Act
      await tester.pumpWidget(widget);

      // Assert
      expect(find.text('0 members'), findsOneWidget);
    });

    testWidgets('should handle zero win rate', (tester) async {
      // Arrange
      final teamWithZeroWinRate = TeamsTestDataFactory.createTestTeamSearchItem(
        winRate: '0.00',
      );
      final widget = createTestWidget(team: teamWithZeroWinRate);

      // Act
      await tester.pumpWidget(widget);

      // Assert
      expect(find.text('0%'), findsOneWidget);
    });
  });

  group('TeamSearchPaginatedListView', () {
    late List<TeamSearchItem> testTeams;

    setUp(() {
      testTeams = TeamsTestDataFactory.createTestTeamSearchItems(count: 3);
    });

    Widget createTestWidget({
      Future<PaginatedResponse<TeamSearchItem>> Function(int page)? onLoadPage,
      void Function(TeamSearchItem team)? onTeamTap,
      String? emptyMessage,
    }) {
      return ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: TeamSearchPaginatedListView(
              onLoadPage: onLoadPage ?? (_) async => 
                TeamsTestDataFactory.createTestPaginatedResponse(items: testTeams),
              onTeamTap: onTeamTap,
              emptyMessage: emptyMessage,
            ),
          ),
        ),
      );
    }

    testWidgets('should display team cards', (tester) async {
      // Arrange
      final widget = createTestWidget();

      // Act
      await tester.pumpWidget(widget);
      await tester.pump();

      // Assert
      expect(find.byType(TeamSearchItemCard), findsNWidgets(3));
      expect(find.text('Team 1'), findsOneWidget);
      expect(find.text('Team 2'), findsOneWidget);
      expect(find.text('Team 3'), findsOneWidget);
    });

    testWidgets('should handle team tap events', (tester) async {
      // Arrange
      TeamSearchItem? tappedTeam;
      final widget = createTestWidget(
        onTeamTap: (team) => tappedTeam = team,
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.pump();
      await tester.tap(find.text('Team 1'));

      // Assert
      expect(tappedTeam, isNotNull);
      expect(tappedTeam!.name, equals('Team 1'));
    });

    testWidgets('should display custom empty message', (tester) async {
      // Arrange
      const customEmptyMessage = 'No teams available at the moment';
      final widget = createTestWidget(
        onLoadPage: (_) async => TeamsTestDataFactory.createTestPaginatedResponse(
          items: [],
          total: 0,
        ),
        emptyMessage: customEmptyMessage,
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.pump();

      // Assert
      expect(find.text(customEmptyMessage), findsOneWidget);
      expect(find.byIcon(Icons.groups_outlined), findsOneWidget);
    });

    testWidgets('should display default empty message when none provided', (tester) async {
      // Arrange
      final widget = createTestWidget(
        onLoadPage: (_) async => TeamsTestDataFactory.createTestPaginatedResponse(
          items: [],
          total: 0,
        ),
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.pump();

      // Assert
      expect(find.text('No teams found'), findsOneWidget);
    });

    testWidgets('should display loading state', (tester) async {
      // Arrange
      final widget = createTestWidget(
        onLoadPage: (_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return TeamsTestDataFactory.createTestPaginatedResponse(items: testTeams);
        },
      );

      // Act
      await tester.pumpWidget(widget);

      // Assert
      expect(find.text('Loading teams...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display error state with retry button', (tester) async {
      // Arrange
      int attemptCount = 0;
      final widget = createTestWidget(
        onLoadPage: (_) async {
          attemptCount++;
          if (attemptCount == 1) {
            throw Exception('Network error');
          }
          return TeamsTestDataFactory.createTestPaginatedResponse(items: testTeams);
        },
      );

      // Act
      await tester.pumpWidget(widget);
      await tester.pump();

      // Assert
      expect(find.text('Failed to load teams'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);

      // Test retry functionality
      await tester.tap(find.text('Retry'));
      await tester.pump();

      expect(attemptCount, equals(2));
      expect(find.byType(TeamSearchItemCard), findsNWidgets(3));
    });

    testWidgets('should handle null onTeamTap gracefully', (tester) async {
      // Arrange
      final widget = createTestWidget(onTeamTap: null);

      // Act
      await tester.pumpWidget(widget);
      await tester.pump();
      await tester.tap(find.text('Team 1'));

      // Assert - Should not throw any errors
      expect(find.byType(TeamSearchItemCard), findsNWidgets(3));
    });
  });
}
