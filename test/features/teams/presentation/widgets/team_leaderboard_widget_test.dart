import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:nextsportz_v2/features/teams/presentation/widgets/team_leaderboard_widget.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';

void main() {
  group('TeamLeaderboardWidget', () {
    late List<Team> mockTeams;

    setUp(() {
      mockTeams = [
        Team(
          id: '1',
          name: 'Thunder Bolts FC',
          description: 'Elite football team',
          createdBy: 'user1',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          members: [],
          invitations: [],
          stats: TeamStats(
            totalMatches: 15,
            wins: 12,
            losses: 2,
            draws: 1,
            winRate: 0.8,
            totalGoals: 45,
            goalsConceded: 12,
            cleanSheets: 8,
            averageGoalsPerMatch: 3.0,
          ),
        ),
        Team(
          id: '2',
          name: 'Lightning Strikers',
          description: 'Fast-paced attacking team',
          createdBy: 'user2',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          members: [],
          invitations: [],
          stats: TeamStats(
            totalMatches: 14,
            wins: 10,
            losses: 3,
            draws: 1,
            winRate: 0.71,
            totalGoals: 38,
            goalsConceded: 18,
            cleanSheets: 5,
            averageGoalsPerMatch: 2.7,
          ),
        ),
      ];
    });

    Widget createTestWidget({
      List<Team>? teams,
      VoidCallback? onViewMore,
      bool isLoading = false,
      String? error,
    }) {
      return ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: TeamLeaderboardWidget(
              teams: teams ?? mockTeams,
              onViewMore: onViewMore,
              isLoading: isLoading,
              error: error,
            ),
          ),
        ),
      );
    }

    testWidgets('displays teams correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Check if header is displayed
      expect(find.text('Top Teams'), findsOneWidget);
      expect(find.text('Best performing teams'), findsOneWidget);

      // Check if teams are displayed
      expect(find.text('Thunder Bolts FC'), findsOneWidget);
      expect(find.text('Lightning Strikers'), findsOneWidget);

      // Check if win rates are displayed
      expect(find.text('80%'), findsOneWidget);
      expect(find.text('71%'), findsOneWidget);

      // Check if match records are displayed
      expect(find.text('12W 1D 2L'), findsOneWidget);
      expect(find.text('10W 1D 3L'), findsOneWidget);
    });

    testWidgets('displays loading state correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget(teams: [], isLoading: true));

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Thunder Bolts FC'), findsNothing);
    });

    testWidgets('displays error state correctly', (WidgetTester tester) async {
      const errorMessage = 'Network error';
      await tester.pumpWidget(createTestWidget(teams: [], error: errorMessage));

      expect(find.text('Failed to load teams'), findsOneWidget);
      expect(find.text(errorMessage), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('displays empty state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(teams: []));

      expect(find.text('No teams found'), findsOneWidget);
      expect(find.byIcon(Icons.groups_outlined), findsOneWidget);
    });

    testWidgets('displays view more button when onViewMore is provided', (
      WidgetTester tester,
    ) async {
      bool viewMoreTapped = false;
      await tester.pumpWidget(
        createTestWidget(onViewMore: () => viewMoreTapped = true),
      );

      expect(find.text('View All Teams'), findsOneWidget);

      await tester.tap(find.text('View All Teams'));
      expect(viewMoreTapped, isTrue);
    });

    testWidgets('does not display view more button when onViewMore is null', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget(onViewMore: null));

      expect(find.text('View All Teams'), findsNothing);
    });

    testWidgets('displays correct rank colors', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find rank containers
      final rankContainers = find.byType(Container);
      expect(rankContainers, findsWidgets);

      // The first team should have gold color (rank 1)
      // The second team should have silver color (rank 2)
      // This is tested indirectly by checking the presence of rank numbers
      expect(find.text('1'), findsOneWidget);
      expect(find.text('2'), findsOneWidget);
    });

    testWidgets('displays team logos correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Check for default logo icons (since mock teams don't have logo URLs)
      expect(find.byIcon(Icons.groups), findsWidgets);
    });

    testWidgets('limits teams to 5 items', (WidgetTester tester) async {
      // Create 7 teams
      final manyTeams = List.generate(
        7,
        (index) => Team(
          id: '$index',
          name: 'Team $index',
          description: 'Description $index',
          createdBy: 'user$index',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          members: [],
          invitations: [],
          stats: TeamStats(
            totalMatches: 10,
            wins: 5,
            losses: 3,
            draws: 2,
            winRate: 0.5,
            totalGoals: 20,
            goalsConceded: 15,
            cleanSheets: 2,
            averageGoalsPerMatch: 2.0,
          ),
        ),
      );

      await tester.pumpWidget(createTestWidget(teams: manyTeams));

      // Should only display first 5 teams
      expect(find.text('Team 0'), findsOneWidget);
      expect(find.text('Team 4'), findsOneWidget);
      expect(find.text('Team 5'), findsNothing);
      expect(find.text('Team 6'), findsNothing);
    });

    group('Navigation Tests', () {
      late GoRouter router;
      late List<String> navigationHistory;

      setUp(() {
        navigationHistory = [];

        router = GoRouter(
          initialLocation: '/',
          routes: [
            GoRoute(
              path: '/',
              name: 'home',
              builder: (context, state) => const Scaffold(body: Text('Home')),
            ),
            GoRoute(
              path: '/teams/:teamId',
              name: 'team-detail',
              builder: (context, state) {
                final teamId = state.pathParameters['teamId']!;
                navigationHistory.add('/teams/$teamId');
                return Scaffold(body: Text('Team Details: $teamId'));
              },
            ),
          ],
        );
      });

      Widget createTestWidgetWithRouter({
        required List<Team> teams,
        VoidCallback? onViewMore,
      }) {
        return ProviderScope(
          child: MaterialApp.router(
            routerConfig: router,
            builder: (context, child) {
              return Scaffold(
                body: TeamLeaderboardWidget(
                  teams: teams,
                  onViewMore: onViewMore,
                ),
              );
            },
          ),
        );
      }

      testWidgets('should navigate to team details when team item is tapped', (
        tester,
      ) async {
        await tester.pumpWidget(createTestWidgetWithRouter(teams: mockTeams));
        await tester.pumpAndSettle();

        // Find the first team item and tap it
        final firstTeamItem = find.text('Thunder Bolts FC');
        expect(firstTeamItem, findsOneWidget);

        await tester.tap(firstTeamItem);
        await tester.pumpAndSettle();

        // Verify navigation occurred
        expect(navigationHistory, contains('/teams/1'));
        expect(
          router.routerDelegate.currentConfiguration.uri.toString(),
          equals('/teams/1'),
        );
      });

      testWidgets(
        'should navigate to correct team when different teams are tapped',
        (tester) async {
          await tester.pumpWidget(createTestWidgetWithRouter(teams: mockTeams));
          await tester.pumpAndSettle();

          // Tap the second team
          final secondTeamItem = find.text('Lightning Strikers');
          expect(secondTeamItem, findsOneWidget);

          await tester.tap(secondTeamItem);
          await tester.pumpAndSettle();

          // Verify correct navigation
          expect(navigationHistory, contains('/teams/2'));
          expect(
            router.routerDelegate.currentConfiguration.uri.toString(),
            equals('/teams/2'),
          );
        },
      );

      testWidgets('should handle URL changes correctly', (tester) async {
        await tester.pumpWidget(createTestWidgetWithRouter(teams: mockTeams));
        await tester.pumpAndSettle();

        // Initial URL should be home
        expect(
          router.routerDelegate.currentConfiguration.uri.toString(),
          equals('/'),
        );

        // Navigate to team details
        await tester.tap(find.text('Thunder Bolts FC'));
        await tester.pumpAndSettle();

        // URL should change to team details
        expect(
          router.routerDelegate.currentConfiguration.uri.toString(),
          equals('/teams/1'),
        );

        // Verify the correct screen content is displayed
        expect(find.text('Team Details: 1'), findsOneWidget);
      });
    });
  });
}
