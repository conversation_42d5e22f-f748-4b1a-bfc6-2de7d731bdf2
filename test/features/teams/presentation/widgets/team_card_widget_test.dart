import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nextsportz_v2/features/teams/presentation/widgets/team_card_widget.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';

void main() {
  group('TeamCardWidget', () {
    late Team mockTeam;

    setUp(() {
      mockTeam = Team(
        id: '1',
        name: 'Thunder Bolts FC',
        description: 'Elite football team',
        createdBy: 'user1',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isActive: true,
        members: [
          TeamMember(
            id: '1',
            userId: 'user1',
            name: '<PERSON>',
            position: 'Forward',
            role: 'captain',
            joinedAt: DateTime.now(),
            isActive: true,
            stats: PlayerStats(
              matchesPlayed: 10,
              goals: 5,
              assists: 3,
              cleanSheets: 2,
              rating: 4.5,
              yellowCards: 1,
              redCards: 0,
              minutesPlayed: 900,
            ),
          ),
          TeamMember(
            id: '2',
            userId: 'user2',
            name: '<PERSON>',
            position: 'Midfielder',
            role: 'member',
            joinedAt: DateTime.now(),
            isActive: true,
            stats: PlayerStats(
              matchesPlayed: 8,
              goals: 2,
              assists: 5,
              cleanSheets: 1,
              rating: 4.2,
              yellowCards: 0,
              redCards: 0,
              minutesPlayed: 720,
            ),
          ),
        ],
        invitations: [],
        stats: TeamStats(
          totalMatches: 15,
          wins: 12,
          losses: 2,
          draws: 1,
          winRate: 0.8,
          totalGoals: 45,
          goalsConceded: 12,
          cleanSheets: 8,
          averageGoalsPerMatch: 3.0,
        ),
      );
    });

    Widget createTestWidget({
      Team? team,
      VoidCallback? onTap,
    }) {
      return ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: TeamCardWidget(
              team: team ?? mockTeam,
              onTap: onTap,
            ),
          ),
        ),
      );
    }

    testWidgets('displays team information correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Check team name
      expect(find.text('Thunder Bolts FC'), findsOneWidget);

      // Check members count
      expect(find.text('2 members'), findsOneWidget);

      // Check match record
      expect(find.text('12W 1D 2L'), findsOneWidget);

      // Check win rate
      expect(find.text('80%'), findsOneWidget);

      // Check icons
      expect(find.byIcon(Icons.people), findsOneWidget);
      expect(find.byIcon(Icons.sports_soccer), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward_ios), findsOneWidget);
    });

    testWidgets('displays default logo when team has no logo', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byIcon(Icons.groups), findsOneWidget);
    });

    testWidgets('handles tap correctly', (WidgetTester tester) async {
      bool tapped = false;
      await tester.pumpWidget(createTestWidget(
        onTap: () => tapped = true,
      ));

      await tester.tap(find.byType(TeamCardWidget));
      expect(tapped, isTrue);
    });

    testWidgets('displays correct win rate color for high win rate', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // High win rate (80%) should display green color
      // This is tested indirectly by checking the presence of the win rate text
      expect(find.text('80%'), findsOneWidget);
    });

    testWidgets('displays correct win rate color for medium win rate', (WidgetTester tester) async {
      final mediumWinRateTeam = mockTeam.copyWith(
        stats: mockTeam.stats.copyWith(winRate: 0.6),
      );

      await tester.pumpWidget(createTestWidget(team: mediumWinRateTeam));

      expect(find.text('60%'), findsOneWidget);
    });

    testWidgets('displays correct win rate color for low win rate', (WidgetTester tester) async {
      final lowWinRateTeam = Team(
        id: '2',
        name: 'Struggling Team',
        description: 'Team with low win rate',
        createdBy: 'user2',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isActive: true,
        members: [],
        invitations: [],
        stats: TeamStats(
          totalMatches: 10,
          wins: 3,
          losses: 6,
          draws: 1,
          winRate: 0.3,
          totalGoals: 15,
          goalsConceded: 25,
          cleanSheets: 1,
          averageGoalsPerMatch: 1.5,
        ),
      );

      await tester.pumpWidget(createTestWidget(team: lowWinRateTeam));

      expect(find.text('30%'), findsOneWidget);
      expect(find.text('3W 1D 6L'), findsOneWidget);
    });

    testWidgets('handles long team names correctly', (WidgetTester tester) async {
      final longNameTeam = mockTeam.copyWith(
        name: 'This is a very long team name that should be truncated',
      );

      await tester.pumpWidget(createTestWidget(team: longNameTeam));

      // The text should be present but truncated
      expect(find.textContaining('This is a very long'), findsOneWidget);
    });

    testWidgets('displays zero members correctly', (WidgetTester tester) async {
      final noMembersTeam = mockTeam.copyWith(members: []);

      await tester.pumpWidget(createTestWidget(team: noMembersTeam));

      expect(find.text('0 members'), findsOneWidget);
    });

    testWidgets('displays team with no matches correctly', (WidgetTester tester) async {
      final noMatchesTeam = mockTeam.copyWith(
        stats: TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
      );

      await tester.pumpWidget(createTestWidget(team: noMatchesTeam));

      expect(find.text('0W 0D 0L'), findsOneWidget);
      expect(find.text('0%'), findsOneWidget);
    });

    testWidgets('widget is accessible', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Check that the widget can be found and interacted with
      expect(find.byType(GestureDetector), findsOneWidget);
      expect(find.byType(TeamCardWidget), findsOneWidget);
    });
  });
}
