import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nextsportz_v2/features/teams/presentation/screens/my_teams_screen.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';
import 'package:nextsportz_v2/features/teams/teams_providers.dart';

void main() {
  group('MyTeamsScreen Widget Tests', () {
    testWidgets('should display loading indicator initially', (
      WidgetTester tester,
    ) async {
      // arrange
      await tester.pumpWidget(
        const ProviderScope(child: MaterialApp(home: MyTeamsScreen())),
      );

      // assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display empty state when no teams', (
      WidgetTester tester,
    ) async {
      // arrange
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            myTeamsProvider.overrideWithValue(const AsyncValue.data([])),
          ],
          child: const MaterialApp(home: MyTeamsScreen()),
        ),
      );

      await tester.pumpAndSettle();

      // assert
      expect(find.text('No Teams Yet'), findsOneWidget);
      expect(
        find.text('Create your first team to get started'),
        findsOneWidget,
      );
      expect(find.text('Create Team'), findsOneWidget);
    });

    testWidgets('should display teams list when teams exist', (
      WidgetTester tester,
    ) async {
      // arrange
      final teams = [
        Team(
          id: '1',
          name: 'Test Team',
          description: 'A test team',
          createdBy: 'user1',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          members: [
            TeamMember(
              id: '1',
              userId: 'user1',
              name: 'John Doe',
              position: 'Forward',
              role: 'captain',
              joinedAt: DateTime.now(),
              stats: const PlayerStats(
                matchesPlayed: 10,
                goals: 5,
                assists: 3,
                cleanSheets: 0,
                rating: 4.5,
                yellowCards: 1,
                redCards: 0,
                minutesPlayed: 900,
              ),
              isActive: true,
            ),
          ],
          invitations: [],
          stats: const TeamStats(
            totalMatches: 10,
            wins: 7,
            losses: 2,
            draws: 1,
            winRate: 70.0,
            totalGoals: 25,
            goalsConceded: 12,
            cleanSheets: 3,
            averageGoalsPerMatch: 2.5,
          ),
        ),
      ];

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            myTeamsProvider.overrideWithValue(AsyncValue.data(teams)),
          ],
          child: const MaterialApp(home: MyTeamsScreen()),
        ),
      );

      await tester.pumpAndSettle();

      // assert
      expect(find.text('Test Team'), findsOneWidget);
      expect(find.text('Football Team'), findsOneWidget);
      expect(find.text('A test team'), findsOneWidget);
      expect(find.text('Members'), findsOneWidget);
      expect(find.text('Matches'), findsOneWidget);
      expect(find.text('Win Rate'), findsOneWidget);
    });

    testWidgets('should display error state when error occurs', (
      WidgetTester tester,
    ) async {
      // arrange
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            myTeamsProvider.overrideWithValue(
              const AsyncValue.error('Error loading teams', StackTrace.empty),
            ),
          ],
          child: const MaterialApp(home: MyTeamsScreen()),
        ),
      );

      await tester.pumpAndSettle();

      // assert
      expect(find.text('Error loading teams'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets(
      'should navigate to create team screen when create button is tapped',
      (WidgetTester tester) async {
        // arrange
        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              myTeamsProvider.overrideWithValue(const AsyncValue.data([])),
            ],
            child: const MaterialApp(home: MyTeamsScreen()),
          ),
        );

        await tester.pumpAndSettle();

        // act
        await tester.tap(find.text('Create Team'));
        await tester.pumpAndSettle();

        // assert
        expect(find.text('Create Team'), findsOneWidget);
      },
    );

    testWidgets('should show both tabs', (WidgetTester tester) async {
      // arrange
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            myTeamsProvider.overrideWithValue(const AsyncValue.data([])),
          ],
          child: const MaterialApp(home: MyTeamsScreen()),
        ),
      );

      await tester.pumpAndSettle();

      // assert
      expect(find.text('My Teams'), findsOneWidget);
      expect(find.text('Invitations'), findsOneWidget);
    });

    testWidgets('should switch between tabs', (WidgetTester tester) async {
      // arrange
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            myTeamsProvider.overrideWithValue(const AsyncValue.data([])),
          ],
          child: const MaterialApp(home: MyTeamsScreen()),
        ),
      );

      await tester.pumpAndSettle();

      // act - tap on Invitations tab
      await tester.tap(find.text('Invitations'));
      await tester.pumpAndSettle();

      // assert
      expect(find.text('No Sent Invitations'), findsOneWidget);
      expect(
        find.text('You haven\'t sent any team invitations yet'),
        findsOneWidget,
      );
    });

    testWidgets('should display team with pending invitations', (
      WidgetTester tester,
    ) async {
      // arrange
      final teams = [
        Team(
          id: '1',
          name: 'Test Team',
          description: 'A test team',
          createdBy: 'user1',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          members: [],
          invitations: [
            TeamInvitation(
              id: '1',
              teamId: '1',
              invitedUserId: 'user2',
              invitedUserName: 'Jane Doe',
              status: 'pending',
              createdAt: DateTime.now(),
            ),
          ],
          stats: const TeamStats(
            totalMatches: 0,
            wins: 0,
            losses: 0,
            draws: 0,
            winRate: 0.0,
            totalGoals: 0,
            goalsConceded: 0,
            cleanSheets: 0,
            averageGoalsPerMatch: 0.0,
          ),
        ),
      ];

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            myTeamsProvider.overrideWithValue(AsyncValue.data(teams)),
          ],
          child: const MaterialApp(home: MyTeamsScreen()),
        ),
      );

      await tester.pumpAndSettle();

      // assert
      expect(find.text('1 pending invitation'), findsOneWidget);
    });

    testWidgets('should display app bar with add button', (
      WidgetTester tester,
    ) async {
      // arrange
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            myTeamsProvider.overrideWithValue(const AsyncValue.data([])),
          ],
          child: const MaterialApp(home: MyTeamsScreen()),
        ),
      );

      await tester.pumpAndSettle();

      // assert
      expect(find.text('My Teams'), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);
    });
  });
}
