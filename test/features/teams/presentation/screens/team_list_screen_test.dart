import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:nextsportz_v2/features/teams/presentation/screens/team_list_screen.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';
import 'package:nextsportz_v2/features/teams/teams_providers.dart';

// Mock data for testing
final mockTeams = [
  Team(
    id: '1',
    name: 'Thunder Bolts FC',
    description: 'Elite football team',
    createdBy: 'user1',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    isActive: true,
    members: [],
    invitations: [],
    stats: TeamStats(
      totalMatches: 15,
      wins: 12,
      losses: 2,
      draws: 1,
      winRate: 0.8,
      totalGoals: 45,
      goalsConceded: 12,
      cleanSheets: 8,
      averageGoalsPerMatch: 3.0,
    ),
  ),
  Team(
    id: '2',
    name: 'Lightning Strikers',
    description: 'Fast-paced attacking team',
    createdBy: 'user2',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    isActive: true,
    members: [],
    invitations: [],
    stats: TeamStats(
      totalMatches: 14,
      wins: 10,
      losses: 3,
      draws: 1,
      winRate: 0.71,
      totalGoals: 38,
      goalsConceded: 18,
      cleanSheets: 5,
      averageGoalsPerMatch: 2.7,
    ),
  ),
];

void main() {
  group('TeamListScreen', () {
    Widget createTestWidget({
      List<Team>? teams,
      bool isLoading = false,
      Object? error,
    }) {
      return ProviderScope(
        overrides: [
          teamLeaderboardProvider.overrideWith((ref) async {
            if (error != null) throw error;
            if (isLoading) {
              await Future.delayed(const Duration(seconds: 1));
            }
            return teams ?? mockTeams;
          }),
        ],
        child: const MaterialApp(
          home: TeamListScreen(),
        ),
      );
    }

    testWidgets('displays app bar correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Teams'), findsOneWidget);
      expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);
    });

    testWidgets('displays search bar correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.search), findsOneWidget);
      expect(find.text('Search teams...'), findsOneWidget);
      expect(find.byType(TextField), findsOneWidget);
    });

    testWidgets('displays teams list correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Thunder Bolts FC'), findsOneWidget);
      expect(find.text('Lightning Strikers'), findsOneWidget);
    });

    testWidgets('displays loading state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isLoading: true));
      await tester.pump();

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('displays error state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(error: Exception('Network error')));
      await tester.pumpAndSettle();

      expect(find.text('Failed to load teams'), findsOneWidget);
    });

    testWidgets('displays empty state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(teams: []));
      await tester.pumpAndSettle();

      expect(find.text('No teams available'), findsOneWidget);
      expect(find.byIcon(Icons.groups_outlined), findsOneWidget);
    });

    testWidgets('search functionality works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Initially both teams should be visible
      expect(find.text('Thunder Bolts FC'), findsOneWidget);
      expect(find.text('Lightning Strikers'), findsOneWidget);

      // Enter search query
      await tester.enterText(find.byType(TextField), 'Thunder');
      await tester.pump();

      // Only Thunder Bolts FC should be visible
      expect(find.text('Thunder Bolts FC'), findsOneWidget);
      expect(find.text('Lightning Strikers'), findsNothing);

      // Clear button should appear
      expect(find.byIcon(Icons.clear), findsOneWidget);
    });

    testWidgets('search clear functionality works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Enter search query
      await tester.enterText(find.byType(TextField), 'Thunder');
      await tester.pump();

      // Clear button should appear
      expect(find.byIcon(Icons.clear), findsOneWidget);

      // Tap clear button
      await tester.tap(find.byIcon(Icons.clear));
      await tester.pump();

      // Both teams should be visible again
      expect(find.text('Thunder Bolts FC'), findsOneWidget);
      expect(find.text('Lightning Strikers'), findsOneWidget);

      // Clear button should disappear
      expect(find.byIcon(Icons.clear), findsNothing);
    });

    testWidgets('search shows empty state when no results', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Enter search query that matches no teams
      await tester.enterText(find.byType(TextField), 'NonExistentTeam');
      await tester.pump();

      expect(find.text('No teams found'), findsOneWidget);
      expect(find.text('Try adjusting your search terms'), findsOneWidget);
      expect(find.byIcon(Icons.search_off), findsOneWidget);
    });

    testWidgets('search is case insensitive', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Enter lowercase search query
      await tester.enterText(find.byType(TextField), 'thunder');
      await tester.pump();

      // Should still find Thunder Bolts FC
      expect(find.text('Thunder Bolts FC'), findsOneWidget);
      expect(find.text('Lightning Strikers'), findsNothing);
    });

    testWidgets('search works on team description', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Search by description
      await tester.enterText(find.byType(TextField), 'Elite');
      await tester.pump();

      // Should find Thunder Bolts FC (has "Elite football team" description)
      expect(find.text('Thunder Bolts FC'), findsOneWidget);
      expect(find.text('Lightning Strikers'), findsNothing);
    });

    testWidgets('back button works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find and tap back button
      final backButton = find.byIcon(Icons.arrow_back_ios);
      expect(backButton, findsOneWidget);

      // Note: In a real app test, you would verify navigation
      // Here we just verify the button exists and is tappable
      await tester.tap(backButton);
      await tester.pump();
    });

    testWidgets('team cards are tappable', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find team cards
      final teamCards = find.byType(GestureDetector);
      expect(teamCards, findsWidgets);

      // Note: In a real app test, you would verify navigation to team detail
      // Here we just verify the cards are present and tappable
    });
  });
}
