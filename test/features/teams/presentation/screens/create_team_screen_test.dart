import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../../lib/features/teams/presentation/screens/create_team_screen.dart';
import '../../../../../lib/features/teams/domain/usecases/teams_usecases.dart';
import '../../../../../lib/features/teams/domain/entities/team.dart';
import '../../../../../lib/features/teams/data/datasources/teams_remote_datasource.dart';
import '../../../../test_helpers.dart';

import 'create_team_screen_test.mocks.dart';

@GenerateMocks([CreateTeamUseCase, TeamsRemoteDataSource])
void main() {
  group('CreateTeamScreen Widget Tests', () {
    late MockCreateTeamUseCase mockCreateTeamUseCase;
    late MockTeamsRemoteDataSource mockRemoteDataSource;
    late Team testTeam;

    setUp(() {
      mockCreateTeamUseCase = MockCreateTeamUseCase();
      mockRemoteDataSource = MockTeamsRemoteDataSource();

      testTeam = Team(
        id: '1',
        name: 'Test Team',
        description: 'Test description',
        slogan: 'Test slogan',
        logo: 'https://example.com/logo.jpg',
        createdBy: 'owner123',
        updatedAt: DateTime(2023, 1, 1),
        invitations: [],
        stats: TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
        members: [],
        createdAt: DateTime.now(),
        isActive: true,
      );
    });

    Widget createTestWidget() {
      return TestHelpers.createTestWidget(
        overrides: [
          createTeamUseCaseProvider.overrideWithValue(mockCreateTeamUseCase),
          teamsRemoteDataSourceProvider.overrideWithValue(mockRemoteDataSource),
        ],
        child: const CreateTeamScreen(),
      );
    }

    group('UI Elements', () {
      testWidgets('displays correct app bar title', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Create Team'), findsOneWidget);
      });

      testWidgets('displays back button in app bar', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byIcon(Icons.arrow_back), findsOneWidget);
      });

      testWidgets('displays create button in app bar', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Create'), findsOneWidget);
      });

      testWidgets('displays team logo placeholder', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byIcon(Icons.add_a_photo), findsOneWidget);
        expect(find.text('Add Team Logo'), findsOneWidget);
      });

      testWidgets('displays all required text fields', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(TextFormField), findsNWidgets(3));

        // Check for specific field labels
        expect(find.text('Team Name'), findsOneWidget);
        expect(find.text('Team Slogan (Optional)'), findsOneWidget);
        expect(find.text('Description'), findsOneWidget);
      });

      testWidgets('displays field icons correctly', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byIcon(Icons.group), findsOneWidget);
        expect(find.byIcon(Icons.format_quote), findsOneWidget);
        expect(find.byIcon(Icons.description), findsOneWidget);
      });

      testWidgets('displays tips section', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Tips for a Great Team'), findsOneWidget);
        expect(find.byIcon(Icons.lightbulb_outline), findsOneWidget);
        expect(find.text('Choose a memorable team name'), findsOneWidget);
        expect(find.text('Add a clear description of your team\'s goals'),
            findsOneWidget);
        expect(find.text('Create an inspiring team slogan'), findsOneWidget);
        expect(find.text('Upload a team logo to stand out'), findsOneWidget);
      });
    });

    group('Form Validation', () {
      testWidgets('shows error for empty team name', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Tap create button without filling team name
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Please enter team name'), findsOneWidget);
      });

      testWidgets('shows error for team name less than 3 characters',
          (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Enter short team name
        final nameField = find.byType(TextFormField).first;
        await tester.enterText(nameField, 'AB');
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Team name must be at least 3 characters'),
            findsOneWidget);
      });

      testWidgets('shows error for empty description', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Fill only team name
        final nameField = find.byType(TextFormField).first;
        await tester.enterText(nameField, 'Valid Team Name');
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Please enter team description'), findsOneWidget);
      });

      testWidgets('shows error for description less than 10 characters',
          (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Fill fields with invalid data
        final nameField = find.byType(TextFormField).first;
        final descriptionField = find.byType(TextFormField).last;

        await tester.enterText(nameField, 'Valid Team Name');
        await tester.enterText(descriptionField, 'Short');
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Description must be at least 10 characters'),
            findsOneWidget);
      });

      testWidgets('shows error for slogan less than 3 characters when provided',
          (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Fill fields
        final fields = find.byType(TextFormField);
        final nameField = fields.at(0);
        final sloganField = fields.at(1);
        final descriptionField = fields.at(2);

        await tester.enterText(nameField, 'Valid Team Name');
        await tester.enterText(sloganField, 'AB');
        await tester.enterText(descriptionField, 'Valid description here');
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        expect(
            find.text('Slogan must be at least 3 characters'), findsOneWidget);
      });

      testWidgets('accepts valid form data', (tester) async {
        // Arrange
        when(mockCreateTeamUseCase.call(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => testTeam);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Fill valid data
        final fields = find.byType(TextFormField);
        await tester.enterText(fields.at(0), 'Valid Team Name');
        await tester.enterText(fields.at(1), 'Valid slogan');
        await tester.enterText(fields.at(2), 'Valid team description here');

        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Please enter team name'), findsNothing);
        expect(find.text('Please enter team description'), findsNothing);
        verify(mockCreateTeamUseCase.call(
          name: 'Valid Team Name',
          description: 'Valid team description here',
          slogan: 'Valid slogan',
          logo: null,
        )).called(1);
      });

      testWidgets('allows empty slogan as it is optional', (tester) async {
        // Arrange
        when(mockCreateTeamUseCase.call(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => testTeam);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Fill data without slogan
        final fields = find.byType(TextFormField);
        await tester.enterText(fields.at(0), 'Team Without Slogan');
        await tester.enterText(fields.at(2), 'Description without slogan');

        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockCreateTeamUseCase.call(
          name: 'Team Without Slogan',
          description: 'Description without slogan',
          slogan: null,
          logo: null,
        )).called(1);
      });
    });

    group('Image Selection', () {
      testWidgets('opens image picker when logo container is tapped',
          (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Tap on the logo container
        await tester.tap(find.byIcon(Icons.add_a_photo));
        await tester.pumpAndSettle();

        // Note: We can't easily test ImagePicker directly in widget tests
        // This test mainly ensures no errors occur when tapping
        expect(find.byIcon(Icons.add_a_photo), findsOneWidget);
      });

      testWidgets('opens image picker when Add Team Logo button is tapped',
          (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Tap on the Add Team Logo button
        await tester.tap(find.text('Add Team Logo'));
        await tester.pumpAndSettle();

        // Note: We can't easily test ImagePicker directly in widget tests
        // This test mainly ensures no errors occur when tapping
        expect(find.text('Add Team Logo'), findsOneWidget);
      });
    });

    group('Team Creation', () {
      testWidgets('shows loading indicator during team creation',
          (tester) async {
        // Arrange
        when(mockCreateTeamUseCase.call(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return testTeam;
        });

        // Act
        await tester.pumpWidget(createTestWidget());

        // Fill valid data and submit
        final fields = find.byType(TextFormField);
        await tester.enterText(fields.at(0), 'Loading Test Team');
        await tester.enterText(fields.at(2), 'Loading test description');

        await tester.tap(find.text('Create'));
        await tester.pump();

        // Assert
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Create'), findsNothing);

        // Wait for completion
        await tester.pumpAndSettle();
      });

      testWidgets('disables create button during team creation',
          (tester) async {
        // Arrange
        when(mockCreateTeamUseCase.call(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return testTeam;
        });

        // Act
        await tester.pumpWidget(createTestWidget());

        // Fill valid data
        final fields = find.byType(TextFormField);
        await tester.enterText(fields.at(0), 'Disable Test Team');
        await tester.enterText(fields.at(2), 'Disable test description');

        await tester.tap(find.text('Create'));
        await tester.pump();

        // Try to tap create button again
        await tester.tap(find.byType(TextButton));
        await tester.pump();

        // Assert - should only be called once
        await tester.pumpAndSettle();
        verify(mockCreateTeamUseCase.call(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).called(1);
      });

      testWidgets(
          'shows success message and navigates back on successful creation',
          (tester) async {
        // Arrange
        when(mockCreateTeamUseCase.call(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => testTeam);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Fill valid data and submit
        final fields = find.byType(TextFormField);
        await tester.enterText(fields.at(0), 'Success Team');
        await tester.enterText(fields.at(2), 'Success description');

        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Team created successfully!'), findsOneWidget);
        expect(find.byType(SnackBar), findsOneWidget);
      });

      testWidgets('shows error message on creation failure', (tester) async {
        // Arrange
        when(mockCreateTeamUseCase.call(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenThrow(Exception('Creation failed'));

        // Act
        await tester.pumpWidget(createTestWidget());

        // Fill valid data and submit
        final fields = find.byType(TextFormField);
        await tester.enterText(fields.at(0), 'Error Team');
        await tester.enterText(fields.at(2), 'Error description');

        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.textContaining('Error creating team'), findsOneWidget);
        expect(find.byType(SnackBar), findsOneWidget);
      });

      testWidgets('calls create team use case with correct parameters',
          (tester) async {
        // Arrange
        when(mockCreateTeamUseCase.call(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => testTeam);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Fill specific data
        final fields = find.byType(TextFormField);
        await tester.enterText(fields.at(0), 'Parameter Test Team');
        await tester.enterText(fields.at(1), 'Parameter slogan');
        await tester.enterText(
            fields.at(2), 'Parameter description for testing');

        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockCreateTeamUseCase.call(
          name: 'Parameter Test Team',
          description: 'Parameter description for testing',
          slogan: 'Parameter slogan',
          logo: null,
        )).called(1);
      });

      testWidgets('trims whitespace from input fields', (tester) async {
        // Arrange
        when(mockCreateTeamUseCase.call(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => testTeam);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Fill data with extra whitespace
        final fields = find.byType(TextFormField);
        await tester.enterText(fields.at(0), '  Trimmed Team  ');
        await tester.enterText(fields.at(1), '  Trimmed slogan  ');
        await tester.enterText(fields.at(2), '  Trimmed description  ');

        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockCreateTeamUseCase.call(
          name: 'Trimmed Team',
          description: 'Trimmed description',
          slogan: 'Trimmed slogan',
          logo: null,
        )).called(1);
      });

      testWidgets('handles empty slogan correctly', (tester) async {
        // Arrange
        when(mockCreateTeamUseCase.call(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => testTeam);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Fill data with empty slogan
        final fields = find.byType(TextFormField);
        await tester.enterText(fields.at(0), 'No Slogan Team');
        await tester.enterText(fields.at(1), ''); // Empty slogan
        await tester.enterText(fields.at(2), 'Team without slogan description');

        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockCreateTeamUseCase.call(
          name: 'No Slogan Team',
          description: 'Team without slogan description',
          slogan: null, // Should be null for empty string
          logo: null,
        )).called(1);
      });
    });

    group('Navigation', () {
      testWidgets('navigates back when back button is pressed', (tester) async {
        // Act
        await tester.pumpWidget(TestHelpers.createTestApp(
          overrides: [
            createTeamUseCaseProvider.overrideWithValue(mockCreateTeamUseCase),
            teamsRemoteDataSourceProvider
                .overrideWithValue(mockRemoteDataSource),
          ],
          child: Navigator(
            onGenerateRoute: (settings) {
              if (settings.name == '/') {
                return MaterialPageRoute(
                  builder: (_) => const Scaffold(body: Text('Home')),
                );
              } else if (settings.name == '/create-team') {
                return MaterialPageRoute(
                  builder: (_) => const CreateTeamScreen(),
                );
              }
              return null;
            },
            initialRoute: '/create-team',
          ),
        ));

        await tester.tap(find.byIcon(Icons.arrow_back));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Home'), findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles very long input values', (tester) async {
        // Arrange
        final longName = 'Very long team name ${'A' * 200}';
        final longSlogan = 'Very long slogan ${'B' * 100}';
        final longDescription = 'Very long description ${'C' * 500}';

        when(mockCreateTeamUseCase.call(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => testTeam);

        // Act
        await tester.pumpWidget(createTestWidget());

        final fields = find.byType(TextFormField);
        await tester.enterText(fields.at(0), longName);
        await tester.enterText(fields.at(1), longSlogan);
        await tester.enterText(fields.at(2), longDescription);

        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockCreateTeamUseCase.call(
          name: longName,
          description: longDescription,
          slogan: longSlogan,
          logo: null,
        )).called(1);
      });

      testWidgets('handles special characters in input', (tester) async {
        // Arrange
        const specialName = 'Team 🚀⚽ @#\$%';
        const specialSlogan = 'Victory & Hope! 💪';
        const specialDescription = 'Special chars: éñ @#\$% 🎯';

        when(mockCreateTeamUseCase.call(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => testTeam);

        // Act
        await tester.pumpWidget(createTestWidget());

        final fields = find.byType(TextFormField);
        await tester.enterText(fields.at(0), specialName);
        await tester.enterText(fields.at(1), specialSlogan);
        await tester.enterText(fields.at(2), specialDescription);

        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockCreateTeamUseCase.call(
          name: specialName,
          description: specialDescription,
          slogan: specialSlogan,
          logo: null,
        )).called(1);
      });
    });

    group('Accessibility', () {
      testWidgets('has proper semantic structure', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(Form), findsOneWidget);
        expect(find.byType(TextFormField), findsNWidgets(3));
        expect(find.byType(TextButton), findsAtLeastNWidgets(1));
      });

      testWidgets('form fields are properly labeled', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        final textFields = find.byType(TextFormField);
        expect(textFields, findsNWidgets(3));

        // Check that all fields have labels
        expect(find.text('Team Name'), findsOneWidget);
        expect(find.text('Team Slogan (Optional)'), findsOneWidget);
        expect(find.text('Description'), findsOneWidget);
      });
    });
  });
}
