// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in nextsportz_v2/test/features/teams/presentation/screens/invite_player_screen_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:fpdart/fpdart.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;
import 'package:nextsportz_v2/core/models/paginated_response.dart' as _i6;
import 'package:nextsportz_v2/core/networking/app_error.dart' as _i5;
import 'package:nextsportz_v2/features/player/domain/entities/player_search_item.dart'
    as _i7;
import 'package:nextsportz_v2/features/player/domain/usecases/search_players_usecase.dart'
    as _i2;
import 'package:nextsportz_v2/features/teams/domain/usecases/invite_player_usecase.dart'
    as _i9;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [SearchPlayersUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockSearchPlayersUseCase extends _i1.Mock
    implements _i2.SearchPlayersUseCase {
  MockSearchPlayersUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<
    _i4.Either<_i5.AppError, _i6.PaginatedResponse<_i7.PlayerSearchItem>>
  >
  call(_i2.SearchPlayersParams? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i3.Future<
              _i4.Either<
                _i5.AppError,
                _i6.PaginatedResponse<_i7.PlayerSearchItem>
              >
            >.value(
              _i8.dummyValue<
                _i4.Either<
                  _i5.AppError,
                  _i6.PaginatedResponse<_i7.PlayerSearchItem>
                >
              >(this, Invocation.method(#call, [params])),
            ),
          )
          as _i3.Future<
            _i4.Either<
              _i5.AppError,
              _i6.PaginatedResponse<_i7.PlayerSearchItem>
            >
          >);
}

/// A class which mocks [InvitePlayerUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockInvitePlayerUseCase extends _i1.Mock
    implements _i9.InvitePlayerUseCase {
  MockInvitePlayerUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> call(
    _i9.InvitePlayerParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i8.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);
}
