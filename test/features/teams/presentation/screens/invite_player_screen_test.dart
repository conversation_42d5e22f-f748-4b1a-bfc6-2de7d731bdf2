import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';
import 'package:nextsportz_v2/features/player/domain/entities/player_search_item.dart';
import 'package:nextsportz_v2/features/player/domain/usecases/search_players_usecase.dart';
import 'package:nextsportz_v2/features/player/player_providers.dart';
import 'package:nextsportz_v2/features/teams/domain/usecases/invite_player_usecase.dart'
    as invite_usecase;
import 'package:nextsportz_v2/features/teams/teams_providers.dart';
import 'package:nextsportz_v2/features/teams/presentation/screens/invite_player_screen.dart';

import 'invite_player_screen_test.mocks.dart';

@GenerateMocks([SearchPlayersUseCase, invite_usecase.InvitePlayerUseCase])
void main() {
  late MockSearchPlayersUseCase mockSearchPlayersUseCase;
  late MockInvitePlayerUseCase mockInvitePlayerUseCase;

  // Provide dummy values for Mockito
  provideDummy<Either<AppError, PaginatedResponse<PlayerSearchItem>>>(
    Right(
      PaginatedResponse<PlayerSearchItem>(
        message: 'Success',
        data: PaginatedData<PlayerSearchItem>(
          items: const [],
          total: 0,
          page: 1,
          pageSize: 20,
          totalPages: 0,
        ),
      ),
    ),
  );

  provideDummy<Either<AppError, void>>(const Right(null));

  setUp(() {
    mockSearchPlayersUseCase = MockSearchPlayersUseCase();
    mockInvitePlayerUseCase = MockInvitePlayerUseCase();
  });

  Widget createWidgetUnderTest({String teamId = 'test_team_123'}) {
    return ProviderScope(
      overrides: [
        searchPlayersUseCaseProvider.overrideWithValue(
          mockSearchPlayersUseCase,
        ),
        invitePlayerUseCaseProvider.overrideWithValue(mockInvitePlayerUseCase),
      ],
      child: MaterialApp(home: InvitePlayerScreen(teamId: teamId)),
    );
  }

  group('InvitePlayerScreen Widget Tests', () {
    const testTeamId = 'test_team_123';

    final mockPlayers = [
      const PlayerSearchItem(
        id: 'player_1',
        fullName: 'John Doe',
        age: 25,
        heightCm: 180,
        weightKg: 75,
        email: '<EMAIL>',
        phone: '+**********',
        description: 'Experienced midfielder',
        photoUrl: 'https://example.com/john.jpg',
        scoutingEnabled: true,
        gameInfo: {'position': 'Midfielder', 'preferredFoot': 'Right'},
      ),
      const PlayerSearchItem(
        id: 'player_2',
        fullName: 'Jane Smith',
        age: 28,
        heightCm: 175,
        weightKg: 70,
        email: '<EMAIL>',
        phone: '+**********',
        description: 'Skilled forward',
        photoUrl: null,
        scoutingEnabled: false,
        gameInfo: {'position': 'Forward', 'preferredFoot': 'Left'},
      ),
    ];

    final mockPaginatedResponse = PaginatedResponse<PlayerSearchItem>(
      message: 'Success',
      data: PaginatedData<PlayerSearchItem>(
        items: mockPlayers,
        total: 2,
        page: 1,
        pageSize: 20,
        totalPages: 1,
      ),
    );

    group('Initial UI State', () {
      testWidgets('should display app bar with correct title', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());

        // Assert
        expect(find.text('Invite Player'), findsOneWidget);
        expect(find.byIcon(Icons.arrow_back), findsOneWidget);
      });

      testWidgets('should display search header with correct elements', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());

        // Assert
        expect(find.text('Search Players'), findsOneWidget);
        expect(
          find.text('Search by player ID, email, or phone number'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.person_search), findsOneWidget);
      });

      testWidgets('should display search bar with correct placeholder', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());

        // Assert
        expect(find.byType(TextField), findsOneWidget);
        expect(find.text('Search by ID, email, or phone...'), findsOneWidget);
        expect(find.byIcon(Icons.search), findsOneWidget);
      });

      testWidgets('should display empty state when no search query', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());

        // Assert
        expect(find.text('Search for Players'), findsOneWidget);
        expect(
          find.text(
            'Enter a player ID, email, or phone number to find players to invite',
          ),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.person_search), findsAtLeastNWidgets(1));
      });
    });

    group('Search Functionality', () {
      testWidgets('should show clear button when text is entered', (
        tester,
      ) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        final textField = find.byType(TextField);

        // Act
        await tester.enterText(textField, 'john');
        await tester.pump();

        // Assert
        expect(find.byIcon(Icons.clear), findsOneWidget);
      });

      testWidgets('should clear text when clear button is tapped', (
        tester,
      ) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        final textField = find.byType(TextField);

        await tester.enterText(textField, 'john');
        await tester.pump();

        // Act
        await tester.tap(find.byIcon(Icons.clear));
        await tester.pump();

        // Assert
        expect(find.byIcon(Icons.clear), findsNothing);
        final textFieldWidget = tester.widget<TextField>(textField);
        expect(textFieldWidget.controller?.text, isEmpty);
      });

      testWidgets('should trigger search after debounce delay', (tester) async {
        // Arrange
        when(
          mockSearchPlayersUseCase.call(any),
        ).thenAnswer((_) async => Right(mockPaginatedResponse));

        await tester.pumpWidget(createWidgetUnderTest());
        final textField = find.byType(TextField);

        // Act
        await tester.enterText(textField, 'john');
        await tester.pump(
          const Duration(milliseconds: 600),
        ); // Wait for debounce

        // Assert
        verify(mockSearchPlayersUseCase.call(any)).called(1);
      });

      testWidgets('should not trigger search before debounce delay', (
        tester,
      ) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        final textField = find.byType(TextField);

        // Act
        await tester.enterText(textField, 'john');
        await tester.pump(
          const Duration(milliseconds: 300),
        ); // Less than debounce

        // Assert
        verifyNever(mockSearchPlayersUseCase.call(any));
      });
    });

    group('Player Search Results', () {
      testWidgets('should display player cards when search returns results', (
        tester,
      ) async {
        // Arrange
        when(
          mockSearchPlayersUseCase.call(any),
        ).thenAnswer((_) async => Right(mockPaginatedResponse));

        await tester.pumpWidget(createWidgetUnderTest());
        final textField = find.byType(TextField);

        // Act
        await tester.enterText(textField, 'john');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pump(); // Allow UI to update

        // Assert
        expect(find.text('John Doe'), findsOneWidget);
        expect(find.text('Jane Smith'), findsOneWidget);
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('+**********'), findsOneWidget);
        expect(find.text('+**********'), findsOneWidget);
      });

      testWidgets('should display invite buttons for each player', (
        tester,
      ) async {
        // Arrange
        when(
          mockSearchPlayersUseCase.call(any),
        ).thenAnswer((_) async => Right(mockPaginatedResponse));

        await tester.pumpWidget(createWidgetUnderTest());
        final textField = find.byType(TextField);

        // Act
        await tester.enterText(textField, 'john');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pump();

        // Assert
        expect(find.text('Invite'), findsNWidgets(2));
        expect(find.byType(ElevatedButton), findsNWidgets(2));
      });

      testWidgets('should display no results state when search returns empty', (
        tester,
      ) async {
        // Arrange
        final emptyResponse = PaginatedResponse<PlayerSearchItem>(
          message: 'Success',
          data: PaginatedData<PlayerSearchItem>(
            items: const [],
            total: 0,
            page: 1,
            pageSize: 20,
            totalPages: 0,
          ),
        );

        when(
          mockSearchPlayersUseCase.call(any),
        ).thenAnswer((_) async => Right(emptyResponse));

        await tester.pumpWidget(createWidgetUnderTest());
        final textField = find.byType(TextField);

        // Act
        await tester.enterText(textField, 'nonexistent');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pump();

        // Assert
        expect(find.text('No Players Found'), findsOneWidget);
        expect(
          find.text(
            'No players match your search criteria. Try a different search term.',
          ),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.search_off), findsOneWidget);
      });

      testWidgets('should handle search error gracefully', (tester) async {
        // Arrange
        when(
          mockSearchPlayersUseCase.call(any),
        ).thenAnswer((_) async => const Left(AppError('Network error')));

        await tester.pumpWidget(createWidgetUnderTest());
        final textField = find.byType(TextField);

        // Act
        await tester.enterText(textField, 'john');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pump();

        // Assert
        // The error should be handled by the PaginatedListView
        expect(find.text('John Doe'), findsNothing);
      });
    });

    group('Player Invitation', () {
      testWidgets('should call invite use case when invite button is tapped', (
        tester,
      ) async {
        // Arrange
        when(
          mockSearchPlayersUseCase.call(any),
        ).thenAnswer((_) async => Right(mockPaginatedResponse));
        when(
          mockInvitePlayerUseCase.call(any),
        ).thenAnswer((_) async => const Right(null));

        await tester.pumpWidget(createWidgetUnderTest(teamId: testTeamId));
        final textField = find.byType(TextField);

        await tester.enterText(textField, 'john');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pump();

        // Act
        await tester.tap(find.text('Invite').first);
        await tester.pump();

        // Assert
        verify(mockInvitePlayerUseCase.call(any)).called(1);
      });

      testWidgets('should show loading state when invitation is in progress', (
        tester,
      ) async {
        // Arrange
        when(
          mockSearchPlayersUseCase.call(any),
        ).thenAnswer((_) async => Right(mockPaginatedResponse));
        when(mockInvitePlayerUseCase.call(any)).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return const Right(null);
        });

        await tester.pumpWidget(createWidgetUnderTest());
        final textField = find.byType(TextField);

        await tester.enterText(textField, 'john');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pump();

        // Act
        await tester.tap(find.text('Invite').first);
        await tester.pump(const Duration(milliseconds: 50));

        // Assert
        expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(1));
      });

      testWidgets('should show success state after successful invitation', (
        tester,
      ) async {
        // Arrange
        when(
          mockSearchPlayersUseCase.call(any),
        ).thenAnswer((_) async => Right(mockPaginatedResponse));
        when(
          mockInvitePlayerUseCase.call(any),
        ).thenAnswer((_) async => const Right(null));

        await tester.pumpWidget(createWidgetUnderTest());
        final textField = find.byType(TextField);

        await tester.enterText(textField, 'john');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pump();

        // Act
        await tester.tap(find.text('Invite').first);
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Invited'), findsOneWidget);
        expect(find.byIcon(Icons.check), findsOneWidget);
      });

      testWidgets('should show error snackbar when invitation fails', (
        tester,
      ) async {
        // Arrange
        when(
          mockSearchPlayersUseCase.call(any),
        ).thenAnswer((_) async => Right(mockPaginatedResponse));
        when(
          mockInvitePlayerUseCase.call(any),
        ).thenAnswer((_) async => const Left(AppError('Invitation failed')));

        await tester.pumpWidget(createWidgetUnderTest());
        final textField = find.byType(TextField);

        await tester.enterText(textField, 'john');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pump();

        // Act
        await tester.tap(find.text('Invite').first);
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.textContaining('Failed to invite'), findsOneWidget);
      });

      testWidgets('should prevent multiple invitations for same player', (
        tester,
      ) async {
        // Arrange
        when(
          mockSearchPlayersUseCase.call(any),
        ).thenAnswer((_) async => Right(mockPaginatedResponse));
        when(
          mockInvitePlayerUseCase.call(any),
        ).thenAnswer((_) async => const Right(null));

        await tester.pumpWidget(createWidgetUnderTest());
        final textField = find.byType(TextField);

        await tester.enterText(textField, 'john');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pump();

        // Act
        await tester.tap(find.text('Invite').first);
        await tester.pumpAndSettle();

        // Try to invite again
        await tester.tap(find.text('Invited'));
        await tester.pump();

        // Assert
        verify(
          mockInvitePlayerUseCase.call(any),
        ).called(1); // Should only be called once
      });
    });

    group('Navigation', () {
      testWidgets('should pop screen when back button is tapped', (
        tester,
      ) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());

        // Act
        await tester.tap(find.byIcon(Icons.arrow_back));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(InvitePlayerScreen), findsNothing);
      });
    });

    group('Accessibility', () {
      testWidgets('should have proper semantics for screen reader', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());

        // Assert
        expect(find.bySemanticsLabel('Invite Player'), findsOneWidget);
        expect(find.byType(TextField), findsOneWidget);
      });

      testWidgets('should support keyboard navigation', (tester) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());

        // Act
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pump();

        // Assert
        expect(tester.binding.focusManager.primaryFocus, isNotNull);
      });
    });
  });
}
