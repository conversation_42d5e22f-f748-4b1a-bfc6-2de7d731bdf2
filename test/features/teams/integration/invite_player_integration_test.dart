import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:integration_test/integration_test.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';
import 'package:nextsportz_v2/features/player/data/datasources/player_mock_datasource.dart';
import 'package:nextsportz_v2/features/player/data/repositories/player_repository_impl.dart';
import 'package:nextsportz_v2/features/player/domain/usecases/search_players_usecase.dart';
import 'package:nextsportz_v2/features/player/player_providers.dart';
import 'package:nextsportz_v2/features/teams/data/datasources/team_invitations_mock_datasource.dart';
import 'package:nextsportz_v2/features/teams/data/repositories/team_invitations_repository_impl.dart';
import 'package:nextsportz_v2/features/teams/domain/usecases/invite_player_usecase.dart'
    as invite_usecase;
import 'package:nextsportz_v2/features/teams/teams_providers.dart';
import 'package:nextsportz_v2/features/teams/presentation/screens/invite_player_screen.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Invite Player Integration Tests', () {
    late PlayerMockDataSource mockPlayerDataSource;
    late TeamInvitationsMockDataSource mockTeamInvitationsDataSource;
    late PlayerRepositoryImpl playerRepository;
    late TeamInvitationsRepositoryImpl teamInvitationsRepository;
    late SearchPlayersUseCase searchPlayersUseCase;
    late invite_usecase.InvitePlayerUseCase invitePlayerUseCase;

    setUp(() {
      mockPlayerDataSource = PlayerMockDataSource();
      mockTeamInvitationsDataSource = TeamInvitationsMockDataSource();
      playerRepository = PlayerRepositoryImpl(mockPlayerDataSource);
      teamInvitationsRepository = TeamInvitationsRepositoryImpl(
        mockTeamInvitationsDataSource,
      );
      searchPlayersUseCase = SearchPlayersUseCase(playerRepository);
      invitePlayerUseCase = invite_usecase.InvitePlayerUseCase(
        teamInvitationsRepository,
      );
    });

    Widget createApp({String teamId = 'test_team_123'}) {
      return ProviderScope(
        overrides: [
          searchPlayersUseCaseProvider.overrideWithValue(searchPlayersUseCase),
          invitePlayerUseCaseProvider.overrideWithValue(invitePlayerUseCase),
        ],
        child: MaterialApp(home: InvitePlayerScreen(teamId: teamId)),
      );
    }

    group('Complete User Flow', () {
      testWidgets('should complete full invite player flow successfully', (
        tester,
      ) async {
        // Arrange
        const testTeamId = 'test_team_123';
        await tester.pumpWidget(createApp(teamId: testTeamId));

        // Verify initial state
        expect(find.text('Invite Player'), findsOneWidget);
        expect(find.text('Search for Players'), findsOneWidget);

        // Act 1: Enter search query
        final searchField = find.byType(TextField);
        await tester.enterText(searchField, 'john');

        // Wait for debounce
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();

        // Assert 1: Search results should be displayed
        expect(find.textContaining('John'), findsAtLeastNWidget(1));
        expect(find.text('Invite'), findsAtLeastNWidget(1));

        // Act 2: Invite first player
        final inviteButtons = find.text('Invite');
        await tester.tap(inviteButtons.first);
        await tester.pumpAndSettle();

        // Assert 2: Invitation should be successful
        expect(find.text('Invited'), findsOneWidget);
        expect(find.byIcon(Icons.check), findsOneWidget);
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.textContaining('Invitation sent'), findsOneWidget);

        // Act 3: Try to invite the same player again
        await tester.tap(find.text('Invited'));
        await tester.pump();

        // Assert 3: Should not allow duplicate invitation
        expect(
          find.text('Invited'),
          findsOneWidget,
        ); // Still shows invited state
        expect(
          find.text('Invite'),
          findsAtLeastNWidget(1),
        ); // Other players still have invite button
      });

      testWidgets('should handle search with no results', (tester) async {
        // Arrange
        await tester.pumpWidget(createApp());

        // Act: Search for non-existent player
        final searchField = find.byType(TextField);
        await tester.enterText(searchField, 'nonexistentplayer12345');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();

        // Assert: Should show no results state
        expect(find.text('No Players Found'), findsOneWidget);
        expect(
          find.text(
            'No players match your search criteria. Try a different search term.',
          ),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.search_off), findsOneWidget);
      });

      testWidgets('should handle multiple player invitations', (tester) async {
        // Arrange
        await tester.pumpWidget(createApp());

        // Act 1: Search for players
        final searchField = find.byType(TextField);
        await tester.enterText(searchField, 'player');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();

        // Get all invite buttons
        final inviteButtons = find.text('Invite');
        final buttonCount = tester.widgetList(inviteButtons).length;
        expect(buttonCount, greaterThan(1));

        // Act 2: Invite multiple players
        for (int i = 0; i < 2 && i < buttonCount; i++) {
          await tester.tap(inviteButtons.at(i));
          await tester.pump(const Duration(milliseconds: 100));
          await tester.pumpAndSettle();
        }

        // Assert: Should have multiple invited players
        expect(find.text('Invited'), findsNWidgets(2));
        expect(find.byIcon(Icons.check), findsNWidgets(2));
      });

      testWidgets('should clear search and return to empty state', (
        tester,
      ) async {
        // Arrange
        await tester.pumpWidget(createApp());

        // Act 1: Enter search query
        final searchField = find.byType(TextField);
        await tester.enterText(searchField, 'john');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();

        // Verify search results are shown
        expect(find.textContaining('John'), findsAtLeastNWidget(1));

        // Act 2: Clear search
        await tester.tap(find.byIcon(Icons.clear));
        await tester.pumpAndSettle();

        // Assert: Should return to empty state
        expect(find.text('Search for Players'), findsOneWidget);
        expect(
          find.text(
            'Enter a player ID, email, or phone number to find players to invite',
          ),
          findsOneWidget,
        );
        expect(find.textContaining('John'), findsNothing);
      });

      testWidgets('should handle navigation back', (tester) async {
        // Arrange
        await tester.pumpWidget(createApp());

        // Act: Tap back button
        await tester.tap(find.byIcon(Icons.arrow_back));
        await tester.pumpAndSettle();

        // Assert: Should navigate back (screen should be popped)
        expect(find.byType(InvitePlayerScreen), findsNothing);
      });
    });

    group('Search Functionality', () {
      testWidgets('should search by different criteria', (tester) async {
        // Arrange
        await tester.pumpWidget(createApp());
        final searchField = find.byType(TextField);

        // Test search by name
        await tester.enterText(searchField, 'john');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();
        expect(find.textContaining('John'), findsAtLeastNWidget(1));

        // Clear and test search by email pattern
        await tester.tap(find.byIcon(Icons.clear));
        await tester.pump();
        await tester.enterText(searchField, '@example.com');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();
        expect(find.textContaining('@example.com'), findsAtLeastNWidget(1));

        // Clear and test search by phone pattern
        await tester.tap(find.byIcon(Icons.clear));
        await tester.pump();
        await tester.enterText(searchField, '+1');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();
        expect(find.textContaining('+1'), findsAtLeastNWidget(1));
      });

      testWidgets('should handle special characters in search', (tester) async {
        // Arrange
        await tester.pumpWidget(createApp());
        final searchField = find.byType(TextField);

        // Act: Search with special characters
        await tester.enterText(searchField, 'João@special');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();

        // Assert: Should handle gracefully (no crash)
        expect(find.byType(InvitePlayerScreen), findsOneWidget);
      });

      testWidgets('should debounce search requests', (tester) async {
        // Arrange
        await tester.pumpWidget(createApp());
        final searchField = find.byType(TextField);

        // Act: Type quickly without waiting for debounce
        await tester.enterText(searchField, 'j');
        await tester.pump(const Duration(milliseconds: 100));
        await tester.enterText(searchField, 'jo');
        await tester.pump(const Duration(milliseconds: 100));
        await tester.enterText(searchField, 'joh');
        await tester.pump(const Duration(milliseconds: 100));
        await tester.enterText(searchField, 'john');

        // Wait for debounce to complete
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();

        // Assert: Should only show results after debounce
        expect(find.textContaining('John'), findsAtLeastNWidget(1));
      });
    });

    group('Error Handling', () {
      testWidgets('should handle invitation errors gracefully', (tester) async {
        // This test would require mocking error scenarios
        // For now, we'll test the UI behavior with successful flows
        await tester.pumpWidget(createApp());

        final searchField = find.byType(TextField);
        await tester.enterText(searchField, 'john');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();

        // Verify error handling UI elements exist
        expect(find.byType(InvitePlayerScreen), findsOneWidget);
        expect(find.text('Invite'), findsAtLeastNWidget(1));
      });
    });

    group('Performance', () {
      testWidgets('should handle large search results efficiently', (
        tester,
      ) async {
        // Arrange
        await tester.pumpWidget(createApp());

        // Act: Search for a common term that returns many results
        final searchField = find.byType(TextField);
        await tester.enterText(searchField, 'player');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();

        // Assert: Should render without performance issues
        expect(find.byType(InvitePlayerScreen), findsOneWidget);
        expect(find.text('Invite'), findsAtLeastNWidget(1));

        // Test scrolling performance
        await tester.drag(find.byType(ListView), const Offset(0, -300));
        await tester.pumpAndSettle();
        expect(find.byType(InvitePlayerScreen), findsOneWidget);
      });

      testWidgets('should handle rapid search changes efficiently', (
        tester,
      ) async {
        // Arrange
        await tester.pumpWidget(createApp());
        final searchField = find.byType(TextField);

        // Act: Rapidly change search terms
        for (int i = 0; i < 5; i++) {
          await tester.enterText(searchField, 'search$i');
          await tester.pump(const Duration(milliseconds: 50));
        }

        // Wait for final debounce
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();

        // Assert: Should handle gracefully
        expect(find.byType(InvitePlayerScreen), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('should be accessible to screen readers', (tester) async {
        // Arrange
        await tester.pumpWidget(createApp());

        // Assert: Check for semantic labels
        expect(find.bySemanticsLabel('Invite Player'), findsOneWidget);

        // Check that interactive elements have proper semantics
        final searchField = find.byType(TextField);
        expect(searchField, findsOneWidget);

        // Verify buttons have proper semantics
        await tester.enterText(searchField, 'john');
        await tester.pump(const Duration(milliseconds: 600));
        await tester.pumpAndSettle();

        expect(find.text('Invite'), findsAtLeastNWidget(1));
      });

      testWidgets('should support keyboard navigation', (tester) async {
        // Arrange
        await tester.pumpWidget(createApp());

        // Act: Use keyboard navigation
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pump();

        // Assert: Focus should be manageable
        expect(tester.binding.focusManager.primaryFocus, isNotNull);
      });
    });
  });
}
