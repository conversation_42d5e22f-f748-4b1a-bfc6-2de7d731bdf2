import 'package:flutter_test/flutter_test.dart';
import 'package:fpdart/fpdart.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:nextsportz_v2/core/networking/api_client.dart';
import 'package:nextsportz_v2/features/teams/data/datasources/team_invitations_remote_datasource.dart';
import 'package:nextsportz_v2/features/teams/data/repositories/team_invitations_repository_impl.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team_role.dart';
import 'package:nextsportz_v2/features/teams/domain/usecases/team_invitations_usecases.dart';

import 'team_invitations_integration_test.mocks.dart';

@GenerateMocks([ApiClient])
void main() {
  late MockApiClient mockApiClient;
  late TeamInvitationsRemoteDataSource dataSource;
  late TeamInvitationsRepositoryImpl repository;
  late InviteTeamMemberUseCase inviteTeamMemberUseCase;
  late AcceptInvitationUseCase acceptInvitationUseCase;
  late DeclineInvitationUseCase declineInvitationUseCase;
  late DeleteTeamInviteUseCase deleteTeamInviteUseCase;
  late AcceptTeamInviteUseCase acceptTeamInviteUseCase;

  setUp(() {
    mockApiClient = MockApiClient();
    dataSource = TeamInvitationsRemoteDataSource(mockApiClient);
    repository = TeamInvitationsRepositoryImpl(dataSource);
    inviteTeamMemberUseCase = InviteTeamMemberUseCase(repository);
    acceptInvitationUseCase = AcceptInvitationUseCase(repository);
    declineInvitationUseCase = DeclineInvitationUseCase(repository);
    deleteTeamInviteUseCase = DeleteTeamInviteUseCase(repository);
    acceptTeamInviteUseCase = AcceptTeamInviteUseCase(repository);
  });

  group('Team Invitations Integration Tests', () {
    group('Invite Team Member Flow', () {
      test('should successfully invite a player to team', () async {
        // Arrange
        const params = InviteTeamMemberParams(
          teamId: 'team-123',
          playerId: 'player-456',
          role: TeamRole.player,
        );

        when(mockApiClient.post(any, data: anyNamed('data')))
            .thenAnswer((_) async => {});

        // Act
        final result = await inviteTeamMemberUseCase(params);

        // Assert
        expect(result, isA<Right>());
        verify(mockApiClient.post(
          '/api/teams/team-123/invitations',
          data: {
            'playerId': 'player-456',
            'role': 'Player',
          },
        )).called(1);
      });

      test('should successfully invite a captain to team', () async {
        // Arrange
        const params = InviteTeamMemberParams(
          teamId: 'team-123',
          playerId: 'player-456',
          role: TeamRole.captain,
        );

        when(mockApiClient.post(any, data: anyNamed('data')))
            .thenAnswer((_) async => {});

        // Act
        final result = await inviteTeamMemberUseCase(params);

        // Assert
        expect(result, isA<Right>());
        verify(mockApiClient.post(
          '/api/teams/team-123/invitations',
          data: {
            'playerId': 'player-456',
            'role': 'Captain',
          },
        )).called(1);
      });
    });

    group('Accept Invitation Flow', () {
      test('should successfully accept invitation', () async {
        // Arrange
        const params = AcceptInvitationParams(
          teamId: 'team-123',
          invitationId: 'invitation-789',
        );

        when(mockApiClient.post(any)).thenAnswer((_) async => {});

        // Act
        final result = await acceptInvitationUseCase(params);

        // Assert
        expect(result, isA<Right>());
        verify(mockApiClient.post(
          '/api/teams/team-123/invitations/invitation-789/accept',
        )).called(1);
      });
    });

    group('Decline Invitation Flow', () {
      test('should successfully decline invitation', () async {
        // Arrange
        const params = DeclineInvitationParams(
          teamId: 'team-123',
          invitationId: 'invitation-789',
        );

        when(mockApiClient.post(any)).thenAnswer((_) async => {});

        // Act
        final result = await declineInvitationUseCase(params);

        // Assert
        expect(result, isA<Right>());
        verify(mockApiClient.post(
          '/api/teams/team-123/invitations/invitation-789/decline',
        )).called(1);
      });
    });

    group('Delete Team Invite Flow', () {
      test('should successfully delete team invite', () async {
        // Arrange
        const params = DeleteTeamInviteParams(
          teamId: 'team-123',
          memberId: 'member-456',
        );

        when(mockApiClient.delete(any)).thenAnswer((_) async => {});

        // Act
        final result = await deleteTeamInviteUseCase(params);

        // Assert
        expect(result, isA<Right>());
        verify(mockApiClient.delete(
          '/api/teams/invitations/member-456',
        )).called(1);
      });
    });

    group('Accept Team Invite Flow', () {
      test('should successfully accept team invite', () async {
        // Arrange
        const params = AcceptTeamInviteParams(
          teamId: 'team-123',
          memberId: 'member-456',
        );

        when(mockApiClient.post(any)).thenAnswer((_) async => {});

        // Act
        final result = await acceptTeamInviteUseCase(params);

        // Assert
        expect(result, isA<Right>());
        verify(mockApiClient.post(
          '/api/teams/team-123/invitations/member-456/accept',
        )).called(1);
      });
    });

    group('Error Handling', () {
      test('should handle network errors gracefully', () async {
        // Arrange
        const params = InviteTeamMemberParams(
          teamId: 'team-123',
          playerId: 'player-456',
          role: TeamRole.player,
        );

        when(mockApiClient.post(any, data: anyNamed('data')))
            .thenThrow(Exception('Network error'));

        // Act
        final result = await inviteTeamMemberUseCase(params);

        // Assert
        expect(result, isA<Left>());
        expect(result.fold((l) => l.message, (r) => null), contains('Network error'));
      });

      test('should handle API errors for accept invitation', () async {
        // Arrange
        const params = AcceptInvitationParams(
          teamId: 'team-123',
          invitationId: 'invitation-789',
        );

        when(mockApiClient.post(any))
            .thenThrow(Exception('API error'));

        // Act
        final result = await acceptInvitationUseCase(params);

        // Assert
        expect(result, isA<Left>());
        expect(result.fold((l) => l.message, (r) => null), contains('API error'));
      });
    });

    group('End-to-End Invitation Workflow', () {
      test('should complete full invitation workflow', () async {
        // Arrange - Invite a player
        const inviteParams = InviteTeamMemberParams(
          teamId: 'team-123',
          playerId: 'player-456',
          role: TeamRole.player,
        );

        const acceptParams = AcceptInvitationParams(
          teamId: 'team-123',
          invitationId: 'invitation-789',
        );

        when(mockApiClient.post(any, data: anyNamed('data')))
            .thenAnswer((_) async => {});
        when(mockApiClient.post(any))
            .thenAnswer((_) async => {});

        // Act & Assert - Invite player
        final inviteResult = await inviteTeamMemberUseCase(inviteParams);
        expect(inviteResult, isA<Right>());

        // Act & Assert - Accept invitation
        final acceptResult = await acceptInvitationUseCase(acceptParams);
        expect(acceptResult, isA<Right>());

        // Verify both API calls were made
        verify(mockApiClient.post(
          '/api/teams/team-123/invitations',
          data: {
            'playerId': 'player-456',
            'role': 'Player',
          },
        )).called(1);

        verify(mockApiClient.post(
          '/api/teams/team-123/invitations/invitation-789/accept',
        )).called(1);
      });
    });
  });
}
