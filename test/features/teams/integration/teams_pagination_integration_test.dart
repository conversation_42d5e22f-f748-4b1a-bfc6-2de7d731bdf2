import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/features/teams/domain/repositories/teams_repository.dart';
import 'package:nextsportz_v2/features/teams/presentation/screens/team_list_screen.dart';
import 'package:nextsportz_v2/features/teams/teams_providers.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';

import 'teams_pagination_integration_test.mocks.dart';
import '../test_data_factory.dart';

@GenerateMocks([TeamsRepository])
void main() {
  group('Teams Pagination Integration Tests', () {
    late MockTeamsRepository mockRepository;

    setUp(() {
      mockRepository = MockTeamsRepository();
    });

    Widget createTestApp() {
      return ProviderScope(
        overrides: [
          teamsRepositoryProvider.overrideWithValue(mockRepository),
        ],
        child: const MaterialApp(
          home: TeamListScreen(),
        ),
      );
    }

    group('Team List Screen Pagination', () {
      testWidgets('should load and display teams on initial load', (tester) async {
        // Arrange
        final testTeams = TeamsTestDataFactory.createTestTeamSearchItems(count: 5);
        final paginatedResponse = TeamsTestDataFactory.createTestPaginatedResponse(
          items: testTeams,
          total: 20,
          page: 1,
          pageSize: 20,
          totalPages: 1,
        );

        when(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).thenAnswer((_) async => Right(paginatedResponse));

        // Act
        await tester.pumpWidget(createTestApp());
        await tester.pump(); // Allow initial load to complete

        // Assert
        expect(find.text('Team 1'), findsOneWidget);
        expect(find.text('Team 2'), findsOneWidget);
        expect(find.text('Team 3'), findsOneWidget);
        expect(find.text('Team 4'), findsOneWidget);
        expect(find.text('Team 5'), findsOneWidget);
        
        verify(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).called(1);
      });

      testWidgets('should load more teams when scrolling to bottom', (tester) async {
        // Arrange
        final firstPageTeams = TeamsTestDataFactory.createTestTeamSearchItems(
          count: 3,
          namePrefix: 'First',
        );
        final secondPageTeams = TeamsTestDataFactory.createTestTeamSearchItems(
          count: 2,
          namePrefix: 'Second',
        );

        final firstPageResponse = TeamsTestDataFactory.createTestPaginatedResponse(
          items: firstPageTeams,
          total: 5,
          page: 1,
          pageSize: 3,
          totalPages: 2,
        );

        final secondPageResponse = TeamsTestDataFactory.createTestPaginatedResponse(
          items: secondPageTeams,
          total: 5,
          page: 2,
          pageSize: 3,
          totalPages: 2,
        );

        when(mockRepository.getAllTeamsPaginated(page: 1, pageSize: 20))
            .thenAnswer((_) async => Right(firstPageResponse));
        when(mockRepository.getAllTeamsPaginated(page: 2, pageSize: 20))
            .thenAnswer((_) async => Right(secondPageResponse));

        // Act
        await tester.pumpWidget(createTestApp());
        await tester.pump(); // Load first page

        // Verify first page is loaded
        expect(find.text('First 1'), findsOneWidget);
        expect(find.text('Second 1'), findsNothing);

        // Scroll to bottom to trigger loading more
        await tester.drag(find.byType(ListView), const Offset(0, -500));
        await tester.pump();
        await tester.pump(); // Allow second page to load

        // Assert
        expect(find.text('First 1'), findsOneWidget); // Original items still there
        expect(find.text('Second 1'), findsOneWidget); // New items loaded
        expect(find.text('Second 2'), findsOneWidget);
        
        verify(mockRepository.getAllTeamsPaginated(page: 1, pageSize: 20)).called(1);
        verify(mockRepository.getAllTeamsPaginated(page: 2, pageSize: 20)).called(1);
      });

      testWidgets('should handle search with pagination', (tester) async {
        // Arrange
        final searchResults = TeamsTestDataFactory.createTestTeamSearchItems(
          count: 2,
          namePrefix: 'Search Result',
        );
        final searchResponse = TeamsTestDataFactory.createTestPaginatedResponse(
          items: searchResults,
          total: 2,
          page: 1,
          pageSize: 20,
          totalPages: 1,
        );

        when(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).thenAnswer((_) async => Right(TeamsTestDataFactory.createTestPaginatedResponse()));

        when(mockRepository.searchTeamsPaginated(
          query: 'search',
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).thenAnswer((_) async => Right(searchResponse));

        // Act
        await tester.pumpWidget(createTestApp());
        await tester.pump(); // Initial load

        // Enter search query
        await tester.enterText(find.byType(TextField), 'search');
        await tester.pump(); // Trigger search

        // Assert
        expect(find.text('Search Result 1'), findsOneWidget);
        expect(find.text('Search Result 2'), findsOneWidget);
        
        verify(mockRepository.searchTeamsPaginated(
          query: 'search',
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).called(1);
      });

      testWidgets('should clear search and reload all teams', (tester) async {
        // Arrange
        final allTeams = TeamsTestDataFactory.createTestTeamSearchItems(count: 3);
        final searchResults = TeamsTestDataFactory.createTestTeamSearchItems(
          count: 1,
          namePrefix: 'Search',
        );

        when(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).thenAnswer((_) async => Right(
          TeamsTestDataFactory.createTestPaginatedResponse(items: allTeams),
        ));

        when(mockRepository.searchTeamsPaginated(
          query: anyNamed('query'),
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).thenAnswer((_) async => Right(
          TeamsTestDataFactory.createTestPaginatedResponse(items: searchResults),
        ));

        // Act
        await tester.pumpWidget(createTestApp());
        await tester.pump(); // Initial load

        // Search
        await tester.enterText(find.byType(TextField), 'search');
        await tester.pump();

        expect(find.text('Search 1'), findsOneWidget);

        // Clear search
        await tester.tap(find.byIcon(Icons.clear));
        await tester.pump();

        // Assert
        expect(find.text('Team 1'), findsOneWidget);
        expect(find.text('Search 1'), findsNothing);
      });

      testWidgets('should handle pull to refresh', (tester) async {
        // Arrange
        final initialTeams = TeamsTestDataFactory.createTestTeamSearchItems(
          count: 2,
          namePrefix: 'Initial',
        );
        final refreshedTeams = TeamsTestDataFactory.createTestTeamSearchItems(
          count: 3,
          namePrefix: 'Refreshed',
        );

        when(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        ))
            .thenAnswer((_) async => Right(
              TeamsTestDataFactory.createTestPaginatedResponse(items: initialTeams),
            ))
            .thenAnswer((_) async => Right(
              TeamsTestDataFactory.createTestPaginatedResponse(items: refreshedTeams),
            ));

        // Act
        await tester.pumpWidget(createTestApp());
        await tester.pump(); // Initial load

        expect(find.text('Initial 1'), findsOneWidget);

        // Pull to refresh
        await tester.fling(find.byType(ListView), const Offset(0, 300), 1000);
        await tester.pump();
        await tester.pump(const Duration(seconds: 1));

        // Assert
        expect(find.text('Refreshed 1'), findsOneWidget);
        expect(find.text('Initial 1'), findsNothing);
        
        verify(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).called(2); // Initial load + refresh
      });

      testWidgets('should display error state when API call fails', (tester) async {
        // Arrange
        when(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).thenAnswer((_) async => Left(AppError('Network error')));

        // Act
        await tester.pumpWidget(createTestApp());
        await tester.pump();

        // Assert
        expect(find.text('Failed to load teams'), findsOneWidget);
        expect(find.text('Retry'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });

      testWidgets('should retry loading after error', (tester) async {
        // Arrange
        final teams = TeamsTestDataFactory.createTestTeamSearchItems(count: 2);
        
        when(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        ))
            .thenAnswer((_) async => Left(AppError('Network error')))
            .thenAnswer((_) async => Right(
              TeamsTestDataFactory.createTestPaginatedResponse(items: teams),
            ));

        // Act
        await tester.pumpWidget(createTestApp());
        await tester.pump(); // Initial load fails

        expect(find.text('Retry'), findsOneWidget);

        await tester.tap(find.text('Retry'));
        await tester.pump(); // Retry succeeds

        // Assert
        expect(find.text('Team 1'), findsOneWidget);
        expect(find.text('Team 2'), findsOneWidget);
        expect(find.text('Retry'), findsNothing);
        
        verify(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).called(2);
      });

      testWidgets('should display empty state when no teams found', (tester) async {
        // Arrange
        when(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).thenAnswer((_) async => Right(
          TeamsTestDataFactory.createTestPaginatedResponse(
            items: [],
            total: 0,
          ),
        ));

        // Act
        await tester.pumpWidget(createTestApp());
        await tester.pump();

        // Assert
        expect(find.text('No teams available'), findsOneWidget);
        expect(find.byIcon(Icons.groups_outlined), findsOneWidget);
      });

      testWidgets('should handle navigation to team details', (tester) async {
        // Arrange
        final teams = TeamsTestDataFactory.createTestTeamSearchItems(count: 1);
        
        when(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).thenAnswer((_) async => Right(
          TeamsTestDataFactory.createTestPaginatedResponse(items: teams),
        ));

        // Act
        await tester.pumpWidget(createTestApp());
        await tester.pump();

        // Tap on team card
        await tester.tap(find.text('Team 1'));
        await tester.pump();

        // Assert - Navigation would be tested with integration test framework
        // For unit tests, we just verify the tap doesn't cause errors
        expect(find.text('Team 1'), findsOneWidget);
      });
    });

    group('Error Handling Integration', () {
      testWidgets('should handle malformed API response gracefully', (tester) async {
        // Arrange
        final malformedResponse = PaginatedResponse<TeamSearchItem>(
          message: 'Success',
          data: PaginatedData<TeamSearchItem>(
            items: [],
            total: -1, // Invalid
            page: 0, // Invalid
            pageSize: 0, // Invalid
            totalPages: -1, // Invalid
          ),
        );

        when(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).thenAnswer((_) async => Right(malformedResponse));

        // Act
        await tester.pumpWidget(createTestApp());
        await tester.pump();

        // Assert - Should handle gracefully without crashing
        expect(find.text('No teams available'), findsOneWidget);
      });

      testWidgets('should handle network timeout gracefully', (tester) async {
        // Arrange
        when(mockRepository.getAllTeamsPaginated(
          page: anyNamed('page'),
          pageSize: anyNamed('pageSize'),
        )).thenAnswer((_) async {
          await Future.delayed(const Duration(seconds: 2));
          return Left(AppError('Request timeout'));
        });

        // Act
        await tester.pumpWidget(createTestApp());
        await tester.pump();
        await tester.pump(const Duration(seconds: 3));

        // Assert
        expect(find.text('Failed to load teams'), findsOneWidget);
      });
    });
  });
}
