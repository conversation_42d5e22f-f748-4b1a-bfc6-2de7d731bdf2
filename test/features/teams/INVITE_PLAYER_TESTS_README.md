# Invite Player Feature - Test Suite Documentation

This document provides comprehensive documentation for the test suite covering the invite player functionality in the NextSportz application.

## Overview

The invite player feature allows team managers to search for and invite players to join their teams. The test suite provides comprehensive coverage across all layers of the application architecture following Clean Architecture principles.

## Test Coverage

### 🏗️ Domain Layer Tests

#### 1. Player Search Item Entity (`test/features/player/domain/entities/player_search_item_test.dart`)
- **Constructor validation**: Tests creation with required and optional fields
- **copyWith functionality**: Tests immutable updates and field modifications
- **Equality and hashCode**: Tests object comparison and hash consistency
- **toString representation**: Tests string output for debugging
- **Edge cases**: Tests with extreme values, special characters, and long strings

#### 2. Search Players Use Case (`test/features/player/domain/usecases/search_players_usecase_test.dart`)
- **Success scenarios**: Tests successful player search with various parameters
- **Error handling**: Tests repository failures and timeout scenarios
- **Parameter validation**: Tests default values and edge cases
- **SearchPlayersParams**: Tests parameter object creation, equality, and copyWith

#### 3. Invite Player Use Case (`test/features/teams/domain/usecases/invite_player_usecase_test.dart`)
- **Success scenarios**: Tests successful player invitations with different roles
- **Error handling**: Tests various failure scenarios (network, validation, duplicates)
- **Multiple invitations**: Tests sequential invitation handling
- **InvitePlayerParams**: Tests parameter object functionality

### 💾 Data Layer Tests

#### 1. Player Search Item DTO (`test/features/player/data/dto/player_search_item_dto_test.dart`)
- **JSON serialization**: Tests fromJson and toJson with complete and partial data
- **Entity conversion**: Tests toEntity and fromEntity transformations
- **Null handling**: Tests behavior with null and missing fields
- **Round-trip conversion**: Tests data integrity through multiple conversions
- **Complex data**: Tests handling of nested gameInfo objects

#### 2. Player Repository Implementation (`test/features/player/data/repositories/player_repository_impl_test.dart`)
- **Success scenarios**: Tests successful data retrieval and conversion
- **Error handling**: Tests DioExceptionHandle and generic exception handling
- **Edge cases**: Tests large datasets, special characters, and empty queries
- **Datasource integration**: Tests proper delegation to datasource layer

#### 3. Player Remote Datasource (`test/features/player/data/datasources/player_remote_datasource_test.dart`)
- **API integration**: Tests HTTP requests with proper parameters
- **Response handling**: Tests successful response parsing
- **Error scenarios**: Tests network errors, timeouts, and server errors
- **Edge cases**: Tests special characters and various query formats

### 🎨 Presentation Layer Tests

#### 1. Invite Player Screen (`test/features/teams/presentation/screens/invite_player_screen_test.dart`)
- **Initial state**: Tests screen initialization and empty state
- **Search functionality**: Tests search input, debouncing, and results display
- **Invitation flow**: Tests player invitation process and state updates
- **Error handling**: Tests error states and user feedback
- **Loading states**: Tests progress indicators and async operations
- **User interactions**: Tests button taps, text input, and navigation

### 🔗 Integration Tests

#### 1. Complete Flow Integration (`test/features/teams/integration/invite_player_integration_test.dart`)
- **End-to-end flow**: Tests complete user journey from search to invitation
- **Multiple scenarios**: Tests various search criteria and invitation outcomes
- **Error handling**: Tests graceful error handling in real scenarios
- **Performance**: Tests with large datasets and rapid interactions
- **Accessibility**: Tests screen reader support and keyboard navigation

## Test Utilities

### 1. Test Data Factory (`test/features/player/test_data_factory.dart`)
- **Entity creation**: Factory methods for creating test PlayerSearchItem entities
- **DTO creation**: Factory methods for creating test PlayerSearchItemDto objects
- **Paginated responses**: Factory methods for creating paginated API responses
- **Searchable data**: Pre-configured test data for search functionality testing
- **JSON generation**: Factory methods for creating test JSON data

### 2. Test Suite Runner (`test/features/teams/invite_player_test_suite.dart`)
- **Complete suite**: Runs all tests for the invite player feature
- **Layer-specific**: Allows running tests for specific architectural layers
- **Coverage reporting**: Supports test coverage analysis

## Running Tests

### Run All Tests
```bash
flutter test test/features/teams/invite_player_test_suite.dart
```

### Run Specific Layer Tests
```bash
# Domain layer only
flutter test test/features/player/domain/

# Data layer only
flutter test test/features/player/data/

# Presentation layer only
flutter test test/features/teams/presentation/
```

### Run Individual Test Files
```bash
# Entity tests
flutter test test/features/player/domain/entities/player_search_item_test.dart

# Use case tests
flutter test test/features/player/domain/usecases/search_players_usecase_test.dart

# Repository tests
flutter test test/features/player/data/repositories/player_repository_impl_test.dart

# Widget tests
flutter test test/features/teams/presentation/screens/invite_player_screen_test.dart
```

### Generate Coverage Report
```bash
flutter test --coverage test/features/teams/invite_player_test_suite.dart
genhtml coverage/lcov.info -o coverage/html
open coverage/html/index.html
```

## Test Statistics

- **Total test files**: 8
- **Total test cases**: 100+
- **Coverage target**: 80%+
- **Test types**: Unit, Widget, Integration
- **Mock objects**: Comprehensive mocking of dependencies

## Key Testing Patterns

### 1. Arrange-Act-Assert (AAA)
All tests follow the AAA pattern for clarity and consistency.

### 2. Mock Dependencies
Uses Mockito for mocking external dependencies and isolating units under test.

### 3. Test Data Factories
Centralized test data creation for consistency and maintainability.

### 4. Error Scenario Testing
Comprehensive testing of error conditions and edge cases.

### 5. Round-trip Testing
Data integrity validation through serialization/deserialization cycles.

## Dependencies

### Test Dependencies
- `flutter_test`: Flutter testing framework
- `mockito`: Mocking framework
- `integration_test`: Integration testing support
- `build_runner`: Code generation for mocks

### Production Dependencies Tested
- `dio`: HTTP client
- `flutter_riverpod`: State management
- `fpdart`: Functional programming utilities

## Maintenance

### Adding New Tests
1. Follow existing naming conventions
2. Use the test data factory for consistent test data
3. Include both success and error scenarios
4. Add integration tests for new user flows

### Updating Tests
1. Update tests when modifying business logic
2. Ensure mock objects reflect actual interfaces
3. Update test data factory when entities change
4. Maintain test coverage above 80%

## Best Practices

1. **Test Isolation**: Each test should be independent and not rely on other tests
2. **Clear Naming**: Test names should clearly describe what is being tested
3. **Comprehensive Coverage**: Test both happy path and edge cases
4. **Mock External Dependencies**: Use mocks for network calls, databases, etc.
5. **Readable Assertions**: Use descriptive matchers and error messages
6. **Performance Testing**: Include tests for performance-critical operations

## Troubleshooting

### Common Issues
1. **Mock Generation**: Run `dart run build_runner build` to generate mocks
2. **Import Errors**: Ensure all necessary imports are included
3. **Async Testing**: Use `await tester.pumpAndSettle()` for async operations
4. **Widget Testing**: Use appropriate finders and matchers for UI elements

### Debug Tips
1. Use `debugDumpApp()` to inspect widget tree during tests
2. Add print statements in test data factories for debugging
3. Use `expect` with descriptive messages for better error reporting
4. Run tests with `--verbose` flag for detailed output
