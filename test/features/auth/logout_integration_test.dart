import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/controller.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/auth_state.dart';
import 'package:nextsportz_v2/features/auth/data/repositories/auth_repository_provider.dart';
import 'package:nextsportz_v2/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:nextsportz_v2/core/local/token_storage.dart';
import 'package:nextsportz_v2/core/local/key_value_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Logout Integration Tests', () {
    late ProviderContainer container;

    setUp(() async {
      // Setup shared preferences for testing
      SharedPreferences.setMockInitialValues({});
      final sharedPrefs = await SharedPreferences.getInstance();

      container = ProviderContainer(
        overrides: [
          // Override with local datasource for testing
          authDatasourceProvider.overrideWithValue(AuthLocalDataSource()),
          // Override token storage with test implementation
          tokenStorageProvider.overrideWithValue(
            TokenStorageServiceImpl(KeyValueStorageServiceImpl(sharedPrefs)),
          ),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('should successfully logout and clear tokens', () async {
      final authNotifier = container.read(authNotifierProvider.notifier);
      final tokenStorage = container.read(tokenStorageProvider);

      // First, simulate a login by saving tokens
      await tokenStorage.saveTokens(
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
      );

      // Verify tokens are saved
      final tokensBefore = await tokenStorage.getTokens();
      expect(tokensBefore, isNotNull);
      expect(tokensBefore!['accessToken'], equals('test_access_token'));

      // Perform logout
      await authNotifier.logout();

      // Verify logout state
      final authState = container.read(authNotifierProvider);
      expect(authState.status, equals(AuthStatus.unauthenticated));

      // Verify tokens are cleared
      final tokensAfter = await tokenStorage.getTokens();
      expect(tokensAfter, isNull);
    });

    test('should handle logout with authentication flow', () async {
      final authNotifier = container.read(authNotifierProvider.notifier);

      // First login
      final loginSuccess = await authNotifier.loginWithPhone(
        phoneNumber: '**********',
        password: 'password123',
        role: 'PLAYER',
      );

      expect(loginSuccess, isTrue);

      // Verify authenticated state
      var authState = container.read(authNotifierProvider);
      expect(authState.status, equals(AuthStatus.authenticated));
      expect(authState.user, isNotNull);

      // Perform logout
      await authNotifier.logout();

      // Verify unauthenticated state
      authState = container.read(authNotifierProvider);
      expect(authState.status, equals(AuthStatus.unauthenticated));
      expect(authState.user, isNull);
    });

    test('should clear tokens even if logout API fails', () async {
      final authNotifier = container.read(authNotifierProvider.notifier);
      final tokenStorage = container.read(tokenStorageProvider);

      // Save tokens
      await tokenStorage.saveTokens(
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
      );

      // Verify tokens exist before logout
      final tokensBefore = await tokenStorage.getTokens();
      expect(tokensBefore, isNotNull);

      // Perform logout (local datasource will succeed, but this tests the logic)
      await authNotifier.logout();

      // Verify tokens are cleared regardless
      final tokensAfter = await tokenStorage.getTokens();
      expect(tokensAfter, isNull);
    });
  });
}
