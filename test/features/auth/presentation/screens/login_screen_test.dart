import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:nextsportz_v2/features/auth/presentation/screens/login_screen.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/controller.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/auth_state.dart';
import 'package:nextsportz_v2/features/auth/domain/entities/user.dart';
import 'package:nextsportz_v2/features/auth/domain/repositories/auth_repository.dart';
import 'package:nextsportz_v2/features/auth/data/repositories/auth_repository_provider.dart';
import 'package:nextsportz_v2/core/widgets/blaze_text_form_field.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/core/providers.dart';
import 'package:nextsportz_v2/core/local/key_value_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:fpdart/fpdart.dart';

import 'login_screen_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  setUpAll(() {
    // Provide dummy values for Either types
    provideDummy<Either<AppError, User?>>(const Right(null));
    provideDummy<Either<AppError, User>>(
      Right(
        User(
          id: 'test',
          name: 'Test User',
          email: '<EMAIL>',
          phoneNumber: '**********',
          role: 'PLAYER',
          createdAt: DateTime(2023),
          updatedAt: DateTime(2023),
          isActive: true,
        ),
      ),
    );
    provideDummy<Either<AppError, void>>(const Right(null));
  });

  group('LoginScreen', () {
    late MockAuthRepository mockAuthRepository;
    late SharedPreferences mockSharedPreferences;
    late KeyValueStorageService mockKeyValueStorage;

    setUp(() async {
      mockAuthRepository = MockAuthRepository();

      // Setup mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
      mockSharedPreferences = await SharedPreferences.getInstance();
      mockKeyValueStorage = KeyValueStorageServiceImpl(mockSharedPreferences);
    });

    Widget createTestWidget() {
      final router = GoRouter(
        routes: [
          GoRoute(path: '/', builder: (context, state) => const LoginScreen()),
          GoRoute(
            path: '/forgot-password',
            builder:
                (context, state) => const Scaffold(
                  body: Center(child: Text('Forgot Password Screen')),
                ),
          ),
          GoRoute(
            path: '/register',
            builder:
                (context, state) => const Scaffold(
                  body: Center(child: Text('Register Screen')),
                ),
          ),
          GoRoute(
            path: '/home',
            builder:
                (context, state) =>
                    const Scaffold(body: Center(child: Text('Home Screen'))),
          ),
        ],
      );

      return ProviderScope(
        overrides: [
          authRepositoryProvider.overrideWithValue(mockAuthRepository),
          sharedPrefsProvider.overrideWithValue(mockSharedPreferences),
          keyValueStorageProvider.overrideWithValue(mockKeyValueStorage),
        ],
        child: ScreenUtilInit(
          designSize: const Size(375, 812),
          builder: (context, child) => MaterialApp.router(routerConfig: router),
        ),
      );
    }

    testWidgets('should render login screen with all elements', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.getCurrentUser(),
      ).thenAnswer((_) async => const Right(null));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(LoginScreen), findsOneWidget);
      expect(
        find.byType(BlazeTextFormField),
        findsNWidgets(2),
      ); // Phone and password fields
      expect(find.text('Log In'), findsOneWidget); // Login button text
      expect(find.text('Forgot password?'), findsOneWidget);
      expect(find.text('Sign Up Here'), findsOneWidget);
    });

    testWidgets('should show loading state when logging in', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.getCurrentUser(),
      ).thenAnswer((_) async => const Right(null));
      when(
        mockAuthRepository.loginWithPhone(
          phoneNumber: anyNamed('phoneNumber'),
          password: anyNamed('password'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async {
        // Return immediately to avoid timer issues
        return Right(
          User(
            id: '1',
            name: 'Test User',
            email: '<EMAIL>',
            phoneNumber: '**********',
            role: 'PLAYER',
            createdAt: DateTime(2023),
            updatedAt: DateTime(2023),
            isActive: true,
          ),
        );
      });

      // Act
      await tester.pumpWidget(createTestWidget());

      // Enter valid data and submit
      await tester.enterText(
        find.byType(BlazeTextFormField).first,
        '**********',
      );
      await tester.enterText(
        find.byType(BlazeTextFormField).last,
        'password123',
      );
      await tester.tap(find.text('Log In'));
      await tester.pump(); // Trigger the login

      // Wait for the 500ms timer to complete
      await tester.pump(const Duration(milliseconds: 600));
      await tester.pumpAndSettle();

      // Assert - Check that login was called
      verify(
        mockAuthRepository.loginWithPhone(
          phoneNumber: '**********',
          password: 'password123',
          role: 'PLAYER',
        ),
      ).called(1);
    });

    testWidgets('should show error message when login fails', (
      WidgetTester tester,
    ) async {
      // Arrange
      const errorMessage = 'Invalid credentials';
      when(
        mockAuthRepository.getCurrentUser(),
      ).thenAnswer((_) async => const Right(null));
      when(
        mockAuthRepository.loginWithPhone(
          phoneNumber: anyNamed('phoneNumber'),
          password: anyNamed('password'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async => const Left(AppError(errorMessage)));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Enter valid data and submit
      await tester.enterText(
        find.byType(BlazeTextFormField).first,
        '**********',
      );
      await tester.enterText(
        find.byType(BlazeTextFormField).last,
        'password123',
      );
      await tester.tap(find.text('Log In'));
      await tester.pumpAndSettle();

      // Assert - Check for SnackBar with error message
      expect(find.text(errorMessage), findsOneWidget);
    });

    testWidgets('should call login when form is submitted with valid data', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.getCurrentUser(),
      ).thenAnswer((_) async => const Right(null));
      when(
        mockAuthRepository.loginWithPhone(
          phoneNumber: anyNamed('phoneNumber'),
          password: anyNamed('password'),
          role: anyNamed('role'),
        ),
      ).thenAnswer(
        (_) async => Right(
          User(
            id: '1',
            name: 'Test User',
            email: '<EMAIL>',
            phoneNumber: '**********',
            role: 'PLAYER',
            createdAt: DateTime(2023),
            updatedAt: DateTime(2023),
            isActive: true,
          ),
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());

      // Enter phone number
      await tester.enterText(
        find.byType(BlazeTextFormField).first,
        '**********',
      );

      // Enter password
      await tester.enterText(
        find.byType(BlazeTextFormField).last,
        'password123',
      );

      // Tap login button
      await tester.tap(find.text('Log In'));
      await tester.pump(); // Trigger the login

      // Wait for the 500ms timer to complete
      await tester.pump(const Duration(milliseconds: 600));
      await tester.pumpAndSettle();

      // Assert
      verify(
        mockAuthRepository.loginWithPhone(
          phoneNumber: '**********',
          password: 'password123',
          role: 'PLAYER',
        ),
      ).called(1);
    });

    testWidgets('should navigate to forgot password screen', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.getCurrentUser(),
      ).thenAnswer((_) async => const Right(null));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.tap(find.text('Forgot password?'));
      await tester.pumpAndSettle();

      // Assert
      // Should navigate to forgot password screen
      expect(find.text('Forgot Password Screen'), findsOneWidget);
    });

    testWidgets('should navigate to register screen', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.getCurrentUser(),
      ).thenAnswer((_) async => const Right(null));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.tap(find.text('Sign Up Here'));
      await tester.pumpAndSettle();

      // Assert
      // Should navigate to register screen
      expect(find.text('Register Screen'), findsOneWidget);
    });

    testWidgets('should show form validation errors', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.getCurrentUser(),
      ).thenAnswer((_) async => const Right(null));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Try to submit without entering data
      await tester.tap(find.text('Log In'));
      await tester.pump();

      // Assert
      // Should show validation errors
      expect(find.text('Please enter your phone number'), findsOneWidget);
      expect(find.text('Please enter your password'), findsOneWidget);
    });

    testWidgets('should handle phone number validation', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.getCurrentUser(),
      ).thenAnswer((_) async => const Right(null));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Enter invalid phone number
      await tester.enterText(find.byType(BlazeTextFormField).first, '123');

      await tester.tap(find.text('Log In'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter a valid phone number'), findsOneWidget);
    });

    testWidgets('should handle password validation', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.getCurrentUser(),
      ).thenAnswer((_) async => const Right(null));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Enter short password
      await tester.enterText(find.byType(BlazeTextFormField).last, '123');

      await tester.tap(find.text('Log In'));
      await tester.pump();

      // Assert
      expect(
        find.text('Password must be at least 6 characters'),
        findsOneWidget,
      );
    });

    testWidgets('should toggle password visibility', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.getCurrentUser(),
      ).thenAnswer((_) async => const Right(null));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Find password field and toggle visibility
      await tester.tap(find.byIcon(Icons.remove_red_eye_outlined));
      await tester.pump();

      // Assert
      expect(find.byIcon(Icons.remove_red_eye), findsOneWidget);
    });

    testWidgets('should show login screen even when user is authenticated', (
      WidgetTester tester,
    ) async {
      // Arrange
      final user = User(
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        phoneNumber: '**********',
        role: 'PLAYER',
        createdAt: DateTime(2023),
        updatedAt: DateTime(2023),
        isActive: true,
      );
      when(
        mockAuthRepository.getCurrentUser(),
      ).thenAnswer((_) async => Right(user));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      // Login screen should still be shown (no automatic redirect on load)
      expect(find.byType(LoginScreen), findsOneWidget);
      expect(find.text('Log In'), findsOneWidget);
    });

    testWidgets('should handle keyboard input correctly', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(
        mockAuthRepository.getCurrentUser(),
      ).thenAnswer((_) async => const Right(null));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Test phone number input
      await tester.enterText(
        find.byType(BlazeTextFormField).first,
        '**********',
      );
      await tester.pump();

      // Test password input
      await tester.enterText(
        find.byType(BlazeTextFormField).last,
        'password123',
      );
      await tester.pump();

      // Assert
      expect(find.text('**********'), findsOneWidget);
      expect(find.text('password123'), findsOneWidget);
    });
  });
}
