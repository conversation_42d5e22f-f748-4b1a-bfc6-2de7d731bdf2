import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import '../../../../../lib/features/auth/presentation/screens/forgot_password_screen.dart';
import '../../../../../lib/features/auth/presentation/logic/controller.dart';
import '../../../../../lib/features/auth/presentation/logic/auth_state.dart';
import '../../../../../lib/core/widgets/blaze_text_form_field.dart';
import '../../../../test_helpers.dart';

import 'forgot_password_screen_test.mocks.dart';

@GenerateMocks([AuthNotifier])
void main() {
  group('ForgotPasswordScreen Widget Tests', () {
    late MockAuthNotifier mockAuthNotifier;

    setUp(() {
      mockAuthNotifier = MockAuthNotifier();
    });

    Widget createTestWidget({AuthState? authState}) {
      return TestHelpers.createTestWidget(
        overrides: [
          authNotifierProvider.overrideWith((ref) => mockAuthNotifier),
        ],
        child: const ForgotPasswordScreen(),
      );
    }

    group('UI Elements', () {
      testWidgets('displays correct app bar title', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Forgot password'), findsOneWidget);
      });

      testWidgets('displays back button in app bar', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byIcon(Icons.arrow_back), findsOneWidget);
      });

      testWidgets('displays email text field', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(BlazeTextFormField), findsOneWidget);
        expect(find.byIcon(Icons.email_outlined), findsOneWidget);
      });

      testWidgets('displays send reset link button', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Send reset link'), findsOneWidget);
        expect(find.byType(FilledButton), findsOneWidget);
      });

      testWidgets('email field has correct hint text', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        final textField = find.byType(BlazeTextFormField);
        final textFieldWidget = tester.widget<BlazeTextFormField>(textField);
        expect(textFieldWidget.hintText, 'Email');
      });
    });

    group('Form Validation', () {
      testWidgets('shows error for empty email', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());
        when(mockAuthNotifier.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Enter email'), findsOneWidget);
      });

      testWidgets('shows error for invalid email format', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.enterText(find.byType(TextFormField), 'invalid-email');
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Enter valid email'), findsOneWidget);
      });

      testWidgets('accepts valid email format', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());
        when(mockAuthNotifier.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async => true);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.enterText(find.byType(TextFormField), '<EMAIL>');
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Enter valid email'), findsNothing);
        verify(mockAuthNotifier.forgotPassword(email: '<EMAIL>'))
            .called(1);
      });

      testWidgets('enables auto validation after first submission',
          (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());

        // Act
        await tester.pumpWidget(createTestWidget());

        // First submission with invalid email
        await tester.enterText(find.byType(TextFormField), 'invalid');
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Clear and enter valid email
        await tester.enterText(find.byType(TextFormField), '');
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Enter email'), findsOneWidget);
      });
    });

    group('Submit Functionality', () {
      testWidgets('calls forgotPassword with correct email', (tester) async {
        // Arrange
        const email = '<EMAIL>';
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());
        when(mockAuthNotifier.forgotPassword(email: email))
            .thenAnswer((_) async => true);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.enterText(find.byType(TextFormField), email);
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        verify(mockAuthNotifier.forgotPassword(email: email)).called(1);
      });

      testWidgets('shows loading indicator during submission', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());
        when(mockAuthNotifier.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return true;
        });

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.enterText(find.byType(TextFormField), '<EMAIL>');
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Assert
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Send reset link'), findsNothing);

        // Wait for completion
        await tester.pumpAndSettle();
      });

      testWidgets('disables button during submission', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());
        when(mockAuthNotifier.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return true;
        });

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.enterText(find.byType(TextFormField), '<EMAIL>');
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Try to tap button again
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Assert - should only be called once
        await tester.pumpAndSettle();
        verify(mockAuthNotifier.forgotPassword(email: anyNamed('email')))
            .called(1);
      });

      testWidgets('shows success snackbar and navigates back on success',
          (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());
        when(mockAuthNotifier.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async => true);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.enterText(find.byType(TextFormField), '<EMAIL>');
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Password reset link sent'), findsOneWidget);
        expect(find.byType(SnackBar), findsOneWidget);
      });

      testWidgets('does not submit invalid form', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.enterText(find.byType(TextFormField), 'invalid-email');
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        verifyNever(mockAuthNotifier.forgotPassword(email: anyNamed('email')));
      });
    });

    group('Error Handling', () {
      testWidgets('shows error snackbar when forgotPassword fails',
          (tester) async {
        // Arrange
        const errorMessage = 'Email not found';
        when(mockAuthNotifier.state)
            .thenReturn(AuthState.initial())
            .thenReturn(AuthState.error(errorMessage));
        when(mockAuthNotifier.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(createTestWidget(
          authState: AuthState.error(errorMessage),
        ));
        await tester.enterText(find.byType(TextFormField), '<EMAIL>');
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text(errorMessage), findsOneWidget);
        expect(find.byType(SnackBar), findsOneWidget);
      });

      testWidgets('shows default error message when error message is null',
          (tester) async {
        // Arrange
        when(mockAuthNotifier.state)
            .thenReturn(AuthState.initial())
            .thenReturn(AuthState.error(null));
        when(mockAuthNotifier.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(createTestWidget(
          authState: AuthState.error(null),
        ));
        await tester.enterText(find.byType(TextFormField), '<EMAIL>');
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Failed to send reset email'), findsOneWidget);
      });

      testWidgets('handles state changes during error scenarios',
          (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.loading());
        when(mockAuthNotifier.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert - loading state should disable button
        final button = tester.widget<FilledButton>(find.byType(FilledButton));
        expect(button.onPressed, null);
      });
    });

    group('Navigation', () {
      testWidgets('navigates back when back button is pressed', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());

        // Act
        await tester.pumpWidget(TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith((ref) => mockAuthNotifier),
          ],
          child: Navigator(
            onGenerateRoute: (settings) {
              if (settings.name == '/') {
                return MaterialPageRoute(
                  builder: (_) => const Scaffold(body: Text('Home')),
                );
              } else if (settings.name == '/forgot-password') {
                return MaterialPageRoute(
                  builder: (_) => const ForgotPasswordScreen(),
                );
              }
              return null;
            },
            initialRoute: '/forgot-password',
          ),
        ));

        await tester.tap(find.byIcon(Icons.arrow_back));
        await tester.pumpAndSettle();

        // Assert - should navigate back
        expect(find.text('Home'), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('has proper semantic labels', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
        expect(find.byType(FilledButton), findsOneWidget);
      });

      testWidgets('supports keyboard navigation', (tester) async {
        // Arrange
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());
        when(mockAuthNotifier.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async => true);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.enterText(find.byType(TextFormField), '<EMAIL>');
        await tester.testTextInput.receiveAction(TextInputAction.done);
        await tester.pumpAndSettle();

        // Assert - form should be submitted
        verify(mockAuthNotifier.forgotPassword(email: '<EMAIL>'))
            .called(1);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles very long email addresses', (tester) async {
        // Arrange
        final longEmail = '${'a' * 100}@example.com';
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());
        when(mockAuthNotifier.forgotPassword(email: longEmail))
            .thenAnswer((_) async => true);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.enterText(find.byType(TextFormField), longEmail);
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        verify(mockAuthNotifier.forgotPassword(email: longEmail)).called(1);
      });

      testWidgets('handles special characters in email', (tester) async {
        // Arrange
        const specialEmail = '<EMAIL>';
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());
        when(mockAuthNotifier.forgotPassword(email: specialEmail))
            .thenAnswer((_) async => true);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.enterText(find.byType(TextFormField), specialEmail);
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        verify(mockAuthNotifier.forgotPassword(email: specialEmail)).called(1);
      });

      testWidgets('trims whitespace from email input', (tester) async {
        // Arrange
        const emailWithSpaces = '  <EMAIL>  ';
        const trimmedEmail = '<EMAIL>';
        when(mockAuthNotifier.state).thenReturn(AuthState.initial());
        when(mockAuthNotifier.forgotPassword(email: trimmedEmail))
            .thenAnswer((_) async => true);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.enterText(find.byType(TextFormField), emailWithSpaces);
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        verify(mockAuthNotifier.forgotPassword(email: trimmedEmail)).called(1);
      });
    });
  });
}
