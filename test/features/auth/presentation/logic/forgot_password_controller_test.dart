import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/controller.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/auth_state.dart';
import 'package:nextsportz_v2/features/auth/domain/repositories/auth_repository.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';

import 'forgot_password_controller_test.mocks.dart';

// Provide dummy values for Mockito
@GenerateMocks([AuthRepository])
void main() {
  provideDummy<Either<AppError, void>>(const Right(null));
  group('AuthNotifier ForgotPassword Tests', () {
    late AuthNotifier authNotifier;
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      authNotifier = AuthNotifier(mockAuthRepository);
    });

    group('forgotPassword method', () {
      test('returns true when repository succeeds', () async {
        // Arrange
        const email = '<EMAIL>';
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(result, true);
        expect(authNotifier.state.status, AuthStatus.unauthenticated);
        verify(mockAuthRepository.forgotPassword(email: email)).called(1);
      });

      test('returns false when repository fails', () async {
        // Arrange
        const email = '<EMAIL>';
        const errorMessage = 'Email not found';
        final appError = AppError(errorMessage);
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => Left(appError));

        // Act
        final result = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(result, false);
        expect(authNotifier.state.status, AuthStatus.error);
        expect(authNotifier.state.errorMessage, errorMessage);
        verify(mockAuthRepository.forgotPassword(email: email)).called(1);
      });

      test('sets loading state during operation', () async {
        // Arrange
        const email = '<EMAIL>';
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return const Right(null);
        });

        // Act
        final future = authNotifier.forgotPassword(email: email);

        // Assert loading state
        expect(authNotifier.state.status, AuthStatus.loading);

        // Wait for completion
        final result = await future;
        expect(result, true);
        expect(authNotifier.state.status, AuthStatus.unauthenticated);
      });

      test('calls repository with correct email parameter', () async {
        // Arrange
        const email = '<EMAIL>';
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => const Right(null));

        // Act
        await authNotifier.forgotPassword(email: email);

        // Assert
        verify(mockAuthRepository.forgotPassword(email: email)).called(1);
        verifyNoMoreInteractions(mockAuthRepository);
      });

      test('handles different email formats correctly', () async {
        // Arrange
        final testEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        when(mockAuthRepository.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async => const Right(null));

        // Act & Assert
        for (final email in testEmails) {
          final result = await authNotifier.forgotPassword(email: email);
          expect(result, true);
          verify(mockAuthRepository.forgotPassword(email: email)).called(1);
        }
      });

      test('preserves error message from repository', () async {
        // Arrange
        const email = '<EMAIL>';
        const specificError = 'User account is disabled';
        final appError = AppError(specificError);
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => Left(appError));

        // Act
        final result = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(result, false);
        expect(authNotifier.state.errorMessage, specificError);
      });

      test('state transitions correctly during successful operation', () async {
        // Arrange
        const email = '<EMAIL>';
        final states = <AuthState>[];
        authNotifier.addListener((state) => states.add(state));
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
          return const Right(null);
        });

        // Track state changes

        // Act
        await authNotifier.forgotPassword(email: email);

        // Assert
        expect(states.length, greaterThanOrEqualTo(2));
        expect(states.first.status, AuthStatus.initial);
        expect(states[1].status, AuthStatus.loading);
        expect(states.last.status, AuthStatus.unauthenticated);
      });

      test('state transitions correctly during failed operation', () async {
        // Arrange
        const email = '<EMAIL>';
        const errorMessage = 'Network error';
        final states = <AuthState>[];
        final appError = AppError(errorMessage);
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
          return Left(appError);
        });

        // Track state changes
        authNotifier.addListener((state) => states.add(state));

        // Act
        await authNotifier.forgotPassword(email: email);

        // Assert
        expect(states.length, greaterThanOrEqualTo(2));
        expect(states.first.status, AuthStatus.initial);
        expect(states[1].status, AuthStatus.loading);
        expect(states.last.status, AuthStatus.error);
        expect(states.last.errorMessage, errorMessage);
      });
    });

    group('Error Scenarios', () {
      test('handles invalid email format errors', () async {
        // Arrange
        const email = 'invalid-email';
        const errorMessage = 'Invalid email format';
        final appError = AppError(errorMessage);
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => Left(appError));

        // Act
        final result = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(result, false);
        expect(authNotifier.state.status, AuthStatus.error);
        expect(authNotifier.state.errorMessage, errorMessage);
      });

      test('handles user not found errors', () async {
        // Arrange
        const email = '<EMAIL>';
        const errorMessage = 'User not found';
        final appError = AppError(errorMessage);
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => Left(appError));

        // Act
        final result = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(result, false);
        expect(authNotifier.state.errorMessage, errorMessage);
      });

      test('handles network errors', () async {
        // Arrange
        const email = '<EMAIL>';
        const errorMessage = 'Network connection failed';
        final appError = AppError(errorMessage);
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => Left(appError));

        // Act
        final result = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(result, false);
        expect(authNotifier.state.errorMessage, errorMessage);
      });

      test('handles server errors', () async {
        // Arrange
        const email = '<EMAIL>';
        const errorMessage = 'Internal server error';
        final appError = AppError(errorMessage);
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => Left(appError));

        // Act
        final result = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(result, false);
        expect(authNotifier.state.errorMessage, errorMessage);
      });

      test('handles rate limiting errors', () async {
        // Arrange
        const email = '<EMAIL>';
        const errorMessage = 'Too many requests. Please try again later.';
        final appError = AppError(errorMessage);
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => Left(appError));

        // Act
        final result = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(result, false);
        expect(authNotifier.state.errorMessage, errorMessage);
      });
    });

    group('Edge Cases', () {
      test('handles empty email', () async {
        // Arrange
        const email = '';
        const errorMessage = 'Email is required';
        final appError = AppError(errorMessage);
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => Left(appError));

        // Act
        final result = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(result, false);
        expect(authNotifier.state.errorMessage, errorMessage);
      });

      test('handles email with only whitespace', () async {
        // Arrange
        const email = '   ';
        const errorMessage = 'Invalid email format';
        final appError = AppError(errorMessage);
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => Left(appError));

        // Act
        final result = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(result, false);
        expect(authNotifier.state.errorMessage, errorMessage);
      });

      test('handles very long email addresses', () async {
        // Arrange
        final longEmail = '${'a' * 250}@example.com';
        when(mockAuthRepository.forgotPassword(email: longEmail))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await authNotifier.forgotPassword(email: longEmail);

        // Assert
        expect(result, true);
        verify(mockAuthRepository.forgotPassword(email: longEmail)).called(1);
      });

      test('handles unicode characters in email', () async {
        // Arrange
        const unicodeEmail = 'tëst@ëxämplë.com';
        when(mockAuthRepository.forgotPassword(email: unicodeEmail))
            .thenAnswer((_) async => const Right(null));

        // Act
        final result = await authNotifier.forgotPassword(email: unicodeEmail);

        // Assert
        expect(result, true);
        verify(mockAuthRepository.forgotPassword(email: unicodeEmail))
            .called(1);
      });

      test('handles case sensitivity correctly', () async {
        // Arrange
        const lowerEmail = '<EMAIL>';
        const upperEmail = '<EMAIL>';
        const mixedEmail = '<EMAIL>';

        when(mockAuthRepository.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async => const Right(null));

        // Act & Assert
        final results = [
          await authNotifier.forgotPassword(email: lowerEmail),
          await authNotifier.forgotPassword(email: upperEmail),
          await authNotifier.forgotPassword(email: mixedEmail),
        ];

        for (final result in results) {
          expect(result, true);
        }
        verify(mockAuthRepository.forgotPassword(email: lowerEmail)).called(1);
        verify(mockAuthRepository.forgotPassword(email: upperEmail)).called(1);
        verify(mockAuthRepository.forgotPassword(email: mixedEmail)).called(1);
      });
    });

    group('Performance Tests', () {
      test('handles multiple sequential forgot password requests', () async {
        // Arrange
        const emails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];
        when(mockAuthRepository.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async => const Right(null));

        // Act
        for (final email in emails) {
          final result = await authNotifier.forgotPassword(email: email);
          expect(result, true);
        }

        // Assert
        for (final email in emails) {
          verify(mockAuthRepository.forgotPassword(email: email)).called(1);
        }
      });

      test('handles concurrent forgot password requests', () async {
        // Note: This test is more theoretical since the notifier state would be
        // overwritten, but it tests that the repository is called correctly
        // Arrange
        const emails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];
        when(mockAuthRepository.forgotPassword(email: anyNamed('email')))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
          return const Right(null);
        });

        // Act
        final futures = emails.map(
          (email) => authNotifier.forgotPassword(email: email),
        );
        final results = await Future.wait(futures);

        // Assert
        for (final result in results) {
          expect(result, true);
        }
        // Note: Due to the nature of StateNotifier, only the last call's state would remain
      });

      test('handles repository timeout gracefully', () async {
        // Arrange
        const email = '<EMAIL>';
        const errorMessage = 'Request timeout';
        final appError = AppError(errorMessage);
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(seconds: 2));
          return Left(appError);
        });

        // Act
        final result = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(result, false);
        expect(authNotifier.state.errorMessage, errorMessage);
      });
    });

    group('State Management', () {
      test('resets to unauthenticated state after successful operation',
          () async {
        // Arrange
        const email = '<EMAIL>';
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => const Right(null));

        // Act
        await authNotifier.forgotPassword(email: email);

        // Assert
        expect(authNotifier.state.status, AuthStatus.unauthenticated);
        expect(authNotifier.state.errorMessage, null);
        expect(authNotifier.state.user, null);
      });

      test('maintains error state after failed operation', () async {
        // Arrange
        const email = '<EMAIL>';
        const errorMessage = 'Operation failed';
        final appError = AppError(errorMessage);
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => Left(appError));

        // Act
        await authNotifier.forgotPassword(email: email);

        // Assert
        expect(authNotifier.state.status, AuthStatus.error);
        expect(authNotifier.state.errorMessage, errorMessage);
      });

      test('can perform forgot password after previous error', () async {
        // Arrange
        const email = '<EMAIL>';
        final appError = AppError('First error');
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => Left(appError));

        // Act - First call fails
        final firstResult = await authNotifier.forgotPassword(email: email);
        expect(firstResult, false);
        expect(authNotifier.state.status, AuthStatus.error);

        // Setup for second call
        when(mockAuthRepository.forgotPassword(email: email))
            .thenAnswer((_) async => const Right(null));

        // Act - Second call succeeds
        final secondResult = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(secondResult, true);
        expect(authNotifier.state.status, AuthStatus.unauthenticated);
        expect(authNotifier.state.errorMessage, null);
      });
    });
  });
}
