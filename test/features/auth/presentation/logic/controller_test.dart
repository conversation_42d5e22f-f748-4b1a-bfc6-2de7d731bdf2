import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/controller.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/auth_state.dart';
import 'package:nextsportz_v2/features/auth/domain/repositories/auth_repository.dart';
import 'package:nextsportz_v2/features/auth/domain/entities/user.dart';

import 'controller_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  // Provide dummy values for Either types to prevent MissingDummyValueError
  provideDummy<Either<AppError, User>>(Left(AppError('dummy error')));
  provideDummy<Either<AppError, User?>>(Left(AppError('dummy error')));
  provideDummy<Either<AppError, void>>(Left(AppError('dummy error')));
  provideDummy<Either<AppError, bool>>(Left(AppError('dummy error')));
  group('AuthNotifier', () {
    late MockAuthRepository mockAuthRepository;
    late AuthNotifier authNotifier;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      authNotifier = AuthNotifier(mockAuthRepository);
    });

    test('should initialize with initial state', () {
      expect(authNotifier.state.status, equals(AuthStatus.initial));
      expect(authNotifier.state.user, isNull);
      expect(authNotifier.state.errorMessage, isNull);
    });

    group('loginWithPhone', () {
      test(
        'should login successfully and update state to authenticated',
        () async {
          // Arrange
          const phoneNumber = '1234567890';
          const password = 'password123';
          const role = 'PLAYER';

          final user = User(
            id: '1',
            name: 'Test User',
            email: '<EMAIL>',
            phoneNumber: phoneNumber,
            role: role,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            isActive: true,
          );

          when(
            mockAuthRepository.loginWithPhone(
              phoneNumber: phoneNumber,
              password: password,
              role: role,
            ),
          ).thenAnswer((_) async => Right(user));

          // Act
          final result = await authNotifier.loginWithPhone(
            phoneNumber: phoneNumber,
            password: password,
            role: role,
          );

          // Assert
          expect(result, isTrue);
          expect(authNotifier.state.status, equals(AuthStatus.authenticated));
          expect(authNotifier.state.user, equals(user));
          expect(authNotifier.state.errorMessage, isNull);

          verify(
            mockAuthRepository.loginWithPhone(
              phoneNumber: phoneNumber,
              password: password,
              role: role,
            ),
          ).called(1);
        },
      );

      test('should handle login failure and update state to error', () async {
        // Arrange
        const phoneNumber = '1234567890';
        const password = 'wrong_password';
        const role = 'PLAYER';
        const errorMessage = 'Invalid credentials';

        when(
          mockAuthRepository.loginWithPhone(
            phoneNumber: phoneNumber,
            password: password,
            role: role,
          ),
        ).thenAnswer((_) async => Left(AppError(errorMessage)));

        // Act
        final result = await authNotifier.loginWithPhone(
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        // Assert
        expect(result, isFalse);
        expect(authNotifier.state.status, equals(AuthStatus.error));
        expect(authNotifier.state.user, isNull);
        expect(authNotifier.state.errorMessage, equals(errorMessage));
      });

      test('should set loading state during login', () async {
        // Arrange
        const phoneNumber = '1234567890';
        const password = 'password123';
        const role = 'PLAYER';

        final user = User(
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          phoneNumber: phoneNumber,
          role: role,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
        );

        when(
          mockAuthRepository.loginWithPhone(
            phoneNumber: phoneNumber,
            password: password,
            role: role,
          ),
        ).thenAnswer((_) async {
          // Simulate delay to check loading state
          await Future.delayed(const Duration(milliseconds: 100));
          return Right(user);
        });

        // Act
        final future = authNotifier.loginWithPhone(
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        // Assert loading state
        expect(authNotifier.state.status, equals(AuthStatus.loading));

        // Wait for completion
        await future;
      });
    });

    group('logout', () {
      test(
        'should logout successfully and update state to unauthenticated',
        () async {
          // Arrange
          when(
            mockAuthRepository.logout(),
          ).thenAnswer((_) async => const Right(true));

          // Act
          await authNotifier.logout();

          // Assert
          expect(authNotifier.state.status, equals(AuthStatus.unauthenticated));
          expect(authNotifier.state.user, isNull);
          expect(authNotifier.state.errorMessage, isNull);

          verify(mockAuthRepository.logout()).called(1);
        },
      );

      test('should handle logout failure and update state to error', () async {
        // Arrange
        const errorMessage = 'Logout failed';

        when(
          mockAuthRepository.logout(),
        ).thenAnswer((_) async => Left(AppError(errorMessage)));

        // Act
        await authNotifier.logout();

        // Assert
        expect(authNotifier.state.status, equals(AuthStatus.error));
        expect(authNotifier.state.errorMessage, equals(errorMessage));
      });
    });

    group('checkAuthStatus', () {
      test('should update state to authenticated when user exists', () async {
        // Arrange
        final user = User(
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          phoneNumber: '1234567890',
          role: 'PLAYER',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
        );

        when(
          mockAuthRepository.getCurrentUser(),
        ).thenAnswer((_) async => Right(user));

        // Act
        await authNotifier.checkAuthStatus();

        // Assert
        expect(authNotifier.state.status, equals(AuthStatus.authenticated));
        expect(authNotifier.state.user, equals(user));
        expect(authNotifier.state.errorMessage, isNull);

        verify(mockAuthRepository.getCurrentUser()).called(1);
      });

      test(
        'should update state to unauthenticated when user does not exist',
        () async {
          // Arrange
          when(
            mockAuthRepository.getCurrentUser(),
          ).thenAnswer((_) async => const Right(null));

          // Act
          await authNotifier.checkAuthStatus();

          // Assert
          expect(authNotifier.state.status, equals(AuthStatus.unauthenticated));
          expect(authNotifier.state.user, isNull);
          expect(authNotifier.state.errorMessage, isNull);
        },
      );

      test('should handle error and update state to error', () async {
        // Arrange
        const errorMessage = 'Failed to get current user';

        when(
          mockAuthRepository.getCurrentUser(),
        ).thenAnswer((_) async => Left(AppError(errorMessage)));

        // Act
        await authNotifier.checkAuthStatus();

        // Assert
        expect(authNotifier.state.status, equals(AuthStatus.error));
        expect(authNotifier.state.errorMessage, equals(errorMessage));
      });
    });

    group('checkTokenStatus', () {
      test('should check auth status when tokens exist', () async {
        // Arrange
        final user = User(
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          phoneNumber: '1234567890',
          role: 'PLAYER',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
        );

        when(
          mockAuthRepository.isAuthenticated(),
        ).thenAnswer((_) async => const Right(true));
        when(
          mockAuthRepository.getCurrentUser(),
        ).thenAnswer((_) async => Right(user));

        // Act
        await authNotifier.checkTokenStatus();

        // Assert
        expect(authNotifier.state.status, equals(AuthStatus.authenticated));
        expect(authNotifier.state.user, equals(user));

        verify(mockAuthRepository.isAuthenticated()).called(1);
        verify(mockAuthRepository.getCurrentUser()).called(1);
      });

      test(
        'should update state to unauthenticated when no tokens exist',
        () async {
          // Arrange
          when(
            mockAuthRepository.isAuthenticated(),
          ).thenAnswer((_) async => const Right(false));

          // Act
          await authNotifier.checkTokenStatus();

          // Assert
          expect(authNotifier.state.status, equals(AuthStatus.unauthenticated));
          expect(authNotifier.state.user, isNull);

          verify(mockAuthRepository.isAuthenticated()).called(1);
          verifyNever(mockAuthRepository.getCurrentUser());
        },
      );

      test('should handle error and update state to error', () async {
        // Arrange
        const errorMessage = 'Failed to check authentication';

        when(
          mockAuthRepository.isAuthenticated(),
        ).thenAnswer((_) async => Left(AppError(errorMessage)));

        // Act
        await authNotifier.checkTokenStatus();

        // Assert
        expect(authNotifier.state.status, equals(AuthStatus.error));
        expect(authNotifier.state.errorMessage, equals(errorMessage));
      });
    });

    group('register', () {
      test(
        'should register successfully and update state to unauthenticated',
        () async {
          // Arrange
          const name = 'Test User';
          const email = '<EMAIL>';
          const phoneNumber = '1234567890';
          const password = 'password123';
          const role = 'PLAYER';

          when(
            mockAuthRepository.register(
              name: name,
              email: email,
              phoneNumber: phoneNumber,
              password: password,
              role: role,
            ),
          ).thenAnswer((_) async => const Right(()));

          // Act
          final result = await authNotifier.register(
            name: name,
            email: email,
            phoneNumber: phoneNumber,
            password: password,
            role: role,
          );

          // Assert
          expect(result, isTrue);
          expect(authNotifier.state.status, equals(AuthStatus.unauthenticated));
          expect(authNotifier.state.user,
              isNull); // User not set until OTP verification

          verify(
            mockAuthRepository.register(
              name: name,
              email: email,
              phoneNumber: phoneNumber,
              password: password,
              role: role,
            ),
          ).called(1);
        },
      );

      test(
        'should handle registration failure and update state to error',
        () async {
          // Arrange
          const name = 'Test User';
          const email = '<EMAIL>';
          const phoneNumber = '1234567890';
          const password = 'password123';
          const role = 'PLAYER';
          const errorMessage = 'Registration failed';

          when(
            mockAuthRepository.register(
              name: name,
              email: email,
              phoneNumber: phoneNumber,
              password: password,
              role: role,
            ),
          ).thenAnswer((_) async => Left(AppError(errorMessage)));

          // Act
          final result = await authNotifier.register(
            name: name,
            email: email,
            phoneNumber: phoneNumber,
            password: password,
            role: role,
          );

          // Assert
          expect(result, isFalse);
          expect(authNotifier.state.status, equals(AuthStatus.error));
          expect(authNotifier.state.errorMessage, equals(errorMessage));
        },
      );
    });

    group('forgotPassword', () {
      test('should send forgot password email successfully', () async {
        // Arrange
        const email = '<EMAIL>';

        when(
          mockAuthRepository.forgotPassword(email: email),
        ).thenAnswer((_) async => const Right(true));

        // Act
        final result = await authNotifier.forgotPassword(email: email);

        // Assert
        expect(result, isTrue);
        expect(authNotifier.state.status, equals(AuthStatus.unauthenticated));
        expect(authNotifier.state.errorMessage, isNull);

        verify(mockAuthRepository.forgotPassword(email: email)).called(1);
      });

      test(
        'should handle forgot password failure and update state to error',
        () async {
          // Arrange
          const email = '<EMAIL>';
          const errorMessage = 'Failed to send reset email';

          when(
            mockAuthRepository.forgotPassword(email: email),
          ).thenAnswer((_) async => Left(AppError(errorMessage)));

          // Act
          final result = await authNotifier.forgotPassword(email: email);

          // Assert
          expect(result, isFalse);
          expect(authNotifier.state.status, equals(AuthStatus.error));
          expect(authNotifier.state.errorMessage, equals(errorMessage));
        },
      );
    });

    group('clearError', () {
      test(
        'should clear error and set state to unauthenticated when in error state',
        () {
          // Arrange
          authNotifier.state = AuthState.error('Some error');

          // Act
          authNotifier.clearError();

          // Assert
          expect(authNotifier.state.status, equals(AuthStatus.unauthenticated));
          expect(authNotifier.state.errorMessage, isNull);
        },
      );

      test('should not change state when not in error state', () {
        // Arrange
        final user = User(
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          phoneNumber: '1234567890',
          role: 'PLAYER',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
        );
        authNotifier.state = AuthState.authenticated(user);

        // Act
        authNotifier.clearError();

        // Assert
        expect(authNotifier.state.status, equals(AuthStatus.authenticated));
        expect(authNotifier.state.user, equals(user));
      });
    });
  });
}
