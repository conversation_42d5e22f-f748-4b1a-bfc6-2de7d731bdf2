import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/controller.dart';
import 'package:nextsportz_v2/features/auth/data/dto/auth_response_models.dart';

import '../data/repositories/auth_repository_impl_test.mocks.dart';

void main() {
  group('Registration Flow Integration Test', () {
    late AuthNotifier authNotifier;
    late MockAuthDatasource mockAuthDatasource;

    setUp(() {
      mockAuthDatasource = MockAuthDatasource();
      // We'll need to create the repository and notifier properly
    });

    test('should handle real API response format correctly', () async {
      // This test simulates the actual API response format you're getting
      final apiResponse = {
        "message": "Success",
        "data": "Verify OTP to continue.",
        "error": null,
        "validationErrors": null
      };

      // Convert to SuccessResponse
      final successResponse = SuccessResponse.fromJson(apiResponse);

      // Verify the response is parsed correctly
      expect(successResponse.message, equals('Success'));
      expect(successResponse.data, equals('Verify OTP to continue.'));
      expect(successResponse.error, isNull);
      expect(successResponse.success, isNull); // This should be null since API doesn't provide it

      // Test the new logic
      // Should be successful because error is null and message contains "success"
      final hasError = successResponse.error != null;
      final messageIndicatesSuccess = successResponse.message?.toLowerCase().contains('success') == true;

      expect(hasError, isFalse);
      expect(messageIndicatesSuccess, isTrue);

      print('✅ API response parsing test passed');
      print('📝 Message: ${successResponse.message}');
      print('📦 Data: ${successResponse.data}');
      print('❌ Error: ${successResponse.error}');
      print('🔍 Success field: ${successResponse.success}');
    });

    test('should handle error response format correctly', () async {
      // This test simulates an error API response
      final apiResponse = {
        "message": "Registration failed",
        "data": null,
        "error": "Invalid phone number format",
        "validationErrors": null
      };

      // Convert to SuccessResponse
      final successResponse = SuccessResponse.fromJson(apiResponse);

      // Test the new logic
      final hasError = successResponse.error != null;
      final messageIndicatesSuccess = successResponse.message?.toLowerCase().contains('success') == true;

      expect(hasError, isTrue);
      expect(messageIndicatesSuccess, isFalse);

      print('✅ Error response parsing test passed');
      print('📝 Message: ${successResponse.message}');
      print('❌ Error: ${successResponse.error}');
    });
  });
}
