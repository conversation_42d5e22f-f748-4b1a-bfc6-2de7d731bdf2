import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/utils/color.dart';

void main() {
  group('Color Constants', () {
    test('should have correct primary color value', () {
      expect(PrimeryColor, equals(const Color(0xff202342)));
    });

    test('should have correct dark primary color value', () {
      expect(darkPrimeryColor, equals(Colors.black));
    });

    test('should have correct light blue value', () {
      expect(lightblue, equals(const Color(0xff2d325a)));
    });

    test('should have correct dark blue value', () {
      expect(darkblue, equals(Colors.black));
    });

    test('should have correct medium blue value', () {
      expect(mediumblue, equals(const Color(0xff272b4e)));
    });

    test('should have correct dark medium blue value', () {
      expect(darkmediumblue, equals(Colors.black));
    });

    test('should have correct white value', () {
      expect(white, equals(Colors.white));
    });

    test('should have correct dark white value', () {
      expect(darkwhite, equals(Colors.black));
    });

    test('should have correct light grey value', () {
      expect(lightgrey, equals(const Color(0xff373e58)));
    });

    test('should have correct dark grey value', () {
      expect(darkgrey, equals(Colors.black));
    });

    test('should have correct grey value', () {
      expect(grey, equals(const Color(0xff767d97)));
    });

    test('should have correct dark g value', () {
      expect(darkg, equals(Colors.black));
    });

    test('should have unique color values', () {
      final colors = [PrimeryColor, lightblue, mediumblue, lightgrey, grey];

      final uniqueColors = colors.toSet();
      expect(uniqueColors.length, equals(colors.length));
    });

    test('should have correct color properties', () {
      // Test that colors are not null
      expect(PrimeryColor, isNotNull);
      expect(lightblue, isNotNull);
      expect(mediumblue, isNotNull);
      expect(white, isNotNull);
      expect(lightgrey, isNotNull);
      expect(grey, isNotNull);

      // Test that colors are instances of Color
      expect(PrimeryColor, isA<Color>());
      expect(lightblue, isA<Color>());
      expect(mediumblue, isA<Color>());
      expect(white, isA<Color>());
      expect(lightgrey, isA<Color>());
      expect(grey, isA<Color>());
    });
  });
}
