import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/utils/color_notifire.dart';
import 'package:nextsportz_v2/utils/color.dart';

void main() {
  group('ColorNotifire', () {
    late ColorNotifire colorNotifire;

    setUp(() {
      colorNotifire = ColorNotifire();
    });

    test('should initialize with light mode by default', () {
      expect(colorNotifire.IsDark, isFalse);
    });

    test('should set dark mode correctly', () {
      // Act
      colorNotifire.isDark = true;

      // Assert
      expect(colorNotifire.IsDark, isTrue);
    });

    test('should set light mode correctly', () {
      // Arrange
      colorNotifire.isDark = true;

      // Act
      colorNotifire.isDark = false;

      // Assert
      expect(colorNotifire.IsDark, isFalse);
    });

    group('getprimerycolor', () {
      test('should return light primary color when in light mode', () {
        // Arrange
        colorNotifire.isDark = false;

        // Act
        final result = colorNotifire.getprimerycolor;

        // Assert
        expect(result, equals(PrimeryColor));
      });

      test('should return dark primary color when in dark mode', () {
        // Arrange
        colorNotifire.isDark = true;

        // Act
        final result = colorNotifire.getprimerycolor;

        // Assert
        expect(result, equals(darkPrimeryColor));
      });
    });

    group('getlightblue', () {
      test('should return light blue when in light mode', () {
        // Arrange
        colorNotifire.isDark = false;

        // Act
        final result = colorNotifire.getlightblue;

        // Assert
        expect(result, equals(lightblue));
      });

      test('should return dark blue when in dark mode', () {
        // Arrange
        colorNotifire.isDark = true;

        // Act
        final result = colorNotifire.getlightblue;

        // Assert
        expect(result, equals(darkblue));
      });
    });

    group('getmediumblue', () {
      test('should return medium blue when in light mode', () {
        // Arrange
        colorNotifire.isDark = false;

        // Act
        final result = colorNotifire.getmediumblue;

        // Assert
        expect(result, equals(mediumblue));
      });

      test('should return dark medium blue when in dark mode', () {
        // Arrange
        colorNotifire.isDark = true;

        // Act
        final result = colorNotifire.getmediumblue;

        // Assert
        expect(result, equals(darkmediumblue));
      });
    });

    group('getwhite', () {
      test('should return white when in light mode', () {
        // Arrange
        colorNotifire.isDark = false;

        // Act
        final result = colorNotifire.getwhite;

        // Assert
        expect(result, equals(white));
      });

      test('should return dark white when in dark mode', () {
        // Arrange
        colorNotifire.isDark = true;

        // Act
        final result = colorNotifire.getwhite;

        // Assert
        expect(result, equals(darkwhite));
      });
    });

    group('getlightgrey', () {
      test('should return light grey when in light mode', () {
        // Arrange
        colorNotifire.isDark = false;

        // Act
        final result = colorNotifire.getlightgrey;

        // Assert
        expect(result, equals(lightgrey));
      });

      test('should return dark grey when in dark mode', () {
        // Arrange
        colorNotifire.isDark = true;

        // Act
        final result = colorNotifire.getlightgrey;

        // Assert
        expect(result, equals(darkgrey));
      });
    });

    group('getgrey', () {
      test('should return grey when in light mode', () {
        // Arrange
        colorNotifire.isDark = false;

        // Act
        final result = colorNotifire.getgrey;

        // Assert
        expect(result, equals(grey));
      });

      test('should return dark g when in dark mode', () {
        // Arrange
        colorNotifire.isDark = true;

        // Act
        final result = colorNotifire.getgrey;

        // Assert
        expect(result, equals(darkg));
      });
    });

    test('should notify listeners when dark mode changes', () {
      // Arrange
      bool notified = false;
      colorNotifire.addListener(() {
        notified = true;
      });

      // Act
      colorNotifire.isDark = true;

      // Assert
      expect(notified, isTrue);
    });

    test('should return correct colors for all getters in light mode', () {
      // Arrange
      colorNotifire.isDark = false;

      // Act & Assert
      expect(colorNotifire.getprimerycolor, equals(PrimeryColor));
      expect(colorNotifire.getlightblue, equals(lightblue));
      expect(colorNotifire.getmediumblue, equals(mediumblue));
      expect(colorNotifire.getwhite, equals(white));
      expect(colorNotifire.getlightgrey, equals(lightgrey));
      expect(colorNotifire.getgrey, equals(grey));
    });

    test('should return correct colors for all getters in dark mode', () {
      // Arrange
      colorNotifire.isDark = true;

      // Act & Assert
      expect(colorNotifire.getprimerycolor, equals(darkPrimeryColor));
      expect(colorNotifire.getlightblue, equals(darkblue));
      expect(colorNotifire.getmediumblue, equals(darkmediumblue));
      expect(colorNotifire.getwhite, equals(darkwhite));
      expect(colorNotifire.getlightgrey, equals(darkgrey));
      expect(colorNotifire.getgrey, equals(darkg));
    });

    test('should handle multiple dark mode toggles correctly', () {
      // Test multiple toggles
      colorNotifire.isDark = true;
      expect(colorNotifire.IsDark, isTrue);
      expect(colorNotifire.getprimerycolor, equals(darkPrimeryColor));

      colorNotifire.isDark = false;
      expect(colorNotifire.IsDark, isFalse);
      expect(colorNotifire.getprimerycolor, equals(PrimeryColor));

      colorNotifire.isDark = true;
      expect(colorNotifire.IsDark, isTrue);
      expect(colorNotifire.getprimerycolor, equals(darkPrimeryColor));
    });
  });
}
