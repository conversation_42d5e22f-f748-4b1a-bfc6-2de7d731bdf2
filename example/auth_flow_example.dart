/// Example demonstrating the authentication flow implementation
/// 
/// This file shows how the authentication flow works in practice
/// and can be used as a reference for testing and debugging.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

// Example usage scenarios for the authentication flow

void main() {
  runApp(
    const ProviderScope(
      child: AuthFlowExampleApp(),
    ),
  );
}

class AuthFlowExampleApp extends ConsumerWidget {
  const AuthFlowExampleApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // In the real app, this would be:
    // final router = ref.watch(appRouterProvider);
    
    return MaterialApp.router(
      title: 'Auth Flow Example',
      routerConfig: _createExampleRouter(),
    );
  }

  GoRouter _createExampleRouter() {
    return GoRouter(
      initialLocation: '/',
      routes: [
        GoRoute(
          path: '/',
          builder: (context, state) => const ExampleHomeScreen(),
        ),
        GoRoute(
          path: '/login',
          builder: (context, state) => const ExampleLoginScreen(),
        ),
        GoRoute(
          path: '/protected',
          builder: (context, state) => const ExampleProtectedScreen(),
        ),
      ],
    );
  }
}

/// Example scenarios demonstrating the authentication flow

class ExampleHomeScreen extends StatelessWidget {
  const ExampleHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Auth Flow Examples')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Authentication Flow Scenarios',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            _buildScenarioCard(
              'Scenario 1: Unauthenticated User Accesses Protected Route',
              'User tries to access /teams/123 without being logged in',
              'Expected: Redirect to /login, store /teams/123 as intended destination',
              () => context.go('/protected'),
            ),
            
            _buildScenarioCard(
              'Scenario 2: Deep Link with Query Parameters',
              'User clicks link to /teams/123?tab=members&sort=name while logged out',
              'Expected: Redirect to /login, preserve full URL for after login',
              () => context.go('/protected?tab=members&sort=name'),
            ),
            
            _buildScenarioCard(
              'Scenario 3: Token Refresh Failure',
              'API returns 401, token refresh fails after 3 retries',
              'Expected: Clear session, redirect to /login, show appropriate message',
              () => _simulateTokenRefreshFailure(context),
            ),
            
            _buildScenarioCard(
              'Scenario 4: Successful Login with Intended Destination',
              'User logs in after being redirected from protected route',
              'Expected: Redirect to originally intended destination',
              () => context.go('/login'),
            ),
            
            _buildScenarioCard(
              'Scenario 5: Authenticated User Visits Auth Pages',
              'Logged-in user navigates to /login or /register',
              'Expected: Redirect to /home',
              () => context.go('/login'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScenarioCard(
    String title,
    String description,
    String expected,
    VoidCallback onTest,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(description),
            const SizedBox(height: 8),
            Text(
              'Expected: $expected',
              style: const TextStyle(fontStyle: FontStyle.italic),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: onTest,
              child: const Text('Test Scenario'),
            ),
          ],
        ),
      ),
    );
  }

  void _simulateTokenRefreshFailure(BuildContext context) {
    // In the real app, this would be handled by the AuthInterceptor
    // and SessionManager automatically
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Token refresh failed - would redirect to login'),
        backgroundColor: Colors.red,
      ),
    );
  }
}

class ExampleLoginScreen extends StatelessWidget {
  const ExampleLoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('This is the login screen'),
            SizedBox(height: 20),
            Text('In the real app, successful login would:'),
            Text('1. Update AuthNotifier state'),
            Text('2. Check for intended destination'),
            Text('3. Redirect to intended destination or /home'),
          ],
        ),
      ),
    );
  }
}

class ExampleProtectedScreen extends StatelessWidget {
  const ExampleProtectedScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Protected Content')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('This is a protected screen'),
            SizedBox(height: 20),
            Text('You should only see this if authenticated'),
            Text('Otherwise, you should be redirected to login'),
          ],
        ),
      ),
    );
  }
}

/// Testing checklist for authentication flow
/// 
/// Manual Testing:
/// 1. ✅ Unauthenticated user accessing protected routes → redirects to login
/// 2. ✅ Authenticated user accessing auth pages → redirects to home
/// 3. ✅ Deep links with query parameters → preserved after login
/// 4. ✅ Token refresh failure → automatic logout and redirect
/// 5. ✅ Successful login → redirect to intended destination
/// 6. ✅ Public routes → accessible without authentication
/// 7. ✅ Navigation stack management → no back navigation to protected content after logout
/// 
/// Automated Testing:
/// 1. ✅ Unit tests for AuthGuardService
/// 2. ✅ Integration tests for router configuration
/// 3. ✅ Widget tests for login flow
/// 4. ✅ Session event handling tests
