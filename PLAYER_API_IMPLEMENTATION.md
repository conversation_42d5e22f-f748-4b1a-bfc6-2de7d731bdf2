# Player API Implementation Summary

This document summarizes the implementation of the new player APIs and the updated `/auth/me` endpoint based on the actual swagger.json specifications.

## Changes Made

### 1. Updated `/auth/me` API Response

**File**: `lib/features/auth/data/dto/auth_response_models.dart`

- Added `PlayerId` model to handle the new `playerIds` field in the API response
- Updated `UserProfileResponse` to include:
  - `dateOfBirth` field
  - `playerIds` list of `PlayerId` objects
  - `roles` list of strings
- Added `footballPlayerId` getter to easily access the Football player ID

**New API Response Structure**:

```json
{
  "message": "Success",
  "data": {
    "id": "bf532cb1-d6d7-4648-89a9-3435b1343cde",
    "fullName": "Prashant Karki",
    "email": "<EMAIL>",
    "dateOfBirth": "2025-08-18T08:46:19.028Z",
    "phoneNumber": "9841786919",
    "playerIds": [
      {
        "gameType": "Football",
        "id": "bb8deb1a-e8e9-4e0c-98ed-e4216880b48d"
      }
    ],
    "selectedRole": "PLAYER",
    "roles": ["PLAYER"]
  }
}
```

### 2. Added Player API Endpoints

**File**: `lib/core/networking/api_const.dart`

Added new API endpoints:

- `playersEndpoint = "/api/players"`
- `playerProfileEndpoint = "/api/players/{playerId}"`
- `playerRatingEndpoint = "/api/players/{playerId}/rating"`

### 3. Player Data Layer Implementation

#### Request Models

**File**: `lib/features/player/data/dto/player_request_models.dart`

- `UpdatePlayerProfileRequest`: For updating player profile information
- `SubmitRatingRequest`: For submitting player ratings with detailed attributes
- `PlayerGameInfoRequest`: For player game-specific information
- `ActiveFoot` enum: Left, Right, Both
- `RatingCategory` enum: Self, Public, Opponent, Teammate, Scout

**Update Player Profile Request Structure**:

```json
{
  "fullName": "string",
  "dateOfBirth": "2025-08-18T16:13:54.321Z",
  "heightCm": 0,
  "weightKg": 0,
  "description": "string",
  "photoUrl": "string",
  "scoutingEnabled": true,
  "gameInfo": {
    "activeFoot": "Left",
    "primaryPosition": "string",
    "playedPositions": ["string"],
    "teamsPlayed": ["string"]
  }
}
```

**Submit Rating Request Structure**:

```json
{
  "ratingCategory": "Self",
  "defense": 0,
  "shooting": 0,
  "passing": 0,
  "pace": 0,
  "physicality": 0,
  "dribbling": 0,
  "comments": "string"
}
```

#### Response Models

**File**: `lib/features/player/data/dto/player_response_models.dart`

- `PlayerProfileResponse`: Complete player profile data
- `PlayerGameInfoResponse`: Player game information
- `PlayerRatingVote`: Individual rating vote with detailed attributes
- `PlayerRatingBreakdown`: Rating statistics and recent votes

#### Data Sources

**File**: `lib/features/player/data/datasources/player_datasource.dart`
**File**: `lib/features/player/data/datasources/player_remote_datasource.dart`

- `getPlayerProfile(String playerId)`: GET `/api/players/{playerId}`
- `updatePlayerProfile(String playerId, UpdatePlayerProfileRequest)`: PUT `/api/players/{playerId}`
- `getPlayerRating(String playerId)`: GET `/api/players/{playerId}/rating`
- `submitRating(String playerId, SubmitRatingRequest)`: POST `/api/players/{playerId}/rating`

### 4. Player Domain Layer

#### Entities

**File**: `lib/features/player/domain/entities/player.dart`

- Updated `Player` entity to match the new API response structure

**File**: `lib/features/player/domain/entities/player_rating.dart`

- `PlayerRatingVote`: Domain entity for rating votes with detailed attributes
- `PlayerRatingBreakdown`: Domain entity for rating statistics

#### Repository

**File**: `lib/features/player/domain/repositories/player_repository.dart`
**File**: `lib/features/player/data/repositories/player_repository_impl.dart`

Repository interface and implementation with methods:

- `getPlayerProfile(String playerId)`
- `updatePlayerProfile(String playerId, UpdatePlayerProfileRequest)`
- `getPlayerRating(String playerId)`
- `submitRating(String playerId, SubmitRatingRequest)`

#### Use Cases

**File**: `lib/features/player/domain/usecases/player_usecases.dart`

- `GetPlayerProfileUseCase`
- `UpdatePlayerProfileUseCase`
- `GetPlayerRatingUseCase`
- `SubmitPlayerRatingUseCase`

### 5. Provider Configuration

**File**: `lib/features/player/player_providers.dart`

Added Riverpod providers for:

- Data sources
- Repositories
- Use cases
- Feature providers for easy consumption

### 6. Example UI Implementation

**File**: `lib/features/player/presentation/widgets/player_profile_widget.dart`

Created a comprehensive example widget that demonstrates:

- Displaying player profile information
- Showing rating breakdown and statistics
- Handling loading and error states
- Pull-to-refresh functionality

## API Endpoints Implemented

1. **GET `/api/players/{playerId}`** - Get player profile
2. **PUT `/api/players/{playerId}`** - Update player profile
3. **GET `/api/players/{playerId}/rating`** - Get player rating breakdown
4. **POST `/api/players/{playerId}/rating`** - Submit player rating

## Usage Examples

### Getting Player Profile

```dart
final playerProfileAsync = ref.watch(playerProfileProvider(playerId));
```

### Updating Player Profile

```dart
final useCase = ref.read(updatePlayerProfileUseCaseProvider);
final result = await useCase(
  playerId: playerId,
  fullName: "New Name",
  heightCm: 180,
  weightKg: 75,
  description: "Professional footballer",
  activeFoot: ActiveFoot.right,
  primaryPosition: "Forward",
  playedPositions: ["Forward", "Winger"],
  teamsPlayed: ["Team A", "Team B"],
);
```

### Getting Player Rating

```dart
final playerRatingAsync = ref.watch(playerRatingProvider(playerId));
```

### Submitting Rating

```dart
final useCase = ref.read(submitPlayerRatingUseCaseProvider);
final result = await useCase(
  playerId: playerId,
  ratingCategory: RatingCategory.self,
  defense: 75,
  shooting: 85,
  passing: 80,
  pace: 90,
  physicality: 70,
  dribbling: 85,
  comments: "Excellent player with great skills!",
);
```

## Key Features

1. **Clean Architecture**: Follows the established pattern with proper separation of concerns
2. **Error Handling**: Comprehensive error handling with Either types
3. **Type Safety**: Strong typing throughout the implementation
4. **Reactive UI**: Riverpod integration for reactive state management
5. **Football Player ID**: Easy access to Football player ID from auth response
6. **Comprehensive Rating System**: Detailed rating system with 6 different attributes
7. **Flexible Profile Updates**: Support for all profile fields including game information
8. **Rating Categories**: Support for different types of ratings (Self, Public, Opponent, etc.)

## Rating System Details

The new rating system includes 6 different attributes:

- **Defense**: Defensive capabilities
- **Shooting**: Shooting accuracy and power
- **Passing**: Passing accuracy and vision
- **Pace**: Speed and acceleration
- **Physicality**: Strength and physical presence
- **Dribbling**: Ball control and dribbling skills

Each attribute is rated on a scale (typically 0-100), and the system calculates an average rating from all attributes.

## Next Steps

1. Implement the edit profile dialog in the UI
2. Implement the rating submission dialog with all 6 attributes
3. Add more player-related features as needed
4. Add unit tests for the new functionality
5. Add integration tests for the API endpoints
