name: Flutter Web Deployment

on:
  push:
    branches:
      - main

permissions:
  contents: read
  issues: write

jobs:
  build-and-deploy-dev:
    name: Build and Deploy Flutter Web to DEV
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.29.0"
          channel: "stable"

      - name: Get Dependencies
        run: flutter pub get

      - name: Build Flutter Web
        run: flutter build web --release

      - name: Deploy to S3 DEV
        uses: jakejarvis/s3-sync-action@v0.5.1
        with:
          args: --delete
        env:
          AWS_S3_BUCKET: ${{ secrets.DEV_BUCKET_NAME }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-1
          SOURCE_DIR: build/web

      - name: Invalidate CloudFront Cache (DEV)
        uses: chetan/invalidate-cloudfront-action@v2
        env:
          DISTRIBUTION: E1IVQB8HE24BFD
          PATHS: "/*"
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-1

  wait-approval:
    name: Wait for Prod Approval
    needs: build-and-deploy-dev
    runs-on: ubuntu-latest
    outputs:
      approved: ${{ steps.set-result.outputs.approved }}
    steps:
      - name: Await Manual Approval (catch failure)
        id: approval
        continue-on-error: true
        uses: trstringer/manual-approval@v1
        timeout-minutes: 5
        with:
          secret: ${{ github.TOKEN }}
          approvers: Prashant-Karki
          minimum-approvals: 1
          issue-title: "Approve Production Deployment"
          issue-body: "Please approve the deployment to production."

      - name: Set approval result
        id: set-result
        run: |
          if [ "${{ steps.approval.outcome }}" == "success" ]; then
            echo "approved=true" >> $GITHUB_OUTPUT
          else
            echo "approved=false" >> $GITHUB_OUTPUT
          fi

  build-and-deploy-prod:
    name: Build and Deploy Flutter Web to PROD
    if: needs.wait-approval.outputs.approved == 'true'
    needs: wait-approval
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.29.0"
          channel: "stable"

      - name: Get Dependencies
        run: flutter pub get

      - name: Build Flutter Web
        run: flutter build web --release

      - name: Deploy to S3 PROD
        uses: jakejarvis/s3-sync-action@v0.5.1
        with:
          args: --delete
        env:
          AWS_S3_BUCKET: ${{ secrets.PROD_BUCKET_NAME }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-1
          SOURCE_DIR: build/web

      - name: Invalidate CloudFront Cache (PROD)
        uses: chetan/invalidate-cloudfront-action@v2
        env:
          DISTRIBUTION: ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID_PROD }}
          PATHS: "/*"
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-1
