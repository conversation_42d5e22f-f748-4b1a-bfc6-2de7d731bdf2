# Pagination Implementation for Teams Feature

This document describes the pagination support implementation for the teams feature in the NextSportz Flutter application.

## Overview

The implementation provides a complete pagination solution with the following components:

1. **TeamSearchItem Entity** - Lightweight team data model for search results
2. **Generic PaginatedResponse Model** - Handles API response structure
3. **Reusable PaginatedListView Widget** - UI component with automatic pagination
4. **Updated Teams Architecture** - Repository and datasource support

## API Response Structure

The pagination follows this API response format:

```json
{
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "9a24850a-15fc-42d4-bf8f-903015139bfd",
        "name": "<PERSON><PERSON><PERSON>",
        "membersCount": 0,
        "winRate": "0.00",
        "logoUrl": ""
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  },
  "error": null,
  "validationErrors": null
}
```

## Implementation Details

### 1. TeamSearchItem Entity

**Location**: `lib/features/teams/domain/entities/team.dart`

```dart
class TeamSearchItem {
  final String id;
  final String name;
  final int membersCount;
  final String winRate;
  final String logoUrl;

  // Utility methods
  double get winRateAsDouble;
  String get winRatePercentage;
}
```

### 2. Generic PaginatedResponse Model

**Location**: `lib/core/models/paginated_response.dart`

```dart
class PaginatedResponse<T> {
  final String message;
  final PaginatedData<T> data;
  final String? error;
  final dynamic validationErrors;

  // Utility methods
  bool get isSuccess;
  bool get hasNextPage;
  bool get hasPreviousPage;
}

class PaginatedData<T> {
  final List<T> items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  // Utility methods and pagination helpers
}
```

### 3. PaginatedListView Widget

**Location**: `lib/core/widgets/paginated_list_view.dart`

Features:

- Automatic pagination on scroll
- Pull-to-refresh support
- Loading states (initial, loading more)
- Error handling with retry
- Empty state display
- Customizable item builders

```dart
PaginatedListView<T>(
  onLoadPage: (page) => loadDataFunction(page),
  itemBuilder: (context, item, index) => ItemWidget(item),
  emptyWidget: EmptyStateWidget(),
  errorBuilder: (error, onRetry) => ErrorWidget(error, onRetry),
  pageSize: 20,
  enableRefresh: true,
)
```

### 4. TeamSearchItemCard Widget

**Location**: `lib/features/teams/presentation/widgets/team_search_item_card.dart`

Specialized card widget for displaying team search items with:

- Team logo (with cached image support)
- Team name and member count
- Win rate with color-coded badge
- Tap handling

### 5. Updated Architecture

#### Datasource

**Location**: `lib/features/teams/data/datasources/teams_datasource.dart`

Added methods:

```dart
Future<PaginatedResponse<TeamSearchItem>> searchTeamsPaginated({
  String? query,
  int? page,
  int? pageSize,
});

Future<PaginatedResponse<TeamSearchItem>> getAllTeamsPaginated({
  int? page,
  int? pageSize,
});
```

#### Repository

**Location**: `lib/features/teams/domain/repositories/teams_repository.dart`

Added corresponding repository methods with error handling using `Either<AppError, T>`.

#### API Endpoints

**Location**: `lib/core/networking/api_const.dart`

Added endpoints:

- `searchTeamsEndpoint = "/api/teams/search"`
- `allTeamsEndpoint = "/api/teams"`

## Usage Examples

### Basic Usage with TeamSearchPaginatedListView

```dart
TeamSearchPaginatedListView(
  onLoadPage: (page) async {
    final result = await teamsRepository.searchTeamsPaginated(
      query: searchQuery,
      page: page,
      pageSize: 20,
    );
    return result.fold(
      (error) => throw Exception(error.message),
      (response) => response,
    );
  },
  onTeamTap: (team) {
    // Navigate to team details
  },
  emptyMessage: 'No teams found',
)
```

### Custom Usage with Generic PaginatedListView

```dart
PaginatedListView<TeamSearchItem>(
  onLoadPage: loadTeamsFunction,
  itemBuilder: (context, team, index) {
    return CustomTeamCard(team: team);
  },
  emptyWidget: CustomEmptyWidget(),
  errorBuilder: (error, onRetry) => CustomErrorWidget(error, onRetry),
  pageSize: 20,
  enableRefresh: true,
  showSeparator: true,
)
```

### Repository Usage

```dart
// In your provider or use case
final result = await teamsRepository.searchTeamsPaginated(
  query: 'search term',
  page: 1,
  pageSize: 20,
);

result.fold(
  (error) => handleError(error),
  (paginatedResponse) => handleSuccess(paginatedResponse),
);
```

## API Query Parameters

The implementation supports these query parameters:

| Parameter | Type    | Description                     |
| --------- | ------- | ------------------------------- |
| Search    | string  | Search term for filtering teams |
| Page      | integer | Page number (1-based)           |
| PageSize  | integer | Number of items per page        |

## Features

### PaginatedListView Features

- ✅ Automatic pagination on scroll
- ✅ Pull-to-refresh
- ✅ Loading states (initial, loading more, refreshing)
- ✅ Error handling with retry
- ✅ Empty state display
- ✅ Customizable widgets for all states
- ✅ Scroll controller support
- ✅ Separator support

### TeamSearchItem Features

- ✅ Lightweight data model
- ✅ Win rate calculations
- ✅ JSON serialization
- ✅ Copy methods

### Architecture Features

- ✅ Clean architecture compliance
- ✅ Generic and reusable
- ✅ Error handling with Either
- ✅ Repository pattern
- ✅ Dependency injection ready

## Testing

To test the implementation:

1. Use the example screen: `TeamsSearchScreen`
2. Implement your repository provider
3. Connect to your API endpoints
4. Test pagination, search, and error scenarios

## Future Enhancements

Potential improvements:

- Add caching for paginated data
- Implement offline support
- Add sorting options
- Add filtering capabilities
- Implement infinite scroll variants
- Add analytics tracking

## Files Created/Modified

### New Files

- `lib/core/models/paginated_response.dart`
- `lib/core/widgets/paginated_list_view.dart`
- `lib/features/teams/data/dto/team_search_item_dto.dart`
- `lib/features/teams/presentation/widgets/team_search_item_card.dart`
- `lib/features/teams/presentation/screens/teams_search_screen.dart`

### Modified Files

- `lib/features/teams/domain/entities/team.dart` (added TeamSearchItem)
- `lib/features/teams/data/datasources/teams_datasource.dart` (added pagination methods)
- `lib/features/teams/data/datasources/teams_remote_datasource.dart` (implemented pagination)
- `lib/features/teams/domain/repositories/teams_repository.dart` (added pagination methods)
- `lib/features/teams/data/repositories/teams_repository_impl.dart` (implemented pagination)
- `lib/core/networking/api_const.dart` (added endpoints)

This implementation provides a solid foundation for pagination throughout the application and can be easily extended for other features.
