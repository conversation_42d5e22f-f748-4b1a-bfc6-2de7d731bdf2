# Enum Updates for AgeGroup and WagerType

## Problem

The API was returning validation errors for invalid age groups and wager types:

```
{
  "message": "Validation Failed",
  "validationErrors": {
    "AgeGroup": ["Invalid age group. Valid groups are: U12, U14, U16, U18, U21, Senior, Open"],
    "WagerType": ["Invalid wager type. Valid types are: Money, Trophy, Bragging Rights"]
  }
}
```

## Solution

Created proper enums to ensure type safety and prevent invalid values from being sent to the API.

## Changes Made

### 1. Created New Enum File

**File**: `lib/features/challenges/domain/entities/challenge_enums.dart`

- **AgeGroup enum** with values: U12, U14, U16, U18, U21, Senior, Open
- **WagerType enum** with values: Money, Trophy, Bragging Rights
- Both enums include `fromString()` and `toString()` methods for conversion

### 2. Updated Challenge Entity

**File**: `lib/features/challenges/domain/entities/challenge.dart`

- Changed `ageGroup` field from `String?` to `AgeGroup?`
- Changed `wagerType` field from `String?` to `WagerType?`
- Updated `fromJson()` method to use `AgeGroup.fromString()` and `WagerType.fromString()`
- Updated `toJson()` method to use `toString()` on enum values
- Updated `copyWith()` method signature to use enum types
- Updated `wagerDisplayText` getter to handle enum values

### 3. Updated Use Cases

**File**: `lib/features/challenges/domain/usecases/challenge_usecases.dart`

- Updated `CreateChallengeParams` to use `AgeGroup?` and `WagerType?`
- Updated `UpdateChallengeParams` to use `WagerType?`
- Updated `GetMatchRequestsParams` to use `AgeGroup?`
- Updated `CreateMatchRequestParams` to use `AgeGroup?`
- Modified use case implementations to convert enums to strings when creating DTOs

### 4. Updated UI Components

**File**: `lib/features/challenges/presentation/screens/challenges_screen.dart`

- Updated form state variables to use enum types
- Changed age group choices from hardcoded strings to `AgeGroup.values`
- Changed wager type choices from hardcoded strings to `WagerType.values`
- Updated form submission to pass enum values to use cases

### 5. Added Tests

**File**: `test/features/challenges/domain/entities/challenge_enums_test.dart`

- Comprehensive tests for both enums
- Tests for `fromString()` and `toString()` methods
- Tests for default fallback values

## Benefits

1. **Type Safety**: Prevents invalid values from being sent to the API
2. **Code Quality**: Better IDE support with autocomplete and error detection
3. **Maintainability**: Centralized definition of valid values
4. **Validation**: Automatic validation at compile time
5. **Consistency**: Ensures consistent values across the application

## Usage Examples

```dart
// Creating a challenge with proper enums
final params = CreateChallengeParams(
  matchType: '5v5',
  ageGroup: AgeGroup.u18,  // Type-safe
  wagerType: WagerType.money,  // Type-safe
  // ... other parameters
);

// Converting from API response
final ageGroup = AgeGroup.fromString(json['ageGroup']);
final wagerType = WagerType.fromString(json['wagerType']);

// Converting to API request
final ageGroupString = ageGroup?.toString(); // "U18"
final wagerTypeString = wagerType?.toString(); // "Money"
```

## Migration Notes

- All existing string-based age groups and wager types have been replaced with enum values
- The UI now shows the correct valid options as defined by the API
- Default values are set to sensible defaults (U18 for age group, Bragging Rights for wager type)
- All conversion between enums and strings is handled automatically
