import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../utils/responsive_utils.dart';
import '../../../../utils/responsive_wrapper.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../profile/profile_providers.dart';
import '../../../profile/domain/entities/profile.dart';
import '../../../profile/presentation/widgets/player_rating_card.dart';
import '../../../profile/presentation/screens/profile_settings_screen.dart';
import '../../../auth/presentation/logic/controller.dart';
import '../../../auth/presentation/screens/login_screen.dart';
import '../../../../core/local/token_storage.dart';
import '../../../player/player_providers.dart';
import '../../../player/presentation/screens/player_profile_dialog_screen.dart';

class ProfileTab extends ConsumerStatefulWidget {
  const ProfileTab({Key? key}) : super(key: key);

  @override
  ConsumerState<ProfileTab> createState() => _ProfileTabState();
}

class _ProfileTabState extends ConsumerState<ProfileTab> {
  @override
  void initState() {
    super.initState();
    // Load profile data when the tab is first opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(profileProvider.notifier).loadProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    final profileAsync = ref.watch(profileProvider);

    return ResponsiveWrapper(
      useScrollView: true,
      child: Padding(
        padding: ResponsiveUtils.getResponsivePadding(horizontal: 16),
        child: ResponsiveColumn(
          children: [
            SizedBox(height: ResponsiveUtils.getResponsiveSafeAreaTop()),

            // Combined Profile Header and Player Profile Card
            _buildCombinedProfileSection(context, profileAsync),

            const SizedBox(height: 12),

            // Player Rating Card
            const PlayerRatingCard(),

            const SizedBox(height: 12),

            // Menu Items
            _buildMenuSection(context),
            SizedBox(
              height: ResponsiveUtils.responsiveLgSpacing,
            ), // Bottom spacing
          ],
        ),
      ),
    );
  }

  Widget _buildCombinedProfileSection(
    BuildContext context,
    AsyncValue<Profile?> profileAsync,
  ) {
    final playerStatsAsync = ref.watch(currentPlayerStatsProvider);
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final accentSecondaryColor =
        isDark
            ? NextSportzTheme.darkAccentSecondary
            : NextSportzTheme.lightAccentSecondary;

    return GestureDetector(
      onTap: () async {
        // Get current player ID and navigate to player profile dialog
        final playerId = await ref.read(currentPlayerIdProvider.future);
        if (context.mounted) {
          // For now, keep as modal navigation since we don't have a dedicated route
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => PlayerProfileDialogScreen(playerId: playerId),
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              accentColor.withOpacity(0.8),
              accentSecondaryColor.withOpacity(0.8),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: accentColor.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Profile Header Section
              Row(
                children: [
                  // Profile Image
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 3),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        "assets/images/profile.png",
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white.withOpacity(0.2),
                            ),
                            child: const Icon(
                              Icons.person,
                              size: 40,
                              color: Colors.white,
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // User Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        profileAsync.when(
                          data:
                              (profile) => Text(
                                profile?.name ?? "Loading...",
                                style: const TextStyle(
                                  fontFamily: 'Gilroy_Bold',
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          loading:
                              () => const Text(
                                "Loading...",
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Bold',
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          error:
                              (error, stack) => const Text(
                                "Error loading profile",
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Bold',
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                        ),
                        const SizedBox(height: 4),
                        profileAsync.when(
                          data:
                              (profile) => Text(
                                profile?.selectedRole == 'player'
                                    ? "Professional Player"
                                    : "User",
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Medium',
                                  color: Colors.white.withOpacity(0.8),
                                  fontSize: 14,
                                ),
                              ),
                          loading:
                              () => Text(
                                "Loading...",
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Medium',
                                  color: Colors.white.withOpacity(0.8),
                                  fontSize: 14,
                                ),
                              ),
                          error:
                              (error, stack) => Text(
                                "Error",
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Medium',
                                  color: Colors.white.withOpacity(0.8),
                                  fontSize: 14,
                                ),
                              ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Text(
                            "Premium Member",
                            style: TextStyle(
                              fontFamily: 'Gilroy_Medium',
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Action Buttons
                  Row(
                    children: [
                      // View Public Preview Button
                      IconButton(
                        onPressed: () async {
                          final playerId = await ref.read(
                            currentPlayerIdProvider.future,
                          );
                          if (context.mounted) {
                            context.go("/players/$playerId");
                          }
                        },
                        icon: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.visibility,
                            color: Colors.white,
                            size: 12,
                          ),
                        ),
                      ),
                      // Edit Button
                      IconButton(
                        onPressed: () {
                          context.push("/home/<USER>");
                        },
                        icon: Container(
                          padding: const EdgeInsets.all(4),

                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.edit,
                            color: Colors.white,
                            size: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              // Performance Stats Section - Creative Design
              const SizedBox(height: 16),

              // Stats Grid with creative layout
              playerStatsAsync.when(
                data: (stats) => _buildCreativeStatsGrid(stats),
                loading: () => _buildCreativeStatsGrid({}),
                error: (error, stack) => _buildCreativeStatsGrid({}),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCreativeStatsGrid(Map<String, dynamic> stats) {
    final totalChallenges = stats['totalChallenges'] ?? 0;
    final challengesWon = stats['challengesWon'] ?? 0;
    final totalWagerWon = stats['totalWagerWon'] ?? 0;
    final winRate =
        totalChallenges > 0 ? (challengesWon / totalChallenges * 100) : 0.0;

    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: secondaryColor.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Row(
        children: [
          // Challenges Stat
          Expanded(
            child: _buildCreativeStatItemInContainer(
              'Challenges',
              totalChallenges.toString(),
              Icons.flash_on,
              Colors.orange,
            ),
          ),
          // Divider
          Container(height: 60, width: 1, color: Colors.white.withOpacity(0.1)),
          // Wins Stat
          Expanded(
            child: _buildCreativeStatItemInContainer(
              'Wins',
              challengesWon.toString(),
              Icons.emoji_events,
              Colors.amber,
            ),
          ),
          // Divider
          Container(height: 60, width: 1, color: Colors.white.withOpacity(0.1)),
          // Wager Stat
          Expanded(
            child: _buildCreativeStatItemInContainer(
              'Wager Won',
              '₹${(totalWagerWon / 1000).toStringAsFixed(1)}k',
              Icons.monetization_on,
              Colors.green,
            ),
          ),
          // Divider
          Container(height: 60, width: 1, color: Colors.white.withOpacity(0.1)),
          // Win Rate Stat
          Expanded(
            child: _buildCreativeStatItemInContainer(
              'Win Rate',
              '${winRate.toStringAsFixed(1)}%',
              Icons.trending_up,
              Colors.blue,
            ),
          ),
          // Chevron Right Icon
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Icon(
              Icons.chevron_right,
              color: Colors.white.withOpacity(0.6),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreativeStatItemInContainer(
    String label,
    String value,
    IconData icon,
    Color accentColor,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Compact icon and value row
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: accentColor, size: 14),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  value,
                  style: TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: 4),

          // Label
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.7),
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildMenuSection(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: secondaryColor.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        children: [
          _buildMenuItem(context, "Matches", Icons.sports_soccer, () {
            context.go('/home/<USER>');
          }),
          _buildDivider(),
          _buildMenuItem(context, "My Teams", Icons.group, () {
            context.go('/home/<USER>');
          }),
          _buildDivider(),
          _buildMenuItem(context, "Booked Venues", Icons.location_on, () {
            // TODO: Navigate to venues
          }),
          _buildDivider(),
          _buildMenuItem(context, "Settings", Icons.settings, () {
            context.go('/home/<USER>');
          }),
          _buildDivider(),
          _buildMenuItem(context, "Help & Support", Icons.help_outline, () {
            // TODO: Navigate to help
          }),
          _buildDivider(),
          _buildMenuItem(context, "Logout", Icons.logout, () {
            _showLogoutDialog(context);
          }, isDestructive: true),
        ],
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color:
                    isDestructive
                        ? Colors.red.withOpacity(0.2)
                        : accentColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: isDestructive ? Colors.red : accentColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: isDestructive ? Colors.red : Colors.white,
                  fontSize: 16,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: isDestructive ? Colors.red : greyColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      color: Colors.white.withOpacity(0.1),
      indent: 60,
      endIndent: 20,
    );
  }

  void _showLogoutDialog(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: secondaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Text(
            "Logout",
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 18,
            ),
          ),
          content: const Text(
            "Are you sure you want to logout?",
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white,
              fontSize: 14,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                "Cancel",
                style: TextStyle(fontFamily: 'Gilroy_Medium', color: greyColor),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _performLogout();
              },
              child: Text(
                "Logout",
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.red,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _performLogout() async {
    try {
      // First, clear local storage immediately for better UX
      final tokenStorage = ref.read(tokenStorageProvider);
      await tokenStorage.clearTokens();

      // Try to call the logout API (but don't block on it since it may fail)
      try {
        // This will also update the auth state properly
        await ref.read(authNotifierProvider.notifier).logout();
      } catch (apiError) {
        // If API fails, manually set auth state to unauthenticated
        // Since we cleared local storage, user should be logged out
        print('Logout API failed (ignored): $apiError');
      }

      // Navigate to login screen
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (Route<dynamic> route) => false,
        );
      }
    } catch (e) {
      // Show error but still try to navigate to login
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Logout error: ${e.toString()}',
              style: const TextStyle(fontFamily: 'Gilroy_Medium'),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );

        // Still navigate to login even if there was an error
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (Route<dynamic> route) => false,
        );
      }
    }
  }
}
