import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../auth/presentation/logic/controller.dart';
import '../../../auth/presentation/logic/auth_state.dart';
import '../../../../utils/color.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    // Add a minimum delay for splash screen
    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      await ref.read(authNotifierProvider.notifier).checkTokenStatus();
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authNotifierProvider);

    // Listen to auth state changes and navigate accordingly
    ref.listen<AuthState>(authNotifierProvider, (previous, next) {
      if (mounted) {
        switch (next.status) {
          case AuthStatus.authenticated:
            context.go('/home');
            break;
          case AuthStatus.unauthenticated:
            context.go('/login');
            break;
          case AuthStatus.error:
            // On error, go to login screen
            context.go('/login');
            break;
          case AuthStatus.loading:
          case AuthStatus.initial:
            // Stay on splash screen while loading
            break;
        }
      }
    });

    return Scaffold(
      backgroundColor: PrimeryColor,
      body: Container(
        color: PrimeryColor,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo
              Container(
                color: Colors.transparent,
                height: MediaQuery.of(context).size.height / 3,
                width: MediaQuery.of(context).size.width / 2,
                child: Image.asset("assets/images/nextsportz.png"),
              ),

              const SizedBox(height: 40),

              // Loading indicator
              if (authState.status == AuthStatus.loading)
                const CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
