import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:animated_bottom_navigation_bar/animated_bottom_navigation_bar.dart';
import 'package:go_router/go_router.dart';
import '../../../../utils/responsive_utils.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../challenges/presentation/screens/challenges_screen.dart';
import '../../../tournaments/presentation/screens/tournaments_screen.dart';
import '../../../venues/presentation/screens/venues_screen.dart';
import '../widgets/dashboard_tab.dart';
import '../widgets/profile_tab.dart';

class HomeScreen extends ConsumerStatefulWidget {
  final String? initialTab;

  const HomeScreen({Key? key, this.initialTab}) : super(key: key);

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with TickerProviderStateMixin {
  late int _currentIndex;

  late final List<Widget> _screens;

  final List<IconData> _icons = [
    Icons.dashboard,
    Icons.location_on,
    Icons.emoji_events,
    Icons.person,
  ];

  final List<String> _labels = [
    'Dashboard',
    'Venues',
    'Tournaments',
    'Profile',
  ];

  late AnimationController _fabAnimationController;
  late AnimationController _borderRadiusAnimationController;
  late Animation<double> fabAnimation;
  late Animation<double> borderRadiusAnimation;
  late CurvedAnimation fabCurve;
  late CurvedAnimation borderRadiusCurve;

  @override
  void initState() {
    super.initState();

    // Set initial tab based on query parameter
    _currentIndex = _getInitialTabIndex();

    // Initialize screens with callback
    _screens = [
      DashboardTab(onChallengesTap: _navigateToChallenges),
      const VenuesScreen(),
      const ChallengesScreen(),
      const TournamentsScreen(),
      const ProfileTab(),
    ];

    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _borderRadiusAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    fabCurve = CurvedAnimation(
      parent: _fabAnimationController,
      curve: const Interval(0.5, 1.0, curve: Curves.fastOutSlowIn),
    );
    borderRadiusCurve = CurvedAnimation(
      parent: _borderRadiusAnimationController,
      curve: const Interval(0.5, 1.0, curve: Curves.fastOutSlowIn),
    );

    fabAnimation = Tween<double>(begin: 0, end: 1).animate(fabCurve);
    borderRadiusAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(borderRadiusCurve);

    Future.delayed(
      const Duration(milliseconds: 500),
      () => _fabAnimationController.forward(),
    );
    Future.delayed(
      const Duration(milliseconds: 500),
      () => _borderRadiusAnimationController.forward(),
    );
  }

  int _getInitialTabIndex() {
    switch (widget.initialTab?.toLowerCase()) {
      case 'dashboard':
        return 0;
      case 'venues':
        return 1;
      case 'challenge':
      case 'challenges':
        return 2;
      case 'tournaments':
        return 3;
      case 'profile':
        return 4;
      default:
        return 2; // Default to Challenge tab
    }
  }

  void _updateUrlWithTab() {
    final tabNames = [
      'dashboard',
      'venues',
      'challenge',
      'tournaments',
      'profile',
    ];
    final currentTab = tabNames[_currentIndex];
    context.go('/home?tab=$currentTab');
  }

  void _navigateToChallenges() {
    // Check if user is already on the Challenge tab (index 2)
    if (_currentIndex == 2) {
      // User is already on Challenge tab, open create challenge sheet

      _openCreateChallengeSheet();
    } else {
      // User is not on Challenge tab, navigate to it
      _fabAnimationController.reset();
      _borderRadiusAnimationController.reset();
      _borderRadiusAnimationController.forward();
      _fabAnimationController.forward();
      setState(() => _currentIndex = 2); // Navigate to Challenges tab
    }
    _updateUrlWithTab();
  }

  void _openCreateChallengeSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (ctx) {
        final isDark = ref.watch(theme_providers.isDarkModeProvider);
        final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

        return DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.85,
          maxChildSize: 0.95,
          minChildSize: 0.6,
          builder: (_, controller) {
            return Container(
              decoration: BoxDecoration(
                color: secondaryColor,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: _CreateChallengeSheet(scrollController: controller),
            );
          },
        );
      },
    );
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    _borderRadiusAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    return Scaffold(
      backgroundColor: primaryColor,
      body: SafeArea(child: _screens[_currentIndex]),
      floatingActionButton: ScaleTransition(
        scale: fabAnimation,
        child: FloatingActionButton(
          onPressed: _navigateToChallenges,
          tooltip: 'Challenge',
          backgroundColor: accentColor,
          child: Icon(
            Icons.flash_on,
            color: Colors.white,
            size: ResponsiveUtils.getResponsiveIconSize(24),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: AnimatedBottomNavigationBar.builder(
        itemCount: _icons.length,
        tabBuilder: (int index, bool isActive) {
          final color = isActive ? accentColor : greyColor.withOpacity(0.8);
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _icons[index],
                size: ResponsiveUtils.getResponsiveIconSize(24),
                color: color,
              ),
              SizedBox(height: ResponsiveUtils.responsiveXsSpacing),
              Text(
                _labels[index],
                style: ResponsiveUtils.getResponsiveCaptionTextStyle(
                  fontSize: ResponsiveUtils.responsiveXsFontSize,
                  color: color,
                  fontFamily: 'Gilroy_Medium',
                ),
              ),
            ],
          );
        },
        backgroundColor: primaryColor,
        activeIndex:
            _currentIndex == 2
                ? -1
                : (_currentIndex > 2 ? _currentIndex - 1 : _currentIndex),
        splashColor: accentColor,
        notchAndCornersAnimation: borderRadiusAnimation,
        splashSpeedInMilliseconds: 300,
        notchSmoothness: NotchSmoothness.softEdge,
        gapLocation: GapLocation.center,
        leftCornerRadius: ResponsiveUtils.getResponsiveBorderRadius(),
        rightCornerRadius: ResponsiveUtils.getResponsiveBorderRadius(),
        onTap: (index) {
          // Map the tapped index correctly considering the center gap at index 2
          int targetIndex;
          if (index >= 2) {
            targetIndex = index + 1; // Skip the challenges tab (index 2)
          } else {
            targetIndex = index;
          }

          setState(() {
            _currentIndex = targetIndex;
          });
          _updateUrlWithTab();
        },
        shadow: BoxShadow(
          offset: ResponsiveUtils.getResponsiveShadowOffset(),
          blurRadius: ResponsiveUtils.getResponsiveShadowBlur(),
          spreadRadius: 0.5,
          color: accentColor.withOpacity(0.2),
        ),
      ),
    );
  }
}

class _CreateChallengeSheet extends ConsumerStatefulWidget {
  final ScrollController scrollController;
  const _CreateChallengeSheet({required this.scrollController});

  @override
  ConsumerState<_CreateChallengeSheet> createState() =>
      _CreateChallengeSheetState();
}

class _CreateChallengeSheetState extends ConsumerState<_CreateChallengeSheet> {
  String _matchType = '5v5';
  final TextEditingController _locationCtrl = TextEditingController();
  final TextEditingController _timeCtrl = TextEditingController();
  final TextEditingController _futsalCtrl = TextEditingController();
  bool _losersPay = true;
  double _extraMoney = 0;

  @override
  void dispose() {
    _locationCtrl.dispose();
    _timeCtrl.dispose();
    _futsalCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: SingleChildScrollView(
        controller: widget.scrollController,
        padding: const EdgeInsets.fromLTRB(16, 12, 16, 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'Place a Challenge',
              style: TextStyle(
                fontFamily: 'Gilroy_Bold',
                color: Colors.white,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 16),

            // Match type
            const Text('Match Type', style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 10,
              children:
                  ['5v5', '7v7', '11v11']
                      .map(
                        (t) => ChoiceChip(
                          label: Text(
                            t,
                            style: TextStyle(
                              color:
                                  _matchType == t
                                      ? Colors.white
                                      : Colors.black87,
                              fontWeight:
                                  _matchType == t
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                              fontFamily: 'Gilroy_Medium',
                            ),
                          ),
                          selected: _matchType == t,
                          onSelected: (_) => setState(() => _matchType = t),
                          selectedColor: NextSportzTheme.getAccentColor(
                            ref.watch(theme_providers.isDarkModeProvider),
                          ),
                          backgroundColor: Colors.white.withOpacity(0.9),
                          checkmarkColor: Colors.white,
                          side: BorderSide(
                            color:
                                _matchType == t
                                    ? NextSportzTheme.getAccentColor(
                                      ref.watch(
                                        theme_providers.isDarkModeProvider,
                                      ),
                                    )
                                    : Colors.white.withOpacity(0.3),
                            width: 1.5,
                          ),
                        ),
                      )
                      .toList(),
            ),

            const SizedBox(height: 16),
            // Preferred locations
            const Text(
              'Preferred Location',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            _buildField(
              _locationCtrl,
              hint: 'Area or venue (e.g., Dhumbarahi) ',
              icon: Icons.place,
            ),

            const SizedBox(height: 16),
            // Time
            const Text(
              'Preferred Timing',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            _buildField(
              _timeCtrl,
              hint: 'e.g., Tonight 7-9 PM or Sat Morning',
              icon: Icons.schedule,
            ),

            const SizedBox(height: 16),
            // Preferred futsal
            const Text(
              'Preferred Futsal (optional)',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            _buildField(
              _futsalCtrl,
              hint: 'e.g., Bhatbhateni Futsal',
              icon: Icons.sports_soccer,
            ),

            const SizedBox(height: 16),
            // Wager
            const Text('Wager', style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            Row(
              children: [
                Switch(
                  value: _losersPay,
                  activeColor: NextSportzTheme.getAccentColor(
                    ref.watch(theme_providers.isDarkModeProvider),
                  ),
                  onChanged: (v) => setState(() => _losersPay = v),
                ),
                const SizedBox(width: 8),
                const Text(
                  'Losers pay',
                  style: TextStyle(color: Colors.white70),
                ),
              ],
            ),
            const SizedBox(height: 6),
            Row(
              children: [
                const Icon(Icons.attach_money, color: Colors.white70, size: 18),
                const SizedBox(width: 6),
                Expanded(
                  child: Slider(
                    min: 0,
                    max: 2000,
                    divisions: 20,
                    value: _extraMoney,
                    activeColor: NextSportzTheme.getAccentColor(
                      ref.watch(theme_providers.isDarkModeProvider),
                    ),
                    label: 'NPR ${_extraMoney.toStringAsFixed(0)}',
                    onChanged: (v) => setState(() => _extraMoney = v),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    'NPR ${_extraMoney.toStringAsFixed(0)}',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.flash_on, color: Colors.white),
                label: const Text(
                  'Place Challenge',
                  style: TextStyle(color: Colors.white),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: NextSportzTheme.getAccentColor(
                    ref.watch(theme_providers.isDarkModeProvider),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(14),
                  ),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Challenge posted!'),
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildField(
    TextEditingController ctrl, {
    required String hint,
    required IconData icon,
  }) {
    return TextField(
      controller: ctrl,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: const TextStyle(color: Colors.white54),
        prefixIcon: Icon(icon, color: Colors.white70),
        filled: true,
        fillColor: Colors.white.withOpacity(0.08),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.1)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: NextSportzTheme.getAccentColor(
              ref.watch(theme_providers.isDarkModeProvider),
            ),
          ),
        ),
      ),
    );
  }
}
