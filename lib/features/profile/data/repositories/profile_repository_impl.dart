import 'dart:io';
import 'dart:typed_data';
import 'package:fpdart/fpdart.dart';
import '../../../../core/image_upload/domain/helpers/image_upload_helper.dart';
import '../../../../core/models/image_upload_models.dart';
import '../../domain/entities/profile.dart';
import '../../domain/repositories/profile_repository.dart';
import '../datasources/profile_datasource.dart';

class ProfileRepositoryImpl implements ProfileRepository {
  final ProfileDatasource _profileDatasource;
  final ImageUploadHelper _imageUploadHelper;

  ProfileRepositoryImpl(this._profileDatasource, this._imageUploadHelper);

  @override
  Future<Either<String, Profile>> getProfile(String userId) async {
    try {
      final profile = await _profileDatasource.getProfile();
      return Right(profile);
    } catch (e) {
      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, Profile>> updateProfile(Profile profile) async {
    try {
      final updatedProfile = await _profileDatasource.updateProfile(profile);
      return Right(updatedProfile);
    } catch (e) {
      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, void>> updatePreferences(
    String userId,
    Map<String, dynamic> preferences,
  ) async {
    try {
      // TODO: Implement preferences update
      await Future.delayed(const Duration(seconds: 1));
      return const Right(null);
    } catch (e) {
      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, void>> deleteProfile(String userId) async {
    try {
      // TODO: Implement profile deletion
      await Future.delayed(const Duration(seconds: 1));
      return const Right(null);
    } catch (e) {
      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, Profile>> updateProfilePhoto({
    required String userId,
    File? file,
    Uint8List? bytes,
    String? fileName,
  }) async {
    try {
      // Upload the image using the image upload helper
      final uploadResult = await _imageUploadHelper.uploadUserProfileImage(
        file: file,
        bytes: bytes,
        userId: userId,
        fileName: fileName,
      );

      return uploadResult.fold((error) => Left(error), (downloadUrl) async {
        // Get current profile
        final currentProfileResult = await getProfile(userId);

        return currentProfileResult.fold(
          (error) => Left('Failed to get current profile: $error'),
          (currentProfile) async {
            // Update profile with new photo URL
            final updatedProfile = currentProfile.copyWith(
              profileImage: downloadUrl,
              updatedAt: DateTime.now(),
            );

            // Save the updated profile
            return await updateProfile(updatedProfile);
          },
        );
      });
    } catch (e) {
      return Left('Failed to update profile photo: ${e.toString()}');
    }
  }
}
