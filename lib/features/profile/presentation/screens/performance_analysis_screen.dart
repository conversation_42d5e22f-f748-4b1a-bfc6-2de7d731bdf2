import 'package:flutter/material.dart';
import '../../../../utils/color.dart';

class PerformanceAnalysisScreen extends StatelessWidget {
  const PerformanceAnalysisScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PrimeryColor,
      appBar: AppBar(
        backgroundColor: PrimeryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Performance Analysis',
          style: TextStyle(
            fontFamily: '<PERSON>roy_Bold',
            color: Colors.white,
            fontSize: 18,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),

            // Overall Rating Card
            _buildOverallRatingCard(),

            const SizedBox(height: 24),

            // Performance Stats Grid
            _buildPerformanceStatsGrid(),

            const SizedBox(height: 24),

            // Recent Matches
            _buildRecentMatchesSection(),

            const SizedBox(height: 24),

            // Performance Chart Placeholder
            _buildPerformanceChartPlaceholder(),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildOverallRatingCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: const LinearGradient(
          colors: [Color(0xffDA22FF), Color(0xff9D4EDD)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xffDA22FF).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'Overall Rating',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                '8.5',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 48,
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                '/10',
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white70,
                  fontSize: 20,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildRatingItem('Matches', '24'),
              _buildRatingItem('Goals', '12'),
              _buildRatingItem('Assists', '8'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRatingItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 20,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceStatsGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Performance Stats',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 20,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
          children: [
            _buildStatCard('Pace', '85', Icons.speed, Colors.blue),
            _buildStatCard('Shooting', '78', Icons.sports_soccer, Colors.red),
            _buildStatCard('Passing', '82', Icons.swap_horiz, Colors.green),
            _buildStatCard(
                'Dribbling', '79', Icons.track_changes, Colors.orange),
            _buildStatCard('Defending', '75', Icons.shield, Colors.purple),
            _buildStatCard('Physical', '81', Icons.fitness_center, Colors.teal),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.black.withOpacity(0.3),
        border: Border.all(
          color: color.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 12),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 24,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.grey,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentMatchesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recent Matches',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 20,
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3,
          itemBuilder: (context, index) {
            final matches = [
              {
                'team': 'FC Barcelona',
                'result': 'W 3-1',
                'rating': '8.5',
                'date': '2 days ago'
              },
              {
                'team': 'Real Madrid',
                'result': 'D 2-2',
                'rating': '7.8',
                'date': '1 week ago'
              },
              {
                'team': 'Manchester United',
                'result': 'W 2-0',
                'rating': '8.2',
                'date': '2 weeks ago'
              },
            ];
            final match = matches[index];

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.black.withOpacity(0.2),
                border: Border.all(
                  color: Colors.white.withOpacity(0.1),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xffDA22FF),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.sports_soccer,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'vs ${match['team']}',
                          style: const TextStyle(
                            fontFamily: 'Gilroy_Bold',
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          match['date']!,
                          style: const TextStyle(
                            fontFamily: 'Gilroy_Medium',
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        match['result']!,
                        style: const TextStyle(
                          fontFamily: 'Gilroy_Bold',
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                      Row(
                        children: [
                          const Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            match['rating']!,
                            style: const TextStyle(
                              fontFamily: 'Gilroy_Medium',
                              color: Colors.white,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildPerformanceChartPlaceholder() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.black.withOpacity(0.2),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Performance Trend',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            height: 200,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.black.withOpacity(0.3),
            ),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.show_chart,
                    color: Color(0xffDA22FF),
                    size: 48,
                  ),
                  SizedBox(height: 12),
                  Text(
                    'Performance Chart',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    'Coming Soon',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
