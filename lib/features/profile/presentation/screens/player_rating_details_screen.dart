import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'dart:math' as math;
import '../../../../utils/color.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../player/player_providers.dart';
import '../../../player/domain/entities/player_rating.dart';
import '../../../player/domain/entities/player_rating.dart' as player_rating;

class PlayerRatingDetailsScreen extends ConsumerStatefulWidget {
  final String playerId;

  const PlayerRatingDetailsScreen({super.key, required this.playerId});

  @override
  ConsumerState<PlayerRatingDetailsScreen> createState() =>
      _PlayerRatingDetailsScreenState();
}

class _PlayerRatingDetailsScreenState
    extends ConsumerState<PlayerRatingDetailsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  late AnimationController _radarAnimationController;
  late Animation<double> _radarAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _radarAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _radarAnimation = CurvedAnimation(
      parent: _radarAnimationController,
      curve: Curves.easeInOut,
    );

    _animationController.forward();
    _radarAnimationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _radarAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    final playerRatingAsync = ref.watch(playerRatingProvider(widget.playerId));

    return Scaffold(
      backgroundColor: primaryColor,
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => context.go('/home'),
        ),
        title: const Text(
          'Your Performance Rating',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: () {
              // TODO: Implement share functionality
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF1A1A2E), Color(0xFF16213E)],
          ),
        ),
        child: SafeArea(
          child: playerRatingAsync.when(
            data:
                (result) => result.fold(
                  (error) => _buildErrorWidget(error.message),
                  (rating) => _buildContent(context, rating),
                ),
            loading:
                () => const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
            error: (error, stack) => _buildErrorWidget(error.toString()),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, PlayerRating rating) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Radar Chart Section
          _buildRadarChart(rating),

          const SizedBox(height: 24),

          // Recent Assessments
          if (rating.recentVotes.isNotEmpty) _buildRecentAssessments(rating),
        ],
      ),
    );
  }

  Widget _buildPlayerHeader(PlayerRating rating) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [accentColor.withOpacity(0.8), accentColor.withOpacity(0.6)],
        ),
        boxShadow: [
          BoxShadow(
            color: accentColor.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Overall Rating Section
          const Text(
            'Overall Rating',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white,
              fontSize: 14,
            ),
          ),

          const SizedBox(height: 8),

          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              final displayRating =
                  (rating.averageRating / 10) * _animation.value;
              return Text(
                displayRating.toStringAsFixed(1),
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1,
                ),
              );
            },
          ),

          const SizedBox(height: 4),

          Text(
            '/10',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.8),
              fontSize: 16,
            ),
          ),

          const SizedBox(height: 12),

          // Assessment Info
          Text(
            'Based on ${rating.totalRatings} assessment${rating.totalRatings == 1 ? '' : 's'}',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.9),
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRadarChart(PlayerRating rating) {
    final List<RadarDataPoint> radarData = [
      RadarDataPoint('Defense', rating.defense),
      RadarDataPoint('Shooting', rating.shooting),
      RadarDataPoint('Passing', rating.passing),
      RadarDataPoint('Pace', rating.pace),
      RadarDataPoint('Physicality', rating.physicality),
      RadarDataPoint('Dribbling', rating.dribbling),
    ];

    return Container(
      padding: const EdgeInsets.all(40),
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: lightblue.withOpacity(0.3),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: AnimatedBuilder(
        animation: _radarAnimation,
        builder: (context, child) {
          return CustomPaint(
            painter: RadarChartPainter(radarData, _radarAnimation.value),
            size: const Size(300, 300),
          );
        },
      ),
    );
  }

  Widget _buildRecentAssessments(PlayerRating rating) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xffDA22FF),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.history, color: Colors.white, size: 20),
              ),
              const SizedBox(width: 12),
              const Text(
                'Recent Assessments',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  fontSize: 18,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...rating.recentVotes
              .take(5)
              .map((vote) => _buildAssessmentItem(vote)),
        ],
      ),
    );
  }

  Widget _buildAssessmentItem(player_rating.PlayerRatingVote vote) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${vote.averageRating.toStringAsFixed(1)}★',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  vote.voterName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ),
              Text(
                _formatDate(vote.createdAt),
                style: const TextStyle(fontSize: 12, color: Colors.white54),
              ),
            ],
          ),
          if (vote.comments != null && vote.comments!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              vote.comments!,
              style: const TextStyle(fontSize: 13, color: Colors.white70),
            ),
          ],
          const SizedBox(height: 8),
          _buildAttributeGrid(vote),
        ],
      ),
    );
  }

  Widget _buildAttributeGrid(player_rating.PlayerRatingVote vote) {
    final attributes = [
      {'name': 'Def', 'value': vote.defense.toDouble()},
      {'name': 'Shoot', 'value': vote.shooting.toDouble()},
      {'name': 'Pass', 'value': vote.passing.toDouble()},
      {'name': 'Pace', 'value': vote.pace.toDouble()},
      {'name': 'Phys', 'value': vote.physicality.toDouble()},
      {'name': 'Drib', 'value': vote.dribbling.toDouble()},
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children:
          attributes
              .map(
                (attr) => Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getRatingColor(
                      attr['value'] as double,
                    ).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _getRatingColor(
                        attr['value'] as double,
                      ).withOpacity(0.5),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    '${attr['name']}: ${(attr['value'] as double).round()}',
                    style: TextStyle(
                      fontSize: 10,
                      color: _getRatingColor(attr['value'] as double),
                      fontFamily: 'Gilroy_Medium',
                    ),
                  ),
                ),
              )
              .toList(),
    );
  }

  Color _getRatingColor(double rating) {
    if (rating >= 80) return Colors.green;
    if (rating >= 60) return Colors.orange;
    if (rating >= 40) return Colors.yellow[700]!;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inMinutes}m ago';
    }
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 64),
          const SizedBox(height: 16),
          Text(
            'Error loading assessment data',
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              fontSize: 18,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: const TextStyle(
              fontFamily: 'Gilroy_Medium',
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class RadarDataPoint {
  final String label;
  final double value;

  RadarDataPoint(this.label, this.value);
}

class RadarChartPainter extends CustomPainter {
  final List<RadarDataPoint> data;
  final double animationValue;

  RadarChartPainter(this.data, this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 40;

    // Draw radar background
    _drawRadarBackground(canvas, center, radius);

    // Draw data
    _drawRadarData(canvas, center, radius);

    // Draw labels
    _drawLabels(canvas, center, radius);
  }

  void _drawRadarBackground(Canvas canvas, Offset center, double radius) {
    final paint =
        Paint()
          ..color = Colors.white.withOpacity(0.1)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1;

    // Draw concentric circles
    for (int i = 1; i <= 5; i++) {
      canvas.drawCircle(center, radius * i / 5, paint);
    }

    // Draw axis lines
    for (int i = 0; i < data.length; i++) {
      final angle = (i * 2 * math.pi / data.length) - math.pi / 2;
      final endPoint = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle),
      );
      canvas.drawLine(center, endPoint, paint);
    }
  }

  void _drawRadarData(Canvas canvas, Offset center, double radius) {
    final path = Path();
    final paint =
        Paint()
          ..color = Colors.green.withOpacity(0.3)
          ..style = PaintingStyle.fill;

    final strokePaint =
        Paint()
          ..color = Colors.green
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;

    for (int i = 0; i < data.length; i++) {
      final angle = (i * 2 * math.pi / data.length) - math.pi / 2;
      final value = data[i].value * animationValue / 100;
      final point = Offset(
        center.dx + radius * value * math.cos(angle),
        center.dy + radius * value * math.sin(angle),
      );

      if (i == 0) {
        path.moveTo(point.dx, point.dy);
      } else {
        path.lineTo(point.dx, point.dy);
      }

      // Draw data points
      canvas.drawCircle(point, 4, strokePaint);
    }

    path.close();
    canvas.drawPath(path, paint);
    canvas.drawPath(path, strokePaint);
  }

  void _drawLabels(Canvas canvas, Offset center, double radius) {
    for (int i = 0; i < data.length; i++) {
      final angle = (i * 2 * math.pi / data.length) - math.pi / 2;
      final labelRadius = radius + 20;
      final labelPoint = Offset(
        center.dx + labelRadius * math.cos(angle),
        center.dy + labelRadius * math.sin(angle),
      );

      final textPainter = TextPainter(
        text: TextSpan(
          text: '${data[i].label}\n(${data[i].value.toInt()})',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontFamily: 'Gilroy_Medium',
          ),
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          labelPoint.dx - textPainter.width / 2,
          labelPoint.dy - textPainter.height / 2,
        ),
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
