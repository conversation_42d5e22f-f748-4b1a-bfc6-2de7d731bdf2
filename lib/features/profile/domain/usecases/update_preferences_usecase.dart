import 'package:fpdart/fpdart.dart';
import '../repositories/profile_repository.dart';

class UpdatePreferencesUseCase {
  final ProfileRepository _profileRepository;

  UpdatePreferencesUseCase(this._profileRepository);

  Future<Either<String, void>> call(
    String userId,
    Map<String, dynamic> preferences,
  ) async {
    return await _profileRepository.updatePreferences(userId, preferences);
  }
}
