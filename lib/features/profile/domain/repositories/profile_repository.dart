import 'dart:io';
import 'dart:typed_data';
import 'package:fpdart/fpdart.dart';
import '../entities/profile.dart';

abstract class ProfileRepository {
  Future<Either<String, Profile>> getProfile(String userId);
  Future<Either<String, Profile>> updateProfile(Profile profile);
  Future<Either<String, void>> updatePreferences(
    String userId,
    Map<String, dynamic> preferences,
  );
  Future<Either<String, void>> deleteProfile(String userId);

  /// Update profile photo using file upload
  Future<Either<String, Profile>> updateProfilePhoto({
    required String userId,
    File? file,
    Uint8List? bytes,
    String? fileName,
  });
}
