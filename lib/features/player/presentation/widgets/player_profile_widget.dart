import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../player_providers.dart';
import '../../domain/entities/player.dart';
import '../../domain/entities/player_rating.dart';

/// Widget to display player profile information
class PlayerProfileWidget extends ConsumerWidget {
  final String playerId;

  const PlayerProfileWidget({
    super.key,
    required this.playerId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final playerProfileAsync = ref.watch(playerProfileProvider(playerId));
    final playerRatingAsync = ref.watch(playerRatingProvider(playerId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Player Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _showEditProfileDialog(context, ref),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(playerProfileProvider(playerId));
          ref.invalidate(playerRatingProvider(playerId));
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Player Profile Section
                playerProfileAsync.when(
                  data: (result) => result.fold(
                    (error) => _buildErrorWidget(error.message),
                    (player) => _buildPlayerProfileSection(player),
                  ),
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => _buildErrorWidget(error.toString()),
                ),

                const SizedBox(height: 24),

                // Player Rating Section
                playerRatingAsync.when(
                  data: (result) => result.fold(
                    (error) => _buildErrorWidget(error.message),
                    (rating) => _buildPlayerRatingSection(rating),
                  ),
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => _buildErrorWidget(error.toString()),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlayerProfileSection(Player player) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 50,
                  backgroundImage: player.photoUrl != null
                      ? NetworkImage(player.photoUrl!)
                      : null,
                  child: player.photoUrl == null
                      ? Text(
                          player.fullName.substring(0, 1).toUpperCase(),
                          style: const TextStyle(
                              fontSize: 32, fontWeight: FontWeight.bold),
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        player.fullName,
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (player.primaryPosition != null) ...[
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.blue[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            player.primaryPosition!,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.blue[700],
                            ),
                          ),
                        ),
                      ],
                      const SizedBox(height: 8),
                      if (player.averageRating != null) ...[
                        Row(
                          children: [
                            Icon(Icons.star, color: Colors.amber, size: 24),
                            const SizedBox(width: 4),
                            Text(
                              '${player.averageRating!.toStringAsFixed(1)}',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '(${player.totalRatings ?? 0} ratings)',
                              style: const TextStyle(
                                  fontSize: 14, color: Colors.grey),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
            if (player.description != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  player.description!,
                  style: const TextStyle(fontSize: 16, height: 1.4),
                ),
              ),
            ],
            const SizedBox(height: 16),
            _buildPlayerStats(player),
            const SizedBox(height: 16),
            _buildPlayerDetails(player),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerStats(Player player) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        if (player.matchesPlayed != null)
          _buildStatItem('Matches', player.matchesPlayed.toString()),
        if (player.heightCm != null)
          _buildStatItem('Height', '${player.heightCm}cm'),
        if (player.weightKg != null)
          _buildStatItem('Weight', '${player.weightKg}kg'),
        if (player.activeFoot != null)
          _buildStatItem('Foot', player.activeFoot!),
      ],
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildPlayerDetails(Player player) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Player Details',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        if (player.dateOfBirth != null) ...[
          _buildDetailRow(
              'Date of Birth', _formatBirthDate(player.dateOfBirth!)),
        ],
        if (player.activeFoot != null) ...[
          _buildDetailRow('Preferred Foot', player.activeFoot!),
        ],
        if (player.playedPositions != null &&
            player.playedPositions!.isNotEmpty) ...[
          _buildDetailRow('Positions', player.playedPositions!.join(', ')),
        ],
        if (player.teamsPlayed != null && player.teamsPlayed!.isNotEmpty) ...[
          _buildDetailRow('Teams', player.teamsPlayed!.join(', ')),
        ],
        if (player.scoutingEnabled != null) ...[
          _buildDetailRow(
              'Scouting', player.scoutingEnabled! ? 'Enabled' : 'Disabled'),
        ],
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  String _formatBirthDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildPlayerRatingSection(PlayerRating rating) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Rating Breakdown',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Builder(
                  builder: (context) => TextButton(
                    onPressed: () => _showRatingDialog(context),
                    child: const Text('Rate Player'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.star, color: Colors.amber, size: 32),
                const SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      rating.averageRating.toStringAsFixed(1),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${rating.totalRatings} total ratings',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildCategoryAverages(rating.categoryAverages),
            const SizedBox(height: 16),
            const Text(
              'Recent Ratings',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...rating.recentVotes.take(5).map((vote) => _buildRatingVote(vote)),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryAverages(Map<String, double> categoryAverages) {
    return Column(
      children: categoryAverages.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              Text(
                entry.key,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: LinearProgressIndicator(
                  value: entry.value / 100, // Assuming max rating is 100
                  backgroundColor: Colors.grey[300],
                ),
              ),
              const SizedBox(width: 8),
              Text('${entry.value.toStringAsFixed(1)}'),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildRatingVote(PlayerRatingVote vote) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(
            '${vote.averageRating.toStringAsFixed(1)}★',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  vote.voterName,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                if (vote.comments != null)
                  Text(
                    vote.comments!,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
              ],
            ),
          ),
          Text(
            _formatDate(vote.createdAt),
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Card(
      color: Colors.red[50],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Error: $message',
                style: TextStyle(color: Colors.red[700]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inMinutes}m ago';
    }
  }

  void _showEditProfileDialog(BuildContext context, WidgetRef ref) {
    // TODO: Implement edit profile dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit profile functionality coming soon!')),
    );
  }

  void _showRatingDialog(BuildContext context) {
    // TODO: Implement rating dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Rating functionality coming soon!')),
    );
  }
}
