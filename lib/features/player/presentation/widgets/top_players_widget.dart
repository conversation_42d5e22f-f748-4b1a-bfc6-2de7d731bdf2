import 'package:flutter/material.dart';
import '../../../../core/config/theme.dart';
import '../../domain/entities/player.dart';

class TopPlayersWidget extends StatelessWidget {
  final List<Player> players;
  const TopPlayersWidget({super.key, required this.players});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Top Players', style: NextSportzTheme.titleStyle),
        const SizedBox(height: 12),
        SizedBox(
          height: 140,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: players.length,
            separatorBuilder: (_, __) => const SizedBox(width: 12),
            itemBuilder: (context, index) {
              final p = players[index];
              return Container(
                width: 200,
                padding: const EdgeInsets.all(12),
                decoration: NextSportzTheme.getCardDecoration(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(p.fullName, style: NextSportzTheme.titleStyle.copyWith(fontSize: 16)),
                    const SizedBox(height: 6),
                    Text(p.playedPositions?.first ?? "-", style: NextSportzTheme.bodyStyle.copyWith(fontSize: 12)),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Matches: ${p.matchesPlayed}', style: NextSportzTheme.bodyStyle),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

