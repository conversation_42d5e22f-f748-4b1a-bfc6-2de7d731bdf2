import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/foundation.dart';
import '../../../../utils/responsive_utils.dart';
import '../../domain/entities/mad_sports_card.dart';

/// NextSportz Player Card (shareable)
/// Compact, polished design with gradient, avatar, name and quick stats.
class MadSportsCardWidget extends StatefulWidget {
  final MadSportsCard card;
  const MadSportsCardWidget({super.key, required this.card});

  @override
  State<MadSportsCardWidget> createState() => _MadSportsCardWidgetState();
}

class _MadSportsCardWidgetState extends State<MadSportsCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final int rating = 60 + (widget.card.jerseyNumber % 41);
    final int rank = ((widget.card.age + widget.card.jerseyNumber) % 100) + 1;

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: kIsWeb ? 400 : double.infinity,
            height: kIsWeb ? 240 : 240.h, // Increased height for ranking badges
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [
                  Color(0xFF1B1F5C),
                  Color(0xFF2432A2),
                  Color(0xFF7A1FA2),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(kIsWeb ? 22 : 22.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.25),
                  blurRadius: 22,
                  offset: const Offset(0, 10),
                ),
                // Subtle glow effect
                BoxShadow(
                  color: const Color(0xFF7A1FA2).withOpacity(0.3),
                  blurRadius: 30,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(kIsWeb ? 22 : 22.r),
              ),
              clipBehavior: Clip.antiAlias,
              child: Stack(
                children: [
                  // subtle pattern overlay
                  Positioned.fill(
                    child: IgnorePointer(
                      ignoring: true,
                      child: CustomPaint(painter: _CardPatternPainter()),
                    ),
                  ),
                  // name top center
                  Positioned(
                    top: kIsWeb ? 12 : 12.h,
                    left: 0,
                    right: 0,
                    child: Text(
                      widget.card.playerName,
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: kIsWeb ? 18 : 18.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // ranking badges row
                  Positioned(
                    top: kIsWeb ? 50 : 50.h,
                    left: 0,
                    right: 0,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _rankingBadge('RANK', '#$rank', Icons.emoji_events),
                        SizedBox(width: kIsWeb ? 12 : 12.w),
                        _rankingBadge(
                          'SEASON',
                          widget.card.season,
                          Icons.calendar_today,
                        ),
                        SizedBox(width: kIsWeb ? 12 : 12.w),
                      ],
                    ),
                  ),
                  // rating top-right
                  Positioned(
                    top: kIsWeb ? 16 : 16.h,
                    right: kIsWeb ? 16 : 16.w,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '$rating',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: kIsWeb ? 22 : 22.sp,
                            fontWeight: FontWeight.w800,
                          ),
                        ),
                        const SizedBox(height: 2),
                        const Text(
                          'DEX',
                          style: TextStyle(color: Colors.white70),
                        ),
                      ],
                    ),
                  ),
                  // flag top-left
                  Positioned(
                    top: kIsWeb ? 18 : 18.h,
                    left: kIsWeb ? 16 : 16.w,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(14),
                        border: Border.all(color: Colors.white24),
                      ),
                      child: Text(
                        widget.card.countryFlag,
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ),
                  // silhouette center - adjusted for better positioning with ranking badges
                  Positioned(
                    top: kIsWeb ? 85 : 85.h,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Opacity(
                        opacity: 0.6,
                        child: Icon(
                          Icons.person,
                          size: kIsWeb ? 90 : 90.sp,
                          color: Colors.black.withOpacity(0.35),
                        ),
                      ),
                    ),
                  ),
                  // bottom stats band
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Container(
                      height: kIsWeb ? 80 : 80.h,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            const Color(0xFF18204D).withOpacity(0.95),
                            const Color(0xFF431F5A).withOpacity(0.9),
                          ],
                        ),
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(kIsWeb ? 22 : 22.r),
                          bottomRight: Radius.circular(kIsWeb ? 22 : 22.r),
                        ),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: kIsWeb ? 18 : 18.w,
                          vertical: kIsWeb ? 10 : 10.h,
                        ),
                        child: Row(
                          children: [
                            _statBlock(
                              'DRI',
                              (70 + widget.card.age % 20).toString(),
                            ),
                            SizedBox(width: kIsWeb ? 18 : 18.w),
                            _statBlock(
                              'PASS',
                              (60 + widget.card.jerseyNumber % 30).toString(),
                            ),
                            const Spacer(),
                            _statBlock(
                              'SHO',
                              (65 + widget.card.age % 25).toString(),
                            ),
                            SizedBox(width: kIsWeb ? 18 : 18.w),
                            _statBlock(
                              'DEF',
                              (55 + widget.card.age % 30).toString(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _rankingBadge(String label, String value, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: kIsWeb ? 10 : 10.w, vertical: kIsWeb ? 6 : 6.h),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(kIsWeb ? 12 : 12.r),
        border: Border.all(
          color: Colors.white.withOpacity(0.25),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon,
              size: kIsWeb ? 14 : 14.sp, color: Colors.white.withOpacity(0.8)),
          SizedBox(width: kIsWeb ? 6 : 6.w),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Colors.white60,
                  fontSize: kIsWeb ? 9 : 9.sp,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.5,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: kIsWeb ? 11 : 11.sp,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.3,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _statBlock(String label, String value) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          value,
          style: TextStyle(
            color: Colors.white,
            fontSize: kIsWeb ? 18 : 18.sp,
            fontWeight: FontWeight.w800,
            letterSpacing: 0.5,
          ),
        ),
        SizedBox(height: kIsWeb ? 4 : 4.h),
        Text(
          label,
          style: TextStyle(
            color: Colors.white70,
            fontSize: kIsWeb ? 12 : 12.sp,
            fontWeight: FontWeight.w600,
            letterSpacing: 1.2,
          ),
        ),
      ],
    );
  }
}

class _CardPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.05)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // diagonal lines grid
    for (double y = 0; y < size.height; y += 18) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y + 24), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
