import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../profile/presentation/widgets/player_profile_card.dart';
import '../../../profile/presentation/widgets/player_rating_card.dart';
import '../../player_providers.dart';

class PlayerCardsDemoScreen extends ConsumerWidget {
  const PlayerCardsDemoScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Player Cards Demo'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Refresh all player data
              ref.invalidate(currentPlayerProfileProvider);
              ref.invalidate(currentPlayerRatingProvider);
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            const Text(
              'Player Profile & Rating Cards',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'These cards now display real data from the player system',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),

            // Current Player Info
            _buildCurrentPlayerInfo(ref),

            const SizedBox(height: 24),

            // Player Profile Card
            const Text(
              'Player Profile Card',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const PlayerProfileCard(),

            const SizedBox(height: 24),

            // Player Rating Card
            const Text(
              'Player Rating Card',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const PlayerRatingCard(),

            const SizedBox(height: 24),

            // Data Source Info
            _buildDataSourceInfo(),

            const SizedBox(height: 24),

            // Instructions
            _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentPlayerInfo(WidgetRef ref) {
    final currentPlayerIdAsync = ref.watch(currentPlayerIdProvider);
    final currentPlayerProfileAsync = ref.watch(currentPlayerProfileProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Current Player Data',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            currentPlayerIdAsync.when(
              data: (playerId) => Text('Player ID: $playerId'),
              loading: () => const Text('Loading player ID...'),
              error: (error, stack) => Text('Error: $error'),
            ),
            const SizedBox(height: 8),
            currentPlayerProfileAsync.when(
              data: (result) => result.fold(
                (error) => Text('Profile Error: ${error.message}'),
                (player) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Name: ${player.fullName}'),
                    if (player.primaryPosition != null)
                      Text('Position: ${player.primaryPosition}'),
                    if (player.averageRating != null)
                      Text(
                          'Rating: ${player.averageRating!.toStringAsFixed(1)}'),
                  ],
                ),
              ),
              loading: () => const Text('Loading profile...'),
              error: (error, stack) => Text('Profile Error: $error'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataSourceInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Data Source Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '• Cards are now connected to real player data',
              style: TextStyle(fontSize: 14),
            ),
            const Text(
              '• Data comes from the player API system',
              style: TextStyle(fontSize: 14),
            ),
            const Text(
              '• Current user ID is retrieved from auth system',
              style: TextStyle(fontSize: 14),
            ),
            const Text(
              '• Fallback to demo data if auth fails',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'How to Test',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '1. Tap on Player Profile Card to view detailed profile',
              style: TextStyle(fontSize: 14),
            ),
            const Text(
              '2. Tap on Player Rating Card to view self-assessment',
              style: TextStyle(fontSize: 14),
            ),
            const Text(
              '3. Use the refresh button to reload data',
              style: TextStyle(fontSize: 14),
            ),
            const Text(
              '4. Check the current player info above',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
