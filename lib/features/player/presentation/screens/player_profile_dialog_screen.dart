import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../../utils/color.dart';
import '../../player_providers.dart';
import '../../data/dto/player_request_models.dart';

class PlayerProfileDialogScreen extends ConsumerStatefulWidget {
  final String playerId;

  const PlayerProfileDialogScreen({
    super.key,
    required this.playerId,
  });

  @override
  ConsumerState<PlayerProfileDialogScreen> createState() =>
      _PlayerProfileDialogScreenState();
}

class _PlayerProfileDialogScreenState
    extends ConsumerState<PlayerProfileDialogScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();
  final _descriptionController = TextEditingController();

  DateTime? _selectedDate;
  String? _selectedPosition;
  String? _selectedFoot;
  bool _isSubmitting = false;

  final List<String> _positions = [
    'Forward',
    'Striker',
    'Winger',
    'Attacking Midfielder',
    'Central Midfielder',
    'Defensive Midfielder',
    'Full Back',
    'Center Back',
    'Goalkeeper'
  ];

  final List<String> _feet = ['Left', 'Right', 'Both'];

  @override
  void initState() {
    super.initState();
    _loadPlayerData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadPlayerData() async {
    try {
      final useCase = ref.read(getPlayerProfileUseCaseProvider);
      final result = await useCase(widget.playerId);

      result.fold(
        (error) => null,
        (player) {
          setState(() {
            _nameController.text = player.fullName;
            _heightController.text = player.heightCm?.toString() ?? '';
            _weightController.text = player.weightKg?.toString() ?? '';
            _descriptionController.text = player.description ?? '';
            _selectedDate = player.dateOfBirth;
            _selectedPosition = player.primaryPosition;
            _selectedFoot = player.activeFoot;
          });
        },
      );
    } catch (e) {
      // Handle error silently for now
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Scaffold(
      backgroundColor: primaryColor,
      appBar: AppBar(
        title: const Text(
          'Edit Profile',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            fontSize: 18,
            color: Colors.white,
          ),
        ),
        backgroundColor: primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: NextSportzTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile Form
                    _buildProfileForm(),

                    const SizedBox(height: 32),

                    // Save Button
                    _buildSaveButton(),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileForm() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Personal Information Section
        _buildSectionHeader('Physical Attributes', Icons.person_outline),
        const SizedBox(height: 16),

        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: secondaryColor.withOpacity(0.8),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: accentColor.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              // Height and Weight Row
              Row(
                children: [
                  Expanded(
                    child: _buildTextField(
                      controller: _heightController,
                      label: 'Height (cm)',
                      icon: Icons.height,
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final height = int.tryParse(value);
                          if (height == null || height < 100 || height > 250) {
                            return 'Enter valid height (100-250 cm)';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildTextField(
                      controller: _weightController,
                      label: 'Weight (kg)',
                      icon: Icons.monitor_weight,
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final weight = int.tryParse(value);
                          if (weight == null || weight < 30 || weight > 200) {
                            return 'Enter valid weight (30-200 kg)';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Game Information Section
        _buildSectionHeader('Game Information', Icons.sports_soccer),
        const SizedBox(height: 16),

        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: secondaryColor.withOpacity(0.8),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: accentColor.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              // Position Field
              _buildDropdownField(
                label: 'Primary Position',
                value: _selectedPosition,
                items: _positions,
                onChanged: (value) {
                  setState(() {
                    _selectedPosition = value;
                  });
                },
                icon: Icons.sports_soccer,
              ),

              const SizedBox(height: 16),

              // Preferred Foot Field
              _buildDropdownField(
                label: 'Preferred Foot',
                value: _selectedFoot,
                items: _feet,
                onChanged: (value) {
                  setState(() {
                    _selectedFoot = value;
                  });
                },
                icon: Icons.directions_walk,
              ),

              const SizedBox(height: 16),

              // Description Field
              _buildTextField(
                controller: _descriptionController,
                label: 'Description (Optional)',
                icon: Icons.description,
                maxLines: 3,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: accentColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: 16,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontFamily: 'Gilroy_Bold',
            fontSize: 18,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final lightBlue =
        isDark ? NextSportzTheme.darkBlue : NextSportzTheme.lightBlue;
    final mediumBlue =
        isDark ? NextSportzTheme.darkMediumBlue : NextSportzTheme.mediumBlue;
    final greyColor = NextSportzTheme.getGreyColor(isDark);
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      style: const TextStyle(
        color: Colors.white,
        fontFamily: 'Gilroy_Medium',
        fontSize: 16,
      ),
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(
          color: greyColor,
          fontFamily: 'Gilroy_Medium',
          fontSize: 14,
        ),
        prefixIcon: Icon(icon, color: accentColor, size: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: lightBlue),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: lightBlue),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: accentColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red),
        ),
        filled: true,
        fillColor: mediumBlue,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required Function(String?) onChanged,
    required IconData icon,
  }) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final lightBlue =
        isDark ? NextSportzTheme.darkBlue : NextSportzTheme.lightBlue;
    final mediumBlue =
        isDark ? NextSportzTheme.darkMediumBlue : NextSportzTheme.mediumBlue;
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: lightBlue),
        borderRadius: BorderRadius.circular(12),
        color: mediumBlue,
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          hint: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Icon(icon, color: accentColor, size: 20),
                const SizedBox(width: 12),
                Text(
                  label,
                  style: TextStyle(
                    color: grey,
                    fontFamily: 'Gilroy_Medium',
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          dropdownColor: lightblue,
          style: const TextStyle(
            color: Colors.white,
            fontFamily: 'Gilroy_Medium',
            fontSize: 16,
          ),
          isExpanded: true,
          icon: Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Icon(Icons.keyboard_arrow_down, color: accentColor),
          ),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(item),
              ),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: NextSportzTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
      ),
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _saveProfile,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
        child: _isSubmitting
            ? const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Save Profile',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  fontSize: 18,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ??
          DateTime.now().subtract(const Duration(days: 6570)), // 18 years ago
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final useCase = ref.read(updatePlayerProfileUseCaseProvider);
      await useCase(
        playerId: widget.playerId,
        fullName: _nameController.text,
        dateOfBirth: _selectedDate,
        heightCm: _heightController.text.isNotEmpty
            ? int.parse(_heightController.text)
            : null,
        weightKg: _weightController.text.isNotEmpty
            ? int.parse(_weightController.text)
            : null,
        description: _descriptionController.text.isNotEmpty
            ? _descriptionController.text
            : null,
        activeFoot:
            _selectedFoot != null ? _getActiveFootEnum(_selectedFoot!) : null,
        primaryPosition: _selectedPosition,
      );

      // Refresh data
      ref.invalidate(playerProfileProvider(widget.playerId));
      ref.invalidate(currentPlayerProfileProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Close the dialog after successful update
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update profile: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  ActiveFoot _getActiveFootEnum(String foot) {
    switch (foot.toLowerCase()) {
      case 'left':
        return ActiveFoot.left;
      case 'right':
        return ActiveFoot.right;
      case 'both':
        return ActiveFoot.both;
      default:
        return ActiveFoot.right;
    }
  }
}
