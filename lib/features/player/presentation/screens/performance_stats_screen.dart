import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math' as math;
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../../utils/color.dart';

class PerformanceStatsScreen extends ConsumerStatefulWidget {
  const PerformanceStatsScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<PerformanceStatsScreen> createState() =>
      _PerformanceStatsScreenState();
}

class _PerformanceStatsScreenState extends ConsumerState<PerformanceStatsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Scaffold(
      backgroundColor: primaryColor,
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Performance Stats',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: () {
              // TODO: Implement share functionality
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Header Section with Player Info and Score
            _buildPlayerHeader(),

            const SizedBox(height: 32),

            // Radar Chart Section
            _buildRadarChart(),

            const SizedBox(height: 32),

            // Performance Metrics Cards
            _buildPerformanceMetrics(),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerHeader() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            accentColor.withOpacity(0.8),
            accentColor.withOpacity(0.6),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: accentColor.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // Player Photo
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 3),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ClipOval(
              child: Image.asset(
                "assets/images/profile.png",
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                    ),
                    child: Icon(
                      Icons.person,
                      size: 40,
                      color: accentColor,
                    ),
                  );
                },
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Player Name
          const Text(
            'Prasanna',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 24),

          // Score Section
          const Text(
            'Score',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white,
              fontSize: 16,
            ),
          ),

          const SizedBox(height: 8),

          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Text(
                '${(15642 * _animation.value).toInt()}',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 2,
                ),
              );
            },
          ),

          const SizedBox(height: 16),

          // Date and Duration
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Column(
                children: [
                  Text(
                    '7/11/2025 6:22 am',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Exercise Duration: 100.93min',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRadarChart() {
    final List<RadarDataPoint> radarData = [
      RadarDataPoint('Explosiveness', 72),
      RadarDataPoint('Dribbling', 77),
      RadarDataPoint('Stamina', 80),
      RadarDataPoint('Passing & Shooting', 74),
      RadarDataPoint('Speed', 57),
      RadarDataPoint('Activity', 82),
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: lightblue.withOpacity(0.3),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        children: [
          const Text(
            'Performance Analysis',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 300,
            width: 300,
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return CustomPaint(
                  painter: RadarChartPainter(radarData, _animation.value),
                  size: const Size(300, 300),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceMetrics() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    final List<PerformanceMetric> metrics = [
      PerformanceMetric(
        icon: Icons.sports_soccer,
        value: '187.66',
        unit: 's',
        label: 'Dribbling Time',
        color: accentColor,
      ),
      PerformanceMetric(
        icon: Icons.sports_handball,
        value: '175',
        unit: '',
        label: 'Passes & Shots',
        color: accentColor,
      ),
      PerformanceMetric(
        icon: Icons.speed,
        value: '24.13',
        unit: 'km/h',
        label: 'Max Speed',
        color: accentColor,
      ),
      PerformanceMetric(
        icon: Icons.timeline,
        value: '6.14',
        unit: 'km',
        label: 'Moving Distance',
        color: accentColor,
      ),
      PerformanceMetric(
        icon: Icons.directions_run,
        value: '88',
        unit: '',
        label: 'Total Sprints',
        color: Colors.orange,
      ),
      PerformanceMetric(
        icon: Icons.local_fire_department,
        value: '381.62',
        unit: 'kcal',
        label: 'Calories Burned',
        color: accentColor,
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.1,
      ),
      itemCount: metrics.length,
      itemBuilder: (context, index) {
        final metric = metrics[index];
        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Transform.scale(
              scale: 0.8 + (0.2 * _animation.value),
              child: _buildMetricCard(metric, index),
            );
          },
        );
      },
    );
  }

  Widget _buildMetricCard(PerformanceMetric metric, int index) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: lightblue.withOpacity(0.4),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
        boxShadow: [
          BoxShadow(
            color: metric.color.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: metric.color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(metric.icon, color: metric.color, size: 24),
          ),

          const SizedBox(height: 12),

          // Value
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                metric.value,
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (metric.unit.isNotEmpty) ...[
                const SizedBox(width: 2),
                Text(
                  metric.unit,
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 8),

          // Label
          Text(
            metric.label,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.8),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}

class RadarDataPoint {
  final String label;
  final double value;

  RadarDataPoint(this.label, this.value);
}

class PerformanceMetric {
  final IconData icon;
  final String value;
  final String unit;
  final String label;
  final Color color;

  PerformanceMetric({
    required this.icon,
    required this.value,
    required this.unit,
    required this.label,
    required this.color,
  });
}

class RadarChartPainter extends CustomPainter {
  final List<RadarDataPoint> data;
  final double animationValue;

  RadarChartPainter(this.data, this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 40;

    // Draw radar background
    _drawRadarBackground(canvas, center, radius);

    // Draw data
    _drawRadarData(canvas, center, radius);

    // Draw labels
    _drawLabels(canvas, center, radius);
  }

  void _drawRadarBackground(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // Draw concentric circles
    for (int i = 1; i <= 5; i++) {
      canvas.drawCircle(center, radius * i / 5, paint);
    }

    // Draw axis lines
    for (int i = 0; i < data.length; i++) {
      final angle = (i * 2 * math.pi / data.length) - math.pi / 2;
      final endPoint = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle),
      );
      canvas.drawLine(center, endPoint, paint);
    }
  }

  void _drawRadarData(Canvas canvas, Offset center, double radius) {
    final path = Path();
    final paint = Paint()
      ..color = Colors.green.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    final strokePaint = Paint()
      ..color = Colors.green
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    for (int i = 0; i < data.length; i++) {
      final angle = (i * 2 * math.pi / data.length) - math.pi / 2;
      final value = data[i].value * animationValue / 100;
      final point = Offset(
        center.dx + radius * value * math.cos(angle),
        center.dy + radius * value * math.sin(angle),
      );

      if (i == 0) {
        path.moveTo(point.dx, point.dy);
      } else {
        path.lineTo(point.dx, point.dy);
      }

      // Draw data points
      canvas.drawCircle(point, 4, strokePaint);
    }

    path.close();
    canvas.drawPath(path, paint);
    canvas.drawPath(path, strokePaint);
  }

  void _drawLabels(Canvas canvas, Offset center, double radius) {
    for (int i = 0; i < data.length; i++) {
      final angle = (i * 2 * math.pi / data.length) - math.pi / 2;
      final labelRadius = radius + 20;
      final labelPoint = Offset(
        center.dx + labelRadius * math.cos(angle),
        center.dy + labelRadius * math.sin(angle),
      );

      final textPainter = TextPainter(
        text: TextSpan(
          text: '${data[i].label}\n(${data[i].value.toInt()})',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontFamily: 'Gilroy_Medium',
          ),
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          labelPoint.dx - textPainter.width / 2,
          labelPoint.dy - textPainter.height / 2,
        ),
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
