import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../player_providers.dart';
import '../../domain/entities/player.dart';
import '../../domain/entities/player_rating.dart';
import '../widgets/player_profile_widget.dart';
import '../widgets/self_assessment_widget.dart';

class PlayerProfileScreen extends ConsumerStatefulWidget {
  final String playerId;

  const PlayerProfileScreen({
    super.key,
    required this.playerId,
  });

  @override
  ConsumerState<PlayerProfileScreen> createState() =>
      _PlayerProfileScreenState();
}

class _PlayerProfileScreenState extends ConsumerState<PlayerProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Player Profile'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Profile', icon: Icon(Icons.person)),
            Tab(text: 'Self Assessment', icon: Icon(Icons.assessment)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(playerProfileProvider(widget.playerId));
              ref.invalidate(playerRatingProvider(widget.playerId));
            },
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Profile Tab
          PlayerProfileWidget(playerId: widget.playerId),
          // Self Assessment Tab
          SelfAssessmentWidget(playerId: widget.playerId),
        ],
      ),
    );
  }
}
