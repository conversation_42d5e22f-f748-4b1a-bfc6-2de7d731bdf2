import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../../core/widgets/responsive_wrapper.dart';
import '../../data/datasources/public_player_mock_datasource.dart';
import '../../domain/entities/public_player_profile.dart';
import '../../player_providers.dart';
import '../widgets/madsports_card_widget.dart';
import '../../domain/entities/mad_sports_card.dart';

class PublicPlayerProfileScreen extends ConsumerStatefulWidget {
  final String? playerId;

  const PublicPlayerProfileScreen({Key? key, this.playerId}) : super(key: key);

  @override
  ConsumerState<PublicPlayerProfileScreen> createState() =>
      _PublicPlayerProfileScreenState();
}

class _PublicPlayerProfileScreenState
    extends ConsumerState<PublicPlayerProfileScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);

    // Use the actual player ID or current user's player ID
    final playerId = widget.playerId ?? 'current_user';

    // Watch the player profile from API
    final playerProfileAsync = ref.watch(playerProfileProvider(playerId));

    return Scaffold(
      backgroundColor: primaryColor,
      body: playerProfileAsync.when(
        data: (playerData) {
          // Convert API data to PublicPlayerProfile for display
          return FutureBuilder<PublicPlayerProfile>(
            future: _convertToPublicProfile(playerData, playerId),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return _buildLoadingState();
              }
              if (snapshot.hasError) {
                return _buildErrorState();
              }
              final player = snapshot.data!;
              return _buildProfileContent(player);
            },
          );
        },
        loading: () => _buildLoadingState(),
        error: (error, stack) => _buildErrorState(),
      ),
    );
  }

  Future<PublicPlayerProfile> _convertToPublicProfile(
    dynamic playerData,
    String playerId,
  ) async {
    // For now, use mock data but in future this will convert API response
    // to PublicPlayerProfile format
    return PublicPlayerMockDataSource().getPublicPlayerProfile(playerId);
  }

  Widget _buildLoadingState() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(accentColor),
          ),
          const SizedBox(height: 16),
          const Text(
            'Loading player profile...',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 16,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 64),
          const SizedBox(height: 16),
          const Text(
            'Failed to load player profile',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontFamily: 'Gilroy_Bold',
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Please try again later',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileContent(PublicPlayerProfile player) {
    return SingleChildScrollView(
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Column(
                children: [
                  // Back button overlay
                  _buildBackButton(),
                  // MadSportz Card at the top
                  _buildMadSportzCard(player),
                  _buildPlayerInfo(player),
                  _buildStatsSection(player),
                  _buildAchievementsSection(player),
                  _buildSkillsSection(player),
                  _buildContactSection(player),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBackButton() {
    return Container(
      margin: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 16,
        left: 16,
        right: 16,
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              print('Back button tapped'); // Debug
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              } else {
                context.go('/home'); // Fallback to home
              }
            },
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.3),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              // TODO: Implement share functionality
            },
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.3),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(Icons.share, color: Colors.white, size: 20),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMadSportzCard(PublicPlayerProfile player) {
    final card = MadSportsCard(
      playerId: player.id,
      playerName: player.name,
      age: player.age,
      position: _getPositionAbbreviation(player.position),
      countryFlag: '🇳🇵', // Default to Nepal, can be made dynamic
      jerseyNumber: player.jerseyNumber,
      season: '24/25', // Can be made dynamic
      rarity: _getRarityFromRating(player.stats.averageRating),
      profileImageUrl: player.photoUrl ?? '',
      shareableText:
          'Check out ${player.name}\'s NextSportz player card! #NextSportz',
    );

    return Container(
      margin: const EdgeInsets.all(16),
      child: MadSportsCardWidget(card: card),
    );
  }

  String _getPositionAbbreviation(String position) {
    switch (position.toLowerCase()) {
      case 'forward':
        return 'FW';
      case 'midfielder':
        return 'MF';
      case 'defender':
        return 'DF';
      case 'goalkeeper':
        return 'GK';
      default:
        return 'FW';
    }
  }

  String _getRarityFromRating(double rating) {
    if (rating >= 9.0) return 'Gold';
    if (rating >= 8.0) return 'Silver';
    return 'Bronze';
  }

  Widget _buildPlayerInfo(PublicPlayerProfile player) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: secondaryColor.withOpacity(0.7),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'About',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            player.bio,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.8),
              fontSize: 14,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'Experience',
                  '${player.experience} years',
                ),
              ),
              Expanded(
                child: _buildInfoItem('Preferred Foot', player.preferredFoot),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(child: _buildInfoItem('Weight', '${player.weight}kg')),
              Expanded(
                child: _buildInfoItem(
                  'Jersey Number',
                  '#${player.jerseyNumber}',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: Colors.white.withOpacity(0.6),
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildStatsSection(PublicPlayerProfile player) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: secondaryColor.withOpacity(0.7),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.bar_chart, color: accentColor, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Performance Stats',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Matches',
                  player.stats.matchesPlayed.toString(),
                  Icons.sports_soccer,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Goals',
                  player.stats.goals.toString(),
                  Icons.sports_score,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Tournaments',
                  player.stats.tournamentsPlayed.toString(),
                  Icons.emoji_events,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Rating',
                  player.stats.averageRating.toStringAsFixed(1),
                  Icons.star,
                  Colors.amber,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: color,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.7),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementsSection(PublicPlayerProfile player) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: secondaryColor.withOpacity(0.7),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.emoji_events, color: accentColor, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Achievements',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...player.achievements
              .take(3)
              .map((achievement) => _buildAchievementItem(achievement))
              .toList(),
        ],
      ),
    );
  }

  Widget _buildAchievementItem(Achievement achievement) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: Text(
                achievement.icon,
                style: const TextStyle(fontSize: 20),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  achievement.title,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  achievement.description,
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkillsSection(PublicPlayerProfile player) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: secondaryColor.withOpacity(0.7),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.psychology, color: accentColor, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Skills & Ratings',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: accentColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  'Overall: ${player.skills.overall.toStringAsFixed(1)}',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: accentColor,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildSkillBar('Pace', player.skills.pace, Colors.red),
          _buildSkillBar('Shooting', player.skills.shooting, Colors.orange),
          _buildSkillBar('Passing', player.skills.passing, Colors.blue),
          _buildSkillBar('Dribbling', player.skills.dribbling, Colors.purple),
          _buildSkillBar('Defense', player.skills.defense, Colors.green),
          _buildSkillBar(
            'Physicality',
            player.skills.physicality,
            Colors.brown,
          ),
        ],
      ),
    );
  }

  Widget _buildSkillBar(String skill, int value, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                skill,
                style: const TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
              Text(
                value.toString(),
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: color,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          LinearProgressIndicator(
            value: value / 100.0,
            backgroundColor: Colors.white.withOpacity(0.1),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 6,
          ),
        ],
      ),
    );
  }

  Widget _buildContactSection(PublicPlayerProfile player) {
    if (player.contactInfo == null || !player.contactInfo!.isPublic) {
      return const SizedBox.shrink();
    }

    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: secondaryColor.withOpacity(0.7),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.contact_page, color: accentColor, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Connect',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (player.contactInfo!.email != null)
            _buildContactItem(
              Icons.email,
              'Email',
              player.contactInfo!.email!,
              Colors.red,
            ),
          if (player.contactInfo!.phone != null)
            _buildContactItem(
              Icons.phone,
              'Phone',
              player.contactInfo!.phone!,
              Colors.green,
            ),
          if (player.contactInfo!.instagram != null)
            _buildContactItem(
              Icons.camera_alt,
              'Instagram',
              player.contactInfo!.instagram!,
              Colors.purple,
            ),
          if (player.contactInfo!.twitter != null)
            _buildContactItem(
              Icons.alternate_email,
              'Twitter',
              player.contactInfo!.twitter!,
              Colors.blue,
            ),
        ],
      ),
    );
  }

  Widget _buildContactItem(
    IconData icon,
    String label,
    String value,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(18),
            ),
            child: Icon(icon, color: color, size: 18),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 12,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Custom painter for background pattern
class _PatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white.withOpacity(0.05)
          ..strokeWidth = 1;

    // Draw diagonal lines pattern
    for (double i = -size.height; i < size.width + size.height; i += 30) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
