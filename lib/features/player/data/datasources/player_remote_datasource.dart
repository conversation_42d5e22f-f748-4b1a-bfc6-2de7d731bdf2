import 'package:nextsportz_v2/core/networking/api_client.dart';
import 'package:nextsportz_v2/core/networking/api_const.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/core/models/paginated_response.dart';
import 'package:nextsportz_v2/core/networking/exception.dart';
import 'package:dio/dio.dart';

import '../dto/player_request_models.dart';
import '../dto/player_response_models.dart';
import '../dto/player_search_item_dto.dart';
import 'player_datasource.dart';

/// Remote data source implementation for player operations
class PlayerRemoteDataSource implements PlayerDatasource {
  final ApiClient apiClient;

  PlayerRemoteDataSource(this.apiClient);

  @override
  Future<PlayerProfileResponse> getPlayerProfile(String playerId) async {
    final endpoint = ApiConst.playerProfileEndpoint.replaceAll(
      '{playerId}',
      playerId,
    );
    final response = await apiClient.get(endpoint);
    return _handlePlayerProfileResponse(response);
  }

  @override
  Future<PlayerProfileResponse> updatePlayerProfile(
    String playerId,
    UpdatePlayerProfileRequest request,
  ) async {
    final endpoint = ApiConst.playerProfileEndpoint.replaceAll(
      '{playerId}',
      playerId,
    );
    final response = await apiClient.put(endpoint, data: request.toJson());
    return _handlePlayerProfileResponse(response);
  }

  @override
  Future<PlayerRatingResponse> getPlayerRating(String playerId) async {
    final endpoint = ApiConst.playerRatingEndpoint.replaceAll(
      '{playerId}',
      playerId,
    );
    final response = await apiClient.get(endpoint);
    return _handlePlayerRatingResponse(response);
  }

  @override
  Future<void> submitRating(
    String playerId,
    SubmitRatingRequest request,
  ) async {
    final endpoint = ApiConst.playerRatingEndpoint.replaceAll(
      '{playerId}',
      playerId,
    );
    await apiClient.post(endpoint, data: request.toJson());
  }

  PlayerProfileResponse _handlePlayerProfileResponse(
    Map<String, dynamic> response,
  ) {
    // Handle the case where player data is nested in 'data' field
    Map<String, dynamic> playerData = response;
    if (response['data'] != null && response['data'] is Map<String, dynamic>) {
      playerData = response['data'] as Map<String, dynamic>;
    }

    return PlayerProfileResponse.fromJson(playerData);
  }

  PlayerRatingResponse _handlePlayerRatingResponse(
    Map<String, dynamic> response,
  ) {
    // Handle the case where rating data is nested in 'data' field
    Map<String, dynamic> ratingData = response;
    if (response['data'] != null && response['data'] is Map<String, dynamic>) {
      ratingData = response['data'] as Map<String, dynamic>;
    }

    return PlayerRatingResponse.fromJson(ratingData);
  }

  @override
  Future<PaginatedResponse<PlayerSearchItemDto>> searchPlayers({
    String? query,
    int? page,
    int? pageSize,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        if (query != null && query.isNotEmpty) 'search': query,
        if (page != null) 'page': page,
        if (pageSize != null) 'pageSize': pageSize,
      };

      final response = await apiClient.get(
        ApiConst.playersEndpoint,
        query: queryParams,
      );

      return PaginatedResponse<PlayerSearchItemDto>.fromJson(
        response,
        (json) => PlayerSearchItemDto.fromJson(json),
      );
    } catch (e) {
      if (e is DioExceptionHandle) {
        rethrow;
      }
      if (e is DioException) {
        throw DioExceptionHandle.fromDioError(e);
      }
      throw Exception('Failed to search players: ${e.toString()}');
    }
  }
}
