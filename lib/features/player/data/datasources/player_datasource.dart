import '../dto/player_request_models.dart';
import '../dto/player_response_models.dart';
import '../dto/player_search_item_dto.dart';
import '../../../../core/models/paginated_response.dart';
import '../../../../core/networking/app_error.dart';

/// Abstract class defining the contract for player data operations
abstract class PlayerDatasource {
  /// Get player profile by ID
  Future<PlayerProfileResponse> getPlayerProfile(String playerId);

  /// Update player profile
  Future<PlayerProfileResponse> updatePlayerProfile(
    String playerId,
    UpdatePlayerProfileRequest request,
  );

  /// Get player rating breakdown and recent votes
  Future<PlayerRatingResponse> getPlayerRating(String playerId);

  /// Submit rating for a player
  Future<void> submitRating(String playerId, SubmitRatingRequest request);

  /// Search players with pagination
  Future<PaginatedResponse<PlayerSearchItemDto>> searchPlayers({
    String? query,
    int? page,
    int? pageSize,
  });
}
