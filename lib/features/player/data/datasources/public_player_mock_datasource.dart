import '../../domain/entities/public_player_profile.dart';

class PublicPlayerMockDataSource {
  Future<PublicPlayerProfile> getPublicPlayerProfile(String playerId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1000));

    // Return mock data based on playerId
    if (playerId == 'current_user') {
      return _getCurrentUserProfile();
    }

    return _getMockPlayerProfile(playerId);
  }

  PublicPlayerProfile _getCurrentUserProfile() {
    return PublicPlayerProfile(
      id: 'current_user',
      name: '<PERSON>',
      position: 'Forward',
      team: 'NextSportz FC',
      age: 24,
      height: 175,
      weight: 72,
      photoUrl: 'https://images.unsplash.com/photo-1502877338535-766e1452684a',
      bio:
          'Passionate footballer with a love for the beautiful game. Always striving to improve and help my team achieve greatness. Known for my speed, technical skills, and ability to score crucial goals.',
      experience: 8,
      preferredFoot: 'Right',
      jerseyNumber: 9,
      stats: PlayerStats(
        matchesPlayed: 45,
        goals: 23,
        assists: 12,
        tournamentsPlayed: 8,
        averageRating: 8.7,
        cleanSheets: 0,
        yellowCards: 3,
        redCards: 0,
        passAccuracy: 85.5,
        shotsOnTarget: 67,
      ),
      achievements: [
        Achievement(
          title: 'Top Scorer',
          description: 'Leading goal scorer in the league',
          icon: '⚽',
          dateAchieved: DateTime(2024, 1, 15),
          category: 'Performance',
        ),
        Achievement(
          title: 'Player of the Month',
          description: 'Outstanding performance in January 2024',
          icon: '🏆',
          dateAchieved: DateTime(2024, 1, 31),
          category: 'Recognition',
        ),
        Achievement(
          title: 'Hat-trick Hero',
          description: 'Scored 3 goals in a single match',
          icon: '🎯',
          dateAchieved: DateTime(2023, 12, 10),
          category: 'Milestone',
        ),
      ],
      skills: PlayerSkills(
        pace: 88,
        shooting: 85,
        passing: 82,
        dribbling: 87,
        defense: 65,
        physicality: 78,
      ),
      contactInfo: ContactInfo(
        email: '<EMAIL>',
        phone: '+977-9841234567',
        instagram: '@alexsubedi9',
        twitter: '@alexsubedi_fc',
        linkedin: 'alex-subedi-footballer',
        isPublic: true,
      ),
      socialLinks: [
        'https://instagram.com/alexsubedi9',
        'https://twitter.com/alexsubedi_fc',
        'https://linkedin.com/in/alex-subedi-footballer',
      ],
      recentPerformances: [
        RecentPerformance(
          matchTitle: 'NextSportz FC vs Kathmandu United',
          opponent: 'Kathmandu United',
          date: DateTime(2024, 1, 20),
          goals: 2,
          assists: 1,
          rating: 9.2,
          result: 'W 3-1',
        ),
        RecentPerformance(
          matchTitle: 'Pokhara FC vs NextSportz FC',
          opponent: 'Pokhara FC',
          date: DateTime(2024, 1, 15),
          goals: 1,
          assists: 0,
          rating: 8.5,
          result: 'W 2-0',
        ),
        RecentPerformance(
          matchTitle: 'NextSportz FC vs Lalitpur City',
          opponent: 'Lalitpur City',
          date: DateTime(2024, 1, 10),
          goals: 0,
          assists: 2,
          rating: 8.8,
          result: 'D 2-2',
        ),
      ],
    );
  }

  PublicPlayerProfile _getMockPlayerProfile(String playerId) {
    return PublicPlayerProfile(
      id: 'player_001',
      name: 'Lionel Messi',
      position: 'Forward',
      team: 'Inter Miami CF',
      age: 37,
      height: 170,
      weight: 72,
      photoUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b',
      bio:
          'Argentine professional footballer widely regarded as one of the greatest players of all time. Known for exceptional dribbling skills, vision, and goal-scoring ability.',
      experience: 20,
      preferredFoot: 'Left',
      jerseyNumber: 10,
      stats: PlayerStats(
        matchesPlayed: 1069,
        goals: 815,
        assists: 377,
        tournamentsPlayed: 25,
        averageRating: 9.5,
        cleanSheets: 0,
        yellowCards: 89,
        redCards: 3,
        passAccuracy: 88.2,
        shotsOnTarget: 1205,
      ),
      achievements: [
        Achievement(
          title: 'Ballon d\'Or Winner',
          description: 'Won the prestigious award 8 times',
          icon: '🏆',
          dateAchieved: DateTime(2023, 10, 30),
          category: 'Recognition',
        ),
        Achievement(
          title: 'World Cup Winner',
          description: 'Led Argentina to World Cup victory',
          icon: '🌍',
          dateAchieved: DateTime(2022, 12, 18),
          category: 'Tournament',
        ),
        Achievement(
          title: 'Champions League Winner',
          description: 'Won UEFA Champions League 4 times',
          icon: '🏆',
          dateAchieved: DateTime(2015, 6, 6),
          category: 'Tournament',
        ),
      ],
      skills: PlayerSkills(
        pace: 85,
        shooting: 95,
        passing: 98,
        dribbling: 99,
        defense: 40,
        physicality: 78,
      ),
      contactInfo: ContactInfo(
        email: null,
        phone: null,
        instagram: '@leomessi',
        twitter: null,
        linkedin: null,
        isPublic: false,
      ),
      socialLinks: ['https://instagram.com/leomessi'],
      recentPerformances: [
        RecentPerformance(
          matchTitle: 'Inter Miami vs Orlando City',
          opponent: 'Orlando City',
          date: DateTime(2024, 1, 25),
          goals: 1,
          assists: 2,
          rating: 9.0,
          result: 'W 3-1',
        ),
        RecentPerformance(
          matchTitle: 'Atlanta United vs Inter Miami',
          opponent: 'Atlanta United',
          date: DateTime(2024, 1, 18),
          goals: 2,
          assists: 0,
          rating: 9.5,
          result: 'W 4-2',
        ),
      ],
    );
  }
}
