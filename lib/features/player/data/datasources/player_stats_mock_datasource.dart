import 'dart:math';

/// Mock data source for player statistics
class PlayerStatsMockDataSource {
  final Random _random = Random();

  /// Get player challenge statistics
  Future<Map<String, dynamic>> getPlayerChallengeStats(String playerId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));

    // Generate realistic mock data based on player ID
    final baseStats = _getBaseStatsForPlayer(playerId);

    return {
      'totalChallenges': baseStats['totalChallenges'],
      'challengesWon': baseStats['challengesWon'],
      'challengesLost': baseStats['challengesLost'],
      'winRate': baseStats['winRate'],
      'currentStreak': baseStats['currentStreak'],
      'longestStreak': baseStats['longestStreak'],
      'totalWagerWon': baseStats['totalWagerWon'],
      'totalWagerLost': baseStats['totalWagerLost'],
      'averageWagerAmount': baseStats['averageWagerAmount'],
      'highStakesWins': baseStats['highStakesWins'],
      'gamesPlayed': baseStats['gamesPlayed'],
      'gamesWon': baseStats['gamesWon'],
      'gamesLost': baseStats['gamesLost'],
      'gameWinRate': baseStats['gameWinRate'],
      'lastActive':
          DateTime.now().subtract(Duration(hours: _random.nextInt(48))),
    };
  }

  Map<String, dynamic> _getBaseStatsForPlayer(String playerId) {
    switch (playerId) {
      case 'player_001': // Messi
        return {
          'totalChallenges': 156,
          'challengesWon': 142,
          'challengesLost': 14,
          'winRate': 0.91,
          'currentStreak': 8,
          'longestStreak': 23,
          'totalWagerWon': 28400,
          'totalWagerLost': 2800,
          'averageWagerAmount': 200,
          'highStakesWins': 15,
          'gamesPlayed': 1028,
          'gamesWon': 934,
          'gamesLost': 94,
          'gameWinRate': 0.91,
        };
      case 'player_002': // Ronaldo
        return {
          'totalChallenges': 187,
          'challengesWon': 168,
          'challengesLost': 19,
          'winRate': 0.90,
          'currentStreak': 5,
          'longestStreak': 18,
          'totalWagerWon': 33600,
          'totalWagerLost': 3800,
          'averageWagerAmount': 200,
          'highStakesWins': 22,
          'gamesPlayed': 1187,
          'gamesWon': 1068,
          'gamesLost': 119,
          'gameWinRate': 0.90,
        };
      case 'player_003': // De Bruyne
        return {
          'totalChallenges': 98,
          'challengesWon': 82,
          'challengesLost': 16,
          'winRate': 0.84,
          'currentStreak': 3,
          'longestStreak': 12,
          'totalWagerWon': 16400,
          'totalWagerLost': 3200,
          'averageWagerAmount': 200,
          'highStakesWins': 8,
          'gamesPlayed': 756,
          'gamesWon': 635,
          'gamesLost': 121,
          'gameWinRate': 0.84,
        };
      case 'player_004': // Van Dijk
        return {
          'totalChallenges': 67,
          'challengesWon': 54,
          'challengesLost': 13,
          'winRate': 0.81,
          'currentStreak': 2,
          'longestStreak': 9,
          'totalWagerWon': 10800,
          'totalWagerLost': 2600,
          'averageWagerAmount': 200,
          'highStakesWins': 6,
          'gamesPlayed': 523,
          'gamesWon': 424,
          'gamesLost': 99,
          'gameWinRate': 0.81,
        };
      case 'player_005': // Haaland
        return {
          'totalChallenges': 89,
          'challengesWon': 76,
          'challengesLost': 13,
          'winRate': 0.85,
          'currentStreak': 6,
          'longestStreak': 14,
          'totalWagerWon': 15200,
          'totalWagerLost': 2600,
          'averageWagerAmount': 200,
          'highStakesWins': 11,
          'gamesPlayed': 634,
          'gamesWon': 539,
          'gamesLost': 95,
          'gameWinRate': 0.85,
        };
      default:
        // Generate random stats for unknown players
        final totalChallenges = 20 + _random.nextInt(100);
        final challengesWon =
            (totalChallenges * (0.6 + _random.nextDouble() * 0.3)).round();
        final challengesLost = totalChallenges - challengesWon;
        final winRate = challengesWon / totalChallenges;

        return {
          'totalChallenges': totalChallenges,
          'challengesWon': challengesWon,
          'challengesLost': challengesLost,
          'winRate': winRate,
          'currentStreak': _random.nextInt(8),
          'longestStreak': 3 + _random.nextInt(15),
          'totalWagerWon': challengesWon * (150 + _random.nextInt(100)),
          'totalWagerLost': challengesLost * (150 + _random.nextInt(100)),
          'averageWagerAmount': 150 + _random.nextInt(100),
          'highStakesWins': _random.nextInt(challengesWon ~/ 3),
          'gamesPlayed': totalChallenges * (3 + _random.nextInt(5)),
          'gamesWon':
              (totalChallenges * (3 + _random.nextInt(5)) * winRate).round(),
          'gamesLost':
              (totalChallenges * (3 + _random.nextInt(5)) * (1 - winRate))
                  .round(),
          'gameWinRate': winRate,
        };
    }
  }
}
