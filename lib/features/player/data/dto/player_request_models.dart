/// Player request models for the player feature

/// Active foot enum
enum ActiveFoot {
  left('Left'),
  right('Right'),
  both('Both');

  const ActiveFoot(this.value);
  final String value;
}

/// Rating category enum
enum RatingCategory {
  self('Self'),
  public('Public'),
  opponent('Opponent'),
  teammate('Teammate'),
  scout('Scout');

  const RatingCategory(this.value);
  final String value;
}

/// Player game info request model
class PlayerGameInfoRequest {
  final ActiveFoot? activeFoot;
  final String? primaryPosition;
  final List<String>? playedPositions;
  final List<String>? teamsPlayed;

  const PlayerGameInfoRequest({
    this.activeFoot,
    this.primaryPosition,
    this.playedPositions,
    this.teamsPlayed,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    if (activeFoot != null) data['activeFoot'] = activeFoot!.value;
    if (primaryPosition != null) data['primaryPosition'] = primaryPosition;
    if (playedPositions != null) data['playedPositions'] = playedPositions;
    if (teamsPlayed != null) data['teamsPlayed'] = teamsPlayed;

    return data;
  }

  PlayerGameInfoRequest copyWith({
    ActiveFoot? activeFoot,
    String? primaryPosition,
    List<String>? playedPositions,
    List<String>? teamsPlayed,
  }) {
    return PlayerGameInfoRequest(
      activeFoot: activeFoot ?? this.activeFoot,
      primaryPosition: primaryPosition ?? this.primaryPosition,
      playedPositions: playedPositions ?? this.playedPositions,
      teamsPlayed: teamsPlayed ?? this.teamsPlayed,
    );
  }
}

/// Update player profile request model
class UpdatePlayerProfileRequest {
  final String? fullName;
  final DateTime? dateOfBirth;
  final int? heightCm;
  final int? weightKg;
  final String? description;
  final String? photoUrl;
  final bool? scoutingEnabled;
  final PlayerGameInfoRequest? gameInfo;

  const UpdatePlayerProfileRequest({
    this.fullName,
    this.dateOfBirth,
    this.heightCm,
    this.weightKg,
    this.description,
    this.photoUrl,
    this.scoutingEnabled,
    this.gameInfo,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    if (fullName != null) data['fullName'] = fullName;
    if (dateOfBirth != null)
      data['dateOfBirth'] = dateOfBirth!.toIso8601String();
    if (heightCm != null) data['heightCm'] = heightCm;
    if (weightKg != null) data['weightKg'] = weightKg;
    if (description != null) data['description'] = description;
    if (photoUrl != null) data['photoUrl'] = photoUrl;
    if (scoutingEnabled != null) data['scoutingEnabled'] = scoutingEnabled;
    if (gameInfo != null) data['gameInfo'] = gameInfo!.toJson();

    return data;
  }

  UpdatePlayerProfileRequest copyWith({
    String? fullName,
    DateTime? dateOfBirth,
    int? heightCm,
    int? weightKg,
    String? description,
    String? photoUrl,
    bool? scoutingEnabled,
    PlayerGameInfoRequest? gameInfo,
  }) {
    return UpdatePlayerProfileRequest(
      fullName: fullName ?? this.fullName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      heightCm: heightCm ?? this.heightCm,
      weightKg: weightKg ?? this.weightKg,
      description: description ?? this.description,
      photoUrl: photoUrl ?? this.photoUrl,
      scoutingEnabled: scoutingEnabled ?? this.scoutingEnabled,
      gameInfo: gameInfo ?? this.gameInfo,
    );
  }
}

/// Submit rating request model
class SubmitRatingRequest {
  final RatingCategory ratingCategory;
  final int defense;
  final int shooting;
  final int passing;
  final int pace;
  final int physicality;
  final int dribbling;
  final String? comments;

  const SubmitRatingRequest({
    required this.ratingCategory,
    required this.defense,
    required this.shooting,
    required this.passing,
    required this.pace,
    required this.physicality,
    required this.dribbling,
    this.comments,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'ratingCategory': ratingCategory.value,
      'defense': defense,
      'shooting': shooting,
      'passing': passing,
      'pace': pace,
      'physicality': physicality,
      'dribbling': dribbling,
    };

    if (comments != null) data['comments'] = comments;

    return data;
  }

  SubmitRatingRequest copyWith({
    RatingCategory? ratingCategory,
    int? defense,
    int? shooting,
    int? passing,
    int? pace,
    int? physicality,
    int? dribbling,
    String? comments,
  }) {
    return SubmitRatingRequest(
      ratingCategory: ratingCategory ?? this.ratingCategory,
      defense: defense ?? this.defense,
      shooting: shooting ?? this.shooting,
      passing: passing ?? this.passing,
      pace: pace ?? this.pace,
      physicality: physicality ?? this.physicality,
      dribbling: dribbling ?? this.dribbling,
      comments: comments ?? this.comments,
    );
  }
}
