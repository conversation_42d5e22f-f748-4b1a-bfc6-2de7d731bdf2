/// Player response models for the player feature

/// Player game info response model
class PlayerGameInfoResponse {
  final String? activeFoot;
  final String? primaryPosition;
  final List<String>? playedPositions;
  final List<String>? teamsPlayed;

  const PlayerGameInfoResponse({
    this.activeFoot,
    this.primaryPosition,
    this.playedPositions,
    this.teamsPlayed,
  });

  Map<String, dynamic> toJson() {
    return {
      'activeFoot': activeFoot,
      'primaryPosition': primaryPosition,
      'playedPositions': playedPositions,
      'teamsPlayed': teamsPlayed,
    };
  }

  factory PlayerGameInfoResponse.fromJson(Map<String, dynamic> json) {
    return PlayerGameInfoResponse(
      activeFoot: json['activeFoot'] as String?,
      primaryPosition: json['primaryPosition'] as String?,
      playedPositions: json['playedPositions'] != null
          ? List<String>.from(json['playedPositions'] as List)
          : null,
      teamsPlayed: json['teamsPlayed'] != null
          ? List<String>.from(json['teamsPlayed'] as List)
          : null,
    );
  }

  PlayerGameInfoResponse copyWith({
    String? activeFoot,
    String? primaryPosition,
    List<String>? playedPositions,
    List<String>? teamsPlayed,
  }) {
    return PlayerGameInfoResponse(
      activeFoot: activeFoot ?? this.activeFoot,
      primaryPosition: primaryPosition ?? this.primaryPosition,
      playedPositions: playedPositions ?? this.playedPositions,
      teamsPlayed: teamsPlayed ?? this.teamsPlayed,
    );
  }
}

/// Player profile response model
class PlayerProfileResponse {
  final String id;
  final String fullName;
  final DateTime? dateOfBirth;
  final int? heightCm;
  final int? weightKg;
  final String? description;
  final String? photoUrl;
  final bool? scoutingEnabled;
  final PlayerGameInfoResponse? gameInfo;
  final double? averageRating;
  final int? totalRatings;
  final int? matchesPlayed;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const PlayerProfileResponse({
    required this.id,
    required this.fullName,
    this.dateOfBirth,
    this.heightCm,
    this.weightKg,
    this.description,
    this.photoUrl,
    this.scoutingEnabled,
    this.gameInfo,
    this.averageRating,
    this.totalRatings,
    this.matchesPlayed,
    this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullName': fullName,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'heightCm': heightCm,
      'weightKg': weightKg,
      'description': description,
      'photoUrl': photoUrl,
      'scoutingEnabled': scoutingEnabled,
      'gameInfo': gameInfo?.toJson(),
      'averageRating': averageRating,
      'totalRatings': totalRatings,
      'matchesPlayed': matchesPlayed,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory PlayerProfileResponse.fromJson(Map<String, dynamic> json) {
    return PlayerProfileResponse(
      id: json['id'] as String,
      fullName: json['fullName'] as String,
      dateOfBirth: json['dateOfBirth'] != null
          ? DateTime.parse(json['dateOfBirth'] as String)
          : null,
      heightCm: json['heightCm'] as int?,
      weightKg: json['weightKg'] as int?,
      description: json['description'] as String?,
      photoUrl: json['photoUrl'] as String?,
      scoutingEnabled: json['scoutingEnabled'] as bool?,
      gameInfo: json['gameInfo'] != null
          ? PlayerGameInfoResponse.fromJson(
              json['gameInfo'] as Map<String, dynamic>)
          : null,
      averageRating: json['averageRating'] != null
          ? (json['averageRating'] as num).toDouble()
          : null,
      totalRatings: json['totalRatings'] as int?,
      matchesPlayed: json['matchesPlayed'] as int?,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  PlayerProfileResponse copyWith({
    String? id,
    String? fullName,
    DateTime? dateOfBirth,
    int? heightCm,
    int? weightKg,
    String? description,
    String? photoUrl,
    bool? scoutingEnabled,
    PlayerGameInfoResponse? gameInfo,
    double? averageRating,
    int? totalRatings,
    int? matchesPlayed,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PlayerProfileResponse(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      heightCm: heightCm ?? this.heightCm,
      weightKg: weightKg ?? this.weightKg,
      description: description ?? this.description,
      photoUrl: photoUrl ?? this.photoUrl,
      scoutingEnabled: scoutingEnabled ?? this.scoutingEnabled,
      gameInfo: gameInfo ?? this.gameInfo,
      averageRating: averageRating ?? this.averageRating,
      totalRatings: totalRatings ?? this.totalRatings,
      matchesPlayed: matchesPlayed ?? this.matchesPlayed,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Player rating vote model
class PlayerRatingVote {
  final String id;
  final String ratingCategory;
  final int defense;
  final int shooting;
  final int passing;
  final int pace;
  final int physicality;
  final int dribbling;
  final String? comments;
  final String voterName;
  final DateTime createdAt;

  const PlayerRatingVote({
    required this.id,
    required this.ratingCategory,
    required this.defense,
    required this.shooting,
    required this.passing,
    required this.pace,
    required this.physicality,
    required this.dribbling,
    this.comments,
    required this.voterName,
    required this.createdAt,
  });

  /// Calculate average rating from all attributes
  double get averageRating {
    final total = defense + shooting + passing + pace + physicality + dribbling;
    return total / 6.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ratingCategory': ratingCategory,
      'defense': defense,
      'shooting': shooting,
      'passing': passing,
      'pace': pace,
      'physicality': physicality,
      'dribbling': dribbling,
      'comments': comments,
      'voterName': voterName,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory PlayerRatingVote.fromJson(Map<String, dynamic> json) {
    return PlayerRatingVote(
      id: json['id'] as String,
      ratingCategory: json['ratingCategory'] as String,
      defense: json['defense'] as int,
      shooting: json['shooting'] as int,
      passing: json['passing'] as int,
      pace: json['pace'] as int,
      physicality: json['physicality'] as int,
      dribbling: json['dribbling'] as int,
      comments: json['comments'] as String?,
      voterName: json['voterName'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  PlayerRatingVote copyWith({
    String? id,
    String? ratingCategory,
    int? defense,
    int? shooting,
    int? passing,
    int? pace,
    int? physicality,
    int? dribbling,
    String? comments,
    String? voterName,
    DateTime? createdAt,
  }) {
    return PlayerRatingVote(
      id: id ?? this.id,
      ratingCategory: ratingCategory ?? this.ratingCategory,
      defense: defense ?? this.defense,
      shooting: shooting ?? this.shooting,
      passing: passing ?? this.passing,
      pace: pace ?? this.pace,
      physicality: physicality ?? this.physicality,
      dribbling: dribbling ?? this.dribbling,
      comments: comments ?? this.comments,
      voterName: voterName ?? this.voterName,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// Current rating model from API
class CurrentRating {
  final int defense;
  final int shooting;
  final int passing;
  final int pace;
  final int physicality;
  final int dribbling;
  final double overall;
  final int publicVotes;
  final int opponentVotes;
  final int teammateVotes;
  final int scoutVotes;
  final DateTime lastUpdated;

  const CurrentRating({
    required this.defense,
    required this.shooting,
    required this.passing,
    required this.pace,
    required this.physicality,
    required this.dribbling,
    required this.overall,
    required this.publicVotes,
    required this.opponentVotes,
    required this.teammateVotes,
    required this.scoutVotes,
    required this.lastUpdated,
  });

  factory CurrentRating.fromJson(Map<String, dynamic> json) {
    return CurrentRating(
      defense: json['defense'] as int,
      shooting: json['shooting'] as int,
      passing: json['passing'] as int,
      pace: json['pace'] as int,
      physicality: json['physicality'] as int,
      dribbling: json['dribbling'] as int,
      overall: (json['overall'] as num).toDouble(),
      publicVotes: json['publicVotes'] as int,
      opponentVotes: json['opponentVotes'] as int,
      teammateVotes: json['teammateVotes'] as int,
      scoutVotes: json['scoutVotes'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'defense': defense,
      'shooting': shooting,
      'passing': passing,
      'pace': pace,
      'physicality': physicality,
      'dribbling': dribbling,
      'overall': overall,
      'publicVotes': publicVotes,
      'opponentVotes': opponentVotes,
      'teammateVotes': teammateVotes,
      'scoutVotes': scoutVotes,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

/// Recent vote model from API
class RecentVote {
  final String voterId;
  final String voterName;
  final String category;
  final int defense;
  final int shooting;
  final int passing;
  final int pace;
  final int physicality;
  final int dribbling;
  final DateTime votedAt;
  final String? comments;

  const RecentVote({
    required this.voterId,
    required this.voterName,
    required this.category,
    required this.defense,
    required this.shooting,
    required this.passing,
    required this.pace,
    required this.physicality,
    required this.dribbling,
    required this.votedAt,
    this.comments,
  });

  factory RecentVote.fromJson(Map<String, dynamic> json) {
    return RecentVote(
      voterId: json['voterId'] as String,
      voterName: json['voterName'] as String,
      category: json['category'] as String,
      defense: json['defense'] as int,
      shooting: json['shooting'] as int,
      passing: json['passing'] as int,
      pace: json['pace'] as int,
      physicality: json['physicality'] as int,
      dribbling: json['dribbling'] as int,
      votedAt: DateTime.parse(json['votedAt'] as String),
      comments: json['comments'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'voterId': voterId,
      'voterName': voterName,
      'category': category,
      'defense': defense,
      'shooting': shooting,
      'passing': passing,
      'pace': pace,
      'physicality': physicality,
      'dribbling': dribbling,
      'votedAt': votedAt.toIso8601String(),
      'comments': comments,
    };
  }

  /// Calculate average rating from all attributes
  double get averageRating {
    final total = defense + shooting + passing + pace + physicality + dribbling;
    return total / 6.0;
  }
}

/// Player rating response model (matches API structure)
class PlayerRatingResponse {
  final CurrentRating currentRating;
  final List<RecentVote> recentVotes;

  const PlayerRatingResponse({
    required this.currentRating,
    required this.recentVotes,
  });

  factory PlayerRatingResponse.fromJson(Map<String, dynamic> json) {
    return PlayerRatingResponse(
      currentRating:
          CurrentRating.fromJson(json['currentRating'] as Map<String, dynamic>),
      recentVotes: (json['recentVotes'] as List)
          .map((e) => RecentVote.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentRating': currentRating.toJson(),
      'recentVotes': recentVotes.map((e) => e.toJson()).toList(),
    };
  }
}
