import '../../domain/entities/player_search_item.dart';

/// DTO for player search results
class PlayerSearchItemDto {
  final String id;
  final String fullName;
  final int? age;
  final int? heightCm;
  final int? weightKg;
  final String email;
  final String phone;
  final String? description;
  final String? photoUrl;
  final bool scoutingEnabled;
  final Map<String, dynamic>? gameInfo;

  const PlayerSearchItemDto({
    required this.id,
    required this.fullName,
    this.age,
    this.heightCm,
    this.weightKg,
    required this.email,
    required this.phone,
    this.description,
    this.photoUrl,
    required this.scoutingEnabled,
    this.gameInfo,
  });

  factory PlayerSearchItemDto.fromJson(Map<String, dynamic> json) {
    return PlayerSearchItemDto(
      id: json['id'] ?? '',
      fullName: json['fullName'] ?? '',
      age: json['age'],
      heightCm: json['heightCm'],
      weightKg: json['weightKg'],
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      description: json['description'],
      photoUrl: json['photoUrl'],
      scoutingEnabled: json['scoutingEnabled'] ?? false,
      gameInfo: json['gameInfo'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullName': fullName,
      'age': age,
      'heightCm': heightCm,
      'weightKg': weightKg,
      'email': email,
      'phone': phone,
      'description': description,
      'photoUrl': photoUrl,
      'scoutingEnabled': scoutingEnabled,
      'gameInfo': gameInfo,
    };
  }

  /// Convert DTO to domain entity
  PlayerSearchItem toEntity() {
    return PlayerSearchItem(
      id: id,
      fullName: fullName,
      age: age,
      heightCm: heightCm,
      weightKg: weightKg,
      email: email,
      phone: phone,
      description: description,
      photoUrl: photoUrl,
      scoutingEnabled: scoutingEnabled,
      gameInfo: gameInfo,
    );
  }

  /// Create DTO from domain entity
  factory PlayerSearchItemDto.fromEntity(PlayerSearchItem entity) {
    return PlayerSearchItemDto(
      id: entity.id,
      fullName: entity.fullName,
      age: entity.age,
      heightCm: entity.heightCm,
      weightKg: entity.weightKg,
      email: entity.email,
      phone: entity.phone,
      description: entity.description,
      photoUrl: entity.photoUrl,
      scoutingEnabled: entity.scoutingEnabled,
      gameInfo: entity.gameInfo,
    );
  }
}
