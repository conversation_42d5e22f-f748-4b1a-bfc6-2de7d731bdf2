import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/networking/exception.dart';
import '../../../../core/models/paginated_response.dart';
import '../datasources/player_datasource.dart';
import '../dto/player_request_models.dart';
import '../dto/player_response_models.dart' as dto;
import '../../domain/entities/player.dart';
import '../../domain/entities/player_rating.dart';
import '../../domain/entities/player_search_item.dart';
import '../../domain/repositories/player_repository.dart';

/// Implementation of the player repository
class PlayerRepositoryImpl implements PlayerRepository {
  final PlayerDatasource _playerDatasource;

  PlayerRepositoryImpl(this._playerDatasource);

  @override
  Future<Either<AppError, Player>> getPlayerProfile(String playerId) async {
    try {
      final response = await _playerDatasource.getPlayerProfile(playerId);

      final player = Player(
        id: response.id,
        fullName: response.fullName,
        dateOfBirth: response.dateOfBirth,
        heightCm: response.heightCm,
        weightKg: response.weightKg,
        description: response.description,
        photoUrl: response.photoUrl,
        scoutingEnabled: response.scoutingEnabled,
        activeFoot: response.gameInfo?.activeFoot,
        primaryPosition: response.gameInfo?.primaryPosition,
        playedPositions: response.gameInfo?.playedPositions,
        teamsPlayed: response.gameInfo?.teamsPlayed,
        averageRating: response.averageRating,
        totalRatings: response.totalRatings,
        matchesPlayed: response.matchesPlayed,
        createdAt: response.createdAt,
        updatedAt: response.updatedAt,
      );

      return Right(player);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, Player>> updatePlayerProfile(
    String playerId,
    UpdatePlayerProfileRequest request,
  ) async {
    try {
      final response = await _playerDatasource.updatePlayerProfile(
        playerId,
        request,
      );

      final player = Player(
        id: response.id,
        fullName: response.fullName,
        dateOfBirth: response.dateOfBirth,
        heightCm: response.heightCm,
        weightKg: response.weightKg,
        description: response.description,
        photoUrl: response.photoUrl,
        scoutingEnabled: response.scoutingEnabled,
        activeFoot: response.gameInfo?.activeFoot,
        primaryPosition: response.gameInfo?.primaryPosition,
        playedPositions: response.gameInfo?.playedPositions,
        teamsPlayed: response.gameInfo?.teamsPlayed,
        averageRating: response.averageRating,
        totalRatings: response.totalRatings,
        matchesPlayed: response.matchesPlayed,
        createdAt: response.createdAt,
        updatedAt: response.updatedAt,
      );

      return Right(player);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, PlayerRating>> getPlayerRating(
    String playerId,
  ) async {
    try {
      final response = await _playerDatasource.getPlayerRating(playerId);

      // Convert DTO to domain entity
      final totalVotes =
          response.currentRating.publicVotes +
          response.currentRating.opponentVotes +
          response.currentRating.teammateVotes +
          response.currentRating.scoutVotes;

      final rating = PlayerRating(
        averageRating: response.currentRating.overall,
        totalRatings: totalVotes,
        categoryAverages: {
          'defense': response.currentRating.defense.toDouble(),
          'shooting': response.currentRating.shooting.toDouble(),
          'passing': response.currentRating.passing.toDouble(),
          'pace': response.currentRating.pace.toDouble(),
          'physicality': response.currentRating.physicality.toDouble(),
          'dribbling': response.currentRating.dribbling.toDouble(),
        },
        recentVotes:
            response.recentVotes
                .map(
                  (vote) => PlayerRatingVote(
                    id: vote.voterId,
                    ratingCategory: vote.category,
                    defense: vote.defense,
                    shooting: vote.shooting,
                    passing: vote.passing,
                    pace: vote.pace,
                    physicality: vote.physicality,
                    dribbling: vote.dribbling,
                    comments: vote.comments,
                    voterName: vote.voterName,
                    createdAt: vote.votedAt,
                  ),
                )
                .toList(),
      );

      return Right(rating);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> submitRating(
    String playerId,
    SubmitRatingRequest request,
  ) async {
    try {
      await _playerDatasource.submitRating(playerId, request);
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, PaginatedResponse<PlayerSearchItem>>> searchPlayers({
    String? query,
    int? page,
    int? pageSize,
  }) async {
    try {
      final response = await _playerDatasource.searchPlayers(
        query: query,
        page: page,
        pageSize: pageSize,
      );

      // Convert DTOs to entities
      final entityResponse = PaginatedResponse<PlayerSearchItem>(
        message: response.message,
        data: PaginatedData<PlayerSearchItem>(
          items: response.data.items.map((dto) => dto.toEntity()).toList(),
          total: response.data.total,
          page: response.data.page,
          pageSize: response.data.pageSize,
          totalPages: response.data.totalPages,
        ),
        error: response.error,
        validationErrors: response.validationErrors,
      );

      return Right(entityResponse);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }
}
