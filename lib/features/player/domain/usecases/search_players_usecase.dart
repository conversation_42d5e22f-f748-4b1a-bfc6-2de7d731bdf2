import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/models/paginated_response.dart';
import '../../../../core/use_case.dart';
import '../entities/player_search_item.dart';
import '../repositories/player_repository.dart';

/// Parameters for searching players
class SearchPlayersParams {
  final String? query;
  final int? page;
  final int? pageSize;

  const SearchPlayersParams({
    this.query,
    this.page,
    this.pageSize,
  });

  SearchPlayersParams copyWith({
    String? query,
    int? page,
    int? pageSize,
  }) {
    return SearchPlayersParams(
      query: query ?? this.query,
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SearchPlayersParams &&
        other.query == query &&
        other.page == page &&
        other.pageSize == pageSize;
  }

  @override
  int get hashCode => Object.hash(query, page, pageSize);

  @override
  String toString() {
    return 'SearchPlayersParams(query: $query, page: $page, pageSize: $pageSize)';
  }
}

/// Use case for searching players with pagination
class SearchPlayersUseCase implements UseCase<PaginatedResponse<PlayerSearchItem>, SearchPlayersParams> {
  final PlayerRepository _playerRepository;

  SearchPlayersUseCase(this._playerRepository);

  @override
  Future<Either<AppError, PaginatedResponse<PlayerSearchItem>>> call(SearchPlayersParams params) async {
    return await _playerRepository.searchPlayers(
      query: params.query,
      page: params.page,
      pageSize: params.pageSize,
    );
  }
}
