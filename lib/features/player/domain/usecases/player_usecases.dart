import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../entities/player.dart';
import '../entities/player_rating.dart';
import '../repositories/player_repository.dart';
import '../../data/dto/player_request_models.dart';

/// Use case for getting player profile
class GetPlayerProfileUseCase {
  final PlayerRepository _playerRepository;

  GetPlayerProfileUseCase(this._playerRepository);

  Future<Either<AppError, Player>> call(String playerId) async {
    return await _playerRepository.getPlayerProfile(playerId);
  }
}

/// Use case for updating player profile
class UpdatePlayerProfileUseCase {
  final PlayerRepository _playerRepository;

  UpdatePlayerProfileUseCase(this._playerRepository);

  Future<Either<AppError, Player>> call({
    required String playerId,
    String? fullName,
    DateTime? dateOfBirth,
    int? heightCm,
    int? weightKg,
    String? description,
    String? photoUrl,
    bool? scoutingEnabled,
    ActiveFoot? activeFoot,
    String? primaryPosition,
    List<String>? playedPositions,
    List<String>? teamsPlayed,
  }) async {
    final gameInfo = PlayerGameInfoRequest(
      activeFoot: activeFoot,
      primaryPosition: primaryPosition,
      playedPositions: playedPositions,
      teamsPlayed: teamsPlayed,
    );

    final request = UpdatePlayerProfileRequest(
      fullName: fullName,
      dateOfBirth: dateOfBirth,
      heightCm: heightCm,
      weightKg: weightKg,
      description: description,
      photoUrl: photoUrl,
      scoutingEnabled: scoutingEnabled,
      gameInfo: gameInfo,
    );

    return await _playerRepository.updatePlayerProfile(playerId, request);
  }
}

/// Use case for getting player rating
class GetPlayerRatingUseCase {
  final PlayerRepository _playerRepository;

  GetPlayerRatingUseCase(this._playerRepository);

  Future<Either<AppError, PlayerRating>> call(String playerId) async {
    return await _playerRepository.getPlayerRating(playerId);
  }
}

/// Use case for submitting player rating
class SubmitPlayerRatingUseCase {
  final PlayerRepository _playerRepository;

  SubmitPlayerRatingUseCase(this._playerRepository);

  Future<Either<AppError, void>> call({
    required String playerId,
    required RatingCategory ratingCategory,
    required int defense,
    required int shooting,
    required int passing,
    required int pace,
    required int physicality,
    required int dribbling,
    String? comments,
  }) async {
    final request = SubmitRatingRequest(
      ratingCategory: ratingCategory,
      defense: defense,
      shooting: shooting,
      passing: passing,
      pace: pace,
      physicality: physicality,
      dribbling: dribbling,
      comments: comments,
    );

    return await _playerRepository.submitRating(playerId, request);
  }
}
