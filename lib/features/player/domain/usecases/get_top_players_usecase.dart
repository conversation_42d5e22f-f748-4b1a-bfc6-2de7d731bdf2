import 'package:fpdart/fpdart.dart';
import '../../../../core/use_case.dart';
import '../../../../core/networking/app_error.dart';
import '../entities/player.dart';
import '../repositories/player_repository.dart';

class GetTopPlayersUseCase implements UseCase<List<Player>, NoParams> {
  final PlayerRepository _repo;
  GetTopPlayersUseCase(this._repo);

  @override
  Future<Either<AppError, List<Player>>> call(NoParams params) {
    return Future.value(Left(AppError("not implemented")));
  }
}

