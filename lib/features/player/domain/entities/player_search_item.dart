/// Lightweight player entity for search results
class PlayerSearchItem {
  final String id;
  final String fullName;
  final int? age;
  final int? heightCm;
  final int? weightKg;
  final String email;
  final String phone;
  final String? description;
  final String? photoUrl;
  final bool scoutingEnabled;
  final Map<String, dynamic>? gameInfo;

  const PlayerSearchItem({
    required this.id,
    required this.fullName,
    this.age,
    this.heightCm,
    this.weightKg,
    required this.email,
    required this.phone,
    this.description,
    this.photoUrl,
    required this.scoutingEnabled,
    this.gameInfo,
  });

  PlayerSearchItem copyWith({
    String? id,
    String? fullName,
    int? age,
    int? heightCm,
    int? weightKg,
    String? email,
    String? phone,
    String? description,
    String? photoUrl,
    bool? scoutingEnabled,
    Map<String, dynamic>? gameInfo,
  }) {
    return PlayerSearchItem(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      age: age ?? this.age,
      heightCm: heightCm ?? this.heightCm,
      weightKg: weightKg ?? this.weightKg,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      description: description ?? this.description,
      photoUrl: photoUrl ?? this.photoUrl,
      scoutingEnabled: scoutingEnabled ?? this.scoutingEnabled,
      gameInfo: gameInfo ?? this.gameInfo,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PlayerSearchItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PlayerSearchItem(id: $id, fullName: $fullName, email: $email)';
  }
}
