class MadSportsCard {
  final String playerId;
  final String playerName;
  final int age;
  final String position;
  final String countryFlag; // e.g. 🇳🇵

  final int jerseyNumber;
  final String season; // e.g. 24/25
  final String rarity; // e.g. Bronze / Silver / Gold

  final String profileImageUrl;
  final String shareableText;

  const MadSportsCard({
    required this.playerId,
    required this.playerName,
    required this.age,
    required this.position,
    required this.countryFlag,
    required this.jerseyNumber,
    required this.season,
    required this.rarity,
    required this.profileImageUrl,
    required this.shareableText,
  });
}

