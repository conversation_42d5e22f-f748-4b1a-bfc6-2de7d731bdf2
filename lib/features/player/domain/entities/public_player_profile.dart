class PublicPlayerProfile {
  final String id;
  final String name;
  final String position;
  final String team;
  final int age;
  final int height;
  final int weight;
  final String? photoUrl;
  final String bio;
  final int experience;
  final String preferredFoot;
  final int jerseyNumber;
  final PlayerStats stats;
  final List<Achievement> achievements;
  final PlayerSkills skills;
  final ContactInfo? contactInfo;
  final List<String> socialLinks;
  final List<RecentPerformance> recentPerformances;

  const PublicPlayerProfile({
    required this.id,
    required this.name,
    required this.position,
    required this.team,
    required this.age,
    required this.height,
    required this.weight,
    this.photoUrl,
    required this.bio,
    required this.experience,
    required this.preferredFoot,
    required this.jerseyNumber,
    required this.stats,
    required this.achievements,
    required this.skills,
    this.contactInfo,
    required this.socialLinks,
    required this.recentPerformances,
  });
}

class PlayerStats {
  final int matchesPlayed;
  final int goals;
  final int assists;
  final int tournamentsPlayed;
  final double averageRating;
  final int cleanSheets;
  final int yellowCards;
  final int redCards;
  final double passAccuracy;
  final int shotsOnTarget;

  const PlayerStats({
    required this.matchesPlayed,
    required this.goals,
    required this.assists,
    required this.tournamentsPlayed,
    required this.averageRating,
    required this.cleanSheets,
    required this.yellowCards,
    required this.redCards,
    required this.passAccuracy,
    required this.shotsOnTarget,
  });
}

class Achievement {
  final String title;
  final String description;
  final String icon;
  final DateTime dateAchieved;
  final String category;

  const Achievement({
    required this.title,
    required this.description,
    required this.icon,
    required this.dateAchieved,
    required this.category,
  });
}

class PlayerSkills {
  final int pace;
  final int shooting;
  final int passing;
  final int dribbling;
  final int defense;
  final int physicality;

  const PlayerSkills({
    required this.pace,
    required this.shooting,
    required this.passing,
    required this.dribbling,
    required this.defense,
    required this.physicality,
  });

  double get overall {
    return (pace + shooting + passing + dribbling + defense + physicality) /
        6.0;
  }
}

class ContactInfo {
  final String? email;
  final String? phone;
  final String? instagram;
  final String? twitter;
  final String? linkedin;
  final bool isPublic;

  const ContactInfo({
    this.email,
    this.phone,
    this.instagram,
    this.twitter,
    this.linkedin,
    required this.isPublic,
  });
}

class RecentPerformance {
  final String matchTitle;
  final String opponent;
  final DateTime date;
  final int goals;
  final int assists;
  final double rating;
  final String result;

  const RecentPerformance({
    required this.matchTitle,
    required this.opponent,
    required this.date,
    required this.goals,
    required this.assists,
    required this.rating,
    required this.result,
  });
}
