class Player {
  final String id;
  final String fullName;
  final DateTime? dateOfBirth;
  final int? heightCm;
  final int? weightKg;
  final String? description;
  final String? photoUrl;
  final bool? scoutingEnabled;
  final String? activeFoot;
  final String? primaryPosition;
  final List<String>? playedPositions;
  final List<String>? teamsPlayed;
  final double? averageRating;
  final int? totalRatings;
  final int? matchesPlayed;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Player({
    required this.id,
    required this.fullName,
    this.dateOfBirth,
    this.heightCm,
    this.weightKg,
    this.description,
    this.photoUrl,
    this.scoutingEnabled,
    this.activeFoot,
    this.primaryPosition,
    this.playedPositions,
    this.teamsPlayed,
    this.averageRating,
    this.totalRatings,
    this.matchesPlayed,
    this.createdAt,
    this.updatedAt,
  });

  Player copyWith({
    String? id,
    String? fullName,
    DateTime? dateOfBirth,
    int? heightCm,
    int? weightKg,
    String? description,
    String? photoUrl,
    bool? scoutingEnabled,
    String? activeFoot,
    String? primaryPosition,
    List<String>? playedPositions,
    List<String>? teamsPlayed,
    double? averageRating,
    int? totalRatings,
    int? matchesPlayed,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Player(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      heightCm: heightCm ?? this.heightCm,
      weightKg: weightKg ?? this.weightKg,
      description: description ?? this.description,
      photoUrl: photoUrl ?? this.photoUrl,
      scoutingEnabled: scoutingEnabled ?? this.scoutingEnabled,
      activeFoot: activeFoot ?? this.activeFoot,
      primaryPosition: primaryPosition ?? this.primaryPosition,
      playedPositions: playedPositions ?? this.playedPositions,
      teamsPlayed: teamsPlayed ?? this.teamsPlayed,
      averageRating: averageRating ?? this.averageRating,
      totalRatings: totalRatings ?? this.totalRatings,
      matchesPlayed: matchesPlayed ?? this.matchesPlayed,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
