# Player Profile & Self Assessment Features

This module provides comprehensive player profile management and self-assessment rating functionality with real data integration.

## Features

### 🏃‍♂️ Player Profile

- **Detailed Player Information**: Complete player profiles with personal details, statistics, and game information
- **Visual Profile Display**: Beautiful UI with player photos, ratings, and detailed information
- **Real-time Data**: Live data integration with mock data for development
- **Profile Management**: Edit and update player information

### 📊 Self Assessment Ratings

- **Interactive Rating System**: Rate yourself across 6 key football attributes
- **Real-time Feedback**: Immediate visual feedback on rating changes
- **Assessment History**: View previous assessments and track progress
- **Category Breakdown**: Detailed breakdown of ratings by category

## Architecture

The player feature follows clean architecture principles with clear separation of concerns:

```
lib/features/player/
├── data/
│   ├── datasources/
│   │   ├── player_datasource.dart          # Data source interface
│   │   ├── player_remote_datasource.dart   # Remote API implementation
│   │   └── player_mock_datasource.dart     # Mock data for development
│   ├── dto/
│   │   ├── player_request_models.dart      # Request DTOs
│   │   └── player_response_models.dart     # Response DTOs
│   └── repositories/
│       └── player_repository_impl.dart     # Repository implementation
├── domain/
│   ├── entities/
│   │   ├── player.dart                     # Player entity
│   │   └── player_rating.dart              # Rating entity
│   ├── repositories/
│   │   └── player_repository.dart          # Repository interface
│   └── usecases/
│       └── player_usecases.dart            # Business logic
├── presentation/
│   ├── screens/
│   │   ├── player_profile_screen.dart      # Main profile screen
│   │   └── player_demo_screen.dart         # Demo screen
│   └── widgets/
│       ├── player_profile_widget.dart      # Profile display widget
│       └── self_assessment_widget.dart     # Self-assessment widget
└── player_providers.dart                   # Riverpod providers
```

## Data Models

### Player Entity

```dart
class Player {
  final String id;
  final String fullName;
  final DateTime? dateOfBirth;
  final int? heightCm;
  final int? weightKg;
  final String? description;
  final String? photoUrl;
  final bool? scoutingEnabled;
  final String? activeFoot;
  final String? primaryPosition;
  final List<String>? playedPositions;
  final List<String>? teamsPlayed;
  final double? averageRating;
  final int? totalRatings;
  final int? matchesPlayed;
  // ... more fields
}
```

### Rating Categories

Players can be rated across 6 key attributes:

- **Defense**: Defensive skills and positioning
- **Shooting**: Goal-scoring ability and accuracy
- **Passing**: Passing accuracy and vision
- **Pace**: Speed and acceleration
- **Physicality**: Strength and stamina
- **Dribbling**: Ball control and dribbling skills

## Usage

### Basic Player Profile

```dart
// Navigate to player profile
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => PlayerProfileScreen(playerId: 'player_001'),
  ),
);
```

### Self Assessment

```dart
// Access self-assessment widget
SelfAssessmentWidget(playerId: 'player_001')
```

### Demo Screen

```dart
// Launch demo with multiple players
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const PlayerDemoScreen(),
  ),
);
```

## Mock Data

The system includes comprehensive mock data for development:

### Available Players

1. **Lionel Messi** (player_001) - Forward
2. **Cristiano Ronaldo** (player_002) - Forward
3. **Kevin De Bruyne** (player_003) - Midfielder
4. **Virgil van Dijk** (player_004) - Defender
5. **Erling Haaland** (player_005) - Striker

### Rating Data

Each player comes with:

- Realistic base ratings (89-94)
- Random variation in category ratings
- Historical assessment data
- Multiple voter types (coaches, scouts, teammates, etc.)

## API Endpoints

The system is designed to work with these REST endpoints:

- `GET /api/players/{playerId}` - Get player profile
- `PUT /api/players/{playerId}` - Update player profile
- `GET /api/players/{playerId}/rating` - Get player ratings
- `POST /api/players/{playerId}/rating` - Submit new rating

## Configuration

### Switching Data Sources

To switch between mock and real data:

```dart
// In player_providers.dart
final playerDataSourceProvider = Provider<PlayerDatasource>((ref) {
  // For development (mock data)
  return PlayerMockDataSource();

  // For production (real API)
  // final apiClient = ref.watch(apiClientProvider);
  // return PlayerRemoteDataSource(apiClient);
});
```

## Features in Detail

### Player Profile Widget

- **Profile Header**: Large avatar, name, position, and overall rating
- **Player Stats**: Key statistics displayed in a clean grid
- **Player Details**: Comprehensive information including teams, positions, etc.
- **Rating Breakdown**: Visual representation of category ratings
- **Recent Ratings**: List of recent assessments with comments

### Self Assessment Widget

- **Current Rating Display**: Shows current overall rating and category breakdown
- **Interactive Sliders**: Rate yourself from 1-100 in each category
- **Comments Section**: Optional comments for each assessment
- **Assessment History**: View previous assessments with timestamps
- **Real-time Updates**: Immediate feedback and data refresh

### Demo Screen

- **Player Switcher**: Easy switching between different players
- **Tabbed Interface**: Separate tabs for profile and self-assessment
- **Visual Indicators**: Clear indication of current player
- **Responsive Design**: Works well on different screen sizes

## State Management

The feature uses Riverpod for state management:

```dart
// Providers
final playerProfileProvider = FutureProvider.family<dynamic, String>((ref, playerId) async {
  final useCase = ref.watch(getPlayerProfileUseCaseProvider);
  return await useCase(playerId);
});

final playerRatingProvider = FutureProvider.family<dynamic, String>((ref, playerId) async {
  final useCase = ref.watch(getPlayerRatingUseCaseProvider);
  return await useCase(playerId);
});
```

## Error Handling

The system includes comprehensive error handling:

- Network error handling
- Data validation
- User-friendly error messages
- Retry mechanisms
- Loading states

## Future Enhancements

Planned features for future releases:

- [ ] Player comparison tools
- [ ] Advanced analytics and charts
- [ ] Team-based assessments
- [ ] Performance tracking over time
- [ ] Integration with match statistics
- [ ] Social features (comments, likes)
- [ ] Export functionality
- [ ] Offline support

## Contributing

When contributing to this feature:

1. Follow the existing architecture patterns
2. Add comprehensive tests
3. Update documentation
4. Ensure mock data is realistic
5. Test with different player types

## Testing

The feature includes comprehensive test coverage:

- Unit tests for use cases
- Widget tests for UI components
- Integration tests for data flow
- Mock data validation

Run tests with:

```bash
flutter test lib/features/player/
```
