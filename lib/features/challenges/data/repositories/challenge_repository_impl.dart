import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../domain/entities/challenge.dart';
import '../../domain/repositories/challenge_repository.dart';
import '../datasources/challenge_datasource.dart';
import '../dto/challenge_dto.dart';

class ChallengeRepositoryImpl implements ChallengeRepository {
  final ChallengeDataSource dataSource;

  ChallengeRepositoryImpl(this.dataSource);

  @override
  Future<Either<AppError, PagedChallenges>> getChallenges({
    String? matchType,
    String? location,
    String? skillLevel,
    String? ageGroup,
    double? maxWagerAmount,
    bool? hasWager,
    DateTime? fromDate,
    DateTime? toDate,
    int? page,
    int? pageSize,
  }) async {
    try {
      final challenges = await dataSource.getChallenges(
        matchType: matchType,
        location: location,
        skillLevel: skillLevel,
        ageGroup: ageGroup,
        maxWagerAmount: maxWagerAmount,
        hasWager: hasWager,
        fromDate: fromDate,
        toDate: toDate,
        page: page,
        pageSize: pageSize,
      );
      return Right(challenges);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, Challenge>> getChallengeById(
      String challengeId) async {
    try {
      final challenge = await dataSource.getChallengeById(challengeId);
      return Right(challenge);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, Challenge>> createChallenge(
      CreateChallengeRequestDto request) async {
    try {
      final challenge = await dataSource.createChallenge(request);
      return Right(challenge);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, Challenge>> updateChallenge(
      String challengeId, UpdateChallengeRequestDto request) async {
    try {
      final challenge = await dataSource.updateChallenge(challengeId, request);
      return Right(challenge);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> deleteChallenge(String challengeId) async {
    try {
      await dataSource.deleteChallenge(challengeId);
      return const Right(null);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, Challenge>> respondToChallenge(
      RespondToChallengeRequestDto request) async {
    try {
      final challenge = await dataSource.respondToChallenge(request);
      return Right(challenge);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> submitMatchResult(
      SubmitMatchResultRequestDto request) async {
    try {
      await dataSource.submitMatchResult(request);
      return const Right(null);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> disputeMatchResult(
      DisputeMatchResultRequestDto request) async {
    try {
      await dataSource.disputeMatchResult(request);
      return const Right(null);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, ChallengeStats>> getChallengeStats() async {
    try {
      final stats = await dataSource.getChallengeStats();
      return Right(stats);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<MatchSuggestion>>> getMatchSuggestions() async {
    try {
      final suggestions = await dataSource.getMatchSuggestions();
      return Right(suggestions);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, PagedChallenges>> getMyChallenges({
    int? page,
    int? pageSize,
  }) async {
    try {
      final challenges = await dataSource.getMyChallenges(
        page: page,
        pageSize: pageSize,
      );
      return Right(challenges);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<MatchSuggestion>>> getMatchRequests({
    String? matchType,
    String? location,
    String? skillLevel,
    String? ageGroup,
    int? maxDistance,
    bool? acceptWagers,
    int? page,
    int? pageSize,
  }) async {
    try {
      final requests = await dataSource.getMatchRequests(
        matchType: matchType,
        location: location,
        skillLevel: skillLevel,
        ageGroup: ageGroup,
        maxDistance: maxDistance,
        acceptWagers: acceptWagers,
        page: page,
        pageSize: pageSize,
      );
      return Right(requests);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, MatchSuggestion>> createMatchRequest(
      CreateMatchRequestRequestDto request) async {
    try {
      final matchRequest = await dataSource.createMatchRequest(request);
      return Right(matchRequest);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<MatchSuggestion>>> getMyMatchRequests({
    int? page,
    int? pageSize,
  }) async {
    try {
      final requests = await dataSource.getMyMatchRequests(
        page: page,
        pageSize: pageSize,
      );
      return Right(requests);
    } catch (e) {
      return Left(AppError.serverError(e.toString()));
    }
  }
}
