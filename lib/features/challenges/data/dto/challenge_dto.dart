import 'dart:convert';

/// DTO for creating a new challenge
class CreateChallengeRequestDto {
  final String? matchType;
  final String? ageGroup;
  final String? skillLevel;
  final String? location;
  final String? venueId;
  final DateTime proposedDateTime;
  final DateTime? alternativeDateTime1;
  final DateTime? alternativeDateTime2;
  final double? wagerAmount;
  final String? wagerType;
  final String? description;
  final String? rules;
  final String? specificOpponentId;
  final String? specificOpponentTeamId;
  final String? challengerTeamId;
  final int expirationHours;

  const CreateChallengeRequestDto({
    this.matchType,
    this.ageGroup,
    this.skillLevel,
    this.location,
    this.venueId,
    required this.proposedDateTime,
    this.alternativeDateTime1,
    this.alternativeDateTime2,
    this.wagerAmount,
    this.wagerType,
    this.description,
    this.rules,
    this.specificOpponentId,
    this.specificOpponentTeamId,
    this.challengerTeamId,
    required this.expirationHours,
  });

  Map<String, dynamic> toJson() {
    return {
      'matchType': matchType,
      'ageGroup': ageGroup,
      'skillLevel': skillLevel,
      'location': location,
      'venueId': venueId,
      'proposedDateTime': proposedDateTime.toIso8601String(),
      'alternativeDateTime1': alternativeDateTime1?.toIso8601String(),
      'alternativeDateTime2': alternativeDateTime2?.toIso8601String(),
      'wagerAmount': wagerAmount,
      'wagerType': wagerType,
      'description': description,
      'rules': rules,
      'specificOpponentId': specificOpponentId,
      'specificOpponentTeamId': specificOpponentTeamId,
      'challengerTeamId': challengerTeamId,
      'expirationHours': expirationHours,
    };
  }

  String toJsonString() => json.encode(toJson());
}

/// DTO for updating a challenge
class UpdateChallengeRequestDto {
  final String? matchType;
  final String? ageGroup;
  final String? skillLevel;
  final String? location;
  final String? venueId;
  final DateTime? proposedDateTime;
  final DateTime? alternativeDateTime1;
  final DateTime? alternativeDateTime2;
  final double? wagerAmount;
  final String? wagerType;
  final String? description;
  final String? rules;
  final String? status;

  const UpdateChallengeRequestDto({
    this.matchType,
    this.ageGroup,
    this.skillLevel,
    this.location,
    this.venueId,
    this.proposedDateTime,
    this.alternativeDateTime1,
    this.alternativeDateTime2,
    this.wagerAmount,
    this.wagerType,
    this.description,
    this.rules,
    this.status,
  });

  Map<String, dynamic> toJson() {
    return {
      'matchType': matchType,
      'ageGroup': ageGroup,
      'skillLevel': skillLevel,
      'location': location,
      'venueId': venueId,
      'proposedDateTime': proposedDateTime?.toIso8601String(),
      'alternativeDateTime1': alternativeDateTime1?.toIso8601String(),
      'alternativeDateTime2': alternativeDateTime2?.toIso8601String(),
      'wagerAmount': wagerAmount,
      'wagerType': wagerType,
      'description': description,
      'rules': rules,
      'status': status,
    };
  }

  String toJsonString() => json.encode(toJson());
}

/// DTO for responding to a challenge
class RespondToChallengeRequestDto {
  final String challengeId;
  final String? responseType;
  final DateTime? preferredDateTime;
  final String? message;
  final String? responderTeamId;

  const RespondToChallengeRequestDto({
    required this.challengeId,
    this.responseType,
    this.preferredDateTime,
    this.message,
    this.responderTeamId,
  });

  Map<String, dynamic> toJson() {
    return {
      'challengeId': challengeId,
      'responseType': responseType,
      'preferredDateTime': preferredDateTime?.toIso8601String(),
      'message': message,
      'responderTeamId': responderTeamId,
    };
  }

  String toJsonString() => json.encode(toJson());
}

/// DTO for submitting match result
class SubmitMatchResultRequestDto {
  final String challengeId;
  final String? winnerId;
  final String? winnerTeamId;
  final String? matchResult;
  final String? comments;

  const SubmitMatchResultRequestDto({
    required this.challengeId,
    this.winnerId,
    this.winnerTeamId,
    this.matchResult,
    this.comments,
  });

  Map<String, dynamic> toJson() {
    return {
      'challengeId': challengeId,
      'winnerId': winnerId,
      'winnerTeamId': winnerTeamId,
      'matchResult': matchResult,
      'comments': comments,
    };
  }

  String toJsonString() => json.encode(toJson());
}

/// DTO for disputing match result
class DisputeMatchResultRequestDto {
  final String challengeId;
  final String? disputeReason;
  final String? evidence;
  final String? comments;

  const DisputeMatchResultRequestDto({
    required this.challengeId,
    this.disputeReason,
    this.evidence,
    this.comments,
  });

  Map<String, dynamic> toJson() {
    return {
      'challengeId': challengeId,
      'disputeReason': disputeReason,
      'evidence': evidence,
      'comments': comments,
    };
  }

  String toJsonString() => json.encode(toJson());
}

/// DTO for challenge response from API
class ChallengeResponseDto {
  final String id;
  final String challengerId;
  final String? challengerName;
  final String? challengerTeamId;
  final String? challengerTeamName;
  final String? opponentId;
  final String? opponentName;
  final String? opponentTeamId;
  final String? opponentTeamName;
  final String? matchType;
  final String? ageGroup;
  final String? skillLevel;
  final String? location;
  final String? venueId;
  final String? venueName;
  final DateTime proposedDateTime;
  final DateTime? alternativeDateTime1;
  final DateTime? alternativeDateTime2;
  final double? wagerAmount;
  final String? wagerType;
  final String? description;
  final String? rules;
  final String? status;
  final DateTime expiresAt;
  final DateTime? acceptedAt;
  final DateTime? completedAt;
  final String? winnerId;
  final String? winnerName;
  final String? winnerTeamId;
  final String? winnerTeamName;
  final String? matchResult;
  final bool isResultDisputed;
  final int responseCount;
  final DateTime created;

  const ChallengeResponseDto({
    required this.id,
    required this.challengerId,
    this.challengerName,
    this.challengerTeamId,
    this.challengerTeamName,
    this.opponentId,
    this.opponentName,
    this.opponentTeamId,
    this.opponentTeamName,
    this.matchType,
    this.ageGroup,
    this.skillLevel,
    this.location,
    this.venueId,
    this.venueName,
    required this.proposedDateTime,
    this.alternativeDateTime1,
    this.alternativeDateTime2,
    this.wagerAmount,
    this.wagerType,
    this.description,
    this.rules,
    this.status,
    required this.expiresAt,
    this.acceptedAt,
    this.completedAt,
    this.winnerId,
    this.winnerName,
    this.winnerTeamId,
    this.winnerTeamName,
    this.matchResult,
    required this.isResultDisputed,
    required this.responseCount,
    required this.created,
  });

  factory ChallengeResponseDto.fromJson(Map<String, dynamic> json) {
    return ChallengeResponseDto(
      id: json['id'] as String,
      challengerId: json['challengerId'] as String,
      challengerName: json['challengerName'] as String?,
      challengerTeamId: json['challengerTeamId'] as String?,
      challengerTeamName: json['challengerTeamName'] as String?,
      opponentId: json['opponentId'] as String?,
      opponentName: json['opponentName'] as String?,
      opponentTeamId: json['opponentTeamId'] as String?,
      opponentTeamName: json['opponentTeamName'] as String?,
      matchType: json['matchType'] as String?,
      ageGroup: json['ageGroup'] as String?,
      skillLevel: json['skillLevel'] as String?,
      location: json['location'] as String?,
      venueId: json['venueId'] as String?,
      venueName: json['venueName'] as String?,
      proposedDateTime: DateTime.parse(json['proposedDateTime'] as String),
      alternativeDateTime1: json['alternativeDateTime1'] != null
          ? DateTime.parse(json['alternativeDateTime1'] as String)
          : null,
      alternativeDateTime2: json['alternativeDateTime2'] != null
          ? DateTime.parse(json['alternativeDateTime2'] as String)
          : null,
      wagerAmount: json['wagerAmount'] as double?,
      wagerType: json['wagerType'] as String?,
      description: json['description'] as String?,
      rules: json['rules'] as String?,
      status: json['status'] as String?,
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      acceptedAt: json['acceptedAt'] != null
          ? DateTime.parse(json['acceptedAt'] as String)
          : null,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      winnerId: json['winnerId'] as String?,
      winnerName: json['winnerName'] as String?,
      winnerTeamId: json['winnerTeamId'] as String?,
      winnerTeamName: json['winnerTeamName'] as String?,
      matchResult: json['matchResult'] as String?,
      isResultDisputed: json['isResultDisputed'] as bool? ?? false,
      responseCount: json['responseCount'] as int? ?? 0,
      created: DateTime.parse(
          json['created'] as String? ?? DateTime.now().toIso8601String()),
    );
  }

  factory ChallengeResponseDto.fromJsonString(String jsonString) {
    return ChallengeResponseDto.fromJson(json.decode(jsonString));
  }
}

/// DTO for challenge detail response from API
class ChallengeDetailResponseDto extends ChallengeResponseDto {
  final List<ChallengeResponseResponseDto>? responses;

  const ChallengeDetailResponseDto({
    required super.id,
    required super.challengerId,
    super.challengerName,
    super.challengerTeamId,
    super.challengerTeamName,
    super.opponentId,
    super.opponentName,
    super.opponentTeamId,
    super.opponentTeamName,
    super.matchType,
    super.ageGroup,
    super.skillLevel,
    super.location,
    super.venueId,
    super.venueName,
    required super.proposedDateTime,
    super.alternativeDateTime1,
    super.alternativeDateTime2,
    super.wagerAmount,
    super.wagerType,
    super.description,
    super.rules,
    super.status,
    required super.expiresAt,
    super.acceptedAt,
    super.completedAt,
    super.winnerId,
    super.winnerName,
    super.winnerTeamId,
    super.winnerTeamName,
    super.matchResult,
    required super.isResultDisputed,
    required super.responseCount,
    required super.created,
    this.responses,
  });

  factory ChallengeDetailResponseDto.fromJson(Map<String, dynamic> json) {
    return ChallengeDetailResponseDto(
      id: json['id'] as String,
      challengerId: json['challengerId'] as String,
      challengerName: json['challengerName'] as String?,
      challengerTeamId: json['challengerTeamId'] as String?,
      challengerTeamName: json['challengerTeamName'] as String?,
      opponentId: json['opponentId'] as String?,
      opponentName: json['opponentName'] as String?,
      opponentTeamId: json['opponentTeamId'] as String?,
      opponentTeamName: json['opponentTeamName'] as String?,
      matchType: json['matchType'] as String?,
      ageGroup: json['ageGroup'] as String?,
      skillLevel: json['skillLevel'] as String?,
      location: json['location'] as String?,
      venueId: json['venueId'] as String?,
      venueName: json['venueName'] as String?,
      proposedDateTime: DateTime.parse(json['proposedDateTime'] as String),
      alternativeDateTime1: json['alternativeDateTime1'] != null
          ? DateTime.parse(json['alternativeDateTime1'] as String)
          : null,
      alternativeDateTime2: json['alternativeDateTime2'] != null
          ? DateTime.parse(json['alternativeDateTime2'] as String)
          : null,
      wagerAmount: json['wagerAmount'] as double?,
      wagerType: json['wagerType'] as String?,
      description: json['description'] as String?,
      rules: json['rules'] as String?,
      status: json['status'] as String?,
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      acceptedAt: json['acceptedAt'] != null
          ? DateTime.parse(json['acceptedAt'] as String)
          : null,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      winnerId: json['winnerId'] as String?,
      winnerName: json['winnerName'] as String?,
      winnerTeamId: json['winnerTeamId'] as String?,
      winnerTeamName: json['winnerTeamName'] as String?,
      matchResult: json['matchResult'] as String?,
      isResultDisputed: json['isResultDisputed'] as bool? ?? false,
      responseCount: json['responseCount'] as int? ?? 0,
      created: DateTime.parse(
          json['created'] as String? ?? DateTime.now().toIso8601String()),
      responses: json['responses'] != null
          ? (json['responses'] as List<dynamic>)
              .map((e) => ChallengeResponseResponseDto.fromJson(e))
              .toList()
          : null,
    );
  }
}

/// DTO for challenge response response from API
class ChallengeResponseResponseDto {
  final String id;
  final String responderId;
  final String? responderName;
  final String? responderTeamId;
  final String? responderTeamName;
  final String? responseType;
  final DateTime? preferredDateTime;
  final String? message;
  final DateTime respondedAt;

  const ChallengeResponseResponseDto({
    required this.id,
    required this.responderId,
    this.responderName,
    this.responderTeamId,
    this.responderTeamName,
    this.responseType,
    this.preferredDateTime,
    this.message,
    required this.respondedAt,
  });

  factory ChallengeResponseResponseDto.fromJson(Map<String, dynamic> json) {
    return ChallengeResponseResponseDto(
      id: json['id'] as String,
      responderId: json['responderId'] as String,
      responderName: json['responderName'] as String?,
      responderTeamId: json['responderTeamId'] as String?,
      responderTeamName: json['responderTeamName'] as String?,
      responseType: json['responseType'] as String?,
      preferredDateTime: json['preferredDateTime'] != null
          ? DateTime.parse(json['preferredDateTime'] as String)
          : null,
      message: json['message'] as String?,
      respondedAt: DateTime.parse(json['respondedAt'] as String),
    );
  }
}

/// DTO for paged challenges response from API
class PagedChallengesResponseDto {
  final List<ChallengeResponseDto>? items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  const PagedChallengesResponseDto({
    this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  factory PagedChallengesResponseDto.fromJson(Map<String, dynamic> json) {
    return PagedChallengesResponseDto(
      items: json['items'] != null
          ? (json['items'] as List<dynamic>)
              .map((e) => ChallengeResponseDto.fromJson(e))
              .toList()
          : null,
      total: json['total'] as int? ?? 0,
      page: json['page'] as int? ?? 1,
      pageSize: json['pageSize'] as int? ?? 20,
      totalPages: json['totalPages'] as int? ?? 0,
    );
  }
}

/// DTO for challenge stats response from API
class ChallengeStatsResponseDto {
  final int totalChallenges;
  final int openChallenges;
  final int acceptedChallenges;
  final int completedChallenges;
  final int wonChallenges;
  final int lostChallenges;
  final double winRate;
  final int disputedChallenges;

  const ChallengeStatsResponseDto({
    required this.totalChallenges,
    required this.openChallenges,
    required this.acceptedChallenges,
    required this.completedChallenges,
    required this.wonChallenges,
    required this.lostChallenges,
    required this.winRate,
    required this.disputedChallenges,
  });

  factory ChallengeStatsResponseDto.fromJson(Map<String, dynamic> json) {
    return ChallengeStatsResponseDto(
      totalChallenges: json['totalChallenges'] as int,
      openChallenges: json['openChallenges'] as int,
      acceptedChallenges: json['acceptedChallenges'] as int,
      completedChallenges: json['completedChallenges'] as int,
      wonChallenges: json['wonChallenges'] as int,
      lostChallenges: json['lostChallenges'] as int,
      winRate: (json['winRate'] as num).toDouble(),
      disputedChallenges: json['disputedChallenges'] as int,
    );
  }
}

/// DTO for match suggestion response from API
class MatchSuggestionResponseDto {
  final String id;
  final String? type;
  final String? matchType;
  final String? location;
  final DateTime? proposedDateTime;
  final String? opponentName;
  final String? teamName;
  final double? wagerAmount;
  final int compatibilityScore;
  final String? description;

  const MatchSuggestionResponseDto({
    required this.id,
    this.type,
    this.matchType,
    this.location,
    this.proposedDateTime,
    this.opponentName,
    this.teamName,
    this.wagerAmount,
    required this.compatibilityScore,
    this.description,
  });

  factory MatchSuggestionResponseDto.fromJson(Map<String, dynamic> json) {
    return MatchSuggestionResponseDto(
      id: json['id'] as String,
      type: json['type'] as String?,
      matchType: json['matchType'] as String?,
      location: json['location'] as String?,
      proposedDateTime: json['proposedDateTime'] != null
          ? DateTime.parse(json['proposedDateTime'] as String)
          : null,
      opponentName: json['opponentName'] as String?,
      teamName: json['teamName'] as String?,
      wagerAmount: json['wagerAmount'] as double?,
      compatibilityScore: json['compatibilityScore'] as int,
      description: json['description'] as String?,
    );
  }
}

/// DTO for creating a match request
class CreateMatchRequestRequestDto {
  final String? matchType;
  final String? preferredLocation;
  final String? skillLevel;
  final String? ageGroup;
  final DateTime? preferredDateTime;
  final String? description;
  final int? maxDistance;
  final double? maxWagerAmount;
  final bool acceptWagers;
  final int expirationHours;

  const CreateMatchRequestRequestDto({
    this.matchType,
    this.preferredLocation,
    this.skillLevel,
    this.ageGroup,
    this.preferredDateTime,
    this.description,
    this.maxDistance,
    this.maxWagerAmount,
    required this.acceptWagers,
    required this.expirationHours,
  });

  Map<String, dynamic> toJson() {
    return {
      'matchType': matchType,
      'preferredLocation': preferredLocation,
      'skillLevel': skillLevel,
      'ageGroup': ageGroup,
      'preferredDateTime': preferredDateTime?.toIso8601String(),
      'description': description,
      'maxDistance': maxDistance,
      'maxWagerAmount': maxWagerAmount,
      'acceptWagers': acceptWagers,
      'expirationHours': expirationHours,
    };
  }

  String toJsonString() => json.encode(toJson());
}
