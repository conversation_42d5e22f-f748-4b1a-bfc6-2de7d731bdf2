import 'dart:math';

import '../../../../core/networking/api_client.dart';
import '../../domain/entities/challenge.dart';
import '../../domain/entities/challenge_enums.dart';
import '../dto/challenge_dto.dart';

abstract interface class ChallengeDataSource {
  // Challenge operations
  Future<PagedChallenges> getChallenges({
    String? matchType,
    String? location,
    String? skillLevel,
    String? ageGroup,
    double? maxWagerAmount,
    bool? hasWager,
    DateTime? fromDate,
    DateTime? toDate,
    int? page,
    int? pageSize,
  });

  Future<Challenge> getChallengeById(String challengeId);

  Future<Challenge> createChallenge(CreateChallengeRequestDto request);

  Future<Challenge> updateChallenge(
      String challengeId, UpdateChallengeRequestDto request);

  Future<void> deleteChallenge(String challengeId);

  Future<Challenge> respondToChallenge(RespondToChallengeRequestDto request);

  Future<void> submitMatchResult(SubmitMatchResultRequestDto request);

  Future<void> disputeMatchResult(DisputeMatchResultRequestDto request);

  // Statistics and suggestions
  Future<ChallengeStats> getChallengeStats();

  Future<List<MatchSuggestion>> getMatchSuggestions();

  // User's challenges
  Future<PagedChallenges> getMyChallenges({
    int? page,
    int? pageSize,
  });

  // Match requests
  Future<List<MatchSuggestion>> getMatchRequests({
    String? matchType,
    String? location,
    String? skillLevel,
    String? ageGroup,
    int? maxDistance,
    bool? acceptWagers,
    int? page,
    int? pageSize,
  });

  Future<MatchSuggestion> createMatchRequest(
      CreateMatchRequestRequestDto request);

  Future<List<MatchSuggestion>> getMyMatchRequests({
    int? page,
    int? pageSize,
  });
}

class ChallengeDataSourceImpl implements ChallengeDataSource {
  final ApiClient _apiClient;
  final Random _random = Random();

  // Mock data for challenges
  final List<Challenge> _mockChallenges = [];
  final List<MatchSuggestion> _mockMatchSuggestions = [];

  ChallengeDataSourceImpl(this._apiClient) {
    _initializeMockData();
  }

  void _initializeMockData() {
    // Create mock challenges
    _mockChallenges.addAll([
      Challenge(
        id: 'challenge-1',
        challengerId: 'user-1',
        challengerName: 'John Smith',
        challengerTeamId: 'team-1',
        challengerTeamName: 'Thunder Hawks',
        opponentId: null,
        opponentName: null,
        opponentTeamId: null,
        opponentTeamName: null,
        matchType: 'Basketball',
        ageGroup: AgeGroup.senior,
        skillLevel: 'Intermediate',
        location: 'Downtown Sports Center',
        venueId: 'venue-1',
        venueName: 'Downtown Sports Center',
        proposedDateTime: DateTime.now().add(const Duration(days: 3)),
        alternativeDateTime1: DateTime.now().add(const Duration(days: 4)),
        alternativeDateTime2: DateTime.now().add(const Duration(days: 5)),
        wagerAmount: 50.0,
        wagerType: WagerType.money,
        description:
            'Looking for a competitive basketball match. 5v5 format, standard rules apply.',
        rules: 'Standard basketball rules. 4 quarters of 10 minutes each.',
        status: 'Open',
        expiresAt: DateTime.now().add(const Duration(days: 7)),
        acceptedAt: null,
        completedAt: null,
        winnerId: null,
        winnerName: null,
        winnerTeamId: null,
        winnerTeamName: null,
        matchResult: null,
        isResultDisputed: false,
        responseCount: 2,
        created: DateTime.now().subtract(const Duration(hours: 2)),
        responses: <ChallengeResponse>[
          ChallengeResponse(
            id: 'response-1',
            responderId: 'user-2',
            responderName: 'Mike Johnson',
            responderTeamId: 'team-2',
            responderTeamName: 'Lightning Bolts',
            responseType: 'accepted',
            preferredDateTime: DateTime.now().add(const Duration(days: 3)),
            message: 'Sounds great! We\'re in for the challenge.',
            respondedAt: DateTime.now().subtract(const Duration(hours: 1)),
          ),
          ChallengeResponse(
            id: 'response-2',
            responderId: 'user-3',
            responderName: 'Sarah Wilson',
            responderTeamId: 'team-3',
            responderTeamName: 'Storm Riders',
            responseType: 'counter_offer',
            preferredDateTime: DateTime.now().add(const Duration(days: 4)),
            message: 'Can we play on Friday instead?',
            respondedAt: DateTime.now().subtract(const Duration(minutes: 30)),
          ),
        ],
      ),
      Challenge(
        id: 'challenge-2',
        challengerId: 'user-4',
        challengerName: 'Alex Chen',
        challengerTeamId: 'team-4',
        challengerTeamName: 'Dragon Warriors',
        opponentId: 'user-5',
        opponentName: 'David Brown',
        opponentTeamId: 'team-5',
        opponentTeamName: 'Phoenix Rising',
        matchType: 'Soccer',
        ageGroup: AgeGroup.u21,
        skillLevel: 'Advanced',
        location: 'Central Park Field',
        venueId: 'venue-2',
        venueName: 'Central Park Field',
        proposedDateTime: DateTime.now().add(const Duration(days: 1)),
        alternativeDateTime1: null,
        alternativeDateTime2: null,
        wagerAmount: 100.0,
        wagerType: WagerType.trophy,
        description:
            'High-stakes soccer match. Winner takes the Golden Boot trophy!',
        rules: '11v11, 90 minutes, FIFA rules.',
        status: 'Accepted',
        expiresAt: DateTime.now().add(const Duration(days: 2)),
        acceptedAt: DateTime.now().subtract(const Duration(hours: 1)),
        completedAt: null,
        winnerId: null,
        winnerName: null,
        winnerTeamId: null,
        winnerTeamName: null,
        matchResult: null,
        isResultDisputed: false,
        responseCount: 1,
        created: DateTime.now().subtract(const Duration(days: 1)),
        responses: <ChallengeResponse>[
          ChallengeResponse(
            id: 'response-3',
            responderId: 'user-5',
            responderName: 'David Brown',
            responderTeamId: 'team-5',
            responderTeamName: 'Phoenix Rising',
            responseType: 'accepted',
            preferredDateTime: DateTime.now().add(const Duration(days: 1)),
            message: 'Challenge accepted! Let\'s make this epic!',
            respondedAt: DateTime.now().subtract(const Duration(hours: 1)),
          ),
        ],
      ),
      Challenge(
        id: 'challenge-3',
        challengerId: 'user-6',
        challengerName: 'Emma Davis',
        challengerTeamId: null,
        challengerTeamName: null,
        opponentId: 'user-7',
        opponentName: 'Tom Wilson',
        opponentTeamId: null,
        opponentTeamName: null,
        matchType: 'Tennis',
        ageGroup: AgeGroup.senior,
        skillLevel: 'Beginner',
        location: 'Riverside Tennis Club',
        venueId: 'venue-3',
        venueName: 'Riverside Tennis Club',
        proposedDateTime: DateTime.now().add(const Duration(days: 5)),
        alternativeDateTime1: DateTime.now().add(const Duration(days: 6)),
        alternativeDateTime2: null,
        wagerAmount: null,
        wagerType: WagerType.braggingRights,
        description: 'Friendly tennis match for beginners. Just for fun!',
        rules: 'Best of 3 sets, standard tennis rules.',
        status: 'Completed',
        expiresAt: DateTime.now().add(const Duration(days: 7)),
        acceptedAt: DateTime.now().subtract(const Duration(days: 2)),
        completedAt: DateTime.now().subtract(const Duration(hours: 3)),
        winnerId: 'user-6',
        winnerName: 'Emma Davis',
        winnerTeamId: null,
        winnerTeamName: null,
        matchResult: 'Emma Davis won 2-1',
        isResultDisputed: false,
        responseCount: 1,
        created: DateTime.now().subtract(const Duration(days: 3)),
        responses: <ChallengeResponse>[
          ChallengeResponse(
            id: 'response-4',
            responderId: 'user-7',
            responderName: 'Tom Wilson',
            responderTeamId: null,
            responderTeamName: null,
            responseType: 'accepted',
            preferredDateTime: DateTime.now().add(const Duration(days: 5)),
            message: 'Perfect! I\'ve been wanting to play tennis.',
            respondedAt: DateTime.now().subtract(const Duration(days: 2)),
          ),
        ],
      ),
      Challenge(
        id: 'challenge-4',
        challengerId: 'user-8',
        challengerName: 'Lisa Garcia',
        challengerTeamId: 'team-6',
        challengerTeamName: 'Viking Raiders',
        opponentId: null,
        opponentName: null,
        opponentTeamId: null,
        opponentTeamName: null,
        matchType: 'Volleyball',
        ageGroup: AgeGroup.u18,
        skillLevel: 'Intermediate',
        location: 'Beach Volleyball Court',
        venueId: 'venue-4',
        venueName: 'Beach Volleyball Court',
        proposedDateTime: DateTime.now().add(const Duration(days: 2)),
        alternativeDateTime1: DateTime.now().add(const Duration(days: 3)),
        alternativeDateTime2: DateTime.now().add(const Duration(days: 4)),
        wagerAmount: 25.0,
        wagerType: WagerType.money,
        description: 'Beach volleyball tournament! 6v6 format.',
        rules: 'Best of 3 games, beach volleyball rules.',
        status: 'Open',
        expiresAt: DateTime.now().add(const Duration(days: 5)),
        acceptedAt: null,
        completedAt: null,
        winnerId: null,
        winnerName: null,
        winnerTeamId: null,
        winnerTeamName: null,
        matchResult: null,
        isResultDisputed: false,
        responseCount: 0,
        created: DateTime.now().subtract(const Duration(hours: 4)),
        responses: <ChallengeResponse>[],
      ),
      Challenge(
        id: 'challenge-5',
        challengerId: 'user-9',
        challengerName: 'Carlos Rodriguez',
        challengerTeamId: 'team-7',
        challengerTeamName: 'Elite Warriors',
        opponentId: 'user-10',
        opponentName: 'Maria Santos',
        opponentTeamId: 'team-8',
        opponentTeamName: 'Royal Knights',
        matchType: 'Basketball',
        ageGroup: AgeGroup.senior,
        skillLevel: 'Advanced',
        location: 'Pro Arena',
        venueId: 'venue-5',
        venueName: 'Pro Arena',
        proposedDateTime: DateTime.now().subtract(const Duration(days: 1)),
        alternativeDateTime1: null,
        alternativeDateTime2: null,
        wagerAmount: 200.0,
        wagerType: WagerType.money,
        description: 'Professional level basketball match. High stakes!',
        rules: '5v5, 4 quarters of 12 minutes, NBA rules.',
        status: 'Disputed',
        expiresAt: DateTime.now().add(const Duration(days: 1)),
        acceptedAt: DateTime.now().subtract(const Duration(days: 2)),
        completedAt: DateTime.now().subtract(const Duration(hours: 2)),
        winnerId: 'user-9',
        winnerName: 'Carlos Rodriguez',
        winnerTeamId: 'team-7',
        winnerTeamName: 'Elite Warriors',
        matchResult: 'Elite Warriors won 98-95',
        isResultDisputed: true,
        responseCount: 1,
        created: DateTime.now().subtract(const Duration(days: 3)),
        responses: <ChallengeResponse>[
          ChallengeResponse(
            id: 'response-5',
            responderId: 'user-10',
            responderName: 'Maria Santos',
            responderTeamId: 'team-8',
            responderTeamName: 'Royal Knights',
            responseType: 'accepted',
            preferredDateTime: DateTime.now().subtract(const Duration(days: 1)),
            message: 'Game on! This will be intense!',
            respondedAt: DateTime.now().subtract(const Duration(days: 2)),
          ),
        ],
      ),
    ]);

    // Create mock match suggestions
    _mockMatchSuggestions.addAll([
      MatchSuggestion(
        id: 'suggestion-1',
        type: 'basketball',
        matchType: 'Basketball',
        location: 'Downtown Sports Center',
        proposedDateTime: DateTime.now().add(const Duration(days: 2)),
        opponentName: 'James Wilson',
        teamName: 'Thunder Hawks',
        wagerAmount: 50.0,
        compatibilityScore: 85,
        description: 'Looking for basketball players for a friendly game',
      ),
      MatchSuggestion(
        id: 'suggestion-2',
        type: 'soccer',
        matchType: 'Soccer',
        location: 'Central Park Field',
        proposedDateTime: DateTime.now().add(const Duration(days: 4)),
        opponentName: 'Anna Lee',
        teamName: 'Phoenix Rising',
        wagerAmount: null,
        compatibilityScore: 92,
        description: 'Need players for a competitive soccer match',
      ),
    ]);
  }

  @override
  Future<PagedChallenges> getChallenges({
    String? matchType,
    String? location,
    String? skillLevel,
    String? ageGroup,
    double? maxWagerAmount,
    bool? hasWager,
    DateTime? fromDate,
    DateTime? toDate,
    int? page,
    int? pageSize,
  }) async {
    // Use mock data instead of API call for testing
    await Future.delayed(Duration(milliseconds: 500 + _random.nextInt(1000)));

    // Filter challenges based on parameters
    List<Challenge> filteredChallenges = _mockChallenges.where((challenge) {
      if (matchType != null && challenge.matchType != matchType) return false;
      if (location != null && challenge.location != location) return false;
      if (skillLevel != null && challenge.skillLevel != skillLevel)
        return false;
      if (ageGroup != null && challenge.ageGroup?.value != ageGroup)
        return false;
      if (maxWagerAmount != null &&
          (challenge.wagerAmount == null ||
              challenge.wagerAmount! > maxWagerAmount)) return false;
      if (hasWager != null) {
        if (hasWager && challenge.wagerAmount == null) return false;
        if (!hasWager && challenge.wagerAmount != null) return false;
      }
      if (fromDate != null && challenge.proposedDateTime.isBefore(fromDate))
        return false;
      if (toDate != null && challenge.proposedDateTime.isAfter(toDate))
        return false;
      return true;
    }).toList();

    // Pagination
    final pageNum = page ?? 1;
    final size = pageSize ?? 10;
    final startIndex = (pageNum - 1) * size;
    final endIndex = startIndex + size;

    final paginatedChallenges = filteredChallenges.length > startIndex
        ? filteredChallenges.sublist(
            startIndex,
            endIndex > filteredChallenges.length
                ? filteredChallenges.length
                : endIndex)
        : <Challenge>[];

    return PagedChallenges(
      items: paginatedChallenges,
      total: filteredChallenges.length,
      page: pageNum,
      pageSize: size,
      totalPages: (filteredChallenges.length / size).ceil(),
    );
  }

  @override
  Future<Challenge> getChallengeById(String challengeId) async {
    await Future.delayed(Duration(milliseconds: 300 + _random.nextInt(500)));

    final challenge = _mockChallenges.firstWhere(
      (c) => c.id == challengeId,
      orElse: () => throw Exception('Challenge not found'),
    );

    return challenge;
  }

  @override
  Future<Challenge> createChallenge(CreateChallengeRequestDto request) async {
    await Future.delayed(Duration(milliseconds: 800 + _random.nextInt(1000)));

    final newChallenge = Challenge(
      id: 'challenge-${DateTime.now().millisecondsSinceEpoch}',
      challengerId: 'current-user',
      challengerName: 'Current User',
      challengerTeamId: request.challengerTeamId,
      challengerTeamName: request.challengerTeamId != null ? 'My Team' : null,
      opponentId: request.specificOpponentId,
      opponentName: request.specificOpponentId != null ? 'Opponent Name' : null,
      opponentTeamId: request.specificOpponentTeamId,
      opponentTeamName:
          request.specificOpponentTeamId != null ? 'Opponent Team' : null,
      matchType: request.matchType,
      ageGroup: AgeGroup.fromString(request.ageGroup),
      skillLevel: request.skillLevel,
      location: request.location,
      venueId: request.venueId,
      venueName: request.venueId != null ? 'Venue Name' : null,
      proposedDateTime: request.proposedDateTime,
      alternativeDateTime1: request.alternativeDateTime1,
      alternativeDateTime2: request.alternativeDateTime2,
      wagerAmount: request.wagerAmount,
      wagerType: WagerType.fromString(request.wagerType),
      description: request.description,
      rules: request.rules,
      status: 'Open',
      expiresAt: DateTime.now().add(Duration(hours: request.expirationHours)),
      acceptedAt: null,
      completedAt: null,
      winnerId: null,
      winnerName: null,
      winnerTeamId: null,
      winnerTeamName: null,
      matchResult: null,
      isResultDisputed: false,
      responseCount: 0,
      created: DateTime.now(),
      responses: <ChallengeResponse>[],
    );

    _mockChallenges.add(newChallenge);
    return newChallenge;
  }

  @override
  Future<Challenge> updateChallenge(
      String challengeId, UpdateChallengeRequestDto request) async {
    await Future.delayed(Duration(milliseconds: 600 + _random.nextInt(800)));

    final index = _mockChallenges.indexWhere((c) => c.id == challengeId);
    if (index == -1) throw Exception('Challenge not found');

    final existingChallenge = _mockChallenges[index];
    final updatedChallenge = Challenge(
      id: existingChallenge.id,
      challengerId: existingChallenge.challengerId,
      challengerName: existingChallenge.challengerName,
      challengerTeamId: existingChallenge.challengerTeamId,
      challengerTeamName: existingChallenge.challengerTeamName,
      opponentId: existingChallenge.opponentId,
      opponentName: existingChallenge.opponentName,
      opponentTeamId: existingChallenge.opponentTeamId,
      opponentTeamName: existingChallenge.opponentTeamName,
      matchType: request.matchType ?? existingChallenge.matchType,
      ageGroup:
          AgeGroup.fromString(request.ageGroup) ?? existingChallenge.ageGroup,
      skillLevel: request.skillLevel ?? existingChallenge.skillLevel,
      location: request.location ?? existingChallenge.location,
      venueId: request.venueId ?? existingChallenge.venueId,
      venueName: existingChallenge.venueName,
      proposedDateTime:
          request.proposedDateTime ?? existingChallenge.proposedDateTime,
      alternativeDateTime1: request.alternativeDateTime1 ??
          existingChallenge.alternativeDateTime1,
      alternativeDateTime2: request.alternativeDateTime2 ??
          existingChallenge.alternativeDateTime2,
      wagerAmount: request.wagerAmount ?? existingChallenge.wagerAmount,
      wagerType: WagerType.fromString(request.wagerType) ??
          existingChallenge.wagerType,
      description: request.description ?? existingChallenge.description,
      rules: request.rules ?? existingChallenge.rules,
      status: request.status ?? existingChallenge.status,
      expiresAt: existingChallenge.expiresAt,
      acceptedAt: existingChallenge.acceptedAt,
      completedAt: existingChallenge.completedAt,
      winnerId: existingChallenge.winnerId,
      winnerName: existingChallenge.winnerName,
      winnerTeamId: existingChallenge.winnerTeamId,
      winnerTeamName: existingChallenge.winnerTeamName,
      matchResult: existingChallenge.matchResult,
      isResultDisputed: existingChallenge.isResultDisputed,
      responseCount: existingChallenge.responseCount,
      created: existingChallenge.created,
      responses: existingChallenge.responses,
    );

    _mockChallenges[index] = updatedChallenge;
    return updatedChallenge;
  }

  @override
  Future<void> deleteChallenge(String challengeId) async {
    await Future.delayed(Duration(milliseconds: 400 + _random.nextInt(600)));

    final index = _mockChallenges.indexWhere((c) => c.id == challengeId);
    if (index == -1) throw Exception('Challenge not found');

    _mockChallenges.removeAt(index);
  }

  @override
  Future<Challenge> respondToChallenge(
      RespondToChallengeRequestDto request) async {
    await Future.delayed(Duration(milliseconds: 700 + _random.nextInt(1000)));

    final challengeIndex =
        _mockChallenges.indexWhere((c) => c.id == request.challengeId);
    if (challengeIndex == -1) throw Exception('Challenge not found');

    final existingChallenge = _mockChallenges[challengeIndex];
    final newResponse = ChallengeResponse(
      id: 'response-${DateTime.now().millisecondsSinceEpoch}',
      responderId: 'current-user',
      responderName: 'Current User',
      responderTeamId: request.responderTeamId,
      responderTeamName: request.responderTeamId != null ? 'My Team' : null,
      responseType: request.responseType,
      preferredDateTime:
          request.preferredDateTime ?? existingChallenge.proposedDateTime,
      message: request.message,
      respondedAt: DateTime.now(),
    );

    final updatedResponses = <ChallengeResponse>[
      ...(existingChallenge.responses ?? []),
      newResponse
    ];

    final updatedChallenge = Challenge(
      id: existingChallenge.id,
      challengerId: existingChallenge.challengerId,
      challengerName: existingChallenge.challengerName,
      challengerTeamId: existingChallenge.challengerTeamId,
      challengerTeamName: existingChallenge.challengerTeamName,
      opponentId: existingChallenge.opponentId,
      opponentName: existingChallenge.opponentName,
      opponentTeamId: existingChallenge.opponentTeamId,
      opponentTeamName: existingChallenge.opponentTeamName,
      matchType: existingChallenge.matchType,
      ageGroup: existingChallenge.ageGroup,
      skillLevel: existingChallenge.skillLevel,
      location: existingChallenge.location,
      venueId: existingChallenge.venueId,
      venueName: existingChallenge.venueName,
      proposedDateTime: existingChallenge.proposedDateTime,
      alternativeDateTime1: existingChallenge.alternativeDateTime1,
      alternativeDateTime2: existingChallenge.alternativeDateTime2,
      wagerAmount: existingChallenge.wagerAmount,
      wagerType: existingChallenge.wagerType,
      description: existingChallenge.description,
      rules: existingChallenge.rules,
      status: request.responseType == 'accepted'
          ? 'Accepted'
          : existingChallenge.status,
      expiresAt: existingChallenge.expiresAt,
      acceptedAt: request.responseType == 'accepted'
          ? DateTime.now()
          : existingChallenge.acceptedAt,
      completedAt: existingChallenge.completedAt,
      winnerId: existingChallenge.winnerId,
      winnerName: existingChallenge.winnerName,
      winnerTeamId: existingChallenge.winnerTeamId,
      winnerTeamName: existingChallenge.winnerTeamName,
      matchResult: existingChallenge.matchResult,
      isResultDisputed: existingChallenge.isResultDisputed,
      responseCount: existingChallenge.responseCount + 1,
      created: existingChallenge.created,
      responses: updatedResponses,
    );

    _mockChallenges[challengeIndex] = updatedChallenge;
    return updatedChallenge;
  }

  @override
  Future<void> submitMatchResult(SubmitMatchResultRequestDto request) async {
    await Future.delayed(Duration(milliseconds: 500 + _random.nextInt(800)));

    final challengeIndex =
        _mockChallenges.indexWhere((c) => c.id == request.challengeId);
    if (challengeIndex == -1) throw Exception('Challenge not found');

    final existingChallenge = _mockChallenges[challengeIndex];
    final updatedChallenge = Challenge(
      id: existingChallenge.id,
      challengerId: existingChallenge.challengerId,
      challengerName: existingChallenge.challengerName,
      challengerTeamId: existingChallenge.challengerTeamId,
      challengerTeamName: existingChallenge.challengerTeamName,
      opponentId: existingChallenge.opponentId,
      opponentName: existingChallenge.opponentName,
      opponentTeamId: existingChallenge.opponentTeamId,
      opponentTeamName: existingChallenge.opponentTeamName,
      matchType: existingChallenge.matchType,
      ageGroup: existingChallenge.ageGroup,
      skillLevel: existingChallenge.skillLevel,
      location: existingChallenge.location,
      venueId: existingChallenge.venueId,
      venueName: existingChallenge.venueName,
      proposedDateTime: existingChallenge.proposedDateTime,
      alternativeDateTime1: existingChallenge.alternativeDateTime1,
      alternativeDateTime2: existingChallenge.alternativeDateTime2,
      wagerAmount: existingChallenge.wagerAmount,
      wagerType: existingChallenge.wagerType,
      description: existingChallenge.description,
      rules: existingChallenge.rules,
      status: 'Completed',
      expiresAt: existingChallenge.expiresAt,
      acceptedAt: existingChallenge.acceptedAt,
      completedAt: DateTime.now(),
      winnerId: request.winnerId,
      winnerName: request.winnerId != null ? 'Winner Name' : null,
      winnerTeamId: request.winnerTeamId,
      winnerTeamName: request.winnerTeamId != null ? 'Winner Team' : null,
      matchResult: request.matchResult,
      isResultDisputed: false,
      responseCount: existingChallenge.responseCount,
      created: existingChallenge.created,
      responses: existingChallenge.responses,
    );

    _mockChallenges[challengeIndex] = updatedChallenge;
  }

  @override
  Future<void> disputeMatchResult(DisputeMatchResultRequestDto request) async {
    await Future.delayed(Duration(milliseconds: 400 + _random.nextInt(600)));

    final challengeIndex =
        _mockChallenges.indexWhere((c) => c.id == request.challengeId);
    if (challengeIndex == -1) throw Exception('Challenge not found');

    final existingChallenge = _mockChallenges[challengeIndex];
    final updatedChallenge = Challenge(
      id: existingChallenge.id,
      challengerId: existingChallenge.challengerId,
      challengerName: existingChallenge.challengerName,
      challengerTeamId: existingChallenge.challengerTeamId,
      challengerTeamName: existingChallenge.challengerTeamName,
      opponentId: existingChallenge.opponentId,
      opponentName: existingChallenge.opponentName,
      opponentTeamId: existingChallenge.opponentTeamId,
      opponentTeamName: existingChallenge.opponentTeamName,
      matchType: existingChallenge.matchType,
      ageGroup: existingChallenge.ageGroup,
      skillLevel: existingChallenge.skillLevel,
      location: existingChallenge.location,
      venueId: existingChallenge.venueId,
      venueName: existingChallenge.venueName,
      proposedDateTime: existingChallenge.proposedDateTime,
      alternativeDateTime1: existingChallenge.alternativeDateTime1,
      alternativeDateTime2: existingChallenge.alternativeDateTime2,
      wagerAmount: existingChallenge.wagerAmount,
      wagerType: existingChallenge.wagerType,
      description: existingChallenge.description,
      rules: existingChallenge.rules,
      status: 'Disputed',
      expiresAt: existingChallenge.expiresAt,
      acceptedAt: existingChallenge.acceptedAt,
      completedAt: existingChallenge.completedAt,
      winnerId: existingChallenge.winnerId,
      winnerName: existingChallenge.winnerName,
      winnerTeamId: existingChallenge.winnerTeamId,
      winnerTeamName: existingChallenge.winnerTeamName,
      matchResult: existingChallenge.matchResult,
      isResultDisputed: true,
      responseCount: existingChallenge.responseCount,
      created: existingChallenge.created,
      responses: existingChallenge.responses,
    );

    _mockChallenges[challengeIndex] = updatedChallenge;
  }

  @override
  Future<ChallengeStats> getChallengeStats() async {
    await Future.delayed(Duration(milliseconds: 300 + _random.nextInt(500)));

    final completedChallenges =
        _mockChallenges.where((c) => c.status == 'Completed').length;
    final wonChallenges = _mockChallenges
        .where((c) => c.status == 'Completed' && c.winnerId == 'current-user')
        .length;

    return ChallengeStats(
      totalChallenges: _mockChallenges.length,
      openChallenges: _mockChallenges.where((c) => c.status == 'Open').length,
      acceptedChallenges:
          _mockChallenges.where((c) => c.status == 'Accepted').length,
      completedChallenges: completedChallenges,
      wonChallenges: wonChallenges,
      lostChallenges: completedChallenges - wonChallenges,
      winRate: completedChallenges > 0
          ? (wonChallenges / completedChallenges) * 100
          : 0.0,
      disputedChallenges:
          _mockChallenges.where((c) => c.status == 'Disputed').length,
    );
  }

  @override
  Future<List<MatchSuggestion>> getMatchSuggestions() async {
    await Future.delayed(Duration(milliseconds: 400 + _random.nextInt(600)));
    return _mockMatchSuggestions;
  }

  @override
  Future<PagedChallenges> getMyChallenges({
    int? page,
    int? pageSize,
  }) async {
    await Future.delayed(Duration(milliseconds: 500 + _random.nextInt(800)));

    // Filter challenges where current user is challenger or opponent
    final myChallenges = _mockChallenges
        .where((c) =>
            c.challengerId == 'current-user' || c.opponentId == 'current-user')
        .toList();

    final pageNum = page ?? 1;
    final size = pageSize ?? 10;
    final startIndex = (pageNum - 1) * size;
    final endIndex = startIndex + size;

    final paginatedChallenges = myChallenges.length > startIndex
        ? myChallenges.sublist(startIndex,
            endIndex > myChallenges.length ? myChallenges.length : endIndex)
        : <Challenge>[];

    return PagedChallenges(
      items: paginatedChallenges,
      total: myChallenges.length,
      page: pageNum,
      pageSize: size,
      totalPages: (myChallenges.length / size).ceil(),
    );
  }

  @override
  Future<List<MatchSuggestion>> getMatchRequests({
    String? matchType,
    String? location,
    String? skillLevel,
    String? ageGroup,
    int? maxDistance,
    bool? acceptWagers,
    int? page,
    int? pageSize,
  }) async {
    await Future.delayed(Duration(milliseconds: 400 + _random.nextInt(600)));

    List<MatchSuggestion> filteredSuggestions =
        _mockMatchSuggestions.where((suggestion) {
      if (matchType != null && suggestion.matchType != matchType) return false;
      if (location != null && suggestion.location != location) return false;
      return true;
    }).toList();

    return filteredSuggestions;
  }

  @override
  Future<MatchSuggestion> createMatchRequest(
      CreateMatchRequestRequestDto request) async {
    await Future.delayed(Duration(milliseconds: 700 + _random.nextInt(1000)));

    final newSuggestion = MatchSuggestion(
      id: 'suggestion-${DateTime.now().millisecondsSinceEpoch}',
      type: request.matchType?.toLowerCase(),
      matchType: request.matchType,
      location: request.preferredLocation,
      proposedDateTime: request.preferredDateTime,
      opponentName: 'Current User',
      teamName: null,
      wagerAmount: request.maxWagerAmount,
      compatibilityScore: 90,
      description: request.description,
    );

    _mockMatchSuggestions.add(newSuggestion);
    return newSuggestion;
  }

  @override
  Future<List<MatchSuggestion>> getMyMatchRequests({
    int? page,
    int? pageSize,
  }) async {
    await Future.delayed(Duration(milliseconds: 400 + _random.nextInt(600)));

    return _mockMatchSuggestions
        .where((s) => s.opponentName == 'Current User')
        .toList();
  }
}
