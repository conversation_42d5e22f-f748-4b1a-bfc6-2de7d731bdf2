import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../entities/challenge.dart';
import '../../data/dto/challenge_dto.dart';

abstract interface class ChallengeRepository {
  // Challenge CRUD operations
  Future<Either<AppError, PagedChallenges>> getChallenges({
    String? matchType,
    String? location,
    String? skillLevel,
    String? ageGroup,
    double? maxWagerAmount,
    bool? hasWager,
    DateTime? fromDate,
    DateTime? toDate,
    int? page,
    int? pageSize,
  });

  Future<Either<AppError, Challenge>> getChallengeById(String challengeId);

  Future<Either<AppError, Challenge>> createChallenge(
      CreateChallengeRequestDto request);

  Future<Either<AppError, Challenge>> updateChallenge(
      String challengeId, UpdateChallengeRequestDto request);

  Future<Either<AppError, void>> deleteChallenge(String challengeId);

  // Challenge interactions
  Future<Either<AppError, Challenge>> respondToChallenge(
      RespondToChallengeRequestDto request);

  Future<Either<AppError, void>> submitMatchResult(
      SubmitMatchResultRequestDto request);

  Future<Either<AppError, void>> disputeMatchResult(
      DisputeMatchResultRequestDto request);

  // Statistics and suggestions
  Future<Either<AppError, ChallengeStats>> getChallengeStats();

  Future<Either<AppError, List<MatchSuggestion>>> getMatchSuggestions();

  // User's challenges
  Future<Either<AppError, PagedChallenges>> getMyChallenges({
    int? page,
    int? pageSize,
  });

  // Match requests
  Future<Either<AppError, List<MatchSuggestion>>> getMatchRequests({
    String? matchType,
    String? location,
    String? skillLevel,
    String? ageGroup,
    int? maxDistance,
    bool? acceptWagers,
    int? page,
    int? pageSize,
  });

  Future<Either<AppError, MatchSuggestion>> createMatchRequest(
      CreateMatchRequestRequestDto request);

  Future<Either<AppError, List<MatchSuggestion>>> getMyMatchRequests({
    int? page,
    int? pageSize,
  });
}
