import '../entities/player_ranking.dart';
import '../entities/challenge.dart';

/// Service for managing player rankings and ELO calculations
class RankingService {
  static const double initialEloRating = 1200.0;
  static const double kFactor = 32.0;
  static const int minimumMatchesForRank = 5;

  /// Calculate rank progression after a match
  static RankingUpdate calculateRankingUpdate({
    required PlayerRanking playerRanking,
    required PlayerRanking opponentRanking,
    required bool isWin,
    required Challenge challenge,
  }) {
    // Calculate ELO changes
    final eloResult = EloCalculator.calculateElo(
      winnerRating: isWin ? playerRanking.eloRating : opponentRanking.eloRating,
      loserRating: isWin ? opponentRanking.eloRating : playerRanking.eloRating,
      isUpset: EloCalculator.isUpset(
        isWin ? playerRanking.eloRating : opponentRanking.eloRating,
        isWin ? opponentRanking.eloRating : playerRanking.eloRating,
      ),
    );

    final eloChange = isWin ? eloResult.winnerChange : eloResult.loserChange;
    final newEloRating = playerRanking.eloRating + eloChange;

    // Calculate new rank
    final newRank = ChallengeRank.fromElo(newEloRating);
    final oldRank = playerRanking.rank;
    final hasRankChanged = newRank != oldRank;

    // Calculate streak
    final newStreak = _calculateNewStreak(playerRanking.currentStreak, isWin);
    final isStreakBroken = playerRanking.currentStreak > 0 && !isWin;
    final isNewStreakRecord = newStreak > playerRanking.longestStreak;

    // Calculate additional bonuses
    final bonuses = _calculateBonuses(
      challenge: challenge,
      playerRanking: playerRanking,
      isWin: isWin,
      newStreak: newStreak,
      isUpset: EloCalculator.isUpset(
            playerRanking.eloRating,
            opponentRanking.eloRating,
          ) &&
          isWin,
    );

    // Update win/loss counts
    final newWins =
        isWin ? playerRanking.challengesWon + 1 : playerRanking.challengesWon;
    final newLosses = !isWin
        ? playerRanking.challengesLost + 1
        : playerRanking.challengesLost;
    final newTotal = playerRanking.totalChallenges + 1;
    final newWinRate = newTotal > 0 ? newWins / newTotal : 0.0;

    // Update specialities based on match
    final updatedSpecialities = _updateSpecialities(
      playerRanking.specialities,
      challenge,
      isWin,
    );

    return RankingUpdate(
      newEloRating: newEloRating,
      eloChange: eloChange,
      newRank: newRank,
      oldRank: oldRank,
      hasRankChanged: hasRankChanged,
      rankProgression: hasRankChanged
          ? (newRank.index > oldRank.index
              ? RankProgression.promoted
              : RankProgression.demoted)
          : RankProgression.none,
      newStreak: newStreak,
      isStreakBroken: isStreakBroken,
      isNewStreakRecord: isNewStreakRecord,
      bonuses: bonuses,
      newWins: newWins,
      newLosses: newLosses,
      newTotal: newTotal,
      newWinRate: newWinRate,
      updatedSpecialities: updatedSpecialities,
    );
  }

  /// Calculate new streak value
  static int _calculateNewStreak(int currentStreak, bool isWin) {
    if (isWin) {
      return currentStreak >= 0 ? currentStreak + 1 : 1;
    } else {
      return currentStreak > 0 ? 0 : currentStreak - 1;
    }
  }

  /// Calculate various bonuses for the match
  static List<RankingBonus> _calculateBonuses({
    required Challenge challenge,
    required PlayerRanking playerRanking,
    required bool isWin,
    required int newStreak,
    required bool isUpset,
  }) {
    final bonuses = <RankingBonus>[];

    if (!isWin) return bonuses;

    // Streak bonus (3+ wins = 10% ELO bonus)
    if (newStreak >= 3) {
      bonuses.add(
        RankingBonus(
          type: BonusType.streak,
          description: 'Win streak bonus (${newStreak} wins)',
          multiplier: 1.1,
          points: newStreak * 5,
        ),
      );
    }

    // Upset bonus (beating higher-ranked opponent)
    if (isUpset) {
      bonuses.add(
        RankingBonus(
          type: BonusType.upset,
          description: 'Giant slayer bonus',
          multiplier: 1.2,
          points: 25,
        ),
      );
    }

    // High stakes bonus
    if (challenge.wagerAmount! >= 1000) {
      bonuses.add(
        RankingBonus(
          type: BonusType.highStakes,
          description: 'High stakes victory',
          multiplier: 1.05,
          points: 15,
        ),
      );
    }

    // Outside comfort zone bonus
    final isOutsideComfort = _isOutsideComfortZone(challenge, playerRanking);
    if (isOutsideComfort.isNotEmpty) {
      bonuses.add(
        RankingBonus(
          type: BonusType.outsideComfort,
          description: 'Comfort zone challenge: ${isOutsideComfort.join(', ')}',
          multiplier: 1.1,
          points: 20,
        ),
      );
    }

    // Night owl bonus (after 9 PM)
    if (challenge.proposedDateTime.hour >= 21) {
      bonuses.add(
        RankingBonus(
          type: BonusType.nightOwl,
          description: 'Night owl victory',
          multiplier: 1.02,
          points: 5,
        ),
      );
    }

    // Early bird bonus (before 9 AM)
    if (challenge.proposedDateTime.hour <= 9) {
      bonuses.add(
        RankingBonus(
          type: BonusType.earlyBird,
          description: 'Early bird victory',
          multiplier: 1.02,
          points: 5,
        ),
      );
    }

    // First challenge bonus
    if (playerRanking.totalChallenges == 0) {
      bonuses.add(
        RankingBonus(
          type: BonusType.firstChallenge,
          description: 'First challenge victory',
          multiplier: 1.0,
          points: 50,
        ),
      );
    }

    return bonuses;
  }

  /// Check if challenge is outside player's comfort zone
  static List<String> _isOutsideComfortZone(
    Challenge challenge,
    PlayerRanking playerRanking,
  ) {
    final outsideComfort = <String>[];

    // Check match type preference
    if (playerRanking.stats.preferredMatchType !=
        challenge.matchType.toString()) {
      outsideComfort.add('match type');
    }

    // Check time preference
    final challengeHour = challenge.proposedDateTime.hour;
    final preferredTime = playerRanking.stats.preferredTimeSlot;

    if (preferredTime == 'morning' && challengeHour >= 18) {
      outsideComfort.add('time slot');
    } else if (preferredTime == 'evening' && challengeHour <= 10) {
      outsideComfort.add('time slot');
    }

    // Check location (if they haven't played at this location recently)
    final hasPlayedAtLocation =
        playerRanking.stats.lastPlayedLocation.containsKey(challenge.location);
    if (!hasPlayedAtLocation) {
      outsideComfort.add('new location');
    }

    // Check wager amount (significantly higher than average)
    if (challenge.wagerAmount! > playerRanking.stats.averageWagerAmount * 2) {
      outsideComfort.add('high wager');
    }

    return outsideComfort;
  }

  /// Update player specialities based on performance
  static List<String> _updateSpecialities(
    List<String> currentSpecialities,
    Challenge challenge,
    bool isWin,
  ) {
    final updated = List<String>.from(currentSpecialities);

    if (!isWin) return updated;

    // Add match type speciality if won multiple times
    final matchTypeSpeciality = '${challenge.matchType.toString()}_specialist';
    if (!updated.contains(matchTypeSpeciality)) {
      updated.add(matchTypeSpeciality);
    }

    // Add time-based specialities
    final hour = challenge.proposedDateTime.hour;
    if (hour >= 21 || hour <= 6) {
      if (!updated.contains('night_warrior')) {
        updated.add('night_warrior');
      }
    } else if (hour <= 9) {
      if (!updated.contains('early_riser')) {
        updated.add('early_riser');
      }
    }

    // Add high stakes speciality
    if (challenge.wagerAmount! >= 1000 && !updated.contains('high_roller')) {
      updated.add('high_roller');
    }

    // Limit to top 5 specialities
    return updated.take(5).toList();
  }

  /// Get rank progression requirements
  static RankRequirements getRankRequirements(ChallengeRank currentRank) {
    final nextRank = currentRank.nextRank;
    if (nextRank == null) {
      return RankRequirements(
        currentRank: currentRank,
        nextRank: null,
        eloRequired: 0,
        matchesRequired: 0,
        isMaxRank: true,
      );
    }

    return RankRequirements(
      currentRank: currentRank,
      nextRank: nextRank,
      eloRequired: nextRank.minElo,
      matchesRequired: minimumMatchesForRank,
      isMaxRank: false,
    );
  }

  /// Calculate ELO decay for inactive players
  static double calculateEloDecay({
    required double currentElo,
    required DateTime lastActive,
    required DateTime now,
  }) {
    final daysSinceActive = now.difference(lastActive).inDays;

    if (daysSinceActive < 30) return 0; // No decay for first 30 days

    final monthsInactive = (daysSinceActive / 30).floor();
    final decayPerMonth = 10.0; // 10 ELO points per month
    final totalDecay = monthsInactive * decayPerMonth;

    // Cap decay at 200 points and don't go below initial rating
    return (totalDecay.clamp(
      0.0,
      200.0,
    )).clamp(0.0, currentElo - initialEloRating);
  }
}

/// Result of a ranking update calculation
class RankingUpdate {
  final double newEloRating;
  final double eloChange;
  final ChallengeRank newRank;
  final ChallengeRank oldRank;
  final bool hasRankChanged;
  final RankProgression rankProgression;
  final int newStreak;
  final bool isStreakBroken;
  final bool isNewStreakRecord;
  final List<RankingBonus> bonuses;
  final int newWins;
  final int newLosses;
  final int newTotal;
  final double newWinRate;
  final List<String> updatedSpecialities;

  const RankingUpdate({
    required this.newEloRating,
    required this.eloChange,
    required this.newRank,
    required this.oldRank,
    required this.hasRankChanged,
    required this.rankProgression,
    required this.newStreak,
    required this.isStreakBroken,
    required this.isNewStreakRecord,
    required this.bonuses,
    required this.newWins,
    required this.newLosses,
    required this.newTotal,
    required this.newWinRate,
    required this.updatedSpecialities,
  });

  double get totalBonusMultiplier {
    return bonuses.fold(1.0, (total, bonus) => total * bonus.multiplier);
  }

  int get totalBonusPoints {
    return bonuses.fold(0, (total, bonus) => total + bonus.points);
  }

  double get adjustedEloChange {
    return eloChange * totalBonusMultiplier;
  }
}

/// Types of ranking bonuses
enum BonusType {
  streak,
  upset,
  highStakes,
  outsideComfort,
  nightOwl,
  earlyBird,
  firstChallenge,
  rivalry,
}

/// Ranking bonus details
class RankingBonus {
  final BonusType type;
  final String description;
  final double multiplier;
  final int points;

  const RankingBonus({
    required this.type,
    required this.description,
    required this.multiplier,
    required this.points,
  });
}

/// Rank progression types
enum RankProgression { promoted, demoted, none }

/// Requirements for next rank
class RankRequirements {
  final ChallengeRank currentRank;
  final ChallengeRank? nextRank;
  final double eloRequired;
  final int matchesRequired;
  final bool isMaxRank;

  const RankRequirements({
    required this.currentRank,
    this.nextRank,
    required this.eloRequired,
    required this.matchesRequired,
    required this.isMaxRank,
  });

  double calculateProgress(double currentElo, int totalMatches) {
    if (isMaxRank) return 1.0;

    final eloProgress =
        (currentElo - currentRank.minElo) / (eloRequired - currentRank.minElo);
    final matchProgress = totalMatches / matchesRequired;

    return (eloProgress.clamp(0.0, 1.0) * 0.8) +
        (matchProgress.clamp(0.0, 1.0) * 0.2);
  }
}
