import '../entities/badge.dart';
import '../entities/player_ranking.dart';

/// Service for managing badge awards and progress tracking
class BadgeService {
  /// Check which badges a player should be awarded based on an event
  static List<Badge> checkEligibleBadges({
    required String triggerEvent,
    required Map<String, dynamic> eventData,
    required PlayerRanking playerRanking,
    required PlayerBadgeCollection currentBadges,
  }) {
    final eligibleBadges = <Badge>[];

    // Get all badges that the player hasn't earned yet
    final availableBadges =
        PredefinedBadges.allBadges
            .where((badge) => !currentBadges.hasBadge(badge.id))
            .toList();

    for (final badge in availableBadges) {
      if (_checkBadgeRequirements(
        badge: badge,
        triggerEvent: triggerEvent,
        eventData: eventData,
        playerRanking: playerRanking,
        currentBadges: currentBadges,
      )) {
        eligibleBadges.add(
          badge.copyWith(
            earnedAt: DateTime.now(),
            metadata: _getBadgeMetadata(badge, eventData),
          ),
        );
      }
    }

    return eligibleBadges;
  }

  /// Check if a specific badge's requirements are met
  static bool _checkBadgeRequirements({
    required Badge badge,
    required String triggerEvent,
    required Map<String, dynamic> eventData,
    required PlayerRanking playerRanking,
    required PlayerBadgeCollection currentBadges,
  }) {
    // Check if this trigger event is relevant for this badge
    if (!_isRelevantTrigger(badge, triggerEvent)) {
      return false;
    }

    // Check all requirements for the badge
    for (final requirement in badge.requirements) {
      if (!_checkRequirement(
        requirement: requirement,
        triggerEvent: triggerEvent,
        eventData: eventData,
        playerRanking: playerRanking,
        currentBadges: currentBadges,
      )) {
        return false;
      }
    }

    return true;
  }

  /// Check if a trigger event is relevant for a badge
  static bool _isRelevantTrigger(Badge badge, String triggerEvent) {
    final relevantTriggers = _getBadgeTriggers(badge);
    return relevantTriggers.contains(triggerEvent);
  }

  /// Get relevant trigger events for a badge
  static List<String> _getBadgeTriggers(Badge badge) {
    switch (badge.id) {
      case 'first_challenge':
        return ['challenge_completed'];
      case 'challenger_10':
      case 'challenger_50':
      case 'challenger_100':
        return ['challenge_completed'];
      case 'win_streak_3':
      case 'win_streak_5':
      case 'win_streak_10':
        return ['challenge_won'];
      case 'high_stakes_master':
        return ['challenge_won'];
      case 'upset_master':
        return ['challenge_won'];
      case 'rank_up_gold':
      case 'rank_up_champion':
        return ['ranking_updated'];
      case 'social_butterfly':
        return ['challenge_completed'];
      case 'rival_master':
        return ['challenge_won'];
      case 'comfort_zone_breaker':
        return ['outside_comfort_challenge'];
      case 'night_owl':
        return ['challenge_won'];
      case 'early_bird':
        return ['challenge_won'];
      default:
        return ['challenge_completed', 'challenge_won'];
    }
  }

  /// Check a specific requirement
  static bool _checkRequirement({
    required BadgeRequirement requirement,
    required String triggerEvent,
    required Map<String, dynamic> eventData,
    required PlayerRanking playerRanking,
    required PlayerBadgeCollection currentBadges,
  }) {
    switch (requirement.type) {
      case 'challenge_count':
        return playerRanking.totalChallenges >= (requirement.value as int);

      case 'win_streak':
        return playerRanking.currentStreak >= (requirement.value as int);

      case 'high_stakes_wins':
        return playerRanking.stats.highStakesWins >= (requirement.value as int);

      case 'min_wager':
        final challengeWager = eventData['wager_amount'] as double? ?? 0.0;
        return challengeWager >= (requirement.value as num);

      case 'upset_wins':
        // Count upset victories from event data or player stats
        final isUpset = eventData['is_upset'] as bool? ?? false;
        return isUpset ||
            _countUpsetWins(playerRanking) >= (requirement.value as int);

      case 'min_rank':
        final requiredRank = ChallengeRank.fromString(
          requirement.value as String,
        );
        return playerRanking.rank.index >= requiredRank.index;

      case 'unique_opponents':
        return _countUniqueOpponents(playerRanking) >=
            (requirement.value as int);

      case 'rivalry_wins_same_opponent':
        return _countRivalryWins(playerRanking) >= (requirement.value as int);

      case 'outside_comfort_challenges':
        return playerRanking.stats.outsideComfortChallenges.length >=
            (requirement.value as int);

      case 'night_wins':
        return _countNightWins(playerRanking) >= (requirement.value as int);

      case 'early_wins':
        return _countEarlyWins(playerRanking) >= (requirement.value as int);

      default:
        return false;
    }
  }

  /// Count upset wins from player ranking data
  static int _countUpsetWins(PlayerRanking playerRanking) {
    // This would typically come from stored match history
    // For now, estimate based on win rate and ELO
    final estimatedUpsets = (playerRanking.challengesWon * 0.2).round();
    return estimatedUpsets;
  }

  /// Count unique opponents
  static int _countUniqueOpponents(PlayerRanking playerRanking) {
    return playerRanking.stats.favoriteOpponents.length +
        (playerRanking.totalChallenges * 0.7).round(); // Estimate
  }

  /// Count rivalry wins (multiple wins against same opponent)
  static int _countRivalryWins(PlayerRanking playerRanking) {
    return playerRanking.stats.rivalryWins;
  }

  /// Count night wins (after 9 PM)
  static int _countNightWins(PlayerRanking playerRanking) {
    return playerRanking.stats.timeSlotPreferences['night'] ?? 0;
  }

  /// Count early morning wins (before 9 AM)
  static int _countEarlyWins(PlayerRanking playerRanking) {
    return playerRanking.stats.timeSlotPreferences['morning'] ?? 0;
  }

  /// Get metadata for a badge when earned
  static Map<String, dynamic>? _getBadgeMetadata(
    Badge badge,
    Map<String, dynamic> eventData,
  ) {
    switch (badge.category) {
      case BadgeCategory.streak:
        return {
          'streak_count': eventData['win_streak'] ?? 0,
          'challenge_id': eventData['challenge_id'],
        };
      case BadgeCategory.skill:
        return {
          'elo_at_earning': eventData['player_elo'] ?? 0,
          'opponent_elo': eventData['opponent_elo'] ?? 0,
        };
      case BadgeCategory.special:
        return {
          'special_conditions': eventData['outside_comfort_type'] ?? [],
          'bonus_points': eventData['bonus_points'] ?? 0,
        };
      default:
        return {
          'earned_via': eventData['challenge_id'] ?? 'system',
          'timestamp': DateTime.now().toIso8601String(),
        };
    }
  }

  /// Calculate badge progress for a player
  static Map<String, BadgeProgress> calculateBadgeProgress({
    required PlayerRanking playerRanking,
    required PlayerBadgeCollection currentBadges,
  }) {
    final progressMap = <String, BadgeProgress>{};

    final availableBadges =
        PredefinedBadges.allBadges
            .where((badge) => !currentBadges.hasBadge(badge.id))
            .toList();

    for (final badge in availableBadges) {
      final progress = _calculateIndividualBadgeProgress(badge, playerRanking);
      progressMap[badge.id] = progress;
    }

    return progressMap;
  }

  /// Calculate progress for a specific badge
  static BadgeProgress _calculateIndividualBadgeProgress(
    Badge badge,
    PlayerRanking playerRanking,
  ) {
    final currentProgress = <String, dynamic>{};
    double progressPercentage = 0.0;
    bool isCompleted = false;

    for (final requirement in badge.requirements) {
      final reqProgress = _calculateRequirementProgress(
        requirement,
        playerRanking,
      );
      currentProgress[requirement.type] = reqProgress;
    }

    // Calculate overall progress percentage
    if (badge.requirements.isNotEmpty) {
      final progressValues =
          badge.requirements.map((req) {
            final current = currentProgress[req.type]['current'] as num? ?? 0;
            final required = currentProgress[req.type]['required'] as num? ?? 1;
            return (current / required).clamp(0.0, 1.0);
          }).toList();

      progressPercentage =
          progressValues.reduce((a, b) => a + b) / progressValues.length;
      isCompleted = progressValues.every((p) => p >= 1.0);
    }

    return BadgeProgress(
      badgeId: badge.id,
      currentProgress: currentProgress,
      progressPercentage: progressPercentage,
      isCompleted: isCompleted,
      lastUpdated: DateTime.now(),
    );
  }

  /// Calculate progress for a specific requirement
  static Map<String, dynamic> _calculateRequirementProgress(
    BadgeRequirement requirement,
    PlayerRanking playerRanking,
  ) {
    switch (requirement.type) {
      case 'challenge_count':
        return {
          'current': playerRanking.totalChallenges,
          'required': requirement.value,
          'description': 'Challenges completed',
        };

      case 'win_streak':
        return {
          'current': playerRanking.currentStreak.clamp(0, double.infinity),
          'required': requirement.value,
          'description': 'Current win streak',
        };

      case 'high_stakes_wins':
        return {
          'current': playerRanking.stats.highStakesWins,
          'required': requirement.value,
          'description': 'High stakes wins',
        };

      case 'upset_wins':
        return {
          'current': _countUpsetWins(playerRanking),
          'required': requirement.value,
          'description': 'Upset victories',
        };

      case 'min_rank':
        final requiredRank = ChallengeRank.fromString(
          requirement.value as String,
        );
        return {
          'current': playerRanking.rank.index,
          'required': requiredRank.index,
          'description': 'Rank level',
        };

      case 'unique_opponents':
        return {
          'current': _countUniqueOpponents(playerRanking),
          'required': requirement.value,
          'description': 'Different opponents',
        };

      case 'outside_comfort_challenges':
        return {
          'current': playerRanking.stats.outsideComfortChallenges.length,
          'required': requirement.value,
          'description': 'Outside comfort zone',
        };

      default:
        return {
          'current': 0,
          'required': requirement.value,
          'description': requirement.description ?? 'Progress',
        };
    }
  }

  /// Get recommended actions to earn a badge
  static List<String> getRecommendedActions(
    Badge badge,
    PlayerRanking playerRanking,
  ) {
    final actions = <String>[];

    for (final requirement in badge.requirements) {
      final progress = _calculateRequirementProgress(
        requirement,
        playerRanking,
      );
      final current = progress['current'] as num;
      final required = progress['required'] as num;

      if (current < required) {
        final remaining = required - current;

        switch (requirement.type) {
          case 'challenge_count':
            actions.add('Complete ${remaining} more challenges');
            break;
          case 'win_streak':
            actions.add('Win ${remaining} more challenges in a row');
            break;
          case 'high_stakes_wins':
            actions.add(
              'Win ${remaining} more high-stakes challenges (1000+ NPR)',
            );
            break;
          case 'outside_comfort_challenges':
            actions.add(
              'Accept ${remaining} more challenges outside your preferences',
            );
            break;
          case 'min_rank':
            final targetRank = ChallengeRank.fromString(
              requirement.value as String,
            );
            actions.add('Reach ${targetRank.name} rank');
            break;
          default:
            actions.add('Continue playing to unlock this badge');
        }
      }
    }

    return actions;
  }

  /// Get badge recommendations based on player's current progress
  static List<Badge> getRecommendedBadges({
    required PlayerRanking playerRanking,
    required PlayerBadgeCollection currentBadges,
    int limit = 3,
  }) {
    final progress = calculateBadgeProgress(
      playerRanking: playerRanking,
      currentBadges: currentBadges,
    );

    // Sort badges by progress percentage and rarity
    final sortedBadges =
        progress.entries
            .where(
              (entry) => entry.value.progressPercentage > 0.3,
            ) // At least 30% progress
            .map(
              (entry) => MapEntry(
                PredefinedBadges.getBadgeById(entry.key)!,
                entry.value,
              ),
            )
            .toList();

    sortedBadges.sort((a, b) {
      // Prioritize badges that are closer to completion
      final progressDiff = b.value.progressPercentage.compareTo(
        a.value.progressPercentage,
      );
      if (progressDiff != 0) return progressDiff;

      // Then prioritize by rarity (higher rarity = higher priority)
      return b.key.rarity.index.compareTo(a.key.rarity.index);
    });

    return sortedBadges.take(limit).map((entry) => entry.key).toList();
  }
}
