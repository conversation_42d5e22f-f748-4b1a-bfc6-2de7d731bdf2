import '../entities/challenge.dart';
import '../entities/player_ranking.dart';

/// Service for managing streak bonuses and dynamic bonus calculations
class StreakBonusService {
  // Base multipliers for different bonus types
  static const double baseStreakBonus = 0.1; // 10% for 3+ wins
  static const double maxStreakBonus = 0.5; // 50% max bonus
  static const double outsideComfortBonus = 0.1; // 10% for outside comfort zone
  static const double rivalryBonus = 0.05; // 5% for rivalry matches
  static const double highStakesBonus = 0.08; // 8% for high stakes
  static const double upsetBonus = 0.15; // 15% for upset victories

  /// Calculate total bonus multiplier for wager winnings
  static StreakBonusResult calculateWagerBonus({
    required Challenge challenge,
    required PlayerRanking winnerRanking,
    required PlayerRanking loserRanking,
    required bool isUpset,
    List<String> outsideComfortFactors = const [],
  }) {
    double totalMultiplier = 1.0;
    final bonusDetails = <BonusDetail>[];

    // Streak bonus (3+ wins = 10% bonus, scaling up)
    if (winnerRanking.hasStreakBonus) {
      final streakMultiplier = _calculateStreakMultiplier(
        winnerRanking.currentStreak,
      );
      totalMultiplier += streakMultiplier;

      bonusDetails.add(
        BonusDetail(
          type: BonusType.streak,
          multiplier: streakMultiplier,
          description: '${winnerRanking.currentStreak}-win streak bonus',
          iconEmoji: '🔥',
        ),
      );
    }

    // Outside comfort zone bonus
    if (outsideComfortFactors.isNotEmpty) {
      final comfortMultiplier =
          outsideComfortFactors.length * outsideComfortBonus;
      totalMultiplier += comfortMultiplier;

      bonusDetails.add(
        BonusDetail(
          type: BonusType.outsideComfort,
          multiplier: comfortMultiplier,
          description:
              'Outside comfort zone: ${outsideComfortFactors.join(', ')}',
          iconEmoji: '🚀',
        ),
      );
    }

    // High stakes bonus
    if (challenge.wagerAmount! >= 1000) {
      totalMultiplier += highStakesBonus;

      bonusDetails.add(
        BonusDetail(
          type: BonusType.highStakes,
          multiplier: highStakesBonus,
          description:
              'High stakes match (NPR ${challenge.wagerAmount!.toStringAsFixed(0)})',
          iconEmoji: '💎',
        ),
      );
    }

    // Upset victory bonus
    if (isUpset) {
      totalMultiplier += upsetBonus;

      bonusDetails.add(
        BonusDetail(
          type: BonusType.upset,
          multiplier: upsetBonus,
          description: 'Giant slayer victory',
          iconEmoji: '⚔️',
        ),
      );
    }

    // Rivalry bonus (playing same opponent frequently)
    if (_isRivalryMatch(challenge, winnerRanking)) {
      totalMultiplier += rivalryBonus;

      bonusDetails.add(
        BonusDetail(
          type: BonusType.rivalry,
          multiplier: rivalryBonus,
          description: 'Rivalry match bonus',
          iconEmoji: '🤝',
        ),
      );
    }

    // Time-based bonuses
    final timeBonus = _calculateTimeBonus(challenge.proposedDateTime);
    if (timeBonus.multiplier > 0) {
      totalMultiplier += timeBonus.multiplier;
      bonusDetails.add(timeBonus);
    }

    // Match type mastery bonus
    if (challenge.matchType != null) {
      final masteryBonus = _calculateMasteryBonus(
        challenge.matchType!,
        winnerRanking,
      );
      if (masteryBonus.multiplier > 0) {
        totalMultiplier += masteryBonus.multiplier;
        bonusDetails.add(masteryBonus);
      }
    }

    // Cap the total multiplier
    totalMultiplier = totalMultiplier.clamp(1.0, 1.0 + maxStreakBonus);

    // Calculate actual bonus amounts
    final baseWagerAmount = challenge.wagerAmount!;
    final bonusAmount = baseWagerAmount * (totalMultiplier - 1.0);
    final totalWinning = baseWagerAmount * totalMultiplier;

    return StreakBonusResult(
      baseAmount: baseWagerAmount,
      bonusAmount: bonusAmount,
      totalAmount: totalWinning,
      multiplier: totalMultiplier,
      bonusDetails: bonusDetails,
      hasAnyBonus: bonusDetails.isNotEmpty,
    );
  }

  /// Calculate streak-specific multiplier
  static double _calculateStreakMultiplier(int streak) {
    if (streak < 3) return 0.0;

    // Progressive bonus: 10% at 3 wins, +2% per additional win, cap at 50%
    final progressiveBonus = baseStreakBonus + ((streak - 3) * 0.02);
    return progressiveBonus.clamp(0.0, maxStreakBonus);
  }

  /// Check if this is a rivalry match
  static bool _isRivalryMatch(
    Challenge challenge,
    PlayerRanking winnerRanking,
  ) {
    // Check if the opponent is in the player's frequent opponents list
    final opponentName = challenge.challengerId == winnerRanking.playerId
        ? challenge.challengerName ?? ''
        : challenge.challengerTeamName ?? '';

    return winnerRanking.stats.favoriteOpponents.contains(opponentName);
  }

  /// Calculate time-based bonuses
  static BonusDetail _calculateTimeBonus(DateTime matchTime) {
    final hour = matchTime.hour;

    if (hour >= 22 || hour <= 5) {
      // Late night bonus (10 PM - 5 AM)
      return BonusDetail(
        type: BonusType.timeBonus,
        multiplier: 0.05,
        description: 'Night owl bonus',
        iconEmoji: '🦉',
      );
    } else if (hour <= 7) {
      // Early morning bonus (5 AM - 7 AM)
      return BonusDetail(
        type: BonusType.timeBonus,
        multiplier: 0.03,
        description: 'Early bird bonus',
        iconEmoji: '🐦',
      );
    }

    return BonusDetail(
      type: BonusType.timeBonus,
      multiplier: 0.0,
      description: '',
      iconEmoji: '',
    );
  }

  /// Calculate match type mastery bonus
  static BonusDetail _calculateMasteryBonus(
    String matchType,
    PlayerRanking playerRanking,
  ) {
    final preferredType = playerRanking.stats.preferredMatchType;
    final winRate = playerRanking.stats.winRateForMatchType(matchType);

    // Bonus for playing preferred match type with high win rate
    if (preferredType == matchType && winRate >= 0.7) {
      return BonusDetail(
        type: BonusType.mastery,
        multiplier: 0.03,
        description: '${_getMatchTypeDisplayName(matchType)} mastery',
        iconEmoji: '⭐',
      );
    }

    return BonusDetail(
      type: BonusType.mastery,
      multiplier: 0.0,
      description: '',
      iconEmoji: '',
    );
  }

  /// Helper method to get display name for match type
  static String _getMatchTypeDisplayName(String matchType) {
    switch (matchType.toLowerCase()) {
      case '1v1':
        return '1v1';
      case '2v2':
        return '2v2';
      case '3v3':
        return '3v3';
      case '5v5':
        return '5v5';
      case '11v11':
        return '11v11';
      default:
        return matchType;
    }
  }

  /// Calculate bonus points for ELO/ranking system
  static int calculateRankingBonusPoints({
    required PlayerRanking playerRanking,
    required Challenge challenge,
    required bool isWin,
    List<String> outsideComfortFactors = const [],
  }) {
    int bonusPoints = 0;

    if (!isWin) return bonusPoints;

    // Streak bonus points
    if (playerRanking.hasStreakBonus) {
      bonusPoints += playerRanking.currentStreak * 2;
    }

    // First challenge bonus
    if (playerRanking.totalChallenges == 0) {
      bonusPoints += 50;
    }

    // Outside comfort zone points
    bonusPoints += outsideComfortFactors.length * 10;

    // High stakes points
    if (challenge.wagerAmount! >= 1000) {
      bonusPoints += 15;
    }

    // Perfect game bonus (if score tracking is available)
    // Note: Score tracking will be handled by backend later
    // For now, we'll skip this bonus calculation

    return bonusPoints;
  }

  /// Get outside comfort zone factors for a challenge
  static List<String> getOutsideComfortFactors({
    required Challenge challenge,
    required PlayerRanking playerRanking,
  }) {
    final factors = <String>[];

    // Check match type
    if (challenge.matchType != null &&
        playerRanking.stats.preferredMatchType != challenge.matchType) {
      factors.add('match type');
    }

    // Check time preference
    final challengeHour = challenge.proposedDateTime.hour;
    final preferredTime = playerRanking.stats.preferredTimeSlot;

    if (preferredTime == 'morning' && challengeHour >= 18) {
      factors.add('evening time');
    } else if (preferredTime == 'evening' && challengeHour <= 10) {
      factors.add('morning time');
    } else if (challengeHour >= 22 || challengeHour <= 5) {
      factors.add('late night');
    }

    // Check location familiarity
    final hasPlayedAtLocation =
        playerRanking.stats.lastPlayedLocation.containsKey(challenge.location);
    if (!hasPlayedAtLocation) {
      factors.add('new location');
    }

    // Check wager level
    if (challenge.wagerAmount! > playerRanking.stats.averageWagerAmount * 2) {
      factors.add('high wager');
    } else if (challenge.wagerAmount! <
        playerRanking.stats.averageWagerAmount * 0.5) {
      factors.add('low wager');
    }

    return factors;
  }

  /// Calculate daily/weekly bonus multipliers
  static DailyBonusResult calculateDailyBonus({
    required DateTime lastActiveDate,
    required int consecutiveDays,
    required int challengesToday,
  }) {
    double multiplier = 1.0;
    final bonuses = <String>[];

    // Consecutive day bonus
    if (consecutiveDays >= 3) {
      final dayBonus = (consecutiveDays * 0.02).clamp(0.0, 0.2); // Max 20%
      multiplier += dayBonus;
      bonuses.add(
        '${consecutiveDays}-day streak: +${(dayBonus * 100).toInt()}%',
      );
    }

    // Multiple challenges per day bonus
    if (challengesToday >= 3) {
      multiplier += 0.05;
      bonuses.add('Active player: +5%');
    }

    // Weekend bonus
    final now = DateTime.now();
    if (now.weekday == DateTime.saturday || now.weekday == DateTime.sunday) {
      multiplier += 0.03;
      bonuses.add('Weekend warrior: +3%');
    }

    return DailyBonusResult(
      multiplier: multiplier,
      bonusDescriptions: bonuses,
      hasAnyBonus: bonuses.isNotEmpty,
    );
  }
}

/// Types of bonuses available
enum BonusType {
  streak,
  outsideComfort,
  highStakes,
  upset,
  rivalry,
  timeBonus,
  mastery,
  daily,
}

/// Details of a specific bonus
class BonusDetail {
  final BonusType type;
  final double multiplier;
  final String description;
  final String iconEmoji;

  const BonusDetail({
    required this.type,
    required this.multiplier,
    required this.description,
    required this.iconEmoji,
  });

  String get formattedMultiplier =>
      '+${(multiplier * 100).toStringAsFixed(0)}%';

  String get displayText => '$iconEmoji $description $formattedMultiplier';
}

/// Result of streak bonus calculation
class StreakBonusResult {
  final double baseAmount;
  final double bonusAmount;
  final double totalAmount;
  final double multiplier;
  final List<BonusDetail> bonusDetails;
  final bool hasAnyBonus;

  const StreakBonusResult({
    required this.baseAmount,
    required this.bonusAmount,
    required this.totalAmount,
    required this.multiplier,
    required this.bonusDetails,
    required this.hasAnyBonus,
  });

  String get formattedMultiplier => '${(multiplier * 100).toStringAsFixed(0)}%';

  String get summaryText {
    if (!hasAnyBonus) return 'No bonus';

    final bonusPercent = ((multiplier - 1.0) * 100).toStringAsFixed(0);
    return '+$bonusPercent% bonus (NPR ${bonusAmount.toStringAsFixed(0)})';
  }
}

/// Result of daily bonus calculation
class DailyBonusResult {
  final double multiplier;
  final List<String> bonusDescriptions;
  final bool hasAnyBonus;

  const DailyBonusResult({
    required this.multiplier,
    required this.bonusDescriptions,
    required this.hasAnyBonus,
  });

  String get summaryText {
    if (!hasAnyBonus) return 'No daily bonus';

    final bonusPercent = ((multiplier - 1.0) * 100).toStringAsFixed(0);
    return '+$bonusPercent% daily bonus';
  }
}
