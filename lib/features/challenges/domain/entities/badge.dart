/// Badge system for player achievements and milestones
class Badge {
  final String id;
  final String name;
  final String description;
  final String icon;
  final BadgeCategory category;
  final BadgeRarity rarity;
  final List<BadgeRequirement> requirements;
  final bool isSecret; // Hidden until earned
  final int points; // Points awarded for earning
  final DateTime? earnedAt;
  final Map<String, dynamic>? metadata; // Additional data like streak count

  const Badge({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.category,
    required this.rarity,
    required this.requirements,
    this.isSecret = false,
    required this.points,
    this.earnedAt,
    this.metadata,
  });

  factory Badge.fromJson(Map<String, dynamic> json) {
    return Badge(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      icon: json['icon'] as String,
      category: BadgeCategory.fromString(json['category'] as String),
      rarity: BadgeRarity.fromString(json['rarity'] as String),
      requirements:
          (json['requirements'] as List<dynamic>)
              .map((req) => BadgeRequirement.fromJson(req))
              .toList(),
      isSecret: json['is_secret'] as bool? ?? false,
      points: json['points'] as int,
      earnedAt:
          json['earned_at'] != null
              ? DateTime.parse(json['earned_at'] as String)
              : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'category': category.toString(),
      'rarity': rarity.toString(),
      'requirements': requirements.map((req) => req.toJson()).toList(),
      'is_secret': isSecret,
      'points': points,
      'earned_at': earnedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  Badge copyWith({
    String? id,
    String? name,
    String? description,
    String? icon,
    BadgeCategory? category,
    BadgeRarity? rarity,
    List<BadgeRequirement>? requirements,
    bool? isSecret,
    int? points,
    DateTime? earnedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Badge(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      category: category ?? this.category,
      rarity: rarity ?? this.rarity,
      requirements: requirements ?? this.requirements,
      isSecret: isSecret ?? this.isSecret,
      points: points ?? this.points,
      earnedAt: earnedAt ?? this.earnedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get isEarned => earnedAt != null;
  bool get isVisible => !isSecret || isEarned;

  String get displayName =>
      isEarned
          ? name
          : isSecret
          ? '???'
          : name;
  String get displayDescription =>
      isEarned
          ? description
          : isSecret
          ? 'Hidden achievement'
          : description;
}

/// Categories of badges
enum BadgeCategory {
  milestone('milestone', 'Milestone', '🎯'),
  streak('streak', 'Streak', '🔥'),
  skill('skill', 'Skill', '⚡'),
  social('social', 'Social', '🤝'),
  special('special', 'Special', '✨'),
  seasonal('seasonal', 'Seasonal', '🗓️');

  const BadgeCategory(this.value, this.displayName, this.icon);

  final String value;
  final String displayName;
  final String icon;

  static BadgeCategory fromString(String value) {
    switch (value) {
      case 'milestone':
        return milestone;
      case 'streak':
        return streak;
      case 'skill':
        return skill;
      case 'social':
        return social;
      case 'special':
        return special;
      case 'seasonal':
        return seasonal;
      default:
        throw ArgumentError('Unknown badge category: $value');
    }
  }

  @override
  String toString() => value;
}

/// Rarity levels for badges
enum BadgeRarity {
  common('common', 'Common', 0xFFB0BEC5),
  uncommon('uncommon', 'Uncommon', 0xFF4CAF50),
  rare('rare', 'Rare', 0xFF2196F3),
  epic('epic', 'Epic', 0xFF9C27B0),
  legendary('legendary', 'Legendary', 0xFFFF9800);

  const BadgeRarity(this.value, this.displayName, this.colorCode);

  final String value;
  final String displayName;
  final int colorCode;

  static BadgeRarity fromString(String value) {
    switch (value) {
      case 'common':
        return common;
      case 'uncommon':
        return uncommon;
      case 'rare':
        return rare;
      case 'epic':
        return epic;
      case 'legendary':
        return legendary;
      default:
        throw ArgumentError('Unknown badge rarity: $value');
    }
  }

  @override
  String toString() => value;
}

/// Requirements for earning a badge
class BadgeRequirement {
  final String type; // 'challenge_count', 'win_streak', 'elo_rating', etc.
  final dynamic value; // The required value (number, string, etc.)
  final String? description;
  final bool isOptional; // For badges with multiple optional requirements

  const BadgeRequirement({
    required this.type,
    required this.value,
    this.description,
    this.isOptional = false,
  });

  factory BadgeRequirement.fromJson(Map<String, dynamic> json) {
    return BadgeRequirement(
      type: json['type'] as String,
      value: json['value'],
      description: json['description'] as String?,
      isOptional: json['is_optional'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'value': value,
      'description': description,
      'is_optional': isOptional,
    };
  }
}

/// Player's badge collection and progress
class PlayerBadgeCollection {
  final String playerId;
  final List<Badge> earnedBadges;
  final Map<String, BadgeProgress> badgeProgress;
  final int totalBadgePoints;
  final DateTime lastUpdated;

  const PlayerBadgeCollection({
    required this.playerId,
    required this.earnedBadges,
    required this.badgeProgress,
    required this.totalBadgePoints,
    required this.lastUpdated,
  });

  factory PlayerBadgeCollection.fromJson(Map<String, dynamic> json) {
    return PlayerBadgeCollection(
      playerId: json['player_id'] as String,
      earnedBadges:
          (json['earned_badges'] as List<dynamic>)
              .map((badge) => Badge.fromJson(badge))
              .toList(),
      badgeProgress: (json['badge_progress'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(key, BadgeProgress.fromJson(value)),
      ),
      totalBadgePoints: json['total_badge_points'] as int,
      lastUpdated: DateTime.parse(json['last_updated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'player_id': playerId,
      'earned_badges': earnedBadges.map((badge) => badge.toJson()).toList(),
      'badge_progress': badgeProgress.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
      'total_badge_points': totalBadgePoints,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  List<Badge> getBadgesByCategory(BadgeCategory category) {
    return earnedBadges.where((badge) => badge.category == category).toList();
  }

  List<Badge> getBadgesByRarity(BadgeRarity rarity) {
    return earnedBadges.where((badge) => badge.rarity == rarity).toList();
  }

  bool hasBadge(String badgeId) {
    return earnedBadges.any((badge) => badge.id == badgeId);
  }

  Badge? getBadge(String badgeId) {
    try {
      return earnedBadges.firstWhere((badge) => badge.id == badgeId);
    } catch (e) {
      return null;
    }
  }

  List<Badge> get recentBadges {
    final recent = List<Badge>.from(earnedBadges);
    recent.sort(
      (a, b) =>
          (b.earnedAt ?? DateTime(0)).compareTo(a.earnedAt ?? DateTime(0)),
    );
    return recent.take(5).toList();
  }
}

/// Progress tracking for a specific badge
class BadgeProgress {
  final String badgeId;
  final Map<String, dynamic>
  currentProgress; // {'challenge_count': 5, 'required': 10}
  final double progressPercentage;
  final bool isCompleted;
  final DateTime lastUpdated;

  const BadgeProgress({
    required this.badgeId,
    required this.currentProgress,
    required this.progressPercentage,
    required this.isCompleted,
    required this.lastUpdated,
  });

  factory BadgeProgress.fromJson(Map<String, dynamic> json) {
    return BadgeProgress(
      badgeId: json['badge_id'] as String,
      currentProgress: Map<String, dynamic>.from(json['current_progress']),
      progressPercentage: (json['progress_percentage'] as num).toDouble(),
      isCompleted: json['is_completed'] as bool,
      lastUpdated: DateTime.parse(json['last_updated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'badge_id': badgeId,
      'current_progress': currentProgress,
      'progress_percentage': progressPercentage,
      'is_completed': isCompleted,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}

/// Pre-defined badges for the challenge system
class PredefinedBadges {
  static const List<Badge> allBadges = [
    // Milestone badges
    Badge(
      id: 'first_challenge',
      name: 'First Blood',
      description: 'Complete your first challenge',
      icon: '🔥',
      category: BadgeCategory.milestone,
      rarity: BadgeRarity.common,
      requirements: [BadgeRequirement(type: 'challenge_count', value: 1)],
      points: 10,
    ),
    Badge(
      id: 'challenger_10',
      name: 'Rising Challenger',
      description: 'Complete 10 challenges',
      icon: '⚔️',
      category: BadgeCategory.milestone,
      rarity: BadgeRarity.uncommon,
      requirements: [BadgeRequirement(type: 'challenge_count', value: 10)],
      points: 25,
    ),
    Badge(
      id: 'challenger_50',
      name: 'Veteran Challenger',
      description: 'Complete 50 challenges',
      icon: '🏆',
      category: BadgeCategory.milestone,
      rarity: BadgeRarity.rare,
      requirements: [BadgeRequirement(type: 'challenge_count', value: 50)],
      points: 75,
    ),
    Badge(
      id: 'challenger_100',
      name: 'Challenge Master',
      description: 'Complete 100 challenges',
      icon: '👑',
      category: BadgeCategory.milestone,
      rarity: BadgeRarity.epic,
      requirements: [BadgeRequirement(type: 'challenge_count', value: 100)],
      points: 150,
    ),

    // Streak badges
    Badge(
      id: 'win_streak_3',
      name: 'Hat Trick',
      description: 'Win 3 challenges in a row',
      icon: '🎯',
      category: BadgeCategory.streak,
      rarity: BadgeRarity.uncommon,
      requirements: [BadgeRequirement(type: 'win_streak', value: 3)],
      points: 30,
    ),
    Badge(
      id: 'win_streak_5',
      name: 'Unstoppable',
      description: 'Win 5 challenges in a row',
      icon: '⚡',
      category: BadgeCategory.streak,
      rarity: BadgeRarity.rare,
      requirements: [BadgeRequirement(type: 'win_streak', value: 5)],
      points: 50,
    ),
    Badge(
      id: 'win_streak_10',
      name: 'Legendary Streak',
      description: 'Win 10 challenges in a row',
      icon: '🌟',
      category: BadgeCategory.streak,
      rarity: BadgeRarity.legendary,
      requirements: [BadgeRequirement(type: 'win_streak', value: 10)],
      points: 200,
    ),

    // Skill badges
    Badge(
      id: 'high_stakes_master',
      name: 'High Stakes Master',
      description: 'Win 10 high-stakes challenges (1000+ NPR)',
      icon: '💰',
      category: BadgeCategory.skill,
      rarity: BadgeRarity.epic,
      requirements: [
        BadgeRequirement(type: 'high_stakes_wins', value: 10),
        BadgeRequirement(type: 'min_wager', value: 1000),
      ],
      points: 100,
    ),
    Badge(
      id: 'upset_master',
      name: 'Giant Slayer',
      description: 'Defeat 5 higher-ranked opponents',
      icon: '🗡️',
      category: BadgeCategory.skill,
      rarity: BadgeRarity.rare,
      requirements: [BadgeRequirement(type: 'upset_wins', value: 5)],
      points: 60,
    ),
    Badge(
      id: 'rank_up_gold',
      name: 'Golden Achiever',
      description: 'Reach Gold rank',
      icon: '🥇',
      category: BadgeCategory.skill,
      rarity: BadgeRarity.rare,
      requirements: [BadgeRequirement(type: 'min_rank', value: 'gold')],
      points: 75,
    ),
    Badge(
      id: 'rank_up_champion',
      name: 'The Champion',
      description: 'Reach Champion rank',
      icon: '👑',
      category: BadgeCategory.skill,
      rarity: BadgeRarity.legendary,
      requirements: [BadgeRequirement(type: 'min_rank', value: 'champion')],
      points: 300,
    ),

    // Social badges
    Badge(
      id: 'social_butterfly',
      name: 'Social Butterfly',
      description: 'Play challenges with 20 different opponents',
      icon: '🦋',
      category: BadgeCategory.social,
      rarity: BadgeRarity.uncommon,
      requirements: [BadgeRequirement(type: 'unique_opponents', value: 20)],
      points: 40,
    ),
    Badge(
      id: 'rival_master',
      name: 'Rival Master',
      description: 'Win 5 matches against the same opponent',
      icon: '⚔️',
      category: BadgeCategory.social,
      rarity: BadgeRarity.rare,
      requirements: [
        BadgeRequirement(type: 'rivalry_wins_same_opponent', value: 5),
      ],
      points: 80,
    ),

    // Special badges
    Badge(
      id: 'comfort_zone_breaker',
      name: 'Comfort Zone Breaker',
      description: 'Accept 10 challenges outside your preferences',
      icon: '🚀',
      category: BadgeCategory.special,
      rarity: BadgeRarity.epic,
      requirements: [
        BadgeRequirement(type: 'outside_comfort_challenges', value: 10),
      ],
      points: 120,
    ),
    Badge(
      id: 'night_owl',
      name: 'Night Owl',
      description: 'Win 10 challenges after 9 PM',
      icon: '🦉',
      category: BadgeCategory.special,
      rarity: BadgeRarity.uncommon,
      requirements: [BadgeRequirement(type: 'night_wins', value: 10)],
      points: 35,
    ),
    Badge(
      id: 'early_bird',
      name: 'Early Bird',
      description: 'Win 10 challenges before 9 AM',
      icon: '🐦',
      category: BadgeCategory.special,
      rarity: BadgeRarity.uncommon,
      requirements: [BadgeRequirement(type: 'early_wins', value: 10)],
      points: 35,
    ),
  ];

  static Badge? getBadgeById(String badgeId) {
    try {
      return allBadges.firstWhere((badge) => badge.id == badgeId);
    } catch (e) {
      return null;
    }
  }

  static List<Badge> getBadgesByCategory(BadgeCategory category) {
    return allBadges.where((badge) => badge.category == category).toList();
  }
}
