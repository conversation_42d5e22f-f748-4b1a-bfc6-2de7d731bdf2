import 'challenge_enums.dart';

/// Challenge entity representing a match challenge between teams/players
/// Based on the API specification from swagger.json
class Challenge {
  final String id;
  final String challengerId;
  final String? challengerName;
  final String? challengerTeamId;
  final String? challengerTeamName;
  final String? opponentId;
  final String? opponentName;
  final String? opponentTeamId;
  final String? opponentTeamName;
  final String? matchType;
  final AgeGroup? ageGroup;
  final String? skillLevel;
  final String? location;
  final String? venueId;
  final String? venueName;
  final DateTime proposedDateTime;
  final DateTime? alternativeDateTime1;
  final DateTime? alternativeDateTime2;
  final double? wagerAmount;
  final WagerType? wagerType;
  final String? description;
  final String? rules;
  final String? status;
  final DateTime expiresAt;
  final DateTime? acceptedAt;
  final DateTime? completedAt;
  final String? winnerId;
  final String? winnerName;
  final String? winnerTeamId;
  final String? winnerTeamName;
  final String? matchResult;
  final bool isResultDisputed;
  final int responseCount;
  final DateTime created;
  final List<ChallengeResponse>? responses;

  const Challenge({
    required this.id,
    required this.challengerId,
    this.challengerName,
    this.challengerTeamId,
    this.challengerTeamName,
    this.opponentId,
    this.opponentName,
    this.opponentTeamId,
    this.opponentTeamName,
    this.matchType,
    this.ageGroup,
    this.skillLevel,
    this.location,
    this.venueId,
    this.venueName,
    required this.proposedDateTime,
    this.alternativeDateTime1,
    this.alternativeDateTime2,
    this.wagerAmount,
    this.wagerType,
    this.description,
    this.rules,
    this.status,
    required this.expiresAt,
    this.acceptedAt,
    this.completedAt,
    this.winnerId,
    this.winnerName,
    this.winnerTeamId,
    this.winnerTeamName,
    this.matchResult,
    required this.isResultDisputed,
    required this.responseCount,
    required this.created,
    this.responses,
  });

  factory Challenge.fromJson(Map<String, dynamic> json) {
    return Challenge(
      id: json['id'] as String,
      challengerId: json['challengerId'] as String,
      challengerName: json['challengerName'] as String?,
      challengerTeamId: json['challengerTeamId'] as String?,
      challengerTeamName: json['challengerTeamName'] as String?,
      opponentId: json['opponentId'] as String?,
      opponentName: json['opponentName'] as String?,
      opponentTeamId: json['opponentTeamId'] as String?,
      opponentTeamName: json['opponentTeamName'] as String?,
      matchType: json['matchType'] as String?,
      ageGroup: AgeGroup.fromString(json['ageGroup'] as String?),
      skillLevel: json['skillLevel'] as String?,
      location: json['location'] as String?,
      venueId: json['venueId'] as String?,
      venueName: json['venueName'] as String?,
      proposedDateTime: DateTime.parse(json['proposedDateTime'] as String),
      alternativeDateTime1: json['alternativeDateTime1'] != null
          ? DateTime.parse(json['alternativeDateTime1'] as String)
          : null,
      alternativeDateTime2: json['alternativeDateTime2'] != null
          ? DateTime.parse(json['alternativeDateTime2'] as String)
          : null,
      wagerAmount: json['wagerAmount'] as double?,
      wagerType: WagerType.fromString(json['wagerType'] as String?),
      description: json['description'] as String?,
      rules: json['rules'] as String?,
      status: json['status'] as String?,
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      acceptedAt: json['acceptedAt'] != null
          ? DateTime.parse(json['acceptedAt'] as String)
          : null,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      winnerId: json['winnerId'] as String?,
      winnerName: json['winnerName'] as String?,
      winnerTeamId: json['winnerTeamId'] as String?,
      winnerTeamName: json['winnerTeamName'] as String?,
      matchResult: json['matchResult'] as String?,
      isResultDisputed: json['isResultDisputed'] as bool,
      responseCount: json['responseCount'] as int,
      created: DateTime.parse(json['created'] as String),
      responses: json['responses'] != null
          ? (json['responses'] as List<dynamic>)
              .map((e) => ChallengeResponse.fromJson(e))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'challengerId': challengerId,
      'challengerName': challengerName,
      'challengerTeamId': challengerTeamId,
      'challengerTeamName': challengerTeamName,
      'opponentId': opponentId,
      'opponentName': opponentName,
      'opponentTeamId': opponentTeamId,
      'opponentTeamName': opponentTeamName,
      'matchType': matchType,
      'ageGroup': ageGroup?.toString(),
      'skillLevel': skillLevel,
      'location': location,
      'venueId': venueId,
      'venueName': venueName,
      'proposedDateTime': proposedDateTime.toIso8601String(),
      'alternativeDateTime1': alternativeDateTime1?.toIso8601String(),
      'alternativeDateTime2': alternativeDateTime2?.toIso8601String(),
      'wagerAmount': wagerAmount,
      'wagerType': wagerType?.toString(),
      'description': description,
      'rules': rules,
      'status': status,
      'expiresAt': expiresAt.toIso8601String(),
      'acceptedAt': acceptedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'winnerId': winnerId,
      'winnerName': winnerName,
      'winnerTeamId': winnerTeamId,
      'winnerTeamName': winnerTeamName,
      'matchResult': matchResult,
      'isResultDisputed': isResultDisputed,
      'responseCount': responseCount,
      'created': created.toIso8601String(),
      'responses': responses?.map((e) => e.toJson()).toList(),
    };
  }

  Challenge copyWith({
    String? id,
    String? challengerId,
    String? challengerName,
    String? challengerTeamId,
    String? challengerTeamName,
    String? opponentId,
    String? opponentName,
    String? opponentTeamId,
    String? opponentTeamName,
    String? matchType,
    AgeGroup? ageGroup,
    String? skillLevel,
    String? location,
    String? venueId,
    String? venueName,
    DateTime? proposedDateTime,
    DateTime? alternativeDateTime1,
    DateTime? alternativeDateTime2,
    double? wagerAmount,
    WagerType? wagerType,
    String? description,
    String? rules,
    String? status,
    DateTime? expiresAt,
    DateTime? acceptedAt,
    DateTime? completedAt,
    String? winnerId,
    String? winnerName,
    String? winnerTeamId,
    String? winnerTeamName,
    String? matchResult,
    bool? isResultDisputed,
    int? responseCount,
    DateTime? created,
    List<ChallengeResponse>? responses,
  }) {
    return Challenge(
      id: id ?? this.id,
      challengerId: challengerId ?? this.challengerId,
      challengerName: challengerName ?? this.challengerName,
      challengerTeamId: challengerTeamId ?? this.challengerTeamId,
      challengerTeamName: challengerTeamName ?? this.challengerTeamName,
      opponentId: opponentId ?? this.opponentId,
      opponentName: opponentName ?? this.opponentName,
      opponentTeamId: opponentTeamId ?? this.opponentTeamId,
      opponentTeamName: opponentTeamName ?? this.opponentTeamName,
      matchType: matchType ?? this.matchType,
      ageGroup: ageGroup ?? this.ageGroup,
      skillLevel: skillLevel ?? this.skillLevel,
      location: location ?? this.location,
      venueId: venueId ?? this.venueId,
      venueName: venueName ?? this.venueName,
      proposedDateTime: proposedDateTime ?? this.proposedDateTime,
      alternativeDateTime1: alternativeDateTime1 ?? this.alternativeDateTime1,
      alternativeDateTime2: alternativeDateTime2 ?? this.alternativeDateTime2,
      wagerAmount: wagerAmount ?? this.wagerAmount,
      wagerType: wagerType ?? this.wagerType,
      description: description ?? this.description,
      rules: rules ?? this.rules,
      status: status ?? this.status,
      expiresAt: expiresAt ?? this.expiresAt,
      acceptedAt: acceptedAt ?? this.acceptedAt,
      completedAt: completedAt ?? this.completedAt,
      winnerId: winnerId ?? this.winnerId,
      winnerName: winnerName ?? this.winnerName,
      winnerTeamId: winnerTeamId ?? this.winnerTeamId,
      winnerTeamName: winnerTeamName ?? this.winnerTeamName,
      matchResult: matchResult ?? this.matchResult,
      isResultDisputed: isResultDisputed ?? this.isResultDisputed,
      responseCount: responseCount ?? this.responseCount,
      created: created ?? this.created,
      responses: responses ?? this.responses,
    );
  }

  // Computed properties
  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isAccepted => status == 'accepted';
  bool get isCompleted => status == 'completed';
  bool get isOpen => status == 'open' && !isExpired;
  bool get isCancelled => status == 'cancelled';
  bool get isInProgress => status == 'in_progress';

  // Helper methods
  String get displayName => challengerName ?? 'Unknown Player';
  String get opponentDisplayName => opponentName ?? 'TBD';
  String get teamDisplayName => challengerTeamName ?? 'Individual';
  String get opponentTeamDisplayName => opponentTeamName ?? 'TBD';

  String get wagerDisplayText {
    if (wagerAmount == null || wagerAmount == 0) {
      return 'No wager';
    }
    return '${wagerAmount!.toStringAsFixed(0)} ${wagerType?.toString() ?? 'NPR'}';
  }

  String get statusDisplayText {
    switch (status) {
      case 'open':
        return 'Open';
      case 'accepted':
        return 'Accepted';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'expired':
        return 'Expired';
      default:
        return 'Unknown';
    }
  }

  String get matchTypeDisplayText {
    switch (matchType) {
      case '5v5':
        return '5 vs 5';
      case '7v7':
        return '7 vs 7';
      case '11v11':
        return '11 vs 11';
      default:
        return matchType ?? 'Unknown';
    }
  }
}

/// Challenge response entity representing a response to a challenge
class ChallengeResponse {
  final String id;
  final String responderId;
  final String? responderName;
  final String? responderTeamId;
  final String? responderTeamName;
  final String? responseType;
  final DateTime? preferredDateTime;
  final String? message;
  final DateTime respondedAt;

  const ChallengeResponse({
    required this.id,
    required this.responderId,
    this.responderName,
    this.responderTeamId,
    this.responderTeamName,
    this.responseType,
    this.preferredDateTime,
    this.message,
    required this.respondedAt,
  });

  factory ChallengeResponse.fromJson(Map<String, dynamic> json) {
    return ChallengeResponse(
      id: json['id'] as String,
      responderId: json['responderId'] as String,
      responderName: json['responderName'] as String?,
      responderTeamId: json['responderTeamId'] as String?,
      responderTeamName: json['responderTeamName'] as String?,
      responseType: json['responseType'] as String?,
      preferredDateTime: json['preferredDateTime'] != null
          ? DateTime.parse(json['preferredDateTime'] as String)
          : null,
      message: json['message'] as String?,
      respondedAt: DateTime.parse(json['respondedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'responderId': responderId,
      'responderName': responderName,
      'responderTeamId': responderTeamId,
      'responderTeamName': responderTeamName,
      'responseType': responseType,
      'preferredDateTime': preferredDateTime?.toIso8601String(),
      'message': message,
      'respondedAt': respondedAt.toIso8601String(),
    };
  }

  ChallengeResponse copyWith({
    String? id,
    String? responderId,
    String? responderName,
    String? responderTeamId,
    String? responderTeamName,
    String? responseType,
    DateTime? preferredDateTime,
    String? message,
    DateTime? respondedAt,
  }) {
    return ChallengeResponse(
      id: id ?? this.id,
      responderId: responderId ?? this.responderId,
      responderName: responderName ?? this.responderName,
      responderTeamId: responderTeamId ?? this.responderTeamId,
      responderTeamName: responderTeamName ?? this.responderTeamName,
      responseType: responseType ?? this.responseType,
      preferredDateTime: preferredDateTime ?? this.preferredDateTime,
      message: message ?? this.message,
      respondedAt: respondedAt ?? this.respondedAt,
    );
  }

  bool get isAccepted => responseType == 'accepted';
  bool get isDeclined => responseType == 'declined';
  bool get isCounterOffer => responseType == 'counter_offer';
}

/// Challenge statistics entity
class ChallengeStats {
  final int totalChallenges;
  final int openChallenges;
  final int acceptedChallenges;
  final int completedChallenges;
  final int wonChallenges;
  final int lostChallenges;
  final double winRate;
  final int disputedChallenges;

  const ChallengeStats({
    required this.totalChallenges,
    required this.openChallenges,
    required this.acceptedChallenges,
    required this.completedChallenges,
    required this.wonChallenges,
    required this.lostChallenges,
    required this.winRate,
    required this.disputedChallenges,
  });

  factory ChallengeStats.fromJson(Map<String, dynamic> json) {
    return ChallengeStats(
      totalChallenges: json['totalChallenges'] as int,
      openChallenges: json['openChallenges'] as int,
      acceptedChallenges: json['acceptedChallenges'] as int,
      completedChallenges: json['completedChallenges'] as int,
      wonChallenges: json['wonChallenges'] as int,
      lostChallenges: json['lostChallenges'] as int,
      winRate: (json['winRate'] as num).toDouble(),
      disputedChallenges: json['disputedChallenges'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalChallenges': totalChallenges,
      'openChallenges': openChallenges,
      'acceptedChallenges': acceptedChallenges,
      'completedChallenges': completedChallenges,
      'wonChallenges': wonChallenges,
      'lostChallenges': lostChallenges,
      'winRate': winRate,
      'disputedChallenges': disputedChallenges,
    };
  }
}

/// Match suggestion entity
class MatchSuggestion {
  final String id;
  final String? type;
  final String? matchType;
  final String? location;
  final DateTime? proposedDateTime;
  final String? opponentName;
  final String? teamName;
  final double? wagerAmount;
  final int compatibilityScore;
  final String? description;

  const MatchSuggestion({
    required this.id,
    this.type,
    this.matchType,
    this.location,
    this.proposedDateTime,
    this.opponentName,
    this.teamName,
    this.wagerAmount,
    required this.compatibilityScore,
    this.description,
  });

  factory MatchSuggestion.fromJson(Map<String, dynamic> json) {
    return MatchSuggestion(
      id: json['id'] as String,
      type: json['type'] as String?,
      matchType: json['matchType'] as String?,
      location: json['location'] as String?,
      proposedDateTime: json['proposedDateTime'] != null
          ? DateTime.parse(json['proposedDateTime'] as String)
          : null,
      opponentName: json['opponentName'] as String?,
      teamName: json['teamName'] as String?,
      wagerAmount: json['wagerAmount'] as double?,
      compatibilityScore: json['compatibilityScore'] as int,
      description: json['description'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'matchType': matchType,
      'location': location,
      'proposedDateTime': proposedDateTime?.toIso8601String(),
      'opponentName': opponentName,
      'teamName': teamName,
      'wagerAmount': wagerAmount,
      'compatibilityScore': compatibilityScore,
      'description': description,
    };
  }
}

/// Paged challenges response entity
class PagedChallenges {
  final List<Challenge>? items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  const PagedChallenges({
    this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  factory PagedChallenges.fromJson(Map<String, dynamic> json) {
    return PagedChallenges(
      items: json['items'] != null
          ? (json['items'] as List<dynamic>)
              .map((e) => Challenge.fromJson(e))
              .toList()
          : null,
      total: json['total'] as int,
      page: json['page'] as int,
      pageSize: json['pageSize'] as int,
      totalPages: json['totalPages'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'items': items?.map((e) => e.toJson()).toList(),
      'total': total,
      'page': page,
      'pageSize': pageSize,
      'totalPages': totalPages,
    };
  }
}
