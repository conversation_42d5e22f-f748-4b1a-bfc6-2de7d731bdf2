import 'dart:math' as math;

/// Player ranking system with ELO-like rating calculation
class PlayerRanking {
  final String playerId;
  final String playerName;
  final double eloRating;
  final ChallengeRank rank;
  final int totalChallenges;
  final int challengesWon;
  final int challengesLost;
  final int currentStreak;
  final int longestStreak;
  final double winRate;
  final int rankPosition; // Global position in leaderboard
  final DateTime lastActive;
  final DateTime updatedAt;
  final List<String> specialities; // ["5v5", "high-stakes", "night-matches"]
  final PlayerStats stats;

  const PlayerRanking({
    required this.playerId,
    required this.playerName,
    required this.eloRating,
    required this.rank,
    required this.totalChallenges,
    required this.challengesWon,
    required this.challengesLost,
    required this.currentStreak,
    required this.longestStreak,
    required this.winRate,
    required this.rankPosition,
    required this.lastActive,
    required this.updatedAt,
    required this.specialities,
    required this.stats,
  });

  factory PlayerRanking.fromJson(Map<String, dynamic> json) {
    return PlayerRanking(
      playerId: json['player_id'] as String,
      playerName: json['player_name'] as String,
      eloRating: (json['elo_rating'] as num).toDouble(),
      rank: ChallengeRank.fromElo(json['elo_rating'] as num),
      totalChallenges: json['total_challenges'] as int,
      challengesWon: json['challenges_won'] as int,
      challengesLost: json['challenges_lost'] as int,
      currentStreak: json['current_streak'] as int,
      longestStreak: json['longest_streak'] as int,
      winRate: (json['win_rate'] as num).toDouble(),
      rankPosition: json['rank_position'] as int,
      lastActive: DateTime.parse(json['last_active'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      specialities: (json['specialities'] as List<dynamic>).cast<String>(),
      stats: PlayerStats.fromJson(json['stats']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'player_id': playerId,
      'player_name': playerName,
      'elo_rating': eloRating,
      'rank': rank.toString(),
      'total_challenges': totalChallenges,
      'challenges_won': challengesWon,
      'challenges_lost': challengesLost,
      'current_streak': currentStreak,
      'longest_streak': longestStreak,
      'win_rate': winRate,
      'rank_position': rankPosition,
      'last_active': lastActive.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'specialities': specialities,
      'stats': stats.toJson(),
    };
  }

  PlayerRanking copyWith({
    String? playerId,
    String? playerName,
    double? eloRating,
    ChallengeRank? rank,
    int? totalChallenges,
    int? challengesWon,
    int? challengesLost,
    int? currentStreak,
    int? longestStreak,
    double? winRate,
    int? rankPosition,
    DateTime? lastActive,
    DateTime? updatedAt,
    List<String>? specialities,
    PlayerStats? stats,
  }) {
    return PlayerRanking(
      playerId: playerId ?? this.playerId,
      playerName: playerName ?? this.playerName,
      eloRating: eloRating ?? this.eloRating,
      rank: rank ?? this.rank,
      totalChallenges: totalChallenges ?? this.totalChallenges,
      challengesWon: challengesWon ?? this.challengesWon,
      challengesLost: challengesLost ?? this.challengesLost,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      winRate: winRate ?? this.winRate,
      rankPosition: rankPosition ?? this.rankPosition,
      lastActive: lastActive ?? this.lastActive,
      updatedAt: updatedAt ?? this.updatedAt,
      specialities: specialities ?? this.specialities,
      stats: stats ?? this.stats,
    );
  }

  bool get isOnStreak => currentStreak >= 3;
  bool get hasStreakBonus => currentStreak >= 3;
  double get streakBonusMultiplier => hasStreakBonus ? 1.1 : 1.0; // 10% bonus
}

/// Challenge rank tiers based on ELO rating
enum ChallengeRank {
  bronze('Bronze', 0, 1199, '🥉'),
  silver('Silver', 1200, 1499, '🥈'),
  gold('Gold', 1500, 1799, '🥇'),
  platinum('Platinum', 1800, 2099, '💎'),
  champion('Champion', 2100, double.infinity, '👑');

  const ChallengeRank(this.name, this.minElo, this.maxElo, this.icon);

  final String name;
  final double minElo;
  final double maxElo;
  final String icon;

  static ChallengeRank fromElo(num eloRating) {
    final elo = eloRating.toDouble();
    if (elo >= champion.minElo) return champion;
    if (elo >= platinum.minElo) return platinum;
    if (elo >= gold.minElo) return gold;
    if (elo >= silver.minElo) return silver;
    return bronze;
  }

  static ChallengeRank fromString(String rankName) {
    switch (rankName.toLowerCase()) {
      case 'bronze':
        return bronze;
      case 'silver':
        return silver;
      case 'gold':
        return gold;
      case 'platinum':
        return platinum;
      case 'champion':
        return champion;
      default:
        return bronze;
    }
  }

  @override
  String toString() => name.toLowerCase();

  String get displayName => '$icon $name';

  ChallengeRank? get nextRank {
    switch (this) {
      case bronze:
        return silver;
      case silver:
        return gold;
      case gold:
        return platinum;
      case platinum:
        return champion;
      case champion:
        return null;
    }
  }

  double get progressToNextRank {
    final next = nextRank;
    if (next == null) return 1.0;
    return 1.0; // Will be calculated with actual ELO in UI
  }
}

/// Detailed player statistics for challenges
class PlayerStats {
  final Map<String, int> matchTypeWins; // {"5v5": 10, "7v7": 5}
  final Map<String, int> matchTypeLosses;
  final Map<String, double> locationWinRates; // {"Dhumbarahi": 0.8}
  final Map<String, int> timeSlotPreferences; // {"evening": 15, "morning": 5}
  final double averageWagerAmount;
  final int highStakesWins; // Wins in high-wager matches
  final int rivalryWins; // Wins against frequent opponents
  final List<String> favoriteOpponents;
  final Map<String, DateTime> lastPlayedLocation;
  final double averageMatchDuration; // in minutes
  final List<ChallengeOutsideComfort> outsideComfortChallenges;

  const PlayerStats({
    required this.matchTypeWins,
    required this.matchTypeLosses,
    required this.locationWinRates,
    required this.timeSlotPreferences,
    required this.averageWagerAmount,
    required this.highStakesWins,
    required this.rivalryWins,
    required this.favoriteOpponents,
    required this.lastPlayedLocation,
    required this.averageMatchDuration,
    required this.outsideComfortChallenges,
  });

  factory PlayerStats.fromJson(Map<String, dynamic> json) {
    return PlayerStats(
      matchTypeWins: Map<String, int>.from(json['match_type_wins'] ?? {}),
      matchTypeLosses: Map<String, int>.from(json['match_type_losses'] ?? {}),
      locationWinRates: Map<String, double>.from(
        json['location_win_rates'] ?? {},
      ),
      timeSlotPreferences: Map<String, int>.from(
        json['time_slot_preferences'] ?? {},
      ),
      averageWagerAmount:
          (json['average_wager_amount'] as num?)?.toDouble() ?? 0.0,
      highStakesWins: json['high_stakes_wins'] as int? ?? 0,
      rivalryWins: json['rivalry_wins'] as int? ?? 0,
      favoriteOpponents:
          (json['favorite_opponents'] as List<dynamic>?)?.cast<String>() ?? [],
      lastPlayedLocation:
          (json['last_played_location'] as Map<String, dynamic>?)?.map(
            (k, v) => MapEntry(k, DateTime.parse(v as String)),
          ) ??
          {},
      averageMatchDuration:
          (json['average_match_duration'] as num?)?.toDouble() ?? 0.0,
      outsideComfortChallenges:
          (json['outside_comfort_challenges'] as List<dynamic>?)
              ?.map((item) => ChallengeOutsideComfort.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'match_type_wins': matchTypeWins,
      'match_type_losses': matchTypeLosses,
      'location_win_rates': locationWinRates,
      'time_slot_preferences': timeSlotPreferences,
      'average_wager_amount': averageWagerAmount,
      'high_stakes_wins': highStakesWins,
      'rivalry_wins': rivalryWins,
      'favorite_opponents': favoriteOpponents,
      'last_played_location': lastPlayedLocation.map(
        (k, v) => MapEntry(k, v.toIso8601String()),
      ),
      'average_match_duration': averageMatchDuration,
      'outside_comfort_challenges':
          outsideComfortChallenges.map((item) => item.toJson()).toList(),
    };
  }

  String get preferredMatchType {
    if (matchTypeWins.isEmpty) return '5v5';
    return matchTypeWins.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  String get preferredTimeSlot {
    if (timeSlotPreferences.isEmpty) return 'evening';
    return timeSlotPreferences.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  double winRateForMatchType(String matchType) {
    final wins = matchTypeWins[matchType] ?? 0;
    final losses = matchTypeLosses[matchType] ?? 0;
    final total = wins + losses;
    return total > 0 ? wins / total : 0.0;
  }
}

/// Represents a challenge outside player's comfort zone for bonus points
class ChallengeOutsideComfort {
  final String challengeId;
  final String outsideComfortType; // "location", "time_slot", "match_size"
  final String description;
  final double bonusPoints;
  final DateTime achievedAt;

  const ChallengeOutsideComfort({
    required this.challengeId,
    required this.outsideComfortType,
    required this.description,
    required this.bonusPoints,
    required this.achievedAt,
  });

  factory ChallengeOutsideComfort.fromJson(Map<String, dynamic> json) {
    return ChallengeOutsideComfort(
      challengeId: json['challenge_id'] as String,
      outsideComfortType: json['outside_comfort_type'] as String,
      description: json['description'] as String,
      bonusPoints: (json['bonus_points'] as num).toDouble(),
      achievedAt: DateTime.parse(json['achieved_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'challenge_id': challengeId,
      'outside_comfort_type': outsideComfortType,
      'description': description,
      'bonus_points': bonusPoints,
      'achieved_at': achievedAt.toIso8601String(),
    };
  }
}

/// ELO rating calculation utility
class EloCalculator {
  static const double kFactor = 32.0; // Sensitivity of rating changes
  static const double initialRating = 1200.0;

  /// Calculate new ELO ratings after a match
  static EloResult calculateElo({
    required double winnerRating,
    required double loserRating,
    bool isUpset = false,
  }) {
    final expectedWinnerScore = _expectedScore(winnerRating, loserRating);
    final expectedLoserScore = _expectedScore(loserRating, winnerRating);

    // Actual scores: winner = 1, loser = 0
    final winnerChange = kFactor * (1.0 - expectedWinnerScore);
    final loserChange = kFactor * (0.0 - expectedLoserScore);

    // Bonus for upsets (lower-rated player beating higher-rated player)
    final double upsetBonus = isUpset ? 5.0 : 0.0;

    return EloResult(
      winnerNewRating: winnerRating + winnerChange + upsetBonus,
      loserNewRating: loserRating + loserChange - (upsetBonus * 0.5),
      winnerChange: winnerChange + upsetBonus,
      loserChange: loserChange - (upsetBonus * 0.5),
    );
  }

  static double _expectedScore(double playerRating, double opponentRating) {
    return 1.0 / (1.0 + math.pow(10, (opponentRating - playerRating) / 400));
  }

  /// Determine if a match result is an upset
  static bool isUpset(double winnerRating, double loserRating) {
    return winnerRating < loserRating - 100; // 100+ point difference
  }
}

class EloResult {
  final double winnerNewRating;
  final double loserNewRating;
  final double winnerChange;
  final double loserChange;

  const EloResult({
    required this.winnerNewRating,
    required this.loserNewRating,
    required this.winnerChange,
    required this.loserChange,
  });
}
