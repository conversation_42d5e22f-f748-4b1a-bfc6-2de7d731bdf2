/// Leaderboard system for top challengers and acceptors
class Leaderboard {
  final String id;
  final LeaderboardType type;
  final String title;
  final String description;
  final LeaderboardPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final List<LeaderboardEntry> entries;
  final DateTime lastUpdated;
  final bool isActive;

  const Leaderboard({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.period,
    required this.startDate,
    required this.endDate,
    required this.entries,
    required this.lastUpdated,
    this.isActive = true,
  });

  factory Leaderboard.fromJson(Map<String, dynamic> json) {
    return Leaderboard(
      id: json['id'] as String,
      type: LeaderboardType.fromString(json['type'] as String),
      title: json['title'] as String,
      description: json['description'] as String,
      period: LeaderboardPeriod.fromString(json['period'] as String),
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      entries:
          (json['entries'] as List<dynamic>)
              .map((entry) => LeaderboardEntry.fromJson(entry))
              .toList(),
      lastUpdated: DateTime.parse(json['last_updated'] as String),
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'title': title,
      'description': description,
      'period': period.toString(),
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'entries': entries.map((entry) => entry.toJson()).toList(),
      'last_updated': lastUpdated.toIso8601String(),
      'is_active': isActive,
    };
  }

  Leaderboard copyWith({
    String? id,
    LeaderboardType? type,
    String? title,
    String? description,
    LeaderboardPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    List<LeaderboardEntry>? entries,
    DateTime? lastUpdated,
    bool? isActive,
  }) {
    return Leaderboard(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      entries: entries ?? this.entries,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isActive: isActive ?? this.isActive,
    );
  }

  List<LeaderboardEntry> get topEntries => entries.take(10).toList();

  LeaderboardEntry? findPlayerEntry(String playerId) {
    try {
      return entries.firstWhere((entry) => entry.playerId == playerId);
    } catch (e) {
      return null;
    }
  }

  bool get isCurrentPeriod {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate);
  }

  String get periodDisplayText {
    switch (period) {
      case LeaderboardPeriod.weekly:
        return 'This Week';
      case LeaderboardPeriod.monthly:
        return 'This Month';
      case LeaderboardPeriod.allTime:
        return 'All Time';
      case LeaderboardPeriod.seasonal:
        return 'This Season';
    }
  }
}

/// Types of leaderboards
enum LeaderboardType {
  topChallengers('top_challengers', 'Top Challengers', '⚔️'),
  topAcceptors('top_acceptors', 'Top Acceptors', '🎯'),
  topEloRating('top_elo_rating', 'Top Rated', '🏆'),
  topWinStreak('top_win_streak', 'Win Streaks', '🔥'),
  topBadgeCollectors('top_badge_collectors', 'Badge Collectors', '🏅'),
  topWagersWon('top_wagers_won', 'Biggest Winners', '💰');

  const LeaderboardType(this.value, this.displayName, this.icon);

  final String value;
  final String displayName;
  final String icon;

  static LeaderboardType fromString(String value) {
    switch (value) {
      case 'top_challengers':
        return topChallengers;
      case 'top_acceptors':
        return topAcceptors;
      case 'top_elo_rating':
        return topEloRating;
      case 'top_win_streak':
        return topWinStreak;
      case 'top_badge_collectors':
        return topBadgeCollectors;
      case 'top_wagers_won':
        return topWagersWon;
      default:
        throw ArgumentError('Unknown leaderboard type: $value');
    }
  }

  @override
  String toString() => value;
}

/// Time periods for leaderboards
enum LeaderboardPeriod {
  weekly('weekly', 'Weekly'),
  monthly('monthly', 'Monthly'),
  seasonal('seasonal', 'Seasonal'),
  allTime('all_time', 'All Time');

  const LeaderboardPeriod(this.value, this.displayName);

  final String value;
  final String displayName;

  static LeaderboardPeriod fromString(String value) {
    switch (value) {
      case 'weekly':
        return weekly;
      case 'monthly':
        return monthly;
      case 'seasonal':
        return seasonal;
      case 'all_time':
        return allTime;
      default:
        throw ArgumentError('Unknown leaderboard period: $value');
    }
  }

  @override
  String toString() => value;
}

/// Individual entry in a leaderboard
class LeaderboardEntry {
  final int position;
  final String playerId;
  final String playerName;
  final String? playerAvatar;
  final String? teamId;
  final String? teamName;
  final double primaryScore; // Main metric (challenges created, ELO, etc.)
  final Map<String, dynamic> secondaryStats; // Additional stats
  final String rankDisplay; // Formatted rank (1st, 2nd, 3rd, etc.)
  final bool isCurrentUser;
  final DateTime lastActive;
  final List<String> recentBadges; // Recent badges earned

  const LeaderboardEntry({
    required this.position,
    required this.playerId,
    required this.playerName,
    this.playerAvatar,
    this.teamId,
    this.teamName,
    required this.primaryScore,
    required this.secondaryStats,
    required this.rankDisplay,
    this.isCurrentUser = false,
    required this.lastActive,
    required this.recentBadges,
  });

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) {
    return LeaderboardEntry(
      position: json['position'] as int,
      playerId: json['player_id'] as String,
      playerName: json['player_name'] as String,
      playerAvatar: json['player_avatar'] as String?,
      teamId: json['team_id'] as String?,
      teamName: json['team_name'] as String?,
      primaryScore: (json['primary_score'] as num).toDouble(),
      secondaryStats: Map<String, dynamic>.from(json['secondary_stats'] ?? {}),
      rankDisplay: json['rank_display'] as String,
      isCurrentUser: json['is_current_user'] as bool? ?? false,
      lastActive: DateTime.parse(json['last_active'] as String),
      recentBadges:
          (json['recent_badges'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'position': position,
      'player_id': playerId,
      'player_name': playerName,
      'player_avatar': playerAvatar,
      'team_id': teamId,
      'team_name': teamName,
      'primary_score': primaryScore,
      'secondary_stats': secondaryStats,
      'rank_display': rankDisplay,
      'is_current_user': isCurrentUser,
      'last_active': lastActive.toIso8601String(),
      'recent_badges': recentBadges,
    };
  }

  LeaderboardEntry copyWith({
    int? position,
    String? playerId,
    String? playerName,
    String? playerAvatar,
    String? teamId,
    String? teamName,
    double? primaryScore,
    Map<String, dynamic>? secondaryStats,
    String? rankDisplay,
    bool? isCurrentUser,
    DateTime? lastActive,
    List<String>? recentBadges,
  }) {
    return LeaderboardEntry(
      position: position ?? this.position,
      playerId: playerId ?? this.playerId,
      playerName: playerName ?? this.playerName,
      playerAvatar: playerAvatar ?? this.playerAvatar,
      teamId: teamId ?? this.teamId,
      teamName: teamName ?? this.teamName,
      primaryScore: primaryScore ?? this.primaryScore,
      secondaryStats: secondaryStats ?? this.secondaryStats,
      rankDisplay: rankDisplay ?? this.rankDisplay,
      isCurrentUser: isCurrentUser ?? this.isCurrentUser,
      lastActive: lastActive ?? this.lastActive,
      recentBadges: recentBadges ?? this.recentBadges,
    );
  }

  bool get isTopThree => position <= 3;

  String get positionEmoji {
    switch (position) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return '';
    }
  }

  String get displayName =>
      teamName != null ? '$playerName ($teamName)' : playerName;
}

/// Combined leaderboard data for dashboard display
class LeaderboardDashboard {
  final List<Leaderboard> weeklyLeaderboards;
  final List<Leaderboard> monthlyLeaderboards;
  final Leaderboard? topChallengersWeekly;
  final Leaderboard? topAcceptorsWeekly;
  final Leaderboard? topRatedPlayers;
  final Map<String, LeaderboardEntry?> currentUserEntries;
  final DateTime lastUpdated;

  const LeaderboardDashboard({
    required this.weeklyLeaderboards,
    required this.monthlyLeaderboards,
    this.topChallengersWeekly,
    this.topAcceptorsWeekly,
    this.topRatedPlayers,
    required this.currentUserEntries,
    required this.lastUpdated,
  });

  factory LeaderboardDashboard.fromJson(Map<String, dynamic> json) {
    return LeaderboardDashboard(
      weeklyLeaderboards:
          (json['weekly_leaderboards'] as List<dynamic>)
              .map((lb) => Leaderboard.fromJson(lb))
              .toList(),
      monthlyLeaderboards:
          (json['monthly_leaderboards'] as List<dynamic>)
              .map((lb) => Leaderboard.fromJson(lb))
              .toList(),
      topChallengersWeekly:
          json['top_challengers_weekly'] != null
              ? Leaderboard.fromJson(json['top_challengers_weekly'])
              : null,
      topAcceptorsWeekly:
          json['top_acceptors_weekly'] != null
              ? Leaderboard.fromJson(json['top_acceptors_weekly'])
              : null,
      topRatedPlayers:
          json['top_rated_players'] != null
              ? Leaderboard.fromJson(json['top_rated_players'])
              : null,
      currentUserEntries: (json['current_user_entries'] as Map<String, dynamic>)
          .map(
            (key, value) => MapEntry(
              key,
              value != null ? LeaderboardEntry.fromJson(value) : null,
            ),
          ),
      lastUpdated: DateTime.parse(json['last_updated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'weekly_leaderboards':
          weeklyLeaderboards.map((lb) => lb.toJson()).toList(),
      'monthly_leaderboards':
          monthlyLeaderboards.map((lb) => lb.toJson()).toList(),
      'top_challengers_weekly': topChallengersWeekly?.toJson(),
      'top_acceptors_weekly': topAcceptorsWeekly?.toJson(),
      'top_rated_players': topRatedPlayers?.toJson(),
      'current_user_entries': currentUserEntries.map(
        (key, value) => MapEntry(key, value?.toJson()),
      ),
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  LeaderboardEntry? getUserEntry(LeaderboardType type) {
    return currentUserEntries[type.value];
  }

  int? getUserPosition(LeaderboardType type) {
    return getUserEntry(type)?.position;
  }

  bool get hasUserInTopTen {
    return currentUserEntries.values.any(
      (entry) => entry != null && entry.position <= 10,
    );
  }

  List<Leaderboard> get featuredLeaderboards {
    return [
      if (topChallengersWeekly != null) topChallengersWeekly!,
      if (topAcceptorsWeekly != null) topAcceptorsWeekly!,
      if (topRatedPlayers != null) topRatedPlayers!,
    ];
  }
}

/// Utility for formatting leaderboard-related data
class LeaderboardFormatter {
  static String formatRank(int position) {
    if (position == 1) return '1st';
    if (position == 2) return '2nd';
    if (position == 3) return '3rd';
    if (position >= 11 && position <= 13) return '${position}th';

    switch (position % 10) {
      case 1:
        return '${position}st';
      case 2:
        return '${position}nd';
      case 3:
        return '${position}rd';
      default:
        return '${position}th';
    }
  }

  static String formatScore(LeaderboardType type, double score) {
    switch (type) {
      case LeaderboardType.topChallengers:
      case LeaderboardType.topAcceptors:
        return '${score.toInt()} challenges';
      case LeaderboardType.topEloRating:
        return '${score.toInt()} ELO';
      case LeaderboardType.topWinStreak:
        return '${score.toInt()} wins';
      case LeaderboardType.topBadgeCollectors:
        return '${score.toInt()} badges';
      case LeaderboardType.topWagersWon:
        return 'NPR ${score.toStringAsFixed(0)}';
    }
  }

  static String getLeaderboardDescription(
    LeaderboardType type,
    LeaderboardPeriod period,
  ) {
    final periodText = period.displayName.toLowerCase();

    switch (type) {
      case LeaderboardType.topChallengers:
        return 'Players who created the most challenges this $periodText';
      case LeaderboardType.topAcceptors:
        return 'Players who accepted the most challenges this $periodText';
      case LeaderboardType.topEloRating:
        return 'Highest rated players by ELO rating';
      case LeaderboardType.topWinStreak:
        return 'Current longest winning streaks';
      case LeaderboardType.topBadgeCollectors:
        return 'Players with the most badges earned this $periodText';
      case LeaderboardType.topWagersWon:
        return 'Biggest wager winners this $periodText';
    }
  }
}
