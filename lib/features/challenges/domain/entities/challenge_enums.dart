/// Enums for challenge-related data types
/// Based on API validation requirements

enum AgeGroup {
  u12('U12'),
  u14('U14'),
  u16('U16'),
  u18('U18'),
  u21('U21'),
  senior('Senior'),
  open('Open');

  const AgeGroup(this.value);
  final String value;

  static AgeGroup? fromString(String? value) {
    if (value == null) return null;
    return AgeGroup.values.firstWhere(
      (ageGroup) => ageGroup.value == value,
      orElse: () => AgeGroup.open, // Default fallback
    );
  }

  @override
  String toString() => value;
}

enum WagerType {
  money('Money'),
  trophy('Trophy'),
  braggingRights('Bragging Rights');

  const WagerType(this.value);
  final String value;

  static WagerType? fromString(String? value) {
    if (value == null) return null;
    return WagerType.values.firstWhere(
      (wagerType) => wagerType.value == value,
      orElse: () => WagerType.braggingRights, // Default fallback
    );
  }

  @override
  String toString() => value;
}
