import 'package:flutter_riverpod/flutter_riverpod.dart';

// Leaderboard State - Placeholder until backend leaderboard is implemented
class LeaderboardState {
  final bool isLoading;
  final String? error;
  final String selectedType;
  final String selectedPeriod;

  const LeaderboardState({
    this.isLoading = false,
    this.error,
    this.selectedType = 'top_challengers',
    this.selectedPeriod = 'weekly',
  });

  LeaderboardState copyWith({
    bool? isLoading,
    String? error,
    String? selectedType,
    String? selectedPeriod,
  }) {
    return LeaderboardState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      selectedType: selectedType ?? this.selectedType,
      selectedPeriod: selectedPeriod ?? this.selectedPeriod,
    );
  }
}

class LeaderboardNotifier extends StateNotifier<LeaderboardState> {
  LeaderboardNotifier() : super(const LeaderboardState());

  Future<void> loadDashboard() async {
    // TODO: Implement when backend leaderboard is ready
    state = state.copyWith(isLoading: false);
  }

  Future<void> loadSpecificLeaderboard({
    required String type,
    required String period,
  }) async {
    // TODO: Implement when backend leaderboard is ready
    state = state.copyWith(
      isLoading: false,
      selectedType: type,
      selectedPeriod: period,
    );
  }

  void selectLeaderboard({
    required String type,
    required String period,
  }) {
    state = state.copyWith(selectedType: type, selectedPeriod: period);
  }

  void refresh() {
    loadDashboard();
  }
}
