import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/challenge.dart';
import '../../domain/entities/challenge_enums.dart';
import '../../domain/usecases/challenge_usecases.dart';
import '../../domain/repositories/challenge_repository.dart';

// Challenge List State
class ChallengeListState {
  final List<Challenge> challenges;
  final bool isLoading;
  final String? error;
  final bool hasMore;
  final ChallengeListTab activeTab;

  const ChallengeListState({
    this.challenges = const [],
    this.isLoading = false,
    this.error,
    this.hasMore = true,
    this.activeTab = ChallengeListTab.nearby,
  });

  ChallengeListState copyWith({
    List<Challenge>? challenges,
    bool? isLoading,
    String? error,
    bool? hasMore,
    ChallengeListTab? activeTab,
  }) {
    return ChallengeListState(
      challenges: challenges ?? this.challenges,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasMore: hasMore ?? this.hasMore,
      activeTab: activeTab ?? this.activeTab,
    );
  }
}

enum ChallengeListTab { nearby, recommended, trending, myHistory }

class ChallengeListNotifier extends StateNotifier<ChallengeListState> {
  final GetChallengesUseCase getChallengesUseCase;

  ChallengeListNotifier({
    required this.getChallengesUseCase,
  }) : super(const ChallengeListState());

  Future<void> loadChallenges({
    ChallengeFilters? filters,
    bool refresh = false,
  }) async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    final params = GetChallengesParams(
      lat: filters?.latitude,
      lng: filters?.longitude,
      matchType: filters?.matchType,
      location: filters?.location,
      minWager: filters?.minWager,
      maxWager: filters?.maxWager,
      status: 'open',
      limit: 20,
      offset: refresh ? 0 : state.challenges.length,
    );

    final result = await getChallengesUseCase(params);

    result.fold(
      (error) => state = state.copyWith(isLoading: false, error: error.message),
      (pagedChallenges) => state = state.copyWith(
        isLoading: false,
        challenges: refresh
            ? (pagedChallenges.items ?? [])
            : [...state.challenges, ...(pagedChallenges.items ?? [])],
        hasMore: pagedChallenges.page < pagedChallenges.totalPages,
      ),
    );
  }

  // Search and recommended functionality will be implemented later
  Future<void> searchChallenges(String query) async {
    // TODO: Implement search functionality
  }

  Future<void> loadRecommended(String playerId) async {
    // TODO: Implement recommended functionality
  }

  void switchTab(ChallengeListTab tab) {
    state = state.copyWith(activeTab: tab, challenges: [], hasMore: true);
  }

  void refresh() {
    loadChallenges(refresh: true);
  }
}

// Challenge Details State
class ChallengeDetailsState {
  final Challenge? challenge;
  final bool isLoading;
  final String? error;
  final bool isAccepting;
  final bool isCompleting;

  const ChallengeDetailsState({
    this.challenge,
    this.isLoading = false,
    this.error,
    this.isAccepting = false,
    this.isCompleting = false,
  });

  ChallengeDetailsState copyWith({
    Challenge? challenge,
    bool? isLoading,
    String? error,
    bool? isAccepting,
    bool? isCompleting,
  }) {
    return ChallengeDetailsState(
      challenge: challenge ?? this.challenge,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isAccepting: isAccepting ?? this.isAccepting,
      isCompleting: isCompleting ?? this.isCompleting,
    );
  }
}

class ChallengeDetailsNotifier extends StateNotifier<ChallengeDetailsState> {
  final String challengeId;
  final ChallengeRepository repository;
  final RespondToChallengeUseCase respondToChallengeUseCase;
  final SubmitMatchResultUseCase submitMatchResultUseCase;

  ChallengeDetailsNotifier({
    required this.challengeId,
    required this.repository,
    required this.respondToChallengeUseCase,
    required this.submitMatchResultUseCase,
  }) : super(const ChallengeDetailsState()) {
    loadChallenge();
  }

  Future<void> loadChallenge() async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await repository.getChallengeById(challengeId);

    result.fold(
      (error) => state = state.copyWith(isLoading: false, error: error.message),
      (challenge) =>
          state = state.copyWith(isLoading: false, challenge: challenge),
    );
  }

  Future<bool> acceptChallenge({String? message}) async {
    if (state.isAccepting) return false;

    state = state.copyWith(isAccepting: true, error: null);

    final params = RespondToChallengeParams(
      challengeId: challengeId,
      response: 'accept',
      message: message,
    );

    final result = await respondToChallengeUseCase(params);

    return result.fold(
      (error) {
        state = state.copyWith(isAccepting: false, error: error.message);
        return false;
      },
      (challenge) {
        state = state.copyWith(isAccepting: false, challenge: challenge);
        return true;
      },
    );
  }

  Future<bool> completeChallenge({
    required String winnerId,
    required String loserId,
    required int winnerScore,
    required int loserScore,
  }) async {
    if (state.isCompleting) return false;

    state = state.copyWith(isCompleting: true, error: null);

    final params = SubmitMatchResultParams(
      challengeId: challengeId,
      winnerId: winnerId,
      loserId: loserId,
      winnerScore: winnerScore,
      loserScore: loserScore,
    );

    final result = await submitMatchResultUseCase(params);

    return result.fold(
      (error) {
        state = state.copyWith(isCompleting: false, error: error.message);
        return false;
      },
      (_) {
        state = state.copyWith(isCompleting: false);
        // Reload challenge to get updated state
        loadChallenge();
        return true;
      },
    );
  }
}

// Create Challenge State
class CreateChallengeState {
  final bool isLoading;
  final String? error;
  final Challenge? createdChallenge;
  final String matchType;
  final String location;
  final String? preferredVenue; // Legacy field for backward compatibility
  final List<String> selectedVenueIds; // New field for multiple venues
  final DateTime? preferredDateTime;
  final String? timeSlot;
  final bool losersPay;
  final double extraAmount;
  final String? description;
  final List<String> tags;

  const CreateChallengeState({
    this.isLoading = false,
    this.error,
    this.createdChallenge,
    this.matchType = '5v5',
    this.location = '',
    this.preferredVenue,
    this.selectedVenueIds = const [],
    this.preferredDateTime,
    this.timeSlot,
    this.losersPay = true,
    this.extraAmount = 0.0,
    this.description,
    this.tags = const [],
  });

  CreateChallengeState copyWith({
    bool? isLoading,
    String? error,
    Challenge? createdChallenge,
    String? matchType,
    String? location,
    String? preferredVenue,
    List<String>? selectedVenueIds,
    DateTime? preferredDateTime,
    String? timeSlot,
    bool? losersPay,
    double? extraAmount,
    String? description,
    List<String>? tags,
  }) {
    return CreateChallengeState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      createdChallenge: createdChallenge ?? this.createdChallenge,
      matchType: matchType ?? this.matchType,
      location: location ?? this.location,
      preferredVenue: preferredVenue ?? this.preferredVenue,
      selectedVenueIds: selectedVenueIds ?? this.selectedVenueIds,
      preferredDateTime: preferredDateTime ?? this.preferredDateTime,
      timeSlot: timeSlot ?? this.timeSlot,
      losersPay: losersPay ?? this.losersPay,
      extraAmount: extraAmount ?? this.extraAmount,
      description: description ?? this.description,
      tags: tags ?? this.tags,
    );
  }
}

class CreateChallengeNotifier extends StateNotifier<CreateChallengeState> {
  final CreateChallengeUseCase createChallengeUseCase;

  CreateChallengeNotifier(this.createChallengeUseCase)
      : super(const CreateChallengeState());

  void updateMatchType(String matchType) {
    state = state.copyWith(matchType: matchType);
  }

  void updateLocation(String location) {
    state = state.copyWith(location: location);
  }

  void updatePreferredVenue(String? venue) {
    state = state.copyWith(preferredVenue: venue);
  }

  void updateSelectedVenues(List<String> venueIds) {
    state = state.copyWith(selectedVenueIds: venueIds);
  }

  void updateDateTime(DateTime dateTime) {
    state = state.copyWith(preferredDateTime: dateTime);
  }

  void updateTimeSlot(String? timeSlot) {
    state = state.copyWith(timeSlot: timeSlot);
  }

  void updateWager({bool? losersPay, double? extraAmount}) {
    state = state.copyWith(losersPay: losersPay, extraAmount: extraAmount);
  }

  void updateDescription(String? description) {
    state = state.copyWith(description: description);
  }

  void updateTags(List<String> tags) {
    state = state.copyWith(tags: tags);
  }

  Future<bool> createChallenge() async {
    if (state.isLoading) return false;

    if (state.location.isEmpty || state.preferredDateTime == null) {
      state = state.copyWith(error: 'Please fill in all required fields');
      return false;
    }

    state = state.copyWith(isLoading: true, error: null);

    final params = CreateChallengeParams(
      matchType: state.matchType,
      location: state.location,
      preferredVenue: state.preferredVenue,
      preferredDateTime: state.preferredDateTime!,
      timeSlot: state.timeSlot,
      wagerAmount: state.extraAmount,
      wagerType: state.losersPay ? WagerType.money : WagerType.braggingRights,
      description: state.description,
      tags: state.tags,
    );

    final result = await createChallengeUseCase(params);

    return result.fold(
      (error) {
        state = state.copyWith(isLoading: false, error: error.message);
        return false;
      },
      (challenge) {
        state = state.copyWith(isLoading: false, createdChallenge: challenge);
        return true;
      },
    );
  }

  void reset() {
    state = const CreateChallengeState();
  }
}

// Player Ranking functionality will be implemented later
// TODO: Implement player ranking, badges, and insights

// Challenge Filters
class ChallengeFilters {
  final String? matchType;
  final String? location;
  final double? minWager;
  final double? maxWager;
  final double? latitude;
  final double? longitude;
  final double? radiusKm;
  final List<String> tags;

  const ChallengeFilters({
    this.matchType,
    this.location,
    this.minWager,
    this.maxWager,
    this.latitude,
    this.longitude,
    this.radiusKm,
    this.tags = const [],
  });

  ChallengeFilters copyWith({
    String? matchType,
    String? location,
    double? minWager,
    double? maxWager,
    double? latitude,
    double? longitude,
    double? radiusKm,
    List<String>? tags,
    bool clearMatchType = false,
    bool clearLocation = false,
    bool clearMinWager = false,
    bool clearMaxWager = false,
    bool clearLatitude = false,
    bool clearLongitude = false,
    bool clearRadiusKm = false,
  }) {
    return ChallengeFilters(
      matchType: clearMatchType ? null : (matchType ?? this.matchType),
      location: clearLocation ? null : (location ?? this.location),
      minWager: clearMinWager ? null : (minWager ?? this.minWager),
      maxWager: clearMaxWager ? null : (maxWager ?? this.maxWager),
      latitude: clearLatitude ? null : (latitude ?? this.latitude),
      longitude: clearLongitude ? null : (longitude ?? this.longitude),
      radiusKm: clearRadiusKm ? null : (radiusKm ?? this.radiusKm),
      tags: tags ?? this.tags,
    );
  }

  bool get hasFilters =>
      matchType != null ||
      location != null ||
      minWager != null ||
      maxWager != null ||
      tags.isNotEmpty;

  ChallengeFilters clear() {
    return const ChallengeFilters(location: 'Nearby');
  }
}

class ChallengeFiltersNotifier extends StateNotifier<ChallengeFilters> {
  ChallengeFiltersNotifier()
      : super(const ChallengeFilters(location: 'Nearby'));

  void updateMatchType(String? matchType) {
    state = state.copyWith(
      matchType: matchType,
      clearMatchType: matchType == null,
    );
  }

  void updateLocation(String? location) {
    state = state.copyWith(location: location, clearLocation: location == null);
  }

  void updateWagerRange({double? minWager, double? maxWager}) {
    state = state.copyWith(
      minWager: minWager,
      maxWager: maxWager,
      clearMinWager: minWager == null,
      clearMaxWager: maxWager == null,
    );
  }

  void updateLocationCoordinates({
    double? latitude,
    double? longitude,
    double? radiusKm,
  }) {
    state = state.copyWith(
      latitude: latitude,
      longitude: longitude,
      radiusKm: radiusKm,
    );
  }

  void updateTags(List<String> tags) {
    state = state.copyWith(tags: tags);
  }

  void clearFilters() {
    state = const ChallengeFilters(location: 'Nearby');
  }
}
