import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../utils/color.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;

/// Player Challenge Card - Placeholder until backend ranking is implemented
class PlayerChallengeCard extends ConsumerWidget {
  final String playerId;
  final bool isExpanded;
  final VoidCallback? onTap;

  const PlayerChallengeCard({
    super.key,
    required this.playerId,
    this.isExpanded = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final lightBlue =
        isDark ? NextSportzTheme.darkBlue : NextSportzTheme.lightBlue;
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              lightBlue.withOpacity(0.8),
              secondaryColor.withOpacity(0.6),
              accentColor.withOpacity(0.4),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: _buildPlaceholderContent(ref),
      ),
    );
  }

  Widget _buildPlaceholderContent(WidgetRef ref) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person,
            color: accentColor,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'Player Challenge Card',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Gilroy_Bold',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Ranking system coming soon',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'Player ID: $playerId',
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 12,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Compact player card - Placeholder until backend ranking is implemented
class CompactPlayerCard extends ConsumerWidget {
  final String playerId;
  final VoidCallback? onTap;

  const CompactPlayerCard({super.key, required this.playerId, this.onTap});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final lightBlue =
        isDark ? NextSportzTheme.darkBlue : NextSportzTheme.lightBlue;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: lightBlue.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withOpacity(0.1)),
        ),
        child: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: accentColor.withOpacity(0.3),
              child: Icon(Icons.person, color: accentColor),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Player $playerId',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Gilroy_Bold',
                    ),
                  ),
                  const Text(
                    'Ranking coming soon',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 11,
                      fontFamily: 'Gilroy_Medium',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
