import 'package:flutter/material.dart';

class ExpirationSlider extends StatelessWidget {
  final double value;       // current value
  final double maxValue;    // max (e.g., 24)
  final ValueChanged<double> onChanged;
  final Color accentColor;
  final String centerLabel;
  final String centerSubLabel;

  const ExpirationSlider({
    super.key,
    required this.value,
    required this.maxValue,
    required this.onChanged,
    required this.accentColor,
    this.centerLabel = '',
    this.centerSubLabel = '',
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Slider on the left
        Expanded(
          child: Slider(
            value: value,
            min: 0,
            max: maxValue,
            divisions: maxValue.toInt(),
            label: value.round().toString(),
            activeColor: accentColor,
            inactiveColor: accentColor.withOpacity(0.3),
            onChanged: onChanged,
          ),
        ),

        const SizedBox(width: 12),

        // Label + SubLabel on the right
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (centerLabel.isNotEmpty)
              Text(
                centerLabel,
                style: TextStyle(
                  fontSize: 18, // smaller
                  fontWeight: FontWeight.bold,
                  color: accentColor,
                ),
              ),
            if (centerSubLabel.isNotEmpty)
              Text(
                centerSubLabel,
                style: TextStyle(
                  fontSize: 12, // smaller
                  color: accentColor.withOpacity(0.8),
                  letterSpacing: 1.1,
                ),
              ),
          ],
        ),
      ],
    );
  }
}
