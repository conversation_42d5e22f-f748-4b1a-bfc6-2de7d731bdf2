import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/challenge.dart';
import '../../challenges_providers.dart';
import '../logic/challenge_state.dart';
import '../../../../utils/color.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;

/// Advanced filters widget for challenge list
class ChallengeFiltersWidget extends ConsumerStatefulWidget {
  final VoidCallback? onFiltersApplied;

  const ChallengeFiltersWidget({super.key, this.onFiltersApplied});

  @override
  ConsumerState<ChallengeFiltersWidget> createState() =>
      _ChallengeFiltersWidgetState();
}

class _ChallengeFiltersWidgetState
    extends ConsumerState<ChallengeFiltersWidget> {
  final TextEditingController _locationController = TextEditingController();
  RangeValues _wagerRange = const RangeValues(0, 5000);
  String? _selectedMatchType;
  final List<String> _selectedTags = [];

  static const List<String> availableTags = [
    'competitive',
    'friendly',
    'high-stakes',
    'beginner',
    'rivalry',
    'tournament',
    'weekend',
    'evening',
    'morning',
    'night',
  ];

  @override
  void initState() {
    super.initState();
    final currentFilters = ref.read(challengeFiltersProvider);
    _initializeFromCurrentFilters(currentFilters);
  }

  void _initializeFromCurrentFilters(ChallengeFilters filters) {
    _locationController.text = filters.location ?? '';
    _wagerRange = RangeValues(filters.minWager ?? 0, filters.maxWager ?? 5000);
    _selectedMatchType = filters.matchType;
    _selectedTags.clear();
    _selectedTags.addAll(filters.tags);
  }

  @override
  void dispose() {
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filters = ref.watch(challengeFiltersProvider);
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final lightBlue =
        isDark ? NextSportzTheme.darkBlue : NextSportzTheme.lightBlue;

    return Container(
      decoration: BoxDecoration(
        color: lightBlue,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context, filters),
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildMatchTypeFilter(),
                  const SizedBox(height: 24),
                  _buildLocationFilter(),
                  const SizedBox(height: 24),
                  _buildWagerRangeFilter(),
                  const SizedBox(height: 24),
                  _buildTagsFilter(),
                  const SizedBox(height: 32),
                  _buildActionButtons(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, ChallengeFilters filters) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        children: [
          Icon(Icons.tune, color: accentColor, size: 20),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'Filter Challenges',
              style: TextStyle(
                fontFamily: 'Gilroy_Bold',
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          if (filters.hasFilters)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: accentColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Active',
                style: TextStyle(
                  color: accentColor,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Gilroy_Bold',
                ),
              ),
            ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white70),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildMatchTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Match Type',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'Gilroy_Medium',
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildMatchTypeChip(null, 'All Types'),
            ...['1v1', '2v2', '3v3', '5v5', '11v11'].map(
              (type) => _buildMatchTypeChip(type, type),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMatchTypeChip(String? type, String label) {
    final isSelected = _selectedMatchType == type;
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return InkWell(
      onTap: () => setState(() => _selectedMatchType = type),
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? accentColor : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? accentColor : Colors.white.withOpacity(0.2),
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.white70,
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
            fontFamily: isSelected ? 'Gilroy_Bold' : 'Gilroy_Medium',
          ),
        ),
      ),
    );
  }

  Widget _buildLocationFilter() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Location',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'Gilroy_Medium',
          ),
        ),
        const SizedBox(height: 12),
        TextField(
          controller: _locationController,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: 'Enter location (e.g., Dhumbarahi, Bhatbhateni)',
            hintStyle: const TextStyle(color: Colors.white54),
            prefixIcon: const Icon(Icons.place, color: Colors.white70),
            filled: true,
            fillColor: Colors.white.withOpacity(0.08),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.white.withOpacity(0.1)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: accentColor),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            'Dhumbarahi',
            'Bhatbhateni',
            'Satdobato',
            'New Road',
            'Lagankhel',
          ].map((location) => _buildLocationChip(location)).toList(),
        ),
      ],
    );
  }

  Widget _buildLocationChip(String location) {
    return InkWell(
      onTap: () => _locationController.text = location,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withOpacity(0.2)),
        ),
        child: Text(
          location,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 11,
            fontFamily: 'Gilroy_Medium',
          ),
        ),
      ),
    );
  }

  Widget _buildWagerRangeFilter() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Wager Range',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'NPR ${_wagerRange.start.toInt()} - ${_wagerRange.end.toInt()}',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 11,
                  fontFamily: 'Gilroy_Medium',
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: accentColor,
            inactiveTrackColor: Colors.white.withOpacity(0.2),
            thumbColor: accentColor,
            overlayColor: accentColor.withOpacity(0.2),
            valueIndicatorColor: accentColor,
            valueIndicatorTextStyle: const TextStyle(
              color: Colors.white,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
          child: RangeSlider(
            values: _wagerRange,
            min: 0,
            max: 5000,
            divisions: 50,
            labels: RangeLabels(
              'NPR ${_wagerRange.start.toInt()}',
              'NPR ${_wagerRange.end.toInt()}',
            ),
            onChanged: (values) => setState(() => _wagerRange = values),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildWagerPreset('Free', 0, 0),
            const SizedBox(width: 8),
            _buildWagerPreset('Low', 0, 500),
            const SizedBox(width: 8),
            _buildWagerPreset('Medium', 500, 1500),
            const SizedBox(width: 8),
            _buildWagerPreset('High', 1500, 5000),
          ],
        ),
      ],
    );
  }

  Widget _buildWagerPreset(String label, double min, double max) {
    return Expanded(
      child: InkWell(
        onTap: () => setState(() => _wagerRange = RangeValues(min, max)),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 11,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTagsFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tags',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'Gilroy_Medium',
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: availableTags.map((tag) => _buildTagChip(tag)).toList(),
        ),
      ],
    );
  }

  Widget _buildTagChip(String tag) {
    final isSelected = _selectedTags.contains(tag);
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return InkWell(
      onTap: () {
        setState(() {
          if (isSelected) {
            _selectedTags.remove(tag);
          } else {
            _selectedTags.add(tag);
          }
        });
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? accentColor.withOpacity(0.2)
              : Colors.white.withOpacity(0.08),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? accentColor : Colors.white.withOpacity(0.2),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isSelected) ...[
              Icon(Icons.check, size: 14, color: accentColor),
              const SizedBox(width: 4),
            ],
            Text(
              tag,
              style: TextStyle(
                color: isSelected ? accentColor : Colors.white70,
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _clearFilters,
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: BorderSide(color: Colors.white.withOpacity(0.3)),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Clear All',
              style: TextStyle(
                color: Colors.white70,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _applyFilters,
            style: ElevatedButton.styleFrom(
              backgroundColor: accentColor,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Apply Filters',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontFamily: 'Gilroy_Bold',
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _clearFilters() {
    setState(() {
      _locationController.clear();
      _wagerRange = const RangeValues(0, 5000);
      _selectedMatchType = null;
      _selectedTags.clear();
    });

    ref.read(challengeFiltersProvider.notifier).clearFilters();
    Navigator.of(context).pop();
    widget.onFiltersApplied?.call();
  }

  void _applyFilters() {
    final filtersNotifier = ref.read(challengeFiltersProvider.notifier);

    filtersNotifier.updateMatchType(_selectedMatchType);
    filtersNotifier.updateLocation(
      _locationController.text.isNotEmpty ? _locationController.text : null,
    );
    filtersNotifier.updateWagerRange(
      minWager: _wagerRange.start,
      maxWager: _wagerRange.end,
    );
    filtersNotifier.updateTags(_selectedTags);

    Navigator.of(context).pop();
    widget.onFiltersApplied?.call();
  }
}

/// Quick filter chips widget for the challenge list header
class QuickFiltersWidget extends ConsumerWidget {
  final VoidCallback? onFilterTap;

  const QuickFiltersWidget({super.key, this.onFilterTap});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filters = ref.watch(challengeFiltersProvider);
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          const SizedBox(width: 8),
          _buildFilterButton(
            context,
            ref,
            icon: Icons.tune,
            label: filters.hasFilters
                ? 'Filters (${_getActiveFilterCount(filters)})'
                : 'Filters',
            isActive: filters.hasFilters,
            onTap: onFilterTap,
          ),
          const SizedBox(width: 8),
          _buildQuickFilter(
            context,
            ref,
            label: 'Nearby',
            isActive: filters.location != null,
            onTap: () => _toggleLocationFilter(ref),
          ),
          const SizedBox(width: 8),
          _buildQuickFilter(
            context,
            ref,
            label: '5v5',
            isActive: filters.matchType == '5v5',
            onTap: () => _toggleMatchTypeFilter(ref, '5v5'),
          ),
          const SizedBox(width: 8),
          _buildQuickFilter(
            context,
            ref,
            label: 'High Stakes',
            isActive: filters.minWager != null && filters.minWager! >= 1000,
            onTap: () => _toggleHighStakesFilter(ref),
          ),
          const SizedBox(width: 8),
          _buildQuickFilter(
            context,
            ref,
            label: 'Tonight',
            isActive: filters.tags.contains('evening'),
            onTap: () => _toggleTagFilter(ref, 'evening'),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButton(
    BuildContext context,
    WidgetRef ref, {
    required IconData icon,
    required String label,
    required bool isActive,
    VoidCallback? onTap,
  }) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isActive ? accentColor : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isActive ? accentColor : Colors.white.withOpacity(0.2),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 14,
              color: isActive ? Colors.white : Colors.white70,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: isActive ? Colors.white : Colors.white70,
                fontSize: 12,
                fontWeight: isActive ? FontWeight.bold : FontWeight.w500,
                fontFamily: isActive ? 'Gilroy_Bold' : 'Gilroy_Medium',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickFilter(
    BuildContext context,
    WidgetRef ref, {
    required String label,
    required bool isActive,
    VoidCallback? onTap,
  }) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive
              ? accentColor.withOpacity(0.2)
              : Colors.white.withOpacity(0.08),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isActive ? accentColor : Colors.white.withOpacity(0.2),
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isActive ? accentColor : Colors.white70,
            fontSize: 12,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
            fontFamily: 'Gilroy_Medium',
          ),
        ),
      ),
    );
  }

  int _getActiveFilterCount(ChallengeFilters filters) {
    int count = 0;
    if (filters.matchType != null) count++;
    if (filters.location != null) count++;
    if (filters.minWager != null || filters.maxWager != null) count++;
    if (filters.tags.isNotEmpty) count++;
    return count;
  }

  void _toggleMatchTypeFilter(WidgetRef ref, String type) {
    final notifier = ref.read(challengeFiltersProvider.notifier);
    final current = ref.read(challengeFiltersProvider).matchType;
    notifier.updateMatchType(current == type ? null : type);
    // Force refresh of challenge list
    ref.read(challengeListStateProvider.notifier).refresh();
  }

  void _toggleLocationFilter(WidgetRef ref) {
    final notifier = ref.read(challengeFiltersProvider.notifier);
    final current = ref.read(challengeFiltersProvider).location;
    // This would ideally get user's current location
    notifier.updateLocation(current != null ? null : 'Nearby');
    // Force refresh of challenge list
    ref.read(challengeListStateProvider.notifier).refresh();
  }

  void _toggleHighStakesFilter(WidgetRef ref) {
    final notifier = ref.read(challengeFiltersProvider.notifier);
    final current = ref.read(challengeFiltersProvider);
    final isCurrentlyActive =
        current.minWager != null && current.minWager! >= 1000;

    if (isCurrentlyActive) {
      // Deselect: clear the wager filter completely
      notifier.updateWagerRange(minWager: null, maxWager: null);
    } else {
      // Select: set minimum wager to 1000
      notifier.updateWagerRange(minWager: 1000.0, maxWager: current.maxWager);
    }
    // Force refresh of challenge list
    ref.read(challengeListStateProvider.notifier).refresh();
  }

  void _toggleTagFilter(WidgetRef ref, String tag) {
    final notifier = ref.read(challengeFiltersProvider.notifier);
    final current = ref.read(challengeFiltersProvider).tags;
    final newTags = List<String>.from(current);
    if (newTags.contains(tag)) {
      newTags.remove(tag);
    } else {
      newTags.add(tag);
    }
    notifier.updateTags(newTags);
    // Force refresh of challenge list
    ref.read(challengeListStateProvider.notifier).refresh();
  }
}
