import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/networking/api_client.dart';
import 'data/datasources/teams_datasource.dart';
import 'data/datasources/teams_remote_datasource.dart';
import 'data/repositories/teams_repository_impl.dart';
import 'data/repositories/team_repositories_provider.dart';
import 'domain/entities/team.dart';
import 'domain/repositories/teams_repository.dart';
import 'domain/usecases/teams_usecases.dart';
import 'domain/usecases/team_members_usecases.dart' as members_usecases;
import 'domain/usecases/team_invitations_usecases.dart' as invitations_usecases;
import 'domain/usecases/invite_player_usecase.dart' as invite_usecase;
import 'domain/usecases/join_team_usecase.dart';
import '../../core/use_case.dart';
import '../auth/data/repositories/auth_repository_provider.dart';

// Teams data source provider
final teamsDataSourceProvider = Provider<TeamsDatasource>((ref) {
  // For development/testing, use remote data source
  // In production, this could be switched to mock or other implementations
  final apiClient = ref.read(apiClientProvider);
  return TeamsRemoteDataSource(apiClient);
});

// Repository provider
final teamsRepositoryProvider = Provider<TeamsRepository>((ref) {
  final dataSource = ref.read(teamsDataSourceProvider);
  return TeamsRepositoryImpl(dataSource);
});

// Use cases providers
final getMyTeamsUseCaseProvider = Provider<GetMyTeamsUseCase>((ref) {
  return GetMyTeamsUseCase(ref.read(teamsRepositoryProvider));
});

final getTeamByIdUseCaseProvider = Provider<GetTeamByIdUseCase>((ref) {
  return GetTeamByIdUseCase(ref.read(teamsRepositoryProvider));
});

final createTeamUseCaseProvider = Provider<CreateTeamUseCase>((ref) {
  return CreateTeamUseCase(ref.read(teamsRepositoryProvider));
});

final updateTeamUseCaseProvider = Provider<UpdateTeamUseCase>((ref) {
  return UpdateTeamUseCase(
    ref.read(teamsRepositoryProvider),
    ref.read(authRepositoryProvider),
  );
});

final deleteTeamUseCaseProvider = Provider<DeleteTeamUseCase>((ref) {
  return DeleteTeamUseCase(ref.read(teamsRepositoryProvider));
});

final joinTeamUseCaseProvider = Provider<JoinTeamUseCase>((ref) {
  return JoinTeamUseCase(ref.read(teamsRepositoryProvider));
});

// Team Members Use Cases
final getTeamMembersUseCaseProvider =
    Provider<members_usecases.GetTeamMembersUseCase>((ref) {
      return members_usecases.GetTeamMembersUseCase(
        ref.read(teamMembersRepositoryProvider),
      );
    });

final getTeamMemberUseCaseProvider =
    Provider<members_usecases.GetTeamMemberUseCase>((ref) {
      return members_usecases.GetTeamMemberUseCase(
        ref.read(teamMembersRepositoryProvider),
      );
    });

final removeTeamMemberUseCaseProvider =
    Provider<members_usecases.RemoveTeamMemberUseCase>((ref) {
      return members_usecases.RemoveTeamMemberUseCase(
        ref.read(teamMembersRepositoryProvider),
      );
    });

final updateTeamMemberRoleUseCaseProvider =
    Provider<members_usecases.UpdateTeamMemberRoleUseCase>((ref) {
      return members_usecases.UpdateTeamMemberRoleUseCase(
        ref.read(teamMembersRepositoryProvider),
      );
    });

final getTeamMembersCountUseCaseProvider =
    Provider<members_usecases.GetTeamMembersCountUseCase>((ref) {
      return members_usecases.GetTeamMembersCountUseCase(
        ref.read(teamMembersRepositoryProvider),
      );
    });

// Team Invitations Use Cases
final getPendingInvitationsUseCaseProvider =
    Provider<invitations_usecases.GetPendingInvitationsUseCase>((ref) {
      return invitations_usecases.GetPendingInvitationsUseCase(
        ref.read(teamInvitationsRepositoryProvider),
      );
    });

final getTeamInvitationsUseCaseProvider =
    Provider<invitations_usecases.GetTeamInvitationsUseCase>((ref) {
      return invitations_usecases.GetTeamInvitationsUseCase(
        ref.read(teamInvitationsRepositoryProvider),
      );
    });

final getInvitationUseCaseProvider =
    Provider<invitations_usecases.GetInvitationUseCase>((ref) {
      return invitations_usecases.GetInvitationUseCase(
        ref.read(teamInvitationsRepositoryProvider),
      );
    });

final invitePlayerUseCaseProvider =
    Provider<invite_usecase.InvitePlayerUseCase>((ref) {
      return invite_usecase.InvitePlayerUseCase(
        ref.read(teamInvitationsRepositoryProvider),
      );
    });

final acceptInvitationUseCaseProvider =
    Provider<invitations_usecases.AcceptInvitationUseCase>((ref) {
      return invitations_usecases.AcceptInvitationUseCase(
        ref.read(teamInvitationsRepositoryProvider),
      );
    });

final declineInvitationUseCaseProvider =
    Provider<invitations_usecases.DeclineInvitationUseCase>((ref) {
      return invitations_usecases.DeclineInvitationUseCase(
        ref.read(teamInvitationsRepositoryProvider),
      );
    });

final getTeamInvitationsCountUseCaseProvider =
    Provider<invitations_usecases.GetTeamInvitationsCountUseCase>((ref) {
      return invitations_usecases.GetTeamInvitationsCountUseCase(
        ref.read(teamInvitationsRepositoryProvider),
      );
    });

// New invitation use cases based on swagger endpoints
final inviteTeamMemberUseCaseProvider =
    Provider<invitations_usecases.InviteTeamMemberUseCase>((ref) {
      return invitations_usecases.InviteTeamMemberUseCase(
        ref.read(teamInvitationsRepositoryProvider),
      );
    });

final deleteTeamInviteUseCaseProvider =
    Provider<invitations_usecases.DeleteTeamInviteUseCase>((ref) {
      return invitations_usecases.DeleteTeamInviteUseCase(
        ref.read(teamInvitationsRepositoryProvider),
      );
    });

final acceptTeamInviteUseCaseProvider =
    Provider<invitations_usecases.AcceptTeamInviteUseCase>((ref) {
      return invitations_usecases.AcceptTeamInviteUseCase(
        ref.read(teamInvitationsRepositoryProvider),
      );
    });

// State providers
final myTeamsProvider = FutureProvider.autoDispose<List<TeamSearchItem>>((
  ref,
) async {
  final useCase = ref.read(getMyTeamsUseCaseProvider);

  final result = await useCase();
  return result.fold(
    (error) => throw Exception(error.message),
    (teams) => teams,
  );
});

final pendingInvitationsProvider =
    FutureProvider.autoDispose<List<TeamInvitation>>((ref) async {
      final useCase = ref.read(getPendingInvitationsUseCaseProvider);
      final result = await useCase(NoParams());
      return result.fold(
        (error) => throw Exception(error.message),
        (invitations) => invitations,
      );
    });

final teamByIdProvider = FutureProvider.family<Team, String>((
  ref,
  teamId,
) async {
  final useCase = ref.read(getTeamByIdUseCaseProvider);
  final result = await useCase(teamId);
  return result.fold((error) => throw Exception(error.message), (team) => team);
});

// Mock team leaderboard provider for now
final teamLeaderboardProvider = FutureProvider.autoDispose<List<Team>>((
  ref,
) async {
  // Mock data for development - replace with actual API call later
  await Future.delayed(
    const Duration(milliseconds: 500),
  ); // Simulate network delay

  return [
    Team(
      id: '1',
      teamCode: 'TBF001',
      name: 'Thunder Bolts FC',
      description: 'Elite football team',
      logoUrl: '',
      isActive: true,
      members: [],
      created: DateTime.now().subtract(const Duration(days: 30)),
      stats: TeamStats(
        wins: 12,
        losses: 2,
        draws: 1,
        scoredGoals: 45,
        concededGoals: 12,
      ),
    ),
    Team(
      id: '2',
      teamCode: 'LS002',
      name: 'Lightning Strikers',
      description: 'Fast-paced attacking team',
      logoUrl: '',
      isActive: true,
      members: [],
      created: DateTime.now().subtract(const Duration(days: 25)),
      stats: TeamStats(
        wins: 10,
        losses: 3,
        draws: 1,
        scoredGoals: 38,
        concededGoals: 18,
      ),
    ),
    Team(
      id: '3',
      teamCode: 'PU003',
      name: 'Phoenix United',
      description: 'Rising from the ashes',
      logoUrl: '',
      isActive: true,
      members: [],
      created: DateTime.now().subtract(const Duration(days: 20)),
      stats: TeamStats(
        wins: 8,
        losses: 3,
        draws: 1,
        scoredGoals: 28,
        concededGoals: 15,
      ),
    ),
    Team(
      id: '4',
      teamCode: 'DW004',
      name: 'Dynamo Warriors',
      description: 'Powerful and dynamic',
      logoUrl: '',
      isActive: true,
      members: [],
      created: DateTime.now().subtract(const Duration(days: 15)),
      stats: TeamStats(
        wins: 6,
        losses: 3,
        draws: 1,
        scoredGoals: 22,
        concededGoals: 14,
      ),
    ),
    Team(
      id: '5',
      teamCode: 'VFC005',
      name: 'Velocity FC',
      description: 'Speed and precision',
      logoUrl: '',
      isActive: true,
      members: [],
      created: DateTime.now().subtract(const Duration(days: 10)),
      stats: TeamStats(
        wins: 4,
        losses: 3,
        draws: 1,
        scoredGoals: 18,
        concededGoals: 16,
      ),
    ),
  ];
});
