import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../repositories/teams_repository.dart';

/// Use case for joining a team by team code
class JoinTeamUseCase {
  final TeamsRepository _repository;

  JoinTeamUseCase(this._repository);

  Future<Either<AppError, void>> call(JoinTeamParams params) async {
    return await _repository.joinTeamByCode(
      teamCode: params.teamCode,
      playerId: params.playerId,
    );
  }
}

/// Parameters for joining a team
class JoinTeamParams {
  final String teamCode;
  final String playerId;

  const JoinTeamParams({
    required this.teamCode,
    required this.playerId,
  });
}
