import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/use_case.dart';
import '../entities/team.dart';
import '../repositories/team_members_repository.dart';

/// Use case for getting team members
class GetTeamMembersUseCase implements UseCase<List<TeamMember>, String> {
  final TeamMembersRepository repository;

  GetTeamMembersUseCase(this.repository);

  @override
  Future<Either<AppError, List<TeamMember>>> call(String teamId) async {
    return await repository.getTeamMembers(teamId);
  }
}

/// Use case for getting a specific team member
class GetTeamMemberUseCase implements UseCase<TeamMember, GetTeamMemberParams> {
  final TeamMembersRepository repository;

  GetTeamMemberUseCase(this.repository);

  @override
  Future<Either<AppError, TeamMember>> call(GetTeamMemberParams params) async {
    return await repository.getTeamMember(params.teamId, params.memberId);
  }
}

/// Use case for removing a team member
class RemoveTeamMemberUseCase implements UseCase<void, RemoveTeamMemberParams> {
  final TeamMembersRepository repository;

  RemoveTeamMemberUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(RemoveTeamMemberParams params) async {
    return await repository.removeMember(
      teamId: params.teamId,
      memberId: params.memberId,
    );
  }
}

/// Use case for updating team member role
class UpdateTeamMemberRoleUseCase implements UseCase<void, UpdateTeamMemberRoleParams> {
  final TeamMembersRepository repository;

  UpdateTeamMemberRoleUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(UpdateTeamMemberRoleParams params) async {
    return await repository.updateMemberRole(
      teamId: params.teamId,
      memberId: params.memberId,
      role: params.role,
    );
  }
}

/// Use case for getting team members count
class GetTeamMembersCountUseCase implements UseCase<int, String> {
  final TeamMembersRepository repository;

  GetTeamMembersCountUseCase(this.repository);

  @override
  Future<Either<AppError, int>> call(String teamId) async {
    return await repository.getTeamMembersCount(teamId);
  }
}

// Parameter classes
class GetTeamMemberParams {
  final String teamId;
  final String memberId;

  const GetTeamMemberParams({
    required this.teamId,
    required this.memberId,
  });
}

class RemoveTeamMemberParams {
  final String teamId;
  final String memberId;

  const RemoveTeamMemberParams({
    required this.teamId,
    required this.memberId,
  });
}

class UpdateTeamMemberRoleParams {
  final String teamId;
  final String memberId;
  final String role;

  const UpdateTeamMemberRoleParams({
    required this.teamId,
    required this.memberId,
    required this.role,
  });
}
