import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/use_case.dart';
import '../repositories/team_invitations_repository.dart';

/// Parameters for inviting a player to a team
class InvitePlayerParams {
  final String teamId;
  final String playerId;
  final String role;

  const InvitePlayerParams({
    required this.teamId,
    required this.playerId,
    this.role = 'Player',
  });

  InvitePlayerParams copyWith({
    String? teamId,
    String? playerId,
    String? role,
  }) {
    return InvitePlayerParams(
      teamId: teamId ?? this.teamId,
      playerId: playerId ?? this.playerId,
      role: role ?? this.role,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InvitePlayerParams &&
        other.teamId == teamId &&
        other.playerId == playerId &&
        other.role == role;
  }

  @override
  int get hashCode => Object.hash(teamId, playerId, role);

  @override
  String toString() {
    return 'InvitePlayerParams(teamId: $teamId, playerId: $playerId, role: $role)';
  }
}

/// Use case for inviting a player to a team
class InvitePlayerUseCase implements UseCase<void, InvitePlayerParams> {
  final TeamInvitationsRepository _teamInvitationsRepository;

  InvitePlayerUseCase(this._teamInvitationsRepository);

  @override
  Future<Either<AppError, void>> call(InvitePlayerParams params) async {
    return await _teamInvitationsRepository.invitePlayer(
      teamId: params.teamId,
      playerId: params.playerId,
      role: params.role,
    );
  }
}
