import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/use_case.dart';
import '../entities/team.dart';
import '../entities/team_role.dart';
import '../repositories/team_invitations_repository.dart';

/// Use case for getting pending invitations
class GetPendingInvitationsUseCase
    implements UseCase<List<TeamInvitation>, NoParams> {
  final TeamInvitationsRepository repository;

  GetPendingInvitationsUseCase(this.repository);

  @override
  Future<Either<AppError, List<TeamInvitation>>> call(NoParams params) async {
    return await repository.getPendingInvitations();
  }
}

/// Use case for getting team invitations
class GetTeamInvitationsUseCase
    implements UseCase<List<TeamInvitation>, String> {
  final TeamInvitationsRepository repository;

  GetTeamInvitationsUseCase(this.repository);

  @override
  Future<Either<AppError, List<TeamInvitation>>> call(String teamId) async {
    return await repository.getTeamInvitations(teamId);
  }
}

/// Use case for getting a specific invitation
class GetInvitationUseCase implements UseCase<TeamInvitation, String> {
  final TeamInvitationsRepository repository;

  GetInvitationUseCase(this.repository);

  @override
  Future<Either<AppError, TeamInvitation>> call(String invitationId) async {
    return await repository.getInvitation(invitationId);
  }
}

/// Use case for inviting a player to a team
class InvitePlayerUseCase implements UseCase<void, InvitePlayerParams> {
  final TeamInvitationsRepository repository;

  InvitePlayerUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(InvitePlayerParams params) async {
    return await repository.invitePlayer(
      teamId: params.teamId,
      playerId: params.playerId,
      message: params.message,
    );
  }
}

/// Use case for accepting an invitation
class AcceptInvitationUseCase implements UseCase<void, AcceptInvitationParams> {
  final TeamInvitationsRepository repository;

  AcceptInvitationUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(AcceptInvitationParams params) async {
    return await repository.acceptInvitation(
      teamId: params.teamId,
      invitationId: params.invitationId,
    );
  }
}

/// Use case for declining an invitation
class DeclineInvitationUseCase
    implements UseCase<void, DeclineInvitationParams> {
  final TeamInvitationsRepository repository;

  DeclineInvitationUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(DeclineInvitationParams params) async {
    return await repository.declineInvitation(
      teamId: params.teamId,
      invitationId: params.invitationId,
    );
  }
}

/// Use case for getting team invitations count
class GetTeamInvitationsCountUseCase implements UseCase<int, String> {
  final TeamInvitationsRepository repository;

  GetTeamInvitationsCountUseCase(this.repository);

  @override
  Future<Either<AppError, int>> call(String teamId) async {
    return await repository.getTeamInvitationsCount(teamId);
  }
}

/// Use case for inviting team members using the new endpoint
class InviteTeamMemberUseCase implements UseCase<void, InviteTeamMemberParams> {
  final TeamInvitationsRepository repository;

  InviteTeamMemberUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(InviteTeamMemberParams params) async {
    return await repository.inviteTeamMember(
      teamId: params.teamId,
      playerId: params.playerId,
      role: params.role,
    );
  }
}

/// Use case for deleting team invites
class DeleteTeamInviteUseCase implements UseCase<void, DeleteTeamInviteParams> {
  final TeamInvitationsRepository repository;

  DeleteTeamInviteUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(DeleteTeamInviteParams params) async {
    return await repository.deleteTeamInvite(
      teamId: params.teamId,
      memberId: params.memberId,
    );
  }
}

/// Use case for accepting team invites
class AcceptTeamInviteUseCase implements UseCase<void, AcceptTeamInviteParams> {
  final TeamInvitationsRepository repository;

  AcceptTeamInviteUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(AcceptTeamInviteParams params) async {
    return await repository.acceptTeamInvite(
      teamId: params.teamId,
      memberId: params.memberId,
    );
  }
}

// Parameter classes
class InvitePlayerParams {
  final String teamId;
  final String playerId;
  final String? message;

  const InvitePlayerParams({
    required this.teamId,
    required this.playerId,
    this.message,
  });
}

class InviteTeamMemberParams {
  final String teamId;
  final String playerId;
  final TeamRole role;

  const InviteTeamMemberParams({
    required this.teamId,
    required this.playerId,
    required this.role,
  });
}

class DeleteTeamInviteParams {
  final String teamId;
  final String memberId;

  const DeleteTeamInviteParams({required this.teamId, required this.memberId});
}

class AcceptTeamInviteParams {
  final String teamId;
  final String memberId;

  const AcceptTeamInviteParams({required this.teamId, required this.memberId});
}

class AcceptInvitationParams {
  final String teamId;
  final String invitationId;

  const AcceptInvitationParams({
    required this.teamId,
    required this.invitationId,
  });
}

class DeclineInvitationParams {
  final String teamId;
  final String invitationId;

  const DeclineInvitationParams({
    required this.teamId,
    required this.invitationId,
  });
}
