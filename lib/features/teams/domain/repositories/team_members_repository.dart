import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../entities/team.dart';

/// Repository interface for team members operations
abstract interface class TeamMembersRepository {
  /// Get all members of a specific team
  Future<Either<AppError, List<TeamMember>>> getTeamMembers(String teamId);

  /// Get a specific team member by ID
  Future<Either<AppError, TeamMember>> getTeamMember(String teamId, String memberId);

  /// Remove a member from a team
  Future<Either<AppError, void>> removeMember({
    required String teamId,
    required String memberId,
  });

  /// Update a member's role in a team
  Future<Either<AppError, void>> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  });

  /// Get members count for a team
  Future<Either<AppError, int>> getTeamMembersCount(String teamId);
}
