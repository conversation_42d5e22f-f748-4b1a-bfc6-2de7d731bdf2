import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/models/paginated_response.dart';
import '../entities/team.dart';

abstract interface class TeamsRepository {
  Future<Either<AppError, List<TeamSearchItem>>> getMyTeams();
  Future<Either<AppError, Team>> getTeamById(String teamId);
  Future<Either<AppError, Team>> createTeam({
    String? id,
    required String name,
    required String description,
    String? logo,
    String? slogan,
  });
  Future<Either<AppError, void>> updateTeam({
    required String teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  });
  Future<Either<AppError, void>> deleteTeam(String teamId);
  Future<Either<AppError, void>> invitePlayer({
    required String teamId,
    required String playerId,
    required String role,
  });
  Future<Either<AppError, void>> acceptInvitation(String invitationId);
  Future<Either<AppError, void>> declineInvitation(String invitationId);
  Future<Either<AppError, void>> removeMember({
    required String teamId,
    required String memberId,
  });
  Future<Either<AppError, void>> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  });
  Future<Either<AppError, List<TeamInvitation>>> getPendingInvitations();

  // New methods for leaderboard and search
  Future<Either<AppError, List<Team>>> getTeamsLeaderboard({int? limit});
  Future<Either<AppError, List<Team>>> searchTeams({
    required String query,
    int? limit,
    int? offset,
  });
  Future<Either<AppError, List<Team>>> getAllTeams({int? limit, int? offset});

  // Paginated methods
  Future<Either<AppError, PaginatedResponse<TeamSearchItem>>>
  searchTeamsPaginated({String? query, int? page, int? pageSize});
  Future<Either<AppError, PaginatedResponse<TeamSearchItem>>>
  getAllTeamsPaginated({int? page, int? pageSize});

  // Join team by code
  Future<Either<AppError, void>> joinTeamByCode({
    required String teamCode,
    required String playerId,
  });
}
