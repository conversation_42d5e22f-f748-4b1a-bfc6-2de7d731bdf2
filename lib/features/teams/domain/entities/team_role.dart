/// Team role enum based on swagger.json TeamRole schema
enum TeamRole {
  player('Player'),
  captain('Captain');

  const TeamRole(this.value);

  final String value;

  /// Create TeamRole from string value
  static TeamRole fromString(String value) {
    switch (value) {
      case 'Player':
        return TeamRole.player;
      case 'Captain':
        return TeamRole.captain;
      default:
        throw ArgumentError('Invalid team role: $value');
    }
  }

  /// Convert TeamRole to string value
  @override
  String toString() => value;

  /// Get display names for all roles
  static List<String> get displayNames =>
      TeamRole.values.map((role) => role.value).toList();
}
