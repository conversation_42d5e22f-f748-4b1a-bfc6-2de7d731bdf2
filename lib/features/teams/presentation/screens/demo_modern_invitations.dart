import 'package:flutter/material.dart';
import '../../domain/entities/team.dart';
import '../widgets/team_invitation_card.dart';

class DemoModernInvitationsScreen extends StatelessWidget {
  const DemoModernInvitationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Demo data with only active invitations
    final activeInvitations = [
      TeamInvitation(
        id: '1',
        invitedTeamId: 'team1',
        invitedTeamName: 'Thunder Bolts',
        invitedTeamStats: const TeamStats(
          wins: 12,
          losses: 3,
          draws: 2,
          scoredGoals: 35,
          concededGoals: 18,
        ),
        teamLogoUrl: '',
        status: 'Pending',
        isActive: true,
        invitedAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      TeamInvitation(
        id: '2',
        invitedTeamId: 'team2',
        invitedTeamName: 'Lightning Strikers',
        invitedTeamStats: const TeamStats(
          wins: 8,
          losses: 5,
          draws: 4,
          scoredGoals: 28,
          concededGoals: 22,
        ),
        teamLogoUrl: '',
        status: 'Pending',
        isActive: true,
        invitedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      TeamInvitation(
        id: '3',
        invitedTeamId: 'team3',
        invitedTeamName: 'Fire Dragons',
        invitedTeamStats: const TeamStats(
          wins: 15,
          losses: 1,
          draws: 0,
          scoredGoals: 42,
          concededGoals: 8,
        ),
        teamLogoUrl: '',
        status: 'Pending',
        isActive: true,
        invitedAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
    ];

    return Scaffold(
      backgroundColor: const Color(0xFF0F0F23),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0F0F23),
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(context),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  itemCount: activeInvitations.length,
                  itemBuilder: (context, index) {
                    final invitation = activeInvitations[index];
                    return TeamInvitationCard(
                      invitation: invitation,
                      onAccept: () => _showSnackBar(
                        context,
                        'Accepted invitation from ${invitation.invitedTeamName}! 🎉',
                        Colors.green,
                      ),
                      onDecline: () => _showSnackBar(
                        context,
                        'Declined invitation from ${invitation.invitedTeamName}',
                        Colors.orange,
                      ),
                      showActions: true,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: const Icon(
                    Icons.arrow_back_ios_new,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Team Invitations',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontFamily: 'Gilroy_Bold',
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Sleek, modern & sexy UI ✨',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white.withOpacity(0.7),
                        fontFamily: 'Gilroy_Medium',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              _buildStatCard('Active', 3, const Color(0xFFFFA726)),
              const SizedBox(width: 12),
              _buildStatCard('Pending', 3, const Color(0xFF42A5F5)),
              const SizedBox(width: 12),
              _buildStatCard('Total', 3, const Color(0xFF00D4AA)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, int count, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Text(
              count.toString(),
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
                fontFamily: 'Gilroy_Bold',
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.white.withOpacity(0.8),
                fontFamily: 'Gilroy_Medium',
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
