import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../../core/widgets/paginated_list_view.dart';
import '../../../../core/models/paginated_response.dart';
import '../../../player/domain/entities/player_search_item.dart';
import '../../../player/domain/usecases/search_players_usecase.dart';
import '../../../player/player_providers.dart';
import '../../domain/usecases/invite_player_usecase.dart' as invite_usecase;
import '../../teams_providers.dart';

class InvitePlayerScreen extends ConsumerStatefulWidget {
  final String teamId;

  const InvitePlayerScreen({Key? key, required this.teamId}) : super(key: key);

  @override
  ConsumerState<InvitePlayerScreen> createState() => _InvitePlayerScreenState();
}

class _InvitePlayerScreenState extends ConsumerState<InvitePlayerScreen> {
  final _searchController = TextEditingController();
  Timer? _debounceTimer;
  String _searchQuery = '';
  final Set<String> _invitedPlayerIds = {};
  final Set<String> _loadingInvites = {};

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);
    final lightGreyColor = NextSportzTheme.getLightGreyColor(isDark);

    return Scaffold(
      backgroundColor: primaryColor,
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Invite Player',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 18,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: secondaryColor.withOpacity(0.3),
              border: Border(
                bottom: BorderSide(
                  color: lightGreyColor.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: accentColor.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.person_search,
                        color: accentColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Search Players',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        color: Colors.white,
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  'Search by player ID, email, or phone number',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: greyColor,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 16),
                // Search Bar
                _buildSearchBar(accentColor, secondaryColor, lightGreyColor),
              ],
            ),
          ),

          // Search Results
          Expanded(
            child: _buildSearchResults(
              accentColor,
              secondaryColor,
              greyColor,
              lightGreyColor,
            ),
          ),
        ],
      ),
    );
  }

  // Search Bar Widget
  Widget _buildSearchBar(
    Color accentColor,
    Color secondaryColor,
    Color lightGreyColor,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: secondaryColor.withOpacity(0.5),
        border: Border.all(color: lightGreyColor.withOpacity(0.2), width: 1),
      ),
      child: TextField(
        controller: _searchController,
        onChanged: _onSearchChanged,
        style: const TextStyle(
          fontFamily: 'Gilroy_Medium',
          color: Colors.white,
          fontSize: 14,
        ),
        decoration: InputDecoration(
          hintText: 'Search by ID, email, or phone...',
          hintStyle: TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: Colors.white.withOpacity(0.5),
            fontSize: 14,
          ),
          prefixIcon: Icon(Icons.search, color: accentColor, size: 20),
          suffixIcon:
              _searchController.text.isNotEmpty
                  ? IconButton(
                    icon: Icon(
                      Icons.clear,
                      color: Colors.white.withOpacity(0.7),
                      size: 18,
                    ),
                    onPressed: () {
                      _searchController.clear();
                      _onSearchChanged('');
                    },
                  )
                  : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  // Search functionality
  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      setState(() {
        _searchQuery = query.trim();
      });
    });
  }

  // Search Results Widget using PaginatedListView
  Widget _buildSearchResults(
    Color accentColor,
    Color secondaryColor,
    Color greyColor,
    Color lightGreyColor,
  ) {
    if (_searchQuery.isEmpty) {
      return _buildEmptyState(accentColor, greyColor);
    }

    return PaginatedListView<PlayerSearchItem>(
      onLoadPage: (page) => _loadPlayers(page),
      itemBuilder:
          (context, player, index) => _buildPlayerCard(
            player,
            accentColor,
            secondaryColor,
            lightGreyColor,
          ),
      emptyWidget: _buildNoResultsState(greyColor),
      padding: const EdgeInsets.all(16),
    );
  }

  // Load players using the search use case
  Future<PaginatedResponse<PlayerSearchItem>> _loadPlayers(int page) async {
    final useCase = ref.read(searchPlayersUseCaseProvider);
    final params = SearchPlayersParams(
      query: _searchQuery,
      page: page,
      pageSize: 20,
    );

    final result = await useCase(params);
    return result.fold(
      (error) => throw Exception(error.message),
      (response) => response,
    );
  }

  // Player Card Widget
  Widget _buildPlayerCard(
    PlayerSearchItem player,
    Color accentColor,
    Color secondaryColor,
    Color lightGreyColor,
  ) {
    final isInvited = _invitedPlayerIds.contains(player.id);
    final isLoading = _loadingInvites.contains(player.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: secondaryColor.withOpacity(0.3),
        border: Border.all(color: lightGreyColor.withOpacity(0.2), width: 0.5),
      ),
      child: Row(
        children: [
          // Player Avatar
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: accentColor.withOpacity(0.2),
            ),
            child:
                player.photoUrl != null && player.photoUrl!.isNotEmpty
                    ? ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: CachedNetworkImage(
                        imageUrl: player.photoUrl!,
                        fit: BoxFit.cover,
                        width: 40,
                        height: 40,
                        placeholder:
                            (context, url) => Container(
                              decoration: BoxDecoration(
                                color: lightGreyColor.withOpacity(0.3),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Icon(
                                Icons.person,
                                color: accentColor,
                                size: 20,
                              ),
                            ),
                        errorWidget:
                            (context, url, error) => Container(
                              decoration: BoxDecoration(
                                color: lightGreyColor.withOpacity(0.3),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Icon(
                                Icons.person,
                                color: accentColor,
                                size: 20,
                              ),
                            ),
                      ),
                    )
                    : Icon(Icons.person, color: accentColor, size: 20),
          ),
          const SizedBox(width: 12),
          // Player Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  player.fullName,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  player.email,
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 12,
                  ),
                ),
                if (player.phone.isNotEmpty) ...[
                  const SizedBox(height: 1),
                  Text(
                    player.phone,
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.white.withOpacity(0.5),
                      fontSize: 11,
                    ),
                  ),
                ],
              ],
            ),
          ),
          // Invite Button
          _buildInviteButton(player, accentColor, isInvited, isLoading),
        ],
      ),
    );
  }

  // Invite Button Widget
  Widget _buildInviteButton(
    PlayerSearchItem player,
    Color accentColor,
    bool isInvited,
    bool isLoading,
  ) {
    if (isInvited) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: NextSportzTheme.lightAccent.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: NextSportzTheme.lightAccent, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.check, color: NextSportzTheme.lightAccent, size: 14),
            const SizedBox(width: 4),
            Text(
              'Invited',
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: NextSportzTheme.lightAccent,
                fontSize: 12,
              ),
            ),
          ],
        ),
      );
    }

    return ElevatedButton(
      onPressed: isLoading ? null : () => _invitePlayer(player),
      style: ElevatedButton.styleFrom(
        backgroundColor: accentColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: const Size(0, 32),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child:
          isLoading
              ? SizedBox(
                width: 14,
                height: 14,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
              : const Text(
                'Invite',
                style: TextStyle(fontFamily: 'Gilroy_Medium', fontSize: 12),
              ),
    );
  }

  // Invite Player using use case
  Future<void> _invitePlayer(PlayerSearchItem player) async {
    setState(() {
      _loadingInvites.add(player.id);
    });

    try {
      final useCase = ref.read(invitePlayerUseCaseProvider);
      final params = invite_usecase.InvitePlayerParams(
        teamId: widget.teamId,
        playerId: player.id,
        role: 'Player',
      );

      final result = await useCase(params);
      result.fold((error) => throw Exception(error.message), (_) {
        setState(() {
          _invitedPlayerIds.add(player.id);
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Invitation sent to ${player.fullName}!'),
              backgroundColor: NextSportzTheme.lightAccent,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to invite ${player.fullName}: ${e.toString()}',
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      setState(() {
        _loadingInvites.remove(player.id);
      });
    }
  }

  // Empty State
  Widget _buildEmptyState(Color accentColor, Color greyColor) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: accentColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(Icons.person_search, color: accentColor, size: 48),
          ),
          const SizedBox(height: 16),
          const Text(
            'Search for Players',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Enter a player ID, email, or phone number to find players to invite',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: greyColor,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // No Results State
  Widget _buildNoResultsState(Color greyColor) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, color: greyColor, size: 48),
          const SizedBox(height: 16),
          const Text(
            'No Players Found',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'No players match your search criteria. Try a different search term.',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: greyColor,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
