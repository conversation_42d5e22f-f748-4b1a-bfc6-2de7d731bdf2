import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:share_plus/share_plus.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../../core/image_upload/image_upload_providers.dart';
import '../../../../utils/color.dart';
import '../../teams_providers.dart';
import '../../domain/entities/team.dart';
import '../../domain/entities/team_role.dart';
import '../../domain/usecases/team_invitations_usecases.dart'
    as invitations_usecases;
import 'invite_player_screen.dart';

class TeamDetailsScreen extends ConsumerStatefulWidget {
  final String teamId;
  final bool isPublicView;

  const TeamDetailsScreen({
    super.key,
    required this.teamId,
    this.isPublicView = false,
  });

  @override
  ConsumerState<TeamDetailsScreen> createState() => _TeamDetailsScreenState();
}

class _TeamDetailsScreenState extends ConsumerState<TeamDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Logo update state
  File? _selectedImage;
  Uint8List? _selectedImageBytes;
  bool _isUploadingLogo = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _pickAndUploadTeamLogo(Team team) async {
    if (widget.isPublicView) return; // Don't allow editing in public view

    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 512,
      maxHeight: 512,
      imageQuality: 80,
    );

    if (image != null) {
      setState(() {
        _isUploadingLogo = true;
      });

      try {
        String? logoUrl;

        if (kIsWeb) {
          // For web, read as bytes
          final bytes = await image.readAsBytes();
          setState(() {
            _selectedImageBytes = bytes;
            _selectedImage = null;
          });

          // Upload image using the image upload helper
          final imageUploadHelper = ref.read(imageUploadHelperProvider);
          final uploadResult = await imageUploadHelper.uploadTeamLogo(
            bytes: bytes,
            teamId: team.id,
            fileName: image.name,
          );

          uploadResult.fold(
            (error) => throw Exception(error),
            (url) => logoUrl = url,
          );
        } else {
          // For mobile, use File
          final file = File(image.path);
          setState(() {
            _selectedImage = file;
            _selectedImageBytes = null;
          });

          // Upload image using the image upload helper
          final imageUploadHelper = ref.read(imageUploadHelperProvider);
          final uploadResult = await imageUploadHelper.uploadTeamLogo(
            file: file,
            teamId: team.id,
            fileName: image.name,
          );

          uploadResult.fold(
            (error) => throw Exception(error),
            (url) => logoUrl = url,
          );
        }

        // Update team with new logo URL
        if (logoUrl != null) {
          final updateTeamUseCase = ref.read(updateTeamUseCaseProvider);
          final updateResult = await updateTeamUseCase(
            teamId: team.id,
            logo: logoUrl,
          );

          updateResult.fold((error) => throw Exception(error.message), (_) {
            // Refresh team data to show updated logo
            ref.invalidate(teamByIdProvider(team.id));

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text(
                    'Team logo updated successfully!',
                    style: TextStyle(fontFamily: 'Gilroy_Medium'),
                  ),
                  backgroundColor: NextSportzTheme.getAccentColor(
                    ref.watch(theme_providers.isDarkModeProvider),
                  ),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }
          });
        }
      } catch (error) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to update team logo: ${error.toString()}',
                style: const TextStyle(fontFamily: 'Gilroy_Medium'),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } finally {
        setState(() {
          _isUploadingLogo = false;
          _selectedImage = null;
          _selectedImageBytes = null;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final teamAsync = ref.watch(teamByIdProvider(widget.teamId));
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    return Scaffold(
      backgroundColor: primaryColor,
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () {
            // Navigate back based on the context
            if (widget.isPublicView) {
              context.go('/teams');
            } else {
              context.go('/my-teams');
            }
          },
        ),
        title: teamAsync.when(
          data:
              (team) => Text(
                team.name,
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
          loading:
              () => const Text(
                'Team Details',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
          error:
              (_, __) => const Text(
                'Team Details',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
        ),
        actions: [
          if (!widget.isPublicView) ...[
            IconButton(
              icon: const Icon(Icons.person_add, color: Colors.white),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => InvitePlayerScreen(teamId: widget.teamId),
                  ),
                );
              },
            ),
          ] else ...[
            IconButton(
              icon: const Icon(Icons.share, color: Colors.white),
              onPressed: () {
                final teamAsync = ref.read(teamByIdProvider(widget.teamId));
                teamAsync.whenData((team) => _shareTeam(team));
              },
            ),
          ],
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: accentColor,
          labelColor: Colors.white,
          unselectedLabelColor: greyColor,
          labelStyle: const TextStyle(fontFamily: 'Gilroy_Bold', fontSize: 14),
          unselectedLabelStyle: const TextStyle(
            fontFamily: 'Gilroy_Medium',
            fontSize: 14,
          ),
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Members'),
            Tab(text: 'Stats'),
          ],
        ),
      ),
      body: teamAsync.when(
        data:
            (team) => TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(team),
                _buildMembersTab(team),
                _buildStatsTab(team),
              ],
            ),
        loading:
            () => Center(
              child: CircularProgressIndicator(
                color: NextSportzTheme.getAccentColor(
                  ref.watch(theme_providers.isDarkModeProvider),
                ),
              ),
            ),
        error:
            (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 64),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading team',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed:
                        () => ref.refresh(teamByIdProvider(widget.teamId)),
                    child: Text(
                      'Retry',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color: NextSportzTheme.getAccentColor(
                          ref.watch(theme_providers.isDarkModeProvider),
                        ),
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
      ),
    );
  }

  Widget _buildOverviewTab(Team team) {
    // later get invitations with separate API
    final invitations = [];
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);
    final lightGreyColor = NextSportzTheme.getLightGreyColor(isDark);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Sleek Team Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  accentColor.withValues(alpha: 0.08),
                  secondaryColor.withValues(alpha: 0.03),
                ],
              ),
              border: Border.all(
                color: accentColor.withValues(alpha: 0.15),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    // Compact Team Logo (Clickable)
                    GestureDetector(
                      onTap:
                          widget.isPublicView
                              ? null
                              : () => _pickAndUploadTeamLogo(team),
                      child: Stack(
                        children: [
                          Container(
                            width: 56,
                            height: 56,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(28),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  accentColor.withValues(alpha: 0.2),
                                  accentColor.withValues(alpha: 0.05),
                                ],
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: accentColor.withValues(alpha: 0.2),
                                  blurRadius: 12,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                            child:
                                _isUploadingLogo
                                    ? Center(
                                      child: CircularProgressIndicator(
                                        color: accentColor,
                                        strokeWidth: 2,
                                      ),
                                    )
                                    : team.logoUrl.isNotEmpty
                                    ? ClipRRect(
                                      borderRadius: BorderRadius.circular(28),
                                      child: CachedNetworkImage(
                                        imageUrl: team.logoUrl,
                                        fit: BoxFit.cover,
                                        width: 56,
                                        height: 56,
                                        placeholder:
                                            (context, url) => Container(
                                              decoration: BoxDecoration(
                                                color: lightGreyColor
                                                    .withValues(alpha: 0.2),
                                                borderRadius:
                                                    BorderRadius.circular(28),
                                              ),
                                              child: Center(
                                                child:
                                                    CircularProgressIndicator(
                                                      color: accentColor,
                                                      strokeWidth: 2,
                                                    ),
                                              ),
                                            ),
                                        errorWidget:
                                            (context, url, error) => Container(
                                              decoration: BoxDecoration(
                                                color: lightGreyColor
                                                    .withValues(alpha: 0.2),
                                                borderRadius:
                                                    BorderRadius.circular(28),
                                              ),
                                              child: Icon(
                                                Icons.sports_soccer,
                                                color: accentColor,
                                                size: 28,
                                              ),
                                            ),
                                      ),
                                    )
                                    : Icon(
                                      Icons.sports_soccer,
                                      color: accentColor,
                                      size: 28,
                                    ),
                          ),
                          // Edit overlay for non-public view
                          if (!widget.isPublicView && !_isUploadingLogo)
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: Container(
                                width: 18,
                                height: 18,
                                decoration: BoxDecoration(
                                  color: accentColor,
                                  borderRadius: BorderRadius.circular(9),
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 1.5,
                                  ),
                                ),
                                child: Icon(
                                  Icons.edit,
                                  color: Colors.white,
                                  size: 10,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Team Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            team.name,
                            style: const TextStyle(
                              fontFamily: 'Gilroy_Bold',
                              color: Colors.white,
                              fontSize: 18,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          GestureDetector(
                            onTap: () => _copyTeamCode(team.teamCode),
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: accentColor.withValues(alpha: 0.12),
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(
                                  color: accentColor.withValues(alpha: 0.25),
                                  width: 0.5,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    'Code: ${team.teamCode}',
                                    style: TextStyle(
                                      fontFamily: 'Gilroy_Medium',
                                      color: accentColor,
                                      fontSize: 10,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Icon(
                                    Icons.copy,
                                    color: accentColor,
                                    size: 12,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                // Quick Stats Cards
                Row(
                  children: [
                    Expanded(
                      child: _buildQuickStatCard(
                        'Members',
                        '${team.members.length}',
                        Icons.people,
                        accentColor,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildQuickStatCard(
                        'Matches',
                        '${team.stats.totalMatches}',
                        Icons.sports_soccer,
                        NextSportzTheme.lightAccentSecondary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildQuickStatCard(
                        'Win Rate',
                        '${team.stats.winRate.toStringAsFixed(0)}%',
                        Icons.trending_up,
                        team.stats.winRate >= 50
                            ? NextSportzTheme.lightAccent
                            : NextSportzTheme.lightAccentSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Challenge section for public view
          if (widget.isPublicView) ...[
            _buildChallengeSection(team, accentColor, secondaryColor),
          ],

          if (team.slogan != null && team.slogan!.isNotEmpty) ...[
            const SizedBox(height: 16),
            // Team Slogan with Modern Design
            _buildModernSection(
              'Team Slogan',
              Icons.format_quote,
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      accentColor.withOpacity(0.1),
                      accentColor.withOpacity(0.05),
                    ],
                  ),
                  border: Border.all(
                    color: accentColor.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: accentColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.format_quote,
                        color: accentColor,
                        size: 18,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        '"${team.slogan}"',
                        style: TextStyle(
                          fontFamily: 'Gilroy_Bold',
                          color: Colors.white,
                          fontSize: 15,
                          fontStyle: FontStyle.italic,
                          height: 1.3,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              accentColor,
              secondaryColor,
            ),
          ],

          const SizedBox(height: 16),

          // Team Info
          _buildModernSection(
            'Team Info',
            Icons.info,
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: secondaryColor.withOpacity(0.2),
                border: Border.all(
                  color: lightGreyColor.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  _buildModernInfoRow(
                    'Created',
                    _formatDate(team.created),
                    Icons.calendar_today,
                    greyColor,
                  ),
                  const SizedBox(height: 12),
                  _buildModernInfoRow(
                    'Status',
                    team.isActive ? 'Active' : 'Inactive',
                    team.isActive ? Icons.check_circle : Icons.cancel,
                    team.isActive ? NextSportzTheme.lightAccent : Colors.red,
                  ),
                  const SizedBox(height: 12),
                  if (!widget.isPublicView)
                    GestureDetector(
                      onTap: () => _shareTeam(team),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.transparent,
                        ),
                        child: _buildModernInfoRow(
                          'Share Team',
                          'Invite Players',
                          Icons.share,
                          accentColor,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            accentColor,
            secondaryColor,
          ),

          if (invitations.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildModernSection(
              'Pending Invitations',
              Icons.mail_outline,
              Column(
                children:
                    invitations.map((invitation) {
                      return _buildInvitationItem(invitation);
                    }).toList(),
              ),
              accentColor,
              secondaryColor,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMembersTab(Team team) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);
    final lightGreyColor = NextSportzTheme.getLightGreyColor(isDark);

    if (team.members.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.group_off,
                size: 48,
                color: Colors.white.withOpacity(0.4),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'No Members Yet',
              style: TextStyle(
                fontFamily: 'Gilroy_Bold',
                color: Colors.white.withOpacity(0.9),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Invite players to join your team',
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white.withOpacity(0.6),
                fontSize: 13,
              ),
            ),
            const SizedBox(height: 16),
            if (!widget.isPublicView)
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => InvitePlayerScreen(teamId: team.id),
                    ),
                  );
                },
                icon: const Icon(Icons.person_add, size: 16),
                label: const Text('Invite Players'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: accentColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  textStyle: const TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    fontSize: 13,
                  ),
                ),
              ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Team Summary Header
        Container(
          margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: secondaryColor.withOpacity(0.1),
            border: Border.all(color: Colors.white.withOpacity(0.05), width: 1),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.group, color: accentColor, size: 18),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Team Members',
                      style: const TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        color: Colors.white,
                        fontSize: 15,
                      ),
                    ),
                    Text(
                      '${team.members.length} members',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color: Colors.white.withOpacity(0.6),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              if (!widget.isPublicView)
                Container(
                  decoration: BoxDecoration(
                    color: accentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => InvitePlayerScreen(teamId: team.id),
                        ),
                      );
                    },
                    icon: Icon(Icons.person_add, color: accentColor, size: 18),
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ),
            ],
          ),
        ),

        // Members List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: team.members.length,
            itemBuilder: (context, index) {
              final member = team.members[index];
              return _buildMemberCard(member);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatsTab(Team team) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);
    final lightGreyColor = NextSportzTheme.getLightGreyColor(isDark);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Performance Overview Cards
          Row(
            children: [
              Expanded(
                child: _buildCompactStatCard(
                  'Matches',
                  '${team.stats.totalMatches}',
                  Icons.sports_soccer,
                  accentColor,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildCompactStatCard(
                  'Win Rate',
                  '${team.stats.winRate.toStringAsFixed(1)}%',
                  Icons.trending_up,
                  team.stats.winRate >= 50
                      ? NextSportzTheme.lightAccent
                      : NextSportzTheme.lightAccentSecondary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          Row(
            children: [
              Expanded(
                child: _buildCompactStatCard(
                  'Goals',
                  '${team.stats.scoredGoals}',
                  Icons.sports_soccer,
                  NextSportzTheme.lightAccentSecondary,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildCompactStatCard(
                  'Goal Diff',
                  '${team.stats.goalDifference >= 0 ? '+' : ''}${team.stats.goalDifference}',
                  Icons.compare_arrows,
                  team.stats.goalDifference >= 0
                      ? NextSportzTheme.lightAccent
                      : Colors.red,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Detailed Performance Section
          _buildSection(
            'Match Performance',
            Icons.analytics,
            Column(
              children: [
                _buildDetailedStatRow(
                  'Total Matches',
                  '${team.stats.totalMatches}',
                  Icons.sports_soccer,
                ),
                _buildDetailedStatRow(
                  'Wins',
                  '${team.stats.wins}',
                  Icons.emoji_events,
                  color: NextSportzTheme.lightAccent,
                ),
                _buildDetailedStatRow(
                  'Draws',
                  '${team.stats.draws}',
                  Icons.horizontal_rule,
                  color: NextSportzTheme.lightAccentSecondary,
                ),
                _buildDetailedStatRow(
                  'Losses',
                  '${team.stats.losses}',
                  Icons.close,
                  color: Colors.red,
                ),
                _buildDetailedStatRow(
                  'Win Rate',
                  '${team.stats.winRate.toStringAsFixed(1)}%',
                  Icons.trending_up,
                  color:
                      team.stats.winRate >= 50
                          ? NextSportzTheme.lightAccent
                          : NextSportzTheme.lightAccentSecondary,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Goals Analysis Section
          _buildSection(
            'Goals Analysis',
            Icons.sports_soccer,
            Column(
              children: [
                _buildDetailedStatRow(
                  'Goals Scored',
                  '${team.stats.scoredGoals}',
                  Icons.sports_soccer,
                  color: NextSportzTheme.lightAccent,
                ),
                _buildDetailedStatRow(
                  'Goals Conceded',
                  '${team.stats.concededGoals}',
                  Icons.sports_soccer,
                  color: Colors.red,
                ),
                _buildDetailedStatRow(
                  'Goal Difference',
                  '${team.stats.goalDifference >= 0 ? '+' : ''}${team.stats.goalDifference}',
                  Icons.compare_arrows,
                  color:
                      team.stats.goalDifference >= 0
                          ? NextSportzTheme.lightAccent
                          : Colors.red,
                ),
                if (team.stats.totalMatches > 0)
                  _buildDetailedStatRow(
                    'Avg Goals/Match',
                    '${(team.stats.scoredGoals / team.stats.totalMatches).toStringAsFixed(1)}',
                    Icons.calculate,
                    color: NextSportzTheme.lightAccentSecondary,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, Widget content) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: secondaryColor.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: accentColor, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          content,
        ],
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: NextSportzTheme.getAccentColor(
            ref.watch(theme_providers.isDarkModeProvider),
          ),
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 20,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: grey,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: grey,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactStatCard(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: secondaryColor.withOpacity(0.08),
        border: Border.all(color: color.withOpacity(0.2), width: 0.5),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 6),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.6),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedStatRow(
    String label,
    String value,
    IconData icon, {
    Color? color,
  }) {
    final statColor =
        color ??
        NextSportzTheme.getAccentColor(
          ref.watch(theme_providers.isDarkModeProvider),
        );

    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: statColor.withOpacity(0.15),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, color: statColor, size: 14),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white.withOpacity(0.7),
                fontSize: 13,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: statColor,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemberCard(TeamMember member) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: secondaryColor.withOpacity(0.08),
        border: Border.all(
          color: _getRoleColor(member.role).withOpacity(0.2),
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getRoleColor(member.role).withOpacity(0.15),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: _getRoleColor(member.role).withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                member.userName.isNotEmpty
                    ? member.userName[0].toUpperCase()
                    : 'U',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: _getRoleColor(member.role),
                  fontSize: 16,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  member.userName,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  member.playerName,
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 6),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: _getRoleColor(member.role).withOpacity(0.15),
                      ),
                      child: Text(
                        _getRoleDisplayName(member.role),
                        style: TextStyle(
                          fontFamily: 'Gilroy_Medium',
                          color: _getRoleColor(member.role),
                          fontSize: 10,
                        ),
                      ),
                    ),
                    const SizedBox(width: 6),
                    Icon(
                      member.isActive ? Icons.check_circle : Icons.cancel,
                      color:
                          member.isActive
                              ? NextSportzTheme.lightAccent
                              : Colors.red,
                      size: 14,
                    ),
                  ],
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _formatDate(member.joinedAt),
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white.withOpacity(0.5),
                  fontSize: 10,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                'Joined',
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white.withOpacity(0.4),
                  fontSize: 9,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationItem(TeamInvitation invitation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: NextSportzTheme.lightAccentSecondary.withOpacity(0.1),
        border: Border.all(
          color: NextSportzTheme.lightAccentSecondary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: NextSportzTheme.lightAccentSecondary.withOpacity(
              0.2,
            ),
            child: Text(
              invitation.invitedTeamName[0].toUpperCase(),
              style: TextStyle(
                fontFamily: 'Gilroy_Bold',
                color: NextSportzTheme.lightAccentSecondary,
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  invitation.invitedTeamName,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
                Text(
                  'Invited ${_formatDate(invitation.invitedAt)}',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: NextSportzTheme.lightAccentSecondary.withOpacity(0.2),
                ),
                child: Text(
                  invitation.status.toUpperCase(),
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: NextSportzTheme.lightAccentSecondary,
                    fontSize: 12,
                  ),
                ),
              ),
              if (invitation.status == 'pending' && !widget.isPublicView) ...[
                const SizedBox(height: 8),
                _buildInvitationActions(invitation),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationActions(TeamInvitation invitation) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Delete invitation button
        InkWell(
          onTap: () => _deleteInvitation(invitation),
          child: Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.red.withOpacity(0.3), width: 1),
            ),
            child: Icon(Icons.delete_outline, color: Colors.red, size: 16),
          ),
        ),
        const SizedBox(width: 8),
        // Resend invitation button (optional)
        InkWell(
          onTap: () => _resendInvitation(invitation),
          child: Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: accentColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: accentColor.withOpacity(0.3), width: 1),
            ),
            child: Icon(Icons.refresh, color: accentColor, size: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildTopPerformerCard(TeamMember member) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: secondaryColor.withOpacity(0.06),
        border: Border.all(
          color: _getRoleColor(member.role).withOpacity(0.2),
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getRoleColor(member.role).withOpacity(0.15),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: _getRoleColor(member.role).withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                member.playerName.isNotEmpty
                    ? member.playerName[0].toUpperCase()
                    : 'P',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: _getRoleColor(member.role),
                  fontSize: 14,
                ),
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  member.playerName,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 13,
                  ),
                ),
                Text(
                  member.userName,
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white.withOpacity(0.5),
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getRoleColor(member.role).withOpacity(0.15),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              _getRoleDisplayName(member.role),
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: _getRoleColor(member.role),
                fontSize: 9,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Modern section builder with theme consistency
  Widget _buildModernSection(
    String title,
    IconData icon,
    Widget content,
    Color accentColor,
    Color secondaryColor,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: accentColor.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: accentColor, size: 16),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          content,
        ],
      ),
    );
  }

  // Quick stat card for overview
  Widget _buildQuickStatCard(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: secondaryColor.withValues(alpha: 0.25),
        border: Border.all(color: color.withValues(alpha: 0.25), width: 0.8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 6),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 1),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  // Modern info row for team details
  Widget _buildModernInfoRow(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.15),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(icon, color: color, size: 14),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.7),
              fontSize: 13,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: color,
            fontSize: 13,
          ),
        ),
      ],
    );
  }

  Color _getRoleColor(String role) {
    switch (role) {
      case 'captain':
        return const Color(0xFFFFD700); // Gold color for captain
      case 'vice_captain':
        return NextSportzTheme.lightAccentSecondary; // Orange from theme
      default:
        return NextSportzTheme.lightAccent; // Green from theme
    }
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'captain':
        return 'Captain';
      case 'vice_captain':
        return 'Vice Captain';
      default:
        return 'Member';
    }
  }

  void _shareTeam(Team team) {
    // Generate deep link with team code
    final deepLink =
        'https://nextsportz.com/join-team?teamCode=${team.teamCode}';

    final shareText = '''
🏆 Join my team "${team.name}" on NextSportz!

Team Code: ${team.teamCode}

Tap this link to join automatically:
$deepLink

Or use the team code manually in the NextSportz app.

Download NextSportz: https://nextsportz.com/app
''';

    Share.share(shareText, subject: 'Join my team on NextSportz!');
  }

  void _challengeTeam(Team team) {
    // TODO: Implement challenge team functionality
    // Navigate to challenge creation screen with pre-filled team
  }

  Widget _buildChallengeSection(
    Team team,
    Color accentColor,
    Color secondaryColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            accentColor.withValues(alpha: 0.1),
            accentColor.withValues(alpha: 0.05),
          ],
        ),
        border: Border.all(color: accentColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: accentColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.sports_mma, color: accentColor, size: 18),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Ready for a Challenge?',
                      style: const TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        color: Colors.white,
                        fontSize: 15,
                      ),
                    ),
                    Text(
                      'Challenge ${team.name} to a match',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _challengeTeam(team),
              icon: const Icon(Icons.sports_mma, size: 16),
              label: const Text('Send Challenge'),
              style: ElevatedButton.styleFrom(
                backgroundColor: accentColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                textStyle: const TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Invitation management methods
  Future<void> _deleteInvitation(TeamInvitation invitation) async {
    try {
      final useCase = ref.read(deleteTeamInviteUseCaseProvider);
      final params = invitations_usecases.DeleteTeamInviteParams(
        teamId: invitation.invitedTeamId,
        memberId: invitation.id,
      );

      final result = await useCase(params);
      result.fold(
        (error) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to delete invitation: ${error.message}'),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        (_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Invitation deleted successfully'),
                backgroundColor: NextSportzTheme.lightAccent,
                behavior: SnackBarBehavior.floating,
              ),
            );
            // Refresh the team data to update the invitations list
            ref.invalidate(teamByIdProvider(widget.teamId));
          }
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting invitation: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _resendInvitation(TeamInvitation invitation) async {
    try {
      final useCase = ref.read(inviteTeamMemberUseCaseProvider);
      final params = invitations_usecases.InviteTeamMemberParams(
        teamId: invitation.invitedTeamId,
        playerId: invitation.id, // Using invitation ID as player ID
        role: TeamRole.player, // Default role for resend
      );

      final result = await useCase(params);
      result.fold(
        (error) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to resend invitation: ${error.message}'),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        (_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Invitation resent successfully'),
                backgroundColor: NextSportzTheme.lightAccent,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error resending invitation: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _copyTeamCode(String teamCode) async {
    await Clipboard.setData(ClipboardData(text: teamCode));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Team code "$teamCode" copied to clipboard'),
          backgroundColor: NextSportzTheme.lightAccent,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
