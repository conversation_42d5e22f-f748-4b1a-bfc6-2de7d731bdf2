import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../../core/models/paginated_response.dart';
import '../../domain/entities/team.dart';
import '../../domain/repositories/teams_repository.dart';
import '../widgets/team_search_item_card.dart';

/// Example screen demonstrating the usage of pagination abstractions
class TeamsSearchScreen extends ConsumerStatefulWidget {
  const TeamsSearchScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<TeamsSearchScreen> createState() => _TeamsSearchScreenState();
}

class _TeamsSearchScreenState extends ConsumerState<TeamsSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _currentQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<PaginatedResponse<TeamSearchItem>> _loadTeams(int page) async {
    // Get the teams repository from your provider
    // final teamsRepository = ref.read(teamsRepositoryProvider);

    // For demonstration, we'll create a mock response
    // In real implementation, you would call:
    // final result = await teamsRepository.searchTeamsPaginated(
    //   query: _currentQuery.isEmpty ? null : _currentQuery,
    //   page: page,
    //   pageSize: 20,
    // );
    // return result.fold(
    //   (error) => throw Exception(error.message),
    //   (response) => response,
    // );

    // Mock implementation for demonstration
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay

    final mockItems = List.generate(20, (index) {
      final teamIndex = (page - 1) * 20 + index + 1;
      return TeamSearchItem(
        id: 'team_$teamIndex',
        name: 'Team $teamIndex',
        membersCount: 5 + (teamIndex % 10),
        winRate: '${(50 + (teamIndex % 50)).toString()}.00',
        logoUrl: '', // Empty for demo
      );
    });

    return PaginatedResponse<TeamSearchItem>(
      message: 'Success',
      data: PaginatedData<TeamSearchItem>(
        items: mockItems,
        total: 100, // Mock total
        page: page,
        pageSize: 20,
        totalPages: 5,
      ),
    );
  }

  void _onSearchChanged(String query) {
    setState(() {
      _currentQuery = query;
    });
  }

  void _onTeamTap(TeamSearchItem team) {
    // Navigate to team details using dynamic route
    context.go('/teams/${team.id}');
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final backgroundColor =
        isDark ? NextSportzTheme.darkPrimary : NextSportzTheme.lightPrimary;

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Search Teams',
          style: TextStyle(fontFamily: 'Gilroy_Bold', color: Colors.white),
        ),
        backgroundColor: backgroundColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              onChanged: _onSearchChanged,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Search teams...',
                hintStyle: TextStyle(
                  color: Colors.white.withValues(alpha: 0.6),
                  fontFamily: 'Gilroy_Medium',
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: Colors.white.withValues(alpha: 0.6),
                ),
                suffixIcon:
                    _searchController.text.isNotEmpty
                        ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color: Colors.white.withValues(alpha: 0.6),
                          ),
                          onPressed: () {
                            _searchController.clear();
                            _onSearchChanged('');
                          },
                        )
                        : null,
                filled: true,
                fillColor: Colors.white.withValues(alpha: 0.1),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          // Teams list
          Expanded(
            child: TeamSearchPaginatedListView(
              key: ValueKey(_currentQuery), // Rebuild when search changes
              onLoadPage: _loadTeams,
              onTeamTap: _onTeamTap,
              emptyMessage:
                  _currentQuery.isEmpty
                      ? 'Start typing to search for teams'
                      : 'No teams found for "$_currentQuery"',
            ),
          ),
        ],
      ),
    );
  }
}
