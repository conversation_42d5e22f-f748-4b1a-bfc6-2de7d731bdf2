import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../utils/color.dart';
import '../../teams_providers.dart';
import '../../domain/entities/team.dart';
import '../../domain/usecases/team_invitations_usecases.dart';

class PendingInvitationsScreen extends ConsumerWidget {
  const PendingInvitationsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final invitationsAsync = ref.watch(pendingInvitationsProvider);

    return Scaffold(
      backgroundColor: PrimeryColor,
      appBar: AppBar(
        backgroundColor: PrimeryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Pending Invitations',
          style: TextStyle(
            fontFamily: '<PERSON><PERSON>_<PERSON>',
            color: Colors.white,
            fontSize: 18,
          ),
        ),
      ),
      body: invitationsAsync.when(
        data: (invitations) {
          // Filter for only active invitations
          final activeInvitations =
              invitations.where((inv) => inv.isActive).toList();

          return activeInvitations.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                itemCount: activeInvitations.length,
                itemBuilder: (context, index) {
                  final invitation = activeInvitations[index];
                  return _buildModernInvitationCard(invitation, ref, index);
                },
              );
        },
        loading:
            () => const Center(
              child: CircularProgressIndicator(color: Color(0xff00D4AA)),
            ),
        error:
            (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 64),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading invitations',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () => ref.refresh(pendingInvitationsProvider),
                    child: const Text(
                      'Retry',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color: Color(0xff00D4AA),
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(60),
              color: const Color(0xff00D4AA).withOpacity(0.2),
            ),
            child: const Icon(
              Icons.mail_outline,
              color: Color(0xff00D4AA),
              size: 60,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'No Pending Invitations',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You don\'t have any pending team invitations',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: grey,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildModernInvitationCard(
    TeamInvitation invitation,
    WidgetRef ref,
    int index,
  ) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.08),
                    Colors.white.withOpacity(0.04),
                  ],
                ),
                border: Border.all(
                  color: Colors.white.withOpacity(0.12),
                  width: 0.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 44,
                          height: 44,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                const Color(0xff00D4AA).withOpacity(0.8),
                                const Color(0xff00D4AA).withOpacity(0.6),
                              ],
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0xff00D4AA).withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Text(
                              invitation.invitedTeamName[0].toUpperCase(),
                              style: const TextStyle(
                                fontFamily: 'Gilroy_Bold',
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                invitation.invitedTeamName,
                                style: const TextStyle(
                                  fontFamily: 'Gilroy_Bold',
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                'Invited ${invitation.timeAgo}',
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Medium',
                                  color: Colors.white.withOpacity(0.6),
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xffFFA726).withOpacity(0.15),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: const Color(0xffFFA726).withOpacity(0.3),
                              width: 0.5,
                            ),
                          ),
                          child: Text(
                            'PENDING',
                            style: TextStyle(
                              fontFamily: 'Gilroy_Bold',
                              color: const Color(0xffFFA726),
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildActionButton(
                            label: 'Accept',
                            onTap: () => _acceptInvitation(invitation.id, ref),
                            isPrimary: true,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildActionButton(
                            label: 'Decline',
                            onTap: () => _declineInvitation(invitation.id, ref),
                            isPrimary: false,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton({
    required String label,
    required VoidCallback onTap,
    required bool isPrimary,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(10),
        child: Container(
          height: 36,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            gradient:
                isPrimary
                    ? LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        const Color(0xff00D4AA),
                        const Color(0xff00D4AA).withOpacity(0.8),
                      ],
                    )
                    : null,
            border:
                isPrimary
                    ? null
                    : Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 0.5,
                    ),
            color: isPrimary ? null : Colors.white.withOpacity(0.05),
          ),
          child: Center(
            child: Text(
              label,
              style: TextStyle(
                fontFamily: 'Gilroy_SemiBold',
                color: isPrimary ? Colors.white : Colors.white.withOpacity(0.8),
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'today';
    } else if (difference.inDays == 1) {
      return 'yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Future<void> _acceptInvitation(String invitationId, WidgetRef ref) async {
    try {
      final useCase = ref.read(acceptInvitationUseCaseProvider);
      // Note: We need teamId for the new API, but it's not available here.
      // This might need to be refactored to pass the full invitation object.
      // For now, using a placeholder teamId.
      // Get the invitation to extract teamId
      final invitations = await ref.read(pendingInvitationsProvider.future);
      final invitation = invitations.firstWhere(
        (inv) => inv.id == invitationId,
      );

      final params = AcceptInvitationParams(
        teamId: invitation.invitedTeamId,
        invitationId: invitationId,
      );
      await useCase(params);
      ref.refresh(pendingInvitationsProvider);
      ref.refresh(myTeamsProvider);

      if (ref.context.mounted) {
        ScaffoldMessenger.of(ref.context).showSnackBar(
          const SnackBar(
            content: Text('Invitation accepted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (ref.context.mounted) {
        ScaffoldMessenger.of(ref.context).showSnackBar(
          SnackBar(
            content: Text('Error accepting invitation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _declineInvitation(String invitationId, WidgetRef ref) async {
    try {
      final useCase = ref.read(declineInvitationUseCaseProvider);
      // Note: We need teamId for the new API, but it's not available here.
      // This might need to be refactored to pass the full invitation object.
      // For now, using a placeholder teamId.
      // Get the invitation to extract teamId
      final invitations = await ref.read(pendingInvitationsProvider.future);
      final invitation = invitations.firstWhere(
        (inv) => inv.id == invitationId,
      );

      final params = DeclineInvitationParams(
        teamId: invitation.invitedTeamId,
        invitationId: invitationId,
      );
      await useCase(params);
      ref.refresh(pendingInvitationsProvider);

      if (ref.context.mounted) {
        ScaffoldMessenger.of(ref.context).showSnackBar(
          const SnackBar(
            content: Text('Invitation declined'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (ref.context.mounted) {
        ScaffoldMessenger.of(ref.context).showSnackBar(
          SnackBar(
            content: Text('Error declining invitation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
