import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../../core/models/paginated_response.dart';
import '../../domain/entities/team.dart';
import '../../teams_providers.dart';
import '../widgets/team_search_item_card.dart';

/// Team list page with search functionality
class TeamListScreen extends ConsumerStatefulWidget {
  const TeamListScreen({super.key});

  @override
  ConsumerState<TeamListScreen> createState() => _TeamListScreenState();
}

class _TeamListScreenState extends ConsumerState<TeamListScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  String _searchQuery = '';
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _isSearching = query.isNotEmpty;
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
      _isSearching = false;
    });
    _searchFocusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);

    return Scaffold(
      backgroundColor: primaryColor,
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => context.go('/home'),
        ),
        title: const Text(
          'Teams',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
            fontFamily: 'Gilroy_Bold',
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Search bar
          _buildSearchBar(),

          // Teams list
          Expanded(child: _buildTeamsList()),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: secondaryColor.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.search,
            color: Colors.white.withValues(alpha: 0.7),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              onChanged: _onSearchChanged,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontFamily: 'Gilroy_Medium',
              ),
              decoration: InputDecoration(
                hintText: 'Search teams...',
                hintStyle: TextStyle(
                  color: Colors.white.withValues(alpha: 0.5),
                  fontSize: 16,
                  fontFamily: 'Gilroy_Medium',
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
          if (_isSearching)
            IconButton(
              icon: Icon(
                Icons.clear,
                color: Colors.white.withValues(alpha: 0.7),
                size: 20,
              ),
              onPressed: _clearSearch,
            ),
        ],
      ),
    );
  }

  Widget _buildTeamsList() {
    return TeamSearchPaginatedListView(
      key: ValueKey(_searchQuery), // Rebuild when search changes
      onLoadPage: _loadTeamsPage,
      onTeamTap: _onTeamTap,
      emptyMessage:
          _isSearching
              ? 'No teams found for "$_searchQuery"'
              : 'No teams available',
    );
  }

  Future<PaginatedResponse<TeamSearchItem>> _loadTeamsPage(int page) async {
    final repository = ref.read(teamsRepositoryProvider);

    final result =
        _isSearching && _searchQuery.isNotEmpty
            ? await repository.searchTeamsPaginated(
              query: _searchQuery,
              page: page,
              pageSize: 20,
            )
            : await repository.getAllTeamsPaginated(page: page, pageSize: 20);

    return result.fold(
      (error) => throw Exception(error.message),
      (response) => response,
    );
  }

  void _onTeamTap(TeamSearchItem team) {
    context.go('/teams/${team.id}');
  }
}
