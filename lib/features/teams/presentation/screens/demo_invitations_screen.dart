import 'package:flutter/material.dart';
import '../../domain/entities/team.dart';
import '../widgets/team_invitation_card.dart';

class DemoInvitationsScreen extends StatelessWidget {
  const DemoInvitationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Demo data based on your actual API response
    final demoInvitations = [
      TeamInvitation(
        id: '2e47ebab-2492-41c3-8668-5dffa97ef127',
        invitedTeamId: '1372a957-b5d4-4f3c-9a28-a4ab31d0a266',
        invitedTeamName: 'YOLO',
        invitedTeamStats: const TeamStats(
          wins: 8,
          losses: 2,
          draws: 1,
          scoredGoals: 25,
          concededGoals: 12,
        ),
        teamLogoUrl: '',
        status: 'Pending',
        isActive: true,
        invitedAt: DateTime.now().subtract(const Duration(hours: 5)),
      ),
      TeamInvitation(
        id: '92cbffc7-d4ef-4bfa-919b-3dda2370836b',
        invitedTeamId: '9a24850a-15fc-42d4-bf8f-903015139bfd',
        invitedTeamName: 'Guji Gang',
        invitedTeamStats: const TeamStats(
          wins: 12,
          losses: 3,
          draws: 2,
          scoredGoals: 35,
          concededGoals: 18,
        ),
        teamLogoUrl: '',
        status: 'Accepted',
        isActive: false,
        invitedAt: DateTime.now().subtract(const Duration(hours: 6)),
      ),
      TeamInvitation(
        id: 'demo-3',
        invitedTeamId: 'team-3',
        invitedTeamName: 'Thunder Bolts',
        invitedTeamStats: const TeamStats(
          wins: 15,
          losses: 1,
          draws: 0,
          scoredGoals: 42,
          concededGoals: 8,
        ),
        teamLogoUrl: '',
        status: 'Pending',
        isActive: true,
        invitedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      TeamInvitation(
        id: 'demo-4',
        invitedTeamId: 'team-4',
        invitedTeamName: 'Fire Dragons',
        invitedTeamStats: const TeamStats(
          wins: 6,
          losses: 8,
          draws: 3,
          scoredGoals: 22,
          concededGoals: 28,
        ),
        teamLogoUrl: '',
        status: 'Declined',
        isActive: false,
        invitedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];

    return Scaffold(
      backgroundColor: const Color(0xFF0F0F23),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0F0F23),
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(context),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.only(bottom: 20),
                  itemCount: demoInvitations.length,
                  itemBuilder: (context, index) {
                    final invitation = demoInvitations[index];
                    return TeamInvitationCard(
                      invitation: invitation,
                      onAccept: invitation.isPending
                          ? () => _showSnackBar(context, 'Accepted invitation from ${invitation.invitedTeamName}!', Colors.green)
                          : null,
                      onDecline: invitation.isPending
                          ? () => _showSnackBar(context, 'Declined invitation from ${invitation.invitedTeamName}', Colors.orange)
                          : null,
                      showActions: invitation.isPending,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: const Icon(
                    Icons.arrow_back_ios_new,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Team Invitations',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontFamily: 'Gilroy_Bold',
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Beautiful, intuitive & sexy UI 🔥',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white.withOpacity(0.7),
                        fontFamily: 'Gilroy_Medium',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              _buildStatCard('Pending', 2, const Color(0xFFFFA726)),
              const SizedBox(width: 12),
              _buildStatCard('Accepted', 1, const Color(0xFF66BB6A)),
              const SizedBox(width: 12),
              _buildStatCard('Total', 4, const Color(0xFF42A5F5)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, int count, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Text(
              count.toString(),
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
                fontFamily: 'Gilroy_Bold',
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.white.withOpacity(0.8),
                fontFamily: 'Gilroy_Medium',
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
