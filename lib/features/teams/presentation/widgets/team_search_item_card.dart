import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../../core/widgets/cached_image_widget.dart';
import '../../../../core/widgets/paginated_list_view.dart';
import '../../../../core/models/paginated_response.dart';
import '../../domain/entities/team.dart';

/// Card widget for displaying team search items in a list
class TeamSearchItemCard extends ConsumerWidget {
  final TeamSearchItem team;
  final VoidCallback? onTap;

  const TeamSearchItemCard({Key? key, required this.team, this.onTap})
    : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final lightBlue =
        isDark ? NextSportzTheme.darkBlue : NextSportzTheme.lightBlue;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: lightBlue.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            // Team logo
            _buildTeamLogo(),
            const SizedBox(width: 16),

            // Team info
            Expanded(child: _buildTeamInfo()),

            // Stats and arrow
            _buildTeamStats(),
          ],
        ),
      ),
    );
  }

  Widget _buildTeamLogo() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child:
          team.logoUrl.isNotEmpty
              ? ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: CachedImageWidget(
                  imageUrl: team.logoUrl,
                  width: 48,
                  height: 48,
                  fit: BoxFit.cover,
                  errorWidget: _buildDefaultLogo(),
                ),
              )
              : _buildDefaultLogo(),
    );
  }

  Widget _buildDefaultLogo() {
    return const Icon(Icons.groups, color: Colors.white70, size: 24);
  }

  Widget _buildTeamInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Team name
        Text(
          team.name,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'Gilroy_Bold',
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),

        // Team stats summary
        Row(
          children: [
            // Members count
            Icon(
              Icons.people,
              color: Colors.white.withValues(alpha: 0.7),
              size: 14,
            ),
            const SizedBox(width: 4),
            Text(
              '${team.membersCount} members',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 12,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
            const SizedBox(width: 12),

            // Win rate
            Icon(
              Icons.trending_up,
              color: Colors.white.withValues(alpha: 0.7),
              size: 14,
            ),
            const SizedBox(width: 4),
            Text(
              team.winRatePercentage,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 12,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTeamStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Win rate badge
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getWinRateColor(
              team.winRateAsDouble,
            ).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _getWinRateColor(
                team.winRateAsDouble,
              ).withValues(alpha: 0.3),
            ),
          ),
          child: Text(
            team.winRatePercentage,
            style: TextStyle(
              color: _getWinRateColor(team.winRateAsDouble),
              fontSize: 12,
              fontWeight: FontWeight.bold,
              fontFamily: 'Gilroy_Bold',
            ),
          ),
        ),
        const SizedBox(height: 8),

        // Arrow icon
        Icon(
          Icons.arrow_forward_ios,
          color: Colors.white.withValues(alpha: 0.5),
          size: 16,
        ),
      ],
    );
  }

  Color _getWinRateColor(double winRate) {
    if (winRate >= 0.7) {
      return const Color(0xFF4CAF50); // Green for high win rate
    } else if (winRate >= 0.5) {
      return const Color(0xFFFF9800); // Orange for medium win rate
    } else {
      return const Color(0xFFF44336); // Red for low win rate
    }
  }
}

/// Paginated list view specifically for team search items
class TeamSearchPaginatedListView extends ConsumerWidget {
  final Future<PaginatedResponse<TeamSearchItem>> Function(int page) onLoadPage;
  final void Function(TeamSearchItem team)? onTeamTap;
  final String? emptyMessage;

  const TeamSearchPaginatedListView({
    Key? key,
    required this.onLoadPage,
    this.onTeamTap,
    this.emptyMessage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PaginatedListView<TeamSearchItem>(
      onLoadPage: onLoadPage,
      itemBuilder: (context, team, index) {
        return TeamSearchItemCard(
          team: team,
          onTap: onTeamTap != null ? () => onTeamTap!(team) : null,
        );
      },
      emptyWidget: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.groups_outlined, size: 64, color: Colors.grey),
              const SizedBox(height: 16),
              Text(
                emptyMessage ?? 'No teams found',
                style: const TextStyle(
                  fontSize: 18,
                  color: Colors.grey,
                  fontFamily: 'Gilroy_Medium',
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
      errorBuilder: (error, onRetry) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'Failed to load teams',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Gilroy_Bold',
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  error,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                    fontFamily: 'Gilroy_Medium',
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: onRetry,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: NextSportzTheme.accent,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child: const Text(
                    'Retry',
                    style: TextStyle(fontFamily: 'Gilroy_Medium'),
                  ),
                ),
              ],
            ),
          ),
        );
      },
      loadingWidget: const Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(
                'Loading teams...',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                  fontFamily: 'Gilroy_Medium',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
