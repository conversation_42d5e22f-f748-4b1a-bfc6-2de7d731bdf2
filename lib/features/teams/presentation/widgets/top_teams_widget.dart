import 'package:flutter/material.dart';
import '../../../../core/config/theme.dart' as core show NextSportzTheme;
import '../../domain/entities/team.dart';

class TopTeamsWidget extends StatelessWidget {
  final List<Team> teams;
  const TopTeamsWidget({super.key, required this.teams});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Top Teams', style: core.NextSportzTheme.titleStyle),
        const SizedBox(height: 12),
        SizedBox(
          height: 150,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: teams.length,
            separatorBuilder: (_, __) => const SizedBox(width: 12),
            itemBuilder: (context, index) {
              final t = teams[index];
              return Container(
                width: 240,
                padding: const EdgeInsets.all(12),
                decoration: core.NextSportzTheme.getCardDecoration(),
                child: Row(
                  children: [
                    Container(
                      width: 56,
                      height: 56,
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(14),
                        border: Border.all(color: Colors.white24, width: 1),
                      ),
                      child:
                          t.logoUrl.isNotEmpty
                              ? Image.asset(
                                t.logoUrl,
                                fit: BoxFit.contain,
                                errorBuilder:
                                    (context, error, stackTrace) => const Icon(
                                      Icons.shield,
                                      color: Colors.white,
                                      size: 28,
                                    ),
                              )
                              : const Icon(
                                Icons.shield,
                                color: Colors.white,
                                size: 28,
                              ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            t.name,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: core.NextSportzTheme.titleStyle.copyWith(
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 6),
                          Text(
                            '${t.stats.wins}W ${t.stats.draws}D ${t.stats.losses}L',
                            style: core.NextSportzTheme.bodyStyle.copyWith(
                              fontSize: 12,
                            ),
                          ),
                          const Spacer(),
                          Row(
                            children: [
                              const Icon(
                                Icons.trending_up,
                                color: Colors.white70,
                                size: 16,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'Win rate: ${t.stats.winRate.toStringAsFixed(0)}%',
                                style: core.NextSportzTheme.bodyStyle.copyWith(
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
