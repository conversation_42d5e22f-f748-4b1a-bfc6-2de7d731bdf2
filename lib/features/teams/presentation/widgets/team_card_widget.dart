import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../../core/widgets/cached_image_widget.dart';
import '../../domain/entities/team.dart';

/// Elegant one-liner team card widget
class TeamCardWidget extends ConsumerWidget {
  final Team team;
  final VoidCallback? onTap;

  const TeamCardWidget({super.key, required this.team, this.onTap});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final lightBlue =
        isDark ? NextSportzTheme.darkBlue : NextSportzTheme.lightBlue;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: lightBlue.withOpacity(0.3),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withOpacity(0.1)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            // Team logo
            _buildTeamLogo(),
            const SizedBox(width: 16),

            // Team info
            Expanded(child: _buildTeamInfo()),

            // Stats and arrow
            _buildTeamStats(accentColor),
          ],
        ),
      ),
    );
  }

  Widget _buildTeamLogo() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child:
          team.logoUrl.isNotEmpty
              ? ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Image.network(
                  team.logoUrl,
                  fit: BoxFit.cover,
                  errorBuilder:
                      (context, error, stackTrace) => _buildDefaultLogo(),
                ),
              )
              : _buildDefaultLogo(),
    );
  }

  Widget _buildDefaultLogo() {
    return const Icon(Icons.groups, color: Colors.white70, size: 24);
  }

  Widget _buildTeamInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Team name
        Text(
          team.name,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'Gilroy_Bold',
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),

        // Team description or stats summary
        Row(
          children: [
            // Members count
            Icon(Icons.people, color: Colors.white.withOpacity(0.7), size: 14),
            const SizedBox(width: 4),
            Text(
              '5 members',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 12,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
            const SizedBox(width: 12),

            // Match record
            Icon(
              Icons.sports_soccer,
              color: Colors.white.withOpacity(0.7),
              size: 14,
            ),
            const SizedBox(width: 4),
            Text(
              '${team.stats.wins}W ${team.stats.draws}D ${team.stats.losses}L',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 12,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTeamStats(Color accentColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Win rate
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getWinRateColor(team.stats.winRate).withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _getWinRateColor(team.stats.winRate).withOpacity(0.3),
            ),
          ),
          child: Text(
            '${(team.stats.winRate * 100).toInt()}%',
            style: TextStyle(
              color: _getWinRateColor(team.stats.winRate),
              fontSize: 12,
              fontWeight: FontWeight.bold,
              fontFamily: 'Gilroy_Bold',
            ),
          ),
        ),
        const SizedBox(height: 8),

        // Arrow icon
        Icon(
          Icons.arrow_forward_ios,
          color: Colors.white.withOpacity(0.5),
          size: 16,
        ),
      ],
    );
  }

  Color _getWinRateColor(double winRate) {
    if (winRate >= 0.7) {
      return const Color(0xFF4CAF50); // Green for high win rate
    } else if (winRate >= 0.5) {
      return const Color(0xFFFF9800); // Orange for medium win rate
    } else {
      return const Color(0xFFF44336); // Red for low win rate
    }
  }
}
