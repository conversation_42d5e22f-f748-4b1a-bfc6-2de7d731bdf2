import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nextsportz_v2/core/networking/api_client.dart';
import 'package:nextsportz_v2/features/teams/data/datasources/team_invitations_remote_datasource.dart';
import '../../domain/repositories/team_members_repository.dart';
import '../../domain/repositories/team_invitations_repository.dart';
import '../datasources/team_members_datasource.dart';
import '../datasources/team_members_mock_datasource.dart';
import '../datasources/team_invitations_datasource.dart';
import 'team_members_repository_impl.dart';
import 'team_invitations_repository_impl.dart';

// Commented out imports for production use
// import '../../../../core/networking/api_client.dart';
// import '../datasources/team_members_remote_datasource.dart';
// import '../datasources/team_invitations_remote_datasource.dart';

/// Provider for team members data source
/// Switch between remote and mock for testing
final teamMembersDataSourceProvider = Provider<TeamMembersDatasource>((ref) {
  // For development/testing, use mock data source
  // In production, switch to remote data source
  return TeamMembersMockDataSource();

  // Uncomment the following lines to use remote data source
  // final apiClient = ref.read(apiClientProvider);
  // return TeamMembersRemoteDataSource(apiClient);
});

/// Provider for team invitations data source
/// Switch between remote and mock for testing
final teamInvitationsDataSourceProvider = Provider<TeamInvitationsDatasource>((
  ref,
) {
  // For development/testing, use mock data source
  // In production, switch to remote data source
  // return TeamInvitationsMockDataSource();

  // Uncomment the following lines to use remote data source
  final apiClient = ref.read(apiClientProvider);
  return TeamInvitationsRemoteDataSource(apiClient);
});

/// Provider for team members repository
final teamMembersRepositoryProvider = Provider<TeamMembersRepository>((ref) {
  final dataSource = ref.read(teamMembersDataSourceProvider);
  return TeamMembersRepositoryImpl(dataSource);
});

/// Provider for team invitations repository
final teamInvitationsRepositoryProvider = Provider<TeamInvitationsRepository>((
  ref,
) {
  final dataSource = ref.read(teamInvitationsDataSourceProvider);
  return TeamInvitationsRepositoryImpl(dataSource);
});
