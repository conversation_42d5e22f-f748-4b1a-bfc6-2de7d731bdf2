import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/networking/exception.dart';
import '../../domain/entities/team.dart';
import '../../domain/repositories/team_members_repository.dart';
import '../datasources/team_members_datasource.dart';

/// Repository implementation for team members operations
class TeamMembersRepositoryImpl implements TeamMembersRepository {
  final TeamMembersDatasource _dataSource;

  TeamMembersRepositoryImpl(this._dataSource);

  @override
  Future<Either<AppError, List<TeamMember>>> getTeamMembers(String teamId) async {
    try {
      final members = await _dataSource.getTeamMembers(teamId);
      return Right(members);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, TeamMember>> getTeamMember(String teamId, String memberId) async {
    try {
      final member = await _dataSource.getTeamMember(teamId, memberId);
      return Right(member);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> removeMember({
    required String teamId,
    required String memberId,
  }) async {
    try {
      await _dataSource.removeMember(teamId: teamId, memberId: memberId);
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  }) async {
    try {
      await _dataSource.updateMemberRole(
        teamId: teamId,
        memberId: memberId,
        role: role,
      );
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, int>> getTeamMembersCount(String teamId) async {
    try {
      final count = await _dataSource.getTeamMembersCount(teamId);
      return Right(count);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }
}
