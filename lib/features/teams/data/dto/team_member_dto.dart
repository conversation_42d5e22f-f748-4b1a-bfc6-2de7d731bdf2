class TeamMemberDto {
  final String id;
  final String teamId;
  final String userId;
  final String name;
  final String? profileImage;
  final String position;
  final String role;
  final String joinedAt;
  final PlayerStatsDto stats;
  final bool isActive;

  const TeamMemberDto({
    required this.id,
    required this.teamId,
    required this.userId,
    required this.name,
    this.profileImage,
    required this.position,
    required this.role,
    required this.joinedAt,
    required this.stats,
    required this.isActive,
  });

  factory TeamMemberDto.fromJson(Map<String, dynamic> json) {
    return TeamMemberDto(
      id: json['id'] as String,
      teamId: json['team_id'] as String,
      userId: json['user_id'] as String,
      name: json['name'] as String,
      profileImage: json['profile_image'] as String?,
      position: json['position'] as String,
      role: json['role'] as String,
      joinedAt: json['joined_at'] as String,
      stats: PlayerStatsDto.fromJson(json['stats']),
      isActive: json['is_active'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'team_id': teamId,
      'user_id': userId,
      'name': name,
      'profile_image': profileImage,
      'position': position,
      'role': role,
      'joined_at': joinedAt,
      'stats': stats.toJson(),
      'is_active': isActive,
    };
  }
}

class PlayerStatsDto {
  final int matchesPlayed;
  final int goals;
  final int assists;
  final int cleanSheets;
  final double rating;
  final int yellowCards;
  final int redCards;
  final int minutesPlayed;

  const PlayerStatsDto({
    required this.matchesPlayed,
    required this.goals,
    required this.assists,
    required this.cleanSheets,
    required this.rating,
    required this.yellowCards,
    required this.redCards,
    required this.minutesPlayed,
  });

  factory PlayerStatsDto.fromJson(Map<String, dynamic> json) {
    return PlayerStatsDto(
      matchesPlayed: json['matches_played'] as int,
      goals: json['goals'] as int,
      assists: json['assists'] as int,
      cleanSheets: json['clean_sheets'] as int,
      rating: (json['rating'] as num).toDouble(),
      yellowCards: json['yellow_cards'] as int,
      redCards: json['red_cards'] as int,
      minutesPlayed: json['minutes_played'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'matches_played': matchesPlayed,
      'goals': goals,
      'assists': assists,
      'clean_sheets': cleanSheets,
      'rating': rating,
      'yellow_cards': yellowCards,
      'red_cards': redCards,
      'minutes_played': minutesPlayed,
    };
  }
}

class UpdateMemberRoleRequestDto {
  final String role;

  const UpdateMemberRoleRequestDto({
    required this.role,
  });

  Map<String, dynamic> toJson() {
    return {
      'role': role,
    };
  }
}
