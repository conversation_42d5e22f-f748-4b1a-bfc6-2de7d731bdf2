import '../../domain/entities/team.dart';

/// DTO for team search item from API
class TeamSearchItemDto {
  final String id;
  final String name;
  final int membersCount;
  final String winRate;
  final String logoUrl;

  const TeamSearchItemDto({
    required this.id,
    required this.name,
    required this.membersCount,
    required this.winRate,
    required this.logoUrl,
  });

  factory TeamSearchItemDto.fromJson(Map<String, dynamic> json) {
    return TeamSearchItemDto(
      id: json['id'] as String,
      name: json['name'] as String,
      membersCount: json['membersCount'] as int? ?? 0,
      winRate: json['winRate'] as String? ?? '0.00',
      logoUrl: json['logoUrl'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'membersCount': membersCount,
      'winRate': winRate,
      'logoUrl': logoUrl,
    };
  }

  /// Convert DTO to domain entity
  TeamSearchItem toEntity() {
    return TeamSearchItem(
      id: id,
      name: name,
      membersCount: membersCount,
      winRate: winRate,
      logoUrl: logoUrl,
    );
  }

  /// Create DTO from domain entity
  factory TeamSearchItemDto.fromEntity(TeamSearchItem entity) {
    return TeamSearchItemDto(
      id: entity.id,
      name: entity.name,
      membersCount: entity.membersCount,
      winRate: entity.winRate,
      logoUrl: entity.logoUrl,
    );
  }

  TeamSearchItemDto copyWith({
    String? id,
    String? name,
    int? membersCount,
    String? winRate,
    String? logoUrl,
  }) {
    return TeamSearchItemDto(
      id: id ?? this.id,
      name: name ?? this.name,
      membersCount: membersCount ?? this.membersCount,
      winRate: winRate ?? this.winRate,
      logoUrl: logoUrl ?? this.logoUrl,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TeamSearchItemDto &&
        other.id == id &&
        other.name == name &&
        other.membersCount == membersCount &&
        other.winRate == winRate &&
        other.logoUrl == logoUrl;
  }

  @override
  int get hashCode {
    return Object.hash(id, name, membersCount, winRate, logoUrl);
  }

  @override
  String toString() {
    return 'TeamSearchItemDto(id: $id, name: $name, membersCount: $membersCount, winRate: $winRate, logoUrl: $logoUrl)';
  }
}
