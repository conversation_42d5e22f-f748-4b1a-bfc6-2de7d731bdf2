import '../../domain/entities/team_role.dart';

/// DTO for adding team member request based on swagger schema
class AddTeamMemberRequestDto {
  final String playerId;
  final TeamRole role;

  const AddTeamMemberRequestDto({required this.playerId, required this.role});

  factory AddTeamMemberRequestDto.fromJson(Map<String, dynamic> json) {
    return AddTeamMemberRequestDto(
      playerId: json['playerId'] as String,
      role: TeamRole.fromString(json['role'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {'playerId': playerId, 'role': role.value};
  }

  AddTeamMemberRequestDto copyWith({String? playerId, TeamRole? role}) {
    return AddTeamMemberRequestDto(
      playerId: playerId ?? this.playerId,
      role: role ?? this.role,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AddTeamMemberRequestDto &&
        other.playerId == playerId &&
        other.role == role;
  }

  @override
  int get hashCode => playerId.hashCode ^ role.hashCode;

  @override
  String toString() {
    return 'AddTeamMemberRequestDto(playerId: $playerId, role: $role)';
  }
}
