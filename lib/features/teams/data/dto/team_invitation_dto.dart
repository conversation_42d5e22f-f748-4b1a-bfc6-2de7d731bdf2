import '../../../teams/data/dto/team_dto.dart';

class TeamInvitationDto {
  final String id;
  final String invitedTeamId;
  final String invitedTeamName;
  final TeamStatsDto invitedTeamStats;
  final String teamLogoUrl;
  final String status;
  final bool isActive;
  final String invitedAt;

  const TeamInvitationDto({
    required this.id,
    required this.invitedTeamId,
    required this.invitedTeamName,
    required this.invitedTeamStats,
    required this.teamLogoUrl,
    required this.status,
    required this.isActive,
    required this.invitedAt,
  });

  factory TeamInvitationDto.fromJson(Map<String, dynamic> json) {
    return TeamInvitationDto(
      id: json['id'] as String,
      invitedTeamId: json['invitedTeamId'] as String,
      invitedTeamName: json['invitedTeamName'] as String,
      invitedTeamStats: TeamStatsDto.fromJson(
        json['invitedTeamStats'] as Map<String, dynamic>,
      ),
      teamLogoUrl: json['teamLogoUrl'] as String? ?? '',
      status: json['status'] as String,
      isActive: json['isActive'] as bool,
      invitedAt: json['invitedAt'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invitedTeamId': invitedTeamId,
      'invitedTeamName': invitedTeamName,
      'invitedTeamStats': invitedTeamStats.toJson(),
      'teamLogoUrl': teamLogoUrl,
      'status': status,
      'isActive': isActive,
      'invitedAt': invitedAt,
    };
  }
}

class InvitationResponseRequestDto {
  final String action; // 'accept' or 'decline'

  const InvitationResponseRequestDto({required this.action});

  Map<String, dynamic> toJson() {
    return {'action': action};
  }
}
