class CreateTeamRequestDto {
  final String? id;
  final String name;
  final String description;
  final String? logoUrl;
  final String? slogan;

  const CreateTeamRequestDto({
    this.id,
    required this.name,
    required this.description,
    this.logoUrl,
    this.slogan,
  });

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'description': description,
      if (logoUrl != null) 'logoUrl': logoUrl,
      if (slogan != null) 'slogan': slogan,
    };
  }
}

class UpdateTeamRequestDto {
  final String? name;
  final String? description;
  final String? logoUrl;
  final String? slogan;

  const UpdateTeamRequestDto({
    this.name,
    this.description,
    this.logoUrl,
    this.slogan,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (name != null) data['name'] = name;
    if (description != null) data['description'] = description;
    if (logoUrl != null) data['logoUrl'] = logoUrl;
    if (slogan != null) data['slogan'] = slogan;
    return data;
  }
}

class InvitePlayerRequestDto {
  final String playerId;
  final String role;

  const InvitePlayerRequestDto({required this.playerId, required this.role});

  Map<String, dynamic> toJson() {
    return {'playerId': playerId, 'role': role};
  }
}
