class TeamDto {
  final String id;
  final String teamCode;
  final String name;
  final String description;
  final String? slogan;
  final String logoUrl;
  final bool isActive;
  final List<TeamMemberDto> members;
  final String created;
  final TeamStatsDto stats;

  const TeamDto({
    required this.id,
    required this.teamCode,
    required this.name,
    required this.description,
    this.slogan,
    required this.logoUrl,
    required this.isActive,
    required this.members,
    required this.created,
    required this.stats,
  });

  factory TeamDto.fromJson(Map<String, dynamic> json) {
    return TeamDto(
      id: json['id'] as String,
      teamCode: json['teamCode'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      slogan: json['slogan'] as String?,
      logoUrl: json['logoUrl'] as String? ?? '',
      isActive: true,
      members:
          (json['members'] as List<dynamic>?)
              ?.map((memberJson) => TeamMemberDto.fromJson(memberJson))
              .toList() ??
          [],
      created: json['created'] as String,
      stats: TeamStatsDto.fromJson(json['stats']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'teamCode': teamCode,
      'name': name,
      'description': description,
      'slogan': slogan,
      'logoUrl': logoUrl,
      'isActive': isActive,
      'members': members.map((member) => member.toJson()).toList(),
      'created': created,
      'stats': stats.toJson(),
    };
  }
}

class TeamMemberDto {
  final String id;
  final String userId;
  final String userName;
  final String playerId;
  final String playerName;
  final String role;
  final String joinedAt;
  final bool isActive;

  const TeamMemberDto({
    required this.id,
    required this.userId,
    required this.userName,
    required this.playerId,
    required this.playerName,
    required this.role,
    required this.joinedAt,
    required this.isActive,
  });

  factory TeamMemberDto.fromJson(Map<String, dynamic> json) {
    return TeamMemberDto(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      playerId: json['playerId'] as String,
      playerName: json['playerName'] as String,
      role: json['role'] as String,
      joinedAt: json['joinedAt'] as String,
      isActive: json['isActive'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'playerId': playerId,
      'playerName': playerName,
      'role': role,
      'joinedAt': joinedAt,
      'isActive': isActive,
    };
  }
}

class TeamStatsDto {
  final int wins;
  final int losses;
  final int draws;
  final int scoredGoals;
  final int concededGoals;

  const TeamStatsDto({
    required this.wins,
    required this.losses,
    required this.draws,
    required this.scoredGoals,
    required this.concededGoals,
  });

  factory TeamStatsDto.fromJson(Map<String, dynamic> json) {
    return TeamStatsDto(
      wins: json['wins'] as int,
      losses: json['losses'] as int,
      draws: json['draws'] as int,
      scoredGoals: json['scoredGoals'] as int,
      concededGoals: json['concededGoals'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wins': wins,
      'losses': losses,
      'draws': draws,
      'scoredGoals': scoredGoals,
      'concededGoals': concededGoals,
    };
  }
}
