import '../../domain/entities/team.dart';
import '../../domain/entities/team_role.dart';

/// Abstract interface for team invitations data operations
abstract interface class TeamInvitationsDatasource {
  /// Get all pending invitations for the current user
  Future<List<TeamInvitation>> getPendingInvitations();

  /// Get invitations for a specific team
  Future<List<TeamInvitation>> getTeamInvitations(String teamId);

  /// Get a specific invitation by ID
  Future<TeamInvitation> getInvitation(String invitationId);

  /// Invite a player to a team
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    String role = 'Player',
    String? message,
  });

  /// Accept an invitation
  Future<void> acceptInvitation({
    required String teamId,
    required String invitationId,
  });

  /// Decline an invitation
  Future<void> declineInvitation({
    required String teamId,
    required String invitationId,
  });

  /// Get pending invitations count for a team
  Future<int> getTeamInvitationsCount(String teamId);

  // New methods based on swagger endpoints

  /// Invite team members using the new endpoint
  Future<void> inviteTeamMember({
    required String teamId,
    required String playerId,
    required TeamRole role,
  });

  /// Delete team invite
  Future<void> deleteTeamInvite({
    required String teamId,
    required String memberId,
  });

  /// Accept team invite
  Future<void> acceptTeamInvite({
    required String teamId,
    required String memberId,
  });
}
