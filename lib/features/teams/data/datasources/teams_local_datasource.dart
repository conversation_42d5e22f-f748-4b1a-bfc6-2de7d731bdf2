import 'dart:convert';
import '../../domain/entities/team.dart';
import '../../../../core/local/key_value_storage.dart';

// Adapter to bridge the interface mismatch
class KeyValueStorageAdapter {
  final KeyValueStorageService _storage;

  KeyValueStorageAdapter(this._storage);

  Future<String?> getString(String key) async {
    return _storage.getValue<String>(key);
  }

  Future<void> setString(String key, String value) async {
    await _storage.setKeyValue<String>(key, value);
  }

  Future<void> remove(String key) async {
    await _storage.removeKey(key);
  }
}

abstract class TeamsLocalDataSource {
  Future<List<Team>> getMyTeams();
  Future<Team?> getTeamById(String teamId);
  Future<void> saveTeam(Team team);
  Future<void> saveTeams(List<Team> teams);
  Future<void> deleteTeam(String teamId);
  Future<List<TeamInvitation>> getPendingInvitations();
  Future<void> saveInvitation(TeamInvitation invitation);
  Future<void> deleteInvitation(String invitationId);
}

class TeamsLocalDataSourceImpl implements TeamsLocalDataSource {
  final KeyValueStorageAdapter _storage;
  static const String _teamsKey = 'my_teams';
  static const String _invitationsKey = 'pending_invitations';

  TeamsLocalDataSourceImpl(this._storage);

  @override
  Future<List<Team>> getMyTeams() async {
    try {
      final teamsJson = await _storage.getString(_teamsKey);
      if (teamsJson == null) return [];

      final List<dynamic> teamsList = json.decode(teamsJson);
      return teamsList.map((json) => Team.fromJson(json)).toList();
    } catch (e) {
      // Return empty list if there's an error
      return [];
    }
  }

  @override
  Future<Team?> getTeamById(String teamId) async {
    try {
      final teams = await getMyTeams();
      return teams.firstWhere((team) => team.id == teamId);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> saveTeam(Team team) async {
    try {
      final teams = await getMyTeams();
      final existingIndex = teams.indexWhere((t) => t.id == team.id);

      if (existingIndex >= 0) {
        teams[existingIndex] = team;
      } else {
        teams.add(team);
      }

      await saveTeams(teams);
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  @override
  Future<void> saveTeams(List<Team> teams) async {
    try {
      final teamsJson = json.encode(
        teams.map((team) => team.toJson()).toList(),
      );
      await _storage.setString(_teamsKey, teamsJson);
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  @override
  Future<void> deleteTeam(String teamId) async {
    try {
      final teams = await getMyTeams();
      teams.removeWhere((team) => team.id == teamId);
      await saveTeams(teams);
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  @override
  Future<List<TeamInvitation>> getPendingInvitations() async {
    try {
      final invitationsJson = await _storage.getString(_invitationsKey);
      if (invitationsJson == null) return [];

      final List<dynamic> invitationsList = json.decode(invitationsJson);
      return invitationsList
          .map((json) => TeamInvitation.fromJson(json))
          .toList();
    } catch (e) {
      // Return empty list if there's an error
      return [];
    }
  }

  @override
  Future<void> saveInvitation(TeamInvitation invitation) async {
    try {
      final invitations = await getPendingInvitations();
      final existingIndex = invitations.indexWhere(
        (inv) => inv.id == invitation.id,
      );

      if (existingIndex >= 0) {
        invitations[existingIndex] = invitation;
      } else {
        invitations.add(invitation);
      }

      final invitationsJson = json.encode(
        invitations.map((inv) => inv.toJson()).toList(),
      );
      await _storage.setString(_invitationsKey, invitationsJson);
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  @override
  Future<void> deleteInvitation(String invitationId) async {
    try {
      final invitations = await getPendingInvitations();
      invitations.removeWhere((invitation) => invitation.id == invitationId);

      final invitationsJson = json.encode(
        invitations.map((inv) => inv.toJson()).toList(),
      );
      await _storage.setString(_invitationsKey, invitationsJson);
    } catch (e) {
      // Handle error
      rethrow;
    }
  }
}
