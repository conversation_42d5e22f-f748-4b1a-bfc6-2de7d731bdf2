import '../../domain/entities/team.dart';

/// Abstract interface for team members data operations
abstract interface class TeamMembersDatasource {
  /// Get all members of a specific team
  Future<List<TeamMember>> getTeamMembers(String teamId);

  /// Get a specific team member by ID
  Future<TeamMember> getTeamMember(String teamId, String memberId);

  /// Remove a member from a team
  Future<void> removeMember({
    required String teamId,
    required String memberId,
  });

  /// Update a member's role in a team
  Future<void> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  });

  /// Get members count for a team
  Future<int> getTeamMembersCount(String teamId);
}
