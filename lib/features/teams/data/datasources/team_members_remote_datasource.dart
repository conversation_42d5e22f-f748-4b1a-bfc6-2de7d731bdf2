import '../../domain/entities/team.dart';
import '../../../../core/networking/api_client.dart';
import '../../../../core/networking/api_const.dart';
import '../dto/team_member_dto.dart';
import '../dto/team_request_dto.dart';
import 'team_members_datasource.dart';

/// Remote data source implementation for team members operations
class TeamMembersRemoteDataSource implements TeamMembersDatasource {
  final ApiClient apiClient;

  TeamMembersRemoteDataSource(this.apiClient);

  @override
  Future<List<TeamMember>> getTeamMembers(String teamId) async {
    final endpoint = ApiConst.teamMembersEndpoint.replaceAll(
      '{teamId}',
      teamId,
    );
    final response = await apiClient.get(endpoint);
    final List<dynamic> data = response['data'] ?? response;
    return data.map((json) {
      final dto = TeamMemberDto.fromJson(json);
      return _mapMemberDtoToEntity(dto);
    }).toList();
  }

  @override
  Future<TeamMember> getTeamMember(String teamId, String memberId) async {
    final endpoint = ApiConst.teamMemberDetailEndpoint
        .replaceAll('{teamId}', teamId)
        .replaceAll('{memberId}', memberId);
    final response = await apiClient.get(endpoint);
    final dto = TeamMemberDto.fromJson(response);
    return _mapMemberDtoToEntity(dto);
  }

  @override
  Future<void> removeMember({
    required String teamId,
    required String memberId,
  }) async {
    final endpoint = ApiConst.teamMemberDetailEndpoint
        .replaceAll('{teamId}', teamId)
        .replaceAll('{memberId}', memberId);
    await apiClient.delete(endpoint);
  }

  @override
  Future<void> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  }) async {
    final endpoint = ApiConst.teamMemberRoleEndpoint
        .replaceAll('{teamId}', teamId)
        .replaceAll('{memberId}', memberId);

    final requestDto = UpdateMemberRoleRequestDto(role: role);
    await apiClient.put(endpoint, data: requestDto.toJson());
  }

  @override
  Future<int> getTeamMembersCount(String teamId) async {
    final endpoint =
        '${ApiConst.teamMembersEndpoint.replaceAll('{teamId}', teamId)}/count';
    final response = await apiClient.get(endpoint);
    return response['count'] as int? ?? 0;
  }

  // Helper method to map DTO to entity
  TeamMember _mapMemberDtoToEntity(TeamMemberDto dto) {
    return TeamMember(
      id: dto.id,
      userId: dto.userId,
      userName: dto.name,
      playerId: dto.id, // Use member id as player id for now
      playerName: dto.name,
      role: dto.role,
      joinedAt: DateTime.parse(dto.joinedAt),
      isActive: dto.isActive,
    );
  }
}
