import '../../domain/entities/team.dart';
import 'team_members_datasource.dart';

/// Mock data source implementation for team members operations
class TeamMembersMockDataSource implements TeamMembersDatasource {
  static final Map<String, List<TeamMember>> _mockTeamMembers = {
    '1': [
      TeamMember(
        id: 'member1',
        userId: 'user1',
        userName: '<PERSON>',
        playerId: 'player1',
        playerName: '<PERSON>',
        role: 'captain',
        joinedAt: DateTime.now().subtract(const Duration(days: 30)),
        isActive: true,
      ),
      TeamMember(
        id: 'member2',
        userId: 'user2',
        userName: '<PERSON>',
        playerId: 'player2',
        playerName: '<PERSON>',
        role: 'vice_captain',
        joinedAt: DateTime.now().subtract(const Duration(days: 25)),
        isActive: true,
      ),
      TeamMember(
        id: 'member3',
        userId: 'user3',
        userName: '<PERSON>',
        playerId: 'player3',
        playerName: '<PERSON>',
        role: 'member',
        joinedAt: DateTime.now().subtract(const Duration(days: 20)),
        isActive: true,
      ),
    ],
    '2': [
      TeamM<PERSON>ber(
        id: 'member4',
        userId: 'user4',
        userName: '<PERSON> <PERSON>',
        playerId: 'player4',
        playerName: 'Sarah <PERSON>',
        role: 'captain',
        joinedAt: DateTime.now().subtract(const Duration(days: 35)),
        isActive: true,
      ),
      TeamMember(
        id: 'member5',
        userId: 'user5',
        userName: 'Tom Brown',
        playerId: 'player5',
        playerName: 'Tom Brown',
        role: 'member',
        joinedAt: DateTime.now().subtract(const Duration(days: 15)),
        isActive: true,
      ),
    ],
  };

  @override
  Future<List<TeamMember>> getTeamMembers(String teamId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));
    return _mockTeamMembers[teamId] ?? [];
  }

  @override
  Future<TeamMember> getTeamMember(String teamId, String memberId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 200));

    final members = _mockTeamMembers[teamId] ?? [];
    final member = members.firstWhere(
      (m) => m.id == memberId,
      orElse: () => throw Exception('Team member not found'),
    );
    return member;
  }

  @override
  Future<void> removeMember({
    required String teamId,
    required String memberId,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 250));

    final members = _mockTeamMembers[teamId];
    if (members != null) {
      members.removeWhere((member) => member.id == memberId);
    }
  }

  @override
  Future<void> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 250));

    final members = _mockTeamMembers[teamId];
    if (members != null) {
      final memberIndex = members.indexWhere((m) => m.id == memberId);
      if (memberIndex != -1) {
        final member = members[memberIndex];
        members[memberIndex] = TeamMember(
          id: member.id,
          userId: member.userId,
          userName: member.userName,
          playerId: member.playerId,
          playerName: member.playerName,
          role: role,
          joinedAt: member.joinedAt,
          isActive: member.isActive,
        );
      }
    }
  }

  @override
  Future<int> getTeamMembersCount(String teamId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 150));
    return _mockTeamMembers[teamId]?.length ?? 0;
  }
}
