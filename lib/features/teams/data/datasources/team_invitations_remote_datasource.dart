import '../../domain/entities/team.dart';
import '../../../../core/networking/api_client.dart';
import '../../../../core/networking/api_const.dart';
import '../dto/team_invitation_dto.dart';
import '../dto/team_request_dto.dart';
import '../dto/add_team_member_request_dto.dart';
import '../../domain/entities/team_role.dart';
import 'team_invitations_datasource.dart';

/// Remote data source implementation for team invitations operations
class TeamInvitationsRemoteDataSource implements TeamInvitationsDatasource {
  final ApiClient apiClient;

  TeamInvitationsRemoteDataSource(this.apiClient);

  @override
  Future<List<TeamInvitation>> getPendingInvitations() async {
    try {
      final response = await apiClient.get(ApiConst.teamInvitationsEndpoint);
      final List<dynamic> data = response['data'] ?? response;
      return data.map((json) {
        // Parse the JSON directly to TeamInvitation since the API response
        // matches our entity structure
        return TeamInvitation.fromJson(json as Map<String, dynamic>);
      }).toList();
    } catch (e, stack) {
      print('Error fetching pending invitations: $e');
      print('Stack trace: $stack');
      rethrow;
    }
  }

  @override
  Future<List<TeamInvitation>> getTeamInvitations(String teamId) async {
    final endpoint = '${ApiConst.teamInvitationsEndpoint}?team_id=$teamId';
    final response = await apiClient.get(endpoint);
    final List<dynamic> data = response['data'] ?? response;
    return data.map((json) {
      final dto = TeamInvitationDto.fromJson(json);
      return _mapInvitationDtoToEntity(dto);
    }).toList();
  }

  @override
  Future<TeamInvitation> getInvitation(String invitationId) async {
    final endpoint = ApiConst.teamInvitationDetailEndpoint.replaceAll(
      '{invitationId}',
      invitationId,
    );
    final response = await apiClient.get(endpoint);
    final dto = TeamInvitationDto.fromJson(response);
    return _mapInvitationDtoToEntity(dto);
  }

  @override
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    String role = 'Player',
    String? message,
  }) async {
    final endpoint = ApiConst.teamInvitePlayerEndpoint.replaceAll(
      '{teamId}',
      teamId,
    );
    final requestDto = InvitePlayerRequestDto(playerId: playerId, role: role);

    await apiClient.post(endpoint, data: requestDto.toJson());
  }

  @override
  Future<void> acceptInvitation({
    required String teamId,
    required String invitationId,
  }) async {
    final endpoint = ApiConst.teamInvitationAcceptEndpoint
        .replaceAll('{teamId}', teamId)
        .replaceAll('{invitationId}', invitationId);

    await apiClient.post(endpoint);
  }

  @override
  Future<void> declineInvitation({
    required String teamId,
    required String invitationId,
  }) async {
    final endpoint = ApiConst.teamInvitationDeclineEndpoint
        .replaceAll('{teamId}', teamId)
        .replaceAll('{invitationId}', invitationId);

    await apiClient.post(endpoint);
  }

  @override
  Future<int> getTeamInvitationsCount(String teamId) async {
    final endpoint =
        '${ApiConst.teamInvitationsEndpoint}/count?team_id=$teamId';
    final response = await apiClient.get(endpoint);
    return response['count'] as int? ?? 0;
  }

  @override
  Future<void> inviteTeamMember({
    required String teamId,
    required String playerId,
    required TeamRole role,
  }) async {
    final endpoint = ApiConst.teamInvitePlayerEndpoint.replaceAll(
      '{teamId}',
      teamId,
    );
    final requestDto = AddTeamMemberRequestDto(playerId: playerId, role: role);

    await apiClient.post(endpoint, data: requestDto.toJson());
  }

  @override
  Future<void> deleteTeamInvite({
    required String teamId,
    required String memberId,
  }) async {
    // Note: The swagger shows DELETE /api/teams/invitations/{invitationId}
    // but the interface expects teamId and memberId. This might need clarification.
    // For now, using the memberId as invitationId since they might be the same.
    final endpoint = ApiConst.teamInvitationDeleteEndpoint.replaceAll(
      '{invitationId}',
      memberId,
    );

    await apiClient.delete(endpoint);
  }

  @override
  Future<void> acceptTeamInvite({
    required String teamId,
    required String memberId,
  }) async {
    // Note: Using memberId as invitationId since they might be the same.
    final endpoint = ApiConst.teamInvitationAcceptEndpoint
        .replaceAll('{teamId}', teamId)
        .replaceAll('{invitationId}', memberId);

    await apiClient.post(endpoint);
  }

  // Helper method to map DTO to entity
  TeamInvitation _mapInvitationDtoToEntity(TeamInvitationDto dto) {
    return TeamInvitation(
      id: dto.id,
      invitedTeamId: dto.invitedTeamId,
      invitedTeamName: dto.invitedTeamName,
      invitedTeamStats: TeamStats(
        wins: dto.invitedTeamStats.wins,
        losses: dto.invitedTeamStats.losses,
        draws: dto.invitedTeamStats.draws,
        scoredGoals: dto.invitedTeamStats.scoredGoals,
        concededGoals: dto.invitedTeamStats.concededGoals,
      ),
      teamLogoUrl: dto.teamLogoUrl,
      status: dto.status,
      isActive: dto.isActive,
      invitedAt: DateTime.parse(dto.invitedAt),
    );
  }
}
