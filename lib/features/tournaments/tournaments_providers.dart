import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'data/datasources/tournament_datasource.dart';
import 'data/repositories/tournament_repository_impl.dart';
import 'domain/repositories/tournament_repository.dart';
import 'domain/usecases/tournament_usecases.dart';
import 'presentation/logic/tournament_state.dart';
import 'presentation/logic/tournament_notifiers.dart';

// Data Layer Providers
final tournamentDataSourceProvider = Provider<TournamentDataSource>((ref) {
  return TournamentDataSourceImpl();
});

final tournamentRepositoryProvider = Provider<TournamentRepository>((ref) {
  final dataSource = ref.watch(tournamentDataSourceProvider);
  return TournamentRepositoryImpl(dataSource);
});

// Use Case Providers
final getTournamentsUseCaseProvider = Provider<GetTournamentsUseCase>((ref) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return GetTournamentsUseCase(repository);
});

final getTournamentDetailsUseCaseProvider =
    Provider<GetTournamentDetailsUseCase>((ref) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return GetTournamentDetailsUseCase(repository);
});

final joinTournamentUseCaseProvider = Provider<JoinTournamentUseCase>((ref) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return JoinTournamentUseCase(repository);
});

final leaveTournamentUseCaseProvider = Provider<LeaveTournamentUseCase>((ref) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return LeaveTournamentUseCase(repository);
});

final searchTournamentsUseCaseProvider = Provider<SearchTournamentsUseCase>((
  ref,
) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return SearchTournamentsUseCase(repository);
});

final getFeaturedTournamentsUseCaseProvider =
    Provider<GetFeaturedTournamentsUseCase>((ref) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return GetFeaturedTournamentsUseCase(repository);
});

final getNearbyTournamentsUseCaseProvider =
    Provider<GetNearbyTournamentsUseCase>((ref) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return GetNearbyTournamentsUseCase(repository);
});

final getFavouriteTournamentsUseCaseProvider =
    Provider<GetFavouriteTournamentsUseCase>((ref) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return GetFavouriteTournamentsUseCase(repository);
});

final addToFavouritesUseCaseProvider = Provider<AddToFavouritesUseCase>((ref) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return AddToFavouritesUseCase(repository);
});

final removeFromFavouritesUseCaseProvider =
    Provider<RemoveFromFavouritesUseCase>((ref) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return RemoveFromFavouritesUseCase(repository);
});

final getPlayerBadgesUseCaseProvider = Provider<GetPlayerBadgesUseCase>((ref) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return GetPlayerBadgesUseCase(repository);
});

final getPlayerTournamentStatsUseCaseProvider =
    Provider<GetPlayerTournamentStatsUseCase>((ref) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return GetPlayerTournamentStatsUseCase(repository);
});

final checkForNewBadgesUseCaseProvider = Provider<CheckForNewBadgesUseCase>((
  ref,
) {
  final repository = ref.watch(tournamentRepositoryProvider);
  return CheckForNewBadgesUseCase(repository);
});

// State Management Providers

// Tournament List State
final tournamentListStateProvider =
    StateNotifierProvider<TournamentListNotifier, TournamentListState>((ref) {
  final getTournamentsUseCase = ref.watch(getTournamentsUseCaseProvider);
  final searchTournamentsUseCase = ref.watch(
    searchTournamentsUseCaseProvider,
  );
  final getFeaturedTournamentsUseCase = ref.watch(
    getFeaturedTournamentsUseCaseProvider,
  );
  final getNearbyTournamentsUseCase = ref.watch(
    getNearbyTournamentsUseCaseProvider,
  );

  return TournamentListNotifier(
    getTournamentsUseCase: getTournamentsUseCase,
    searchTournamentsUseCase: searchTournamentsUseCase,
    getFeaturedTournamentsUseCase: getFeaturedTournamentsUseCase,
    getNearbyTournamentsUseCase: getNearbyTournamentsUseCase,
  );
});

// Tournament Details State
final tournamentDetailsStateProvider = StateNotifierProvider.family<
    TournamentDetailsNotifier,
    TournamentDetailsState,
    String>((ref, tournamentId) {
  final getTournamentDetailsUseCase = ref.watch(
    getTournamentDetailsUseCaseProvider,
  );
  final joinTournamentUseCase = ref.watch(joinTournamentUseCaseProvider);
  final leaveTournamentUseCase = ref.watch(leaveTournamentUseCaseProvider);
  final addToFavouritesUseCase = ref.watch(addToFavouritesUseCaseProvider);
  final removeFromFavouritesUseCase = ref.watch(
    removeFromFavouritesUseCaseProvider,
  );

  return TournamentDetailsNotifier(
    tournamentId: tournamentId,
    currentPlayerId: 'current_user', // TODO: Get from auth provider
    getTournamentDetailsUseCase: getTournamentDetailsUseCase,
    joinTournamentUseCase: joinTournamentUseCase,
    leaveTournamentUseCase: leaveTournamentUseCase,
    addToFavouritesUseCase: addToFavouritesUseCase,
    removeFromFavouritesUseCase: removeFromFavouritesUseCase,
  );
});

// Favourite Tournaments State
final favouriteTournamentsStateProvider = StateNotifierProvider<
    FavouriteTournamentsNotifier, FavouriteTournamentsState>((ref) {
  final getFavouriteTournamentsUseCase = ref.watch(
    getFavouriteTournamentsUseCaseProvider,
  );
  final removeFromFavouritesUseCase = ref.watch(
    removeFromFavouritesUseCaseProvider,
  );

  return FavouriteTournamentsNotifier(
    playerId: 'current_user', // TODO: Get from auth provider
    getFavouriteTournamentsUseCase: getFavouriteTournamentsUseCase,
    removeFromFavouritesUseCase: removeFromFavouritesUseCase,
  );
});

// Badge System State
final badgeSystemStateProvider =
    StateNotifierProvider<BadgeSystemNotifier, BadgeSystemState>((ref) {
  final getPlayerBadgesUseCase = ref.watch(getPlayerBadgesUseCaseProvider);
  final getPlayerTournamentStatsUseCase = ref.watch(
    getPlayerTournamentStatsUseCaseProvider,
  );
  final checkForNewBadgesUseCase = ref.watch(
    checkForNewBadgesUseCaseProvider,
  );

  return BadgeSystemNotifier(
    playerId: 'current_user', // TODO: Get from auth provider
    getPlayerBadgesUseCase: getPlayerBadgesUseCase,
    getPlayerTournamentStatsUseCase: getPlayerTournamentStatsUseCase,
    checkForNewBadgesUseCase: checkForNewBadgesUseCase,
  );
});

// Filter State
final tournamentFilterStateProvider =
    StateNotifierProvider<TournamentFilterNotifier, TournamentFilterState>((
  ref,
) {
  return TournamentFilterNotifier();
});

// Search State
final tournamentSearchStateProvider =
    StateNotifierProvider<TournamentSearchNotifier, TournamentSearchState>(
        (ref) {
  final searchTournamentsUseCase = ref.watch(searchTournamentsUseCaseProvider);

  return TournamentSearchNotifier(searchTournamentsUseCase);
});

// Search Notifier (additional for search functionality)
class TournamentSearchNotifier extends StateNotifier<TournamentSearchState> {
  final SearchTournamentsUseCase searchTournamentsUseCase;

  TournamentSearchNotifier(this.searchTournamentsUseCase)
      : super(const TournamentSearchState.initial());

  Future<void> search(
    String searchTerm, {
    int limit = 20,
    int offset = 0,
  }) async {
    if (searchTerm.trim().isEmpty) {
      state = const TournamentSearchState.initial();
      return;
    }

    state = const TournamentSearchState.searching();

    final result = await searchTournamentsUseCase(
      SearchTournamentsParams(
        searchTerm: searchTerm,
        limit: limit,
        offset: offset,
      ),
    );

    result.fold(
      (error) => state = TournamentSearchState.error(error.message),
      (tournaments) => state = TournamentSearchState.loaded(
        results: tournaments,
        searchTerm: searchTerm,
        hasMore: tournaments.length >= limit,
      ),
    );
  }

  Future<void> loadMore(String searchTerm, int currentCount) async {
    final currentState = state;
    if (currentState is! TournamentSearchState) return;

    // Check if it's the loaded state and has more data
    if (currentState.maybeWhen(
      loaded: (results, searchTerm, hasMore) => hasMore,
      orElse: () => false,
    )) {
      final result = await searchTournamentsUseCase(
        SearchTournamentsParams(
          searchTerm: searchTerm,
          limit: 20,
          offset: currentCount,
        ),
      );

      result.fold(
        (error) => state = TournamentSearchState.error(error.message),
        (tournaments) {
          currentState.when(
            loaded: (currentResults, currentSearchTerm, currentHasMore) {
              state = TournamentSearchState.loaded(
                results: [...currentResults, ...tournaments],
                searchTerm: searchTerm,
                hasMore: tournaments.length >= 20,
              );
            },
            initial: () {},
            searching: () {},
            error: (message) {},
          );
        },
      );
    }
  }

  void clearSearch() {
    state = const TournamentSearchState.initial();
  }
}

// Quick Access Providers for UI

// Featured tournaments for home screen
final featuredTournamentsProvider = FutureProvider.autoDispose<List<dynamic>>((
  ref,
) async {
  final repository = ref.watch(tournamentRepositoryProvider);
  final result = await repository.getFeaturedTournaments(limit: 5);

  return result.fold((error) => throw error, (tournaments) => tournaments);
});

// Nearby tournaments (requires location permission)
final nearbyTournamentsProvider = FutureProvider.autoDispose
    .family<List<dynamic>, Map<String, double>>((ref, locationData) async {
  final repository = ref.watch(tournamentRepositoryProvider);
  final result = await repository.getNearbyTournaments(
    latitude: locationData['latitude']!,
    longitude: locationData['longitude']!,
    radiusKm: locationData['radius'] ?? 50.0,
  );

  return result.fold((error) => throw error, (tournaments) => tournaments);
});

// Player's favorite tournaments
final playerFavouriteTournamentsProvider =
    FutureProvider.autoDispose<List<dynamic>>((ref) async {
  final repository = ref.watch(tournamentRepositoryProvider);
  const playerId = 'current_user'; // TODO: Get from auth

  final result = await repository.getFavouriteTournaments(playerId);

  return result.fold((error) => throw error, (favourites) => favourites);
});

// Player badge progress
final playerBadgeProgressProvider = FutureProvider.autoDispose<dynamic>((
  ref,
) async {
  final repository = ref.watch(tournamentRepositoryProvider);
  const playerId = 'current_user'; // TODO: Get from auth

  final result = await repository.getPlayerBadges(playerId);

  return result.fold((error) => throw error, (badges) => badges);
});

// Tournament stats for gamification
final playerTournamentStatsProvider = FutureProvider.autoDispose<dynamic>((
  ref,
) async {
  final repository = ref.watch(tournamentRepositoryProvider);
  const playerId = 'current_user'; // TODO: Get from auth

  final result = await repository.getPlayerTournamentStats(playerId);

  return result.fold((error) => throw error, (stats) => stats);
});

// Popular locations for filters
final popularLocationsProvider = FutureProvider.autoDispose<List<String>>((
  ref,
) async {
  final repository = ref.watch(tournamentRepositoryProvider);
  final result = await repository.getPopularLocations();

  return result.fold((error) => throw error, (locations) => locations);
});

// Popular organizers for filters
final popularOrganizersProvider = FutureProvider.autoDispose<List<String>>((
  ref,
) async {
  final repository = ref.watch(tournamentRepositoryProvider);
  final result = await repository.getPopularOrganizers();

  return result.fold((error) => throw error, (organizers) => organizers);
});
