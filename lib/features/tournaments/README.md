# Tournament Module

A comprehensive tournament management module for the NextSportz football player app, built with Flutter and following clean architecture principles.

## 🏆 Features

### Core Features

- **Tournament Listing**: Browse available tournaments with infinite scroll and pull-to-refresh
- **Tournament Details**: Comprehensive tournament information with Google Maps integration
- **Advanced Filtering**: Filter by format, location, date range, entry fee, prizes, and more
- **Search Functionality**: Smart search with suggestions and quick filters
- **Favorites System**: Mark tournaments as favorites with local storage
- **Gamification**: XP system, badges, and achievement tracking

### UI/UX Features

- **Responsive Design**: Optimized for phones and tablets
- **Smooth Animations**: Card animations, hero transitions, and loading states
- **Modern Material Design**: Following Material 3 design principles
- **Dark/Light Theme Support**: Consistent with app theming
- **Accessibility**: Screen reader support and proper contrast ratios

## 🏗️ Architecture

The module follows **Clean Architecture** principles with clear separation of concerns:

```
lib/features/tournaments/
├── data/
│   ├── datasources/           # Data sources (API, local storage)
│   └── repositories/          # Repository implementations
├── domain/
│   ├── entities/              # Business logic entities
│   ├── repositories/          # Repository interfaces
│   └── usecases/             # Business use cases
└── presentation/
    ├── logic/                # State management (Riverpod)
    ├── screens/              # UI screens
    ├── widgets/              # Reusable UI components
    └── routes/               # Navigation routes
```

## 📱 Screens

### 1. Tournament List Screen

- **Path**: `/tournaments`
- **Features**:
  - Tabbed view (All, Featured, Nearby)
  - Infinite scroll with pagination
  - Pull-to-refresh
  - Floating filter button
  - Search integration

### 2. Tournament Details Screen

- **Path**: `/tournaments/:id`
- **Features**:
  - Hero animation from list
  - Tabbed content (Overview, Participants, Location)
  - Google Maps integration
  - Join/Leave tournament functionality
  - Favorite toggle with animation

### 3. Search Screen

- **Path**: `/tournaments/search`
- **Features**:
  - Real-time search with debouncing
  - Popular search suggestions
  - Quick search buttons
  - Search history

### 4. Filter Screen

- **Path**: `/tournaments/filter`
- **Features**:
  - Tournament format selection
  - Location multi-select
  - Date range picker
  - Price range sliders
  - Sort options
  - Active filter count

### 5. Favorites Screen

- **Path**: `/tournaments/favourites`
- **Features**:
  - Personal favorites list
  - Remove from favorites
  - Share functionality
  - Empty state with CTA

## 🎮 Gamification System

### Badges

- **Tournament Newbie**: First tournament joined
- **Road Warrior**: Tournaments in 3+ different cities
- **Big Spender**: High entry fee tournaments
- **Consistent Player**: 10 tournaments joined
- **Tournament Master**: 50 tournaments joined

### XP System

- Gain XP for joining tournaments
- Level progression based on total XP
- Badge unlock animations with confetti
- Progress tracking and statistics

## 🔧 Technical Implementation

### State Management

- **Riverpod** for dependency injection and state management
- Separate providers for different features
- Proper error handling and loading states

### Data Layer

- **Repository Pattern** for data abstraction
- **Mock Data Source** for development and testing
- **Freezed** for immutable data classes
- **JSON Serialization** for API integration

### Testing

- **Unit Tests**: Entity logic, filters, and use cases
- **Widget Tests**: UI components and user interactions
- **Integration Tests**: End-to-end user flows
- **Mock Data**: Comprehensive test data sets

### Performance

- **Infinite Scroll**: Efficient pagination
- **Image Caching**: Cached network images
- **Lazy Loading**: On-demand data loading
- **Animation Optimization**: Smooth 60fps animations

## 🚀 Getting Started

### Prerequisites

```yaml
dependencies:
  flutter_riverpod: ^2.6.1
  freezed: ^2.6.0
  json_annotation: ^4.9.0
  google_maps_flutter: ^2.12.3
  cached_network_image: ^3.4.1
  go_router: ^14.8.0
  flutter_screenutil: ^5.9.0
  confetti: ^0.7.0

dev_dependencies:
  build_runner: ^2.4.12
  json_serializable: ^6.8.0
  flutter_test:
    sdk: flutter
  mocktail: ^1.0.4
```

### Installation

1. **Add to your app's route configuration**:

```dart
import 'package:nextsportz_v2/features/tournaments/presentation/routes/tournament_routes.dart';

final router = GoRouter(
  routes: [
    ...TournamentRoutes.getRoutes(),
    // ... other routes
  ],
);
```

2. **Add providers to your app**:

```dart
import 'package:nextsportz_v2/features/tournaments/tournaments_providers.dart';

void main() {
  runApp(
    ProviderScope(
      child: MyApp(),
    ),
  );
}
```

3. **Generate code**:

```bash
flutter packages pub run build_runner build
```

### Usage

Navigate to tournaments from anywhere in your app:

```dart
import 'package:nextsportz_v2/features/tournaments/presentation/routes/tournament_routes.dart';

// Navigate to tournament list
TournamentRoutes.goToTournamentList(context);

// Navigate to specific tournament
TournamentRoutes.goToTournamentDetails(context, tournamentId);

// Push modally
await TournamentRoutes.pushTournamentFilter(context);
```

## 🧪 Testing

Run the test suite:

```bash
# Unit tests
flutter test test/features/tournaments/

# Widget tests
flutter test test/features/tournaments/presentation/

# All tests
flutter test
```

## 🎨 Customization

### Theming

The module respects your app's theme. Customize tournament-specific styling by overriding:

```dart
// In your theme
ThemeData(
  colorScheme: ColorScheme.fromSeed(
    seedColor: Colors.green, // Primary color for tournaments
  ),
  // ... other theme properties
)
```

### Mock Data

Customize mock tournaments in `tournament_datasource.dart`:

```dart
void _initializeMockTournaments() {
  // Add your custom tournament data
}
```

## 📋 API Integration

Replace the mock data source with your API implementation:

1. Create `TournamentApiDataSource` implementing `TournamentDataSource`
2. Update the provider in `tournaments_providers.dart`
3. Add error handling and network state management

```dart
final tournamentDataSourceProvider = Provider<TournamentDataSource>((ref) {
  return TournamentApiDataSource(); // Your API implementation
});
```

## 🚀 Deployment Checklist

- [ ] Replace mock data with real API
- [ ] Add proper error handling
- [ ] Configure Google Maps API key
- [ ] Test on different screen sizes
- [ ] Verify accessibility features
- [ ] Performance testing with large datasets
- [ ] Add analytics tracking
- [ ] Test offline functionality

## 🤝 Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation
4. Follow Flutter/Dart style guide
5. Use meaningful commit messages

## 📄 License

This module is part of the NextSportz application. See the main project license for details.

## 🔮 Future Enhancements

- [ ] Offline support with local database
- [ ] Push notifications for tournament updates
- [ ] Social sharing with deep links
- [ ] Advanced analytics and insights
- [ ] Tournament creation for organizers
- [ ] Live tournament updates
- [ ] Video highlights integration
- [ ] Player performance tracking
- [ ] Team management features
- [ ] Payment integration for entry fees

## 📞 Support

For questions or issues related to the tournament module, please refer to the main project documentation or contact the development team.

