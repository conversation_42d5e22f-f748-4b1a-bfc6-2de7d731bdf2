import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/use_case.dart';
import '../entities/tournament.dart';
import '../entities/tournament_filter.dart';
import '../entities/favourite_tournament.dart';
import '../entities/player_badge.dart';
import '../repositories/tournament_repository.dart';

// Get Tournaments Use Case
class GetTournamentsUseCase
    implements UseCase<TournamentSearchResult, GetTournamentsParams> {
  final TournamentRepository repository;

  GetTournamentsUseCase(this.repository);

  @override
  Future<Either<AppError, TournamentSearchResult>> call(
    GetTournamentsParams params,
  ) {
    return repository.getTournaments(query: params.query);
  }
}

class GetTournamentsParams {
  final TournamentSearchQuery? query;

  const GetTournamentsParams({this.query});
}

// Get Tournament Details Use Case
class GetTournamentDetailsUseCase implements UseCase<Tournament, String> {
  final TournamentRepository repository;

  GetTournamentDetailsUseCase(this.repository);

  @override
  Future<Either<AppError, Tournament>> call(String tournamentId) {
    return repository.getTournamentById(tournamentId);
  }
}

// Join Tournament Use Case
class JoinTournamentUseCase
    implements UseCase<Tournament, JoinTournamentParams> {
  final TournamentRepository repository;

  JoinTournamentUseCase(this.repository);

  @override
  Future<Either<AppError, Tournament>> call(JoinTournamentParams params) {
    return repository.joinTournament(params.tournamentId, params.playerId);
  }
}

class JoinTournamentParams {
  final String tournamentId;
  final String playerId;

  const JoinTournamentParams({
    required this.tournamentId,
    required this.playerId,
  });
}

// Leave Tournament Use Case
class LeaveTournamentUseCase
    implements UseCase<Tournament, LeaveTournamentParams> {
  final TournamentRepository repository;

  LeaveTournamentUseCase(this.repository);

  @override
  Future<Either<AppError, Tournament>> call(LeaveTournamentParams params) {
    return repository.leaveTournament(params.tournamentId, params.playerId);
  }
}

class LeaveTournamentParams {
  final String tournamentId;
  final String playerId;

  const LeaveTournamentParams({
    required this.tournamentId,
    required this.playerId,
  });
}

// Search Tournaments Use Case
class SearchTournamentsUseCase
    implements UseCase<List<Tournament>, SearchTournamentsParams> {
  final TournamentRepository repository;

  SearchTournamentsUseCase(this.repository);

  @override
  Future<Either<AppError, List<Tournament>>> call(
    SearchTournamentsParams params,
  ) {
    return repository.searchTournaments(
      searchTerm: params.searchTerm,
      filter: params.filter,
      limit: params.limit,
      offset: params.offset,
    );
  }
}

class SearchTournamentsParams {
  final String searchTerm;
  final TournamentFilter? filter;
  final int limit;
  final int offset;

  const SearchTournamentsParams({
    required this.searchTerm,
    this.filter,
    this.limit = 20,
    this.offset = 0,
  });
}

// Get Featured Tournaments Use Case
class GetFeaturedTournamentsUseCase
    implements UseCase<List<Tournament>, GetFeaturedTournamentsParams> {
  final TournamentRepository repository;

  GetFeaturedTournamentsUseCase(this.repository);

  @override
  Future<Either<AppError, List<Tournament>>> call(
    GetFeaturedTournamentsParams params,
  ) {
    return repository.getFeaturedTournaments(limit: params.limit);
  }
}

class GetFeaturedTournamentsParams {
  final int limit;

  const GetFeaturedTournamentsParams({this.limit = 5});
}

// Get Nearby Tournaments Use Case
class GetNearbyTournamentsUseCase
    implements UseCase<List<Tournament>, GetNearbyTournamentsParams> {
  final TournamentRepository repository;

  GetNearbyTournamentsUseCase(this.repository);

  @override
  Future<Either<AppError, List<Tournament>>> call(
    GetNearbyTournamentsParams params,
  ) {
    return repository.getNearbyTournaments(
      latitude: params.latitude,
      longitude: params.longitude,
      radiusKm: params.radiusKm,
      limit: params.limit,
    );
  }
}

class GetNearbyTournamentsParams {
  final double latitude;
  final double longitude;
  final double radiusKm;
  final int limit;

  const GetNearbyTournamentsParams({
    required this.latitude,
    required this.longitude,
    this.radiusKm = 50.0,
    this.limit = 20,
  });
}

// Favourite Tournaments Use Cases
class GetFavouriteTournamentsUseCase
    implements UseCase<List<FavouriteTournamentDetails>, String> {
  final TournamentRepository repository;

  GetFavouriteTournamentsUseCase(this.repository);

  @override
  Future<Either<AppError, List<FavouriteTournamentDetails>>> call(
    String playerId,
  ) {
    return repository.getFavouriteTournaments(playerId);
  }
}

class AddToFavouritesUseCase
    implements UseCase<FavouriteTournament, AddToFavouritesParams> {
  final TournamentRepository repository;

  AddToFavouritesUseCase(this.repository);

  @override
  Future<Either<AppError, FavouriteTournament>> call(
    AddToFavouritesParams params,
  ) {
    return repository.addToFavourites(
      playerId: params.playerId,
      tournamentId: params.tournamentId,
    );
  }
}

class AddToFavouritesParams {
  final String playerId;
  final String tournamentId;

  const AddToFavouritesParams({
    required this.playerId,
    required this.tournamentId,
  });
}

class RemoveFromFavouritesUseCase
    implements UseCase<void, RemoveFromFavouritesParams> {
  final TournamentRepository repository;

  RemoveFromFavouritesUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(RemoveFromFavouritesParams params) {
    return repository.removeFromFavourites(
      playerId: params.playerId,
      tournamentId: params.tournamentId,
    );
  }
}

class RemoveFromFavouritesParams {
  final String playerId;
  final String tournamentId;

  const RemoveFromFavouritesParams({
    required this.playerId,
    required this.tournamentId,
  });
}

// Badge System Use Cases
class GetPlayerBadgesUseCase implements UseCase<PlayerBadgeCollection, String> {
  final TournamentRepository repository;

  GetPlayerBadgesUseCase(this.repository);

  @override
  Future<Either<AppError, PlayerBadgeCollection>> call(String playerId) {
    return repository.getPlayerBadges(playerId);
  }
}

class GetPlayerTournamentStatsUseCase
    implements UseCase<TournamentStats, String> {
  final TournamentRepository repository;

  GetPlayerTournamentStatsUseCase(this.repository);

  @override
  Future<Either<AppError, TournamentStats>> call(String playerId) {
    return repository.getPlayerTournamentStats(playerId);
  }
}

class CheckForNewBadgesUseCase
    implements UseCase<List<PlayerBadge>, CheckForNewBadgesParams> {
  final TournamentRepository repository;

  CheckForNewBadgesUseCase(this.repository);

  @override
  Future<Either<AppError, List<PlayerBadge>>> call(
    CheckForNewBadgesParams params,
  ) {
    return repository.checkForNewBadges(
      playerId: params.playerId,
      stats: params.stats,
    );
  }
}

class CheckForNewBadgesParams {
  final String playerId;
  final TournamentStats stats;

  const CheckForNewBadgesParams({required this.playerId, required this.stats});
}

