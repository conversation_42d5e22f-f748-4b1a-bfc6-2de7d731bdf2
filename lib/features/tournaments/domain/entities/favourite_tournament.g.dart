// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favourite_tournament.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FavouriteTournamentImpl _$$FavouriteTournamentImplFromJson(
  Map<String, dynamic> json,
) => _$FavouriteTournamentImpl(
  id: json['id'] as String,
  playerId: json['playerId'] as String,
  tournamentId: json['tournamentId'] as String,
  addedAt: DateTime.parse(json['addedAt'] as String),
  isActive: json['isActive'] as bool? ?? true,
);

Map<String, dynamic> _$$FavouriteTournamentImplToJson(
  _$FavouriteTournamentImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'playerId': instance.playerId,
  'tournamentId': instance.tournamentId,
  'addedAt': instance.addedAt.toIso8601String(),
  'isActive': instance.isActive,
};

_$FavouriteTournamentDetailsImpl _$$FavouriteTournamentDetailsImplFromJson(
  Map<String, dynamic> json,
) => _$FavouriteTournamentDetailsImpl(
  favourite: FavouriteTournament.fromJson(
    json['favourite'] as Map<String, dynamic>,
  ),
  tournamentName: json['tournamentName'] as String,
  tournamentLocation: json['tournamentLocation'] as String,
  tournamentStartDate: DateTime.parse(json['tournamentStartDate'] as String),
  tournamentBannerImage: json['tournamentBannerImage'] as String?,
);

Map<String, dynamic> _$$FavouriteTournamentDetailsImplToJson(
  _$FavouriteTournamentDetailsImpl instance,
) => <String, dynamic>{
  'favourite': instance.favourite,
  'tournamentName': instance.tournamentName,
  'tournamentLocation': instance.tournamentLocation,
  'tournamentStartDate': instance.tournamentStartDate.toIso8601String(),
  'tournamentBannerImage': instance.tournamentBannerImage,
};
