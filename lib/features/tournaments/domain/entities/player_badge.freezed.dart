// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'player_badge.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PlayerBadge _$PlayerBadgeFromJson(Map<String, dynamic> json) {
  return _PlayerBadge.fromJson(json);
}

/// @nodoc
mixin _$PlayerBadge {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get iconPath => throw _privateConstructorUsedError;
  BadgeCategory get category => throw _privateConstructorUsedError;
  BadgeRarity get rarity => throw _privateConstructorUsedError;
  BadgeRequirement get requirement => throw _privateConstructorUsedError;
  int get xpReward => throw _privateConstructorUsedError;
  String? get color => throw _privateConstructorUsedError;

  /// Serializes this PlayerBadge to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PlayerBadge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlayerBadgeCopyWith<PlayerBadge> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlayerBadgeCopyWith<$Res> {
  factory $PlayerBadgeCopyWith(
    PlayerBadge value,
    $Res Function(PlayerBadge) then,
  ) = _$PlayerBadgeCopyWithImpl<$Res, PlayerBadge>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    String iconPath,
    BadgeCategory category,
    BadgeRarity rarity,
    BadgeRequirement requirement,
    int xpReward,
    String? color,
  });

  $BadgeRequirementCopyWith<$Res> get requirement;
}

/// @nodoc
class _$PlayerBadgeCopyWithImpl<$Res, $Val extends PlayerBadge>
    implements $PlayerBadgeCopyWith<$Res> {
  _$PlayerBadgeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlayerBadge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? iconPath = null,
    Object? category = null,
    Object? rarity = null,
    Object? requirement = null,
    Object? xpReward = null,
    Object? color = freezed,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as String,
            name:
                null == name
                    ? _value.name
                    : name // ignore: cast_nullable_to_non_nullable
                        as String,
            description:
                null == description
                    ? _value.description
                    : description // ignore: cast_nullable_to_non_nullable
                        as String,
            iconPath:
                null == iconPath
                    ? _value.iconPath
                    : iconPath // ignore: cast_nullable_to_non_nullable
                        as String,
            category:
                null == category
                    ? _value.category
                    : category // ignore: cast_nullable_to_non_nullable
                        as BadgeCategory,
            rarity:
                null == rarity
                    ? _value.rarity
                    : rarity // ignore: cast_nullable_to_non_nullable
                        as BadgeRarity,
            requirement:
                null == requirement
                    ? _value.requirement
                    : requirement // ignore: cast_nullable_to_non_nullable
                        as BadgeRequirement,
            xpReward:
                null == xpReward
                    ? _value.xpReward
                    : xpReward // ignore: cast_nullable_to_non_nullable
                        as int,
            color:
                freezed == color
                    ? _value.color
                    : color // ignore: cast_nullable_to_non_nullable
                        as String?,
          )
          as $Val,
    );
  }

  /// Create a copy of PlayerBadge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BadgeRequirementCopyWith<$Res> get requirement {
    return $BadgeRequirementCopyWith<$Res>(_value.requirement, (value) {
      return _then(_value.copyWith(requirement: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PlayerBadgeImplCopyWith<$Res>
    implements $PlayerBadgeCopyWith<$Res> {
  factory _$$PlayerBadgeImplCopyWith(
    _$PlayerBadgeImpl value,
    $Res Function(_$PlayerBadgeImpl) then,
  ) = __$$PlayerBadgeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    String iconPath,
    BadgeCategory category,
    BadgeRarity rarity,
    BadgeRequirement requirement,
    int xpReward,
    String? color,
  });

  @override
  $BadgeRequirementCopyWith<$Res> get requirement;
}

/// @nodoc
class __$$PlayerBadgeImplCopyWithImpl<$Res>
    extends _$PlayerBadgeCopyWithImpl<$Res, _$PlayerBadgeImpl>
    implements _$$PlayerBadgeImplCopyWith<$Res> {
  __$$PlayerBadgeImplCopyWithImpl(
    _$PlayerBadgeImpl _value,
    $Res Function(_$PlayerBadgeImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PlayerBadge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? iconPath = null,
    Object? category = null,
    Object? rarity = null,
    Object? requirement = null,
    Object? xpReward = null,
    Object? color = freezed,
  }) {
    return _then(
      _$PlayerBadgeImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as String,
        name:
            null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                    as String,
        description:
            null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                    as String,
        iconPath:
            null == iconPath
                ? _value.iconPath
                : iconPath // ignore: cast_nullable_to_non_nullable
                    as String,
        category:
            null == category
                ? _value.category
                : category // ignore: cast_nullable_to_non_nullable
                    as BadgeCategory,
        rarity:
            null == rarity
                ? _value.rarity
                : rarity // ignore: cast_nullable_to_non_nullable
                    as BadgeRarity,
        requirement:
            null == requirement
                ? _value.requirement
                : requirement // ignore: cast_nullable_to_non_nullable
                    as BadgeRequirement,
        xpReward:
            null == xpReward
                ? _value.xpReward
                : xpReward // ignore: cast_nullable_to_non_nullable
                    as int,
        color:
            freezed == color
                ? _value.color
                : color // ignore: cast_nullable_to_non_nullable
                    as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PlayerBadgeImpl implements _PlayerBadge {
  const _$PlayerBadgeImpl({
    required this.id,
    required this.name,
    required this.description,
    required this.iconPath,
    required this.category,
    required this.rarity,
    required this.requirement,
    this.xpReward = 0,
    this.color,
  });

  factory _$PlayerBadgeImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlayerBadgeImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final String iconPath;
  @override
  final BadgeCategory category;
  @override
  final BadgeRarity rarity;
  @override
  final BadgeRequirement requirement;
  @override
  @JsonKey()
  final int xpReward;
  @override
  final String? color;

  @override
  String toString() {
    return 'PlayerBadge(id: $id, name: $name, description: $description, iconPath: $iconPath, category: $category, rarity: $rarity, requirement: $requirement, xpReward: $xpReward, color: $color)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlayerBadgeImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.iconPath, iconPath) ||
                other.iconPath == iconPath) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.rarity, rarity) || other.rarity == rarity) &&
            (identical(other.requirement, requirement) ||
                other.requirement == requirement) &&
            (identical(other.xpReward, xpReward) ||
                other.xpReward == xpReward) &&
            (identical(other.color, color) || other.color == color));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    iconPath,
    category,
    rarity,
    requirement,
    xpReward,
    color,
  );

  /// Create a copy of PlayerBadge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlayerBadgeImplCopyWith<_$PlayerBadgeImpl> get copyWith =>
      __$$PlayerBadgeImplCopyWithImpl<_$PlayerBadgeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlayerBadgeImplToJson(this);
  }
}

abstract class _PlayerBadge implements PlayerBadge {
  const factory _PlayerBadge({
    required final String id,
    required final String name,
    required final String description,
    required final String iconPath,
    required final BadgeCategory category,
    required final BadgeRarity rarity,
    required final BadgeRequirement requirement,
    final int xpReward,
    final String? color,
  }) = _$PlayerBadgeImpl;

  factory _PlayerBadge.fromJson(Map<String, dynamic> json) =
      _$PlayerBadgeImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  String get iconPath;
  @override
  BadgeCategory get category;
  @override
  BadgeRarity get rarity;
  @override
  BadgeRequirement get requirement;
  @override
  int get xpReward;
  @override
  String? get color;

  /// Create a copy of PlayerBadge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlayerBadgeImplCopyWith<_$PlayerBadgeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BadgeRequirement _$BadgeRequirementFromJson(Map<String, dynamic> json) {
  return _BadgeRequirement.fromJson(json);
}

/// @nodoc
mixin _$BadgeRequirement {
  BadgeType get type => throw _privateConstructorUsedError;
  Map<String, dynamic> get criteria => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;

  /// Serializes this BadgeRequirement to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BadgeRequirement
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BadgeRequirementCopyWith<BadgeRequirement> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BadgeRequirementCopyWith<$Res> {
  factory $BadgeRequirementCopyWith(
    BadgeRequirement value,
    $Res Function(BadgeRequirement) then,
  ) = _$BadgeRequirementCopyWithImpl<$Res, BadgeRequirement>;
  @useResult
  $Res call({
    BadgeType type,
    Map<String, dynamic> criteria,
    String? description,
  });
}

/// @nodoc
class _$BadgeRequirementCopyWithImpl<$Res, $Val extends BadgeRequirement>
    implements $BadgeRequirementCopyWith<$Res> {
  _$BadgeRequirementCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BadgeRequirement
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? criteria = null,
    Object? description = freezed,
  }) {
    return _then(
      _value.copyWith(
            type:
                null == type
                    ? _value.type
                    : type // ignore: cast_nullable_to_non_nullable
                        as BadgeType,
            criteria:
                null == criteria
                    ? _value.criteria
                    : criteria // ignore: cast_nullable_to_non_nullable
                        as Map<String, dynamic>,
            description:
                freezed == description
                    ? _value.description
                    : description // ignore: cast_nullable_to_non_nullable
                        as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$BadgeRequirementImplCopyWith<$Res>
    implements $BadgeRequirementCopyWith<$Res> {
  factory _$$BadgeRequirementImplCopyWith(
    _$BadgeRequirementImpl value,
    $Res Function(_$BadgeRequirementImpl) then,
  ) = __$$BadgeRequirementImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    BadgeType type,
    Map<String, dynamic> criteria,
    String? description,
  });
}

/// @nodoc
class __$$BadgeRequirementImplCopyWithImpl<$Res>
    extends _$BadgeRequirementCopyWithImpl<$Res, _$BadgeRequirementImpl>
    implements _$$BadgeRequirementImplCopyWith<$Res> {
  __$$BadgeRequirementImplCopyWithImpl(
    _$BadgeRequirementImpl _value,
    $Res Function(_$BadgeRequirementImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of BadgeRequirement
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? criteria = null,
    Object? description = freezed,
  }) {
    return _then(
      _$BadgeRequirementImpl(
        type:
            null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                    as BadgeType,
        criteria:
            null == criteria
                ? _value._criteria
                : criteria // ignore: cast_nullable_to_non_nullable
                    as Map<String, dynamic>,
        description:
            freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                    as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$BadgeRequirementImpl implements _BadgeRequirement {
  const _$BadgeRequirementImpl({
    required this.type,
    required final Map<String, dynamic> criteria,
    this.description,
  }) : _criteria = criteria;

  factory _$BadgeRequirementImpl.fromJson(Map<String, dynamic> json) =>
      _$$BadgeRequirementImplFromJson(json);

  @override
  final BadgeType type;
  final Map<String, dynamic> _criteria;
  @override
  Map<String, dynamic> get criteria {
    if (_criteria is EqualUnmodifiableMapView) return _criteria;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_criteria);
  }

  @override
  final String? description;

  @override
  String toString() {
    return 'BadgeRequirement(type: $type, criteria: $criteria, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BadgeRequirementImpl &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._criteria, _criteria) &&
            (identical(other.description, description) ||
                other.description == description));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    type,
    const DeepCollectionEquality().hash(_criteria),
    description,
  );

  /// Create a copy of BadgeRequirement
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BadgeRequirementImplCopyWith<_$BadgeRequirementImpl> get copyWith =>
      __$$BadgeRequirementImplCopyWithImpl<_$BadgeRequirementImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$BadgeRequirementImplToJson(this);
  }
}

abstract class _BadgeRequirement implements BadgeRequirement {
  const factory _BadgeRequirement({
    required final BadgeType type,
    required final Map<String, dynamic> criteria,
    final String? description,
  }) = _$BadgeRequirementImpl;

  factory _BadgeRequirement.fromJson(Map<String, dynamic> json) =
      _$BadgeRequirementImpl.fromJson;

  @override
  BadgeType get type;
  @override
  Map<String, dynamic> get criteria;
  @override
  String? get description;

  /// Create a copy of BadgeRequirement
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BadgeRequirementImplCopyWith<_$BadgeRequirementImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PlayerBadgeProgress _$PlayerBadgeProgressFromJson(Map<String, dynamic> json) {
  return _PlayerBadgeProgress.fromJson(json);
}

/// @nodoc
mixin _$PlayerBadgeProgress {
  String get badgeId => throw _privateConstructorUsedError;
  String get playerId => throw _privateConstructorUsedError;
  int get currentProgress => throw _privateConstructorUsedError;
  int get targetProgress => throw _privateConstructorUsedError;
  DateTime get lastUpdated => throw _privateConstructorUsedError;
  bool get isUnlocked => throw _privateConstructorUsedError;
  DateTime? get unlockedAt => throw _privateConstructorUsedError;

  /// Serializes this PlayerBadgeProgress to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PlayerBadgeProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlayerBadgeProgressCopyWith<PlayerBadgeProgress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlayerBadgeProgressCopyWith<$Res> {
  factory $PlayerBadgeProgressCopyWith(
    PlayerBadgeProgress value,
    $Res Function(PlayerBadgeProgress) then,
  ) = _$PlayerBadgeProgressCopyWithImpl<$Res, PlayerBadgeProgress>;
  @useResult
  $Res call({
    String badgeId,
    String playerId,
    int currentProgress,
    int targetProgress,
    DateTime lastUpdated,
    bool isUnlocked,
    DateTime? unlockedAt,
  });
}

/// @nodoc
class _$PlayerBadgeProgressCopyWithImpl<$Res, $Val extends PlayerBadgeProgress>
    implements $PlayerBadgeProgressCopyWith<$Res> {
  _$PlayerBadgeProgressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlayerBadgeProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? badgeId = null,
    Object? playerId = null,
    Object? currentProgress = null,
    Object? targetProgress = null,
    Object? lastUpdated = null,
    Object? isUnlocked = null,
    Object? unlockedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            badgeId:
                null == badgeId
                    ? _value.badgeId
                    : badgeId // ignore: cast_nullable_to_non_nullable
                        as String,
            playerId:
                null == playerId
                    ? _value.playerId
                    : playerId // ignore: cast_nullable_to_non_nullable
                        as String,
            currentProgress:
                null == currentProgress
                    ? _value.currentProgress
                    : currentProgress // ignore: cast_nullable_to_non_nullable
                        as int,
            targetProgress:
                null == targetProgress
                    ? _value.targetProgress
                    : targetProgress // ignore: cast_nullable_to_non_nullable
                        as int,
            lastUpdated:
                null == lastUpdated
                    ? _value.lastUpdated
                    : lastUpdated // ignore: cast_nullable_to_non_nullable
                        as DateTime,
            isUnlocked:
                null == isUnlocked
                    ? _value.isUnlocked
                    : isUnlocked // ignore: cast_nullable_to_non_nullable
                        as bool,
            unlockedAt:
                freezed == unlockedAt
                    ? _value.unlockedAt
                    : unlockedAt // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PlayerBadgeProgressImplCopyWith<$Res>
    implements $PlayerBadgeProgressCopyWith<$Res> {
  factory _$$PlayerBadgeProgressImplCopyWith(
    _$PlayerBadgeProgressImpl value,
    $Res Function(_$PlayerBadgeProgressImpl) then,
  ) = __$$PlayerBadgeProgressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String badgeId,
    String playerId,
    int currentProgress,
    int targetProgress,
    DateTime lastUpdated,
    bool isUnlocked,
    DateTime? unlockedAt,
  });
}

/// @nodoc
class __$$PlayerBadgeProgressImplCopyWithImpl<$Res>
    extends _$PlayerBadgeProgressCopyWithImpl<$Res, _$PlayerBadgeProgressImpl>
    implements _$$PlayerBadgeProgressImplCopyWith<$Res> {
  __$$PlayerBadgeProgressImplCopyWithImpl(
    _$PlayerBadgeProgressImpl _value,
    $Res Function(_$PlayerBadgeProgressImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PlayerBadgeProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? badgeId = null,
    Object? playerId = null,
    Object? currentProgress = null,
    Object? targetProgress = null,
    Object? lastUpdated = null,
    Object? isUnlocked = null,
    Object? unlockedAt = freezed,
  }) {
    return _then(
      _$PlayerBadgeProgressImpl(
        badgeId:
            null == badgeId
                ? _value.badgeId
                : badgeId // ignore: cast_nullable_to_non_nullable
                    as String,
        playerId:
            null == playerId
                ? _value.playerId
                : playerId // ignore: cast_nullable_to_non_nullable
                    as String,
        currentProgress:
            null == currentProgress
                ? _value.currentProgress
                : currentProgress // ignore: cast_nullable_to_non_nullable
                    as int,
        targetProgress:
            null == targetProgress
                ? _value.targetProgress
                : targetProgress // ignore: cast_nullable_to_non_nullable
                    as int,
        lastUpdated:
            null == lastUpdated
                ? _value.lastUpdated
                : lastUpdated // ignore: cast_nullable_to_non_nullable
                    as DateTime,
        isUnlocked:
            null == isUnlocked
                ? _value.isUnlocked
                : isUnlocked // ignore: cast_nullable_to_non_nullable
                    as bool,
        unlockedAt:
            freezed == unlockedAt
                ? _value.unlockedAt
                : unlockedAt // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PlayerBadgeProgressImpl implements _PlayerBadgeProgress {
  const _$PlayerBadgeProgressImpl({
    required this.badgeId,
    required this.playerId,
    required this.currentProgress,
    required this.targetProgress,
    required this.lastUpdated,
    this.isUnlocked = false,
    this.unlockedAt,
  });

  factory _$PlayerBadgeProgressImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlayerBadgeProgressImplFromJson(json);

  @override
  final String badgeId;
  @override
  final String playerId;
  @override
  final int currentProgress;
  @override
  final int targetProgress;
  @override
  final DateTime lastUpdated;
  @override
  @JsonKey()
  final bool isUnlocked;
  @override
  final DateTime? unlockedAt;

  @override
  String toString() {
    return 'PlayerBadgeProgress(badgeId: $badgeId, playerId: $playerId, currentProgress: $currentProgress, targetProgress: $targetProgress, lastUpdated: $lastUpdated, isUnlocked: $isUnlocked, unlockedAt: $unlockedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlayerBadgeProgressImpl &&
            (identical(other.badgeId, badgeId) || other.badgeId == badgeId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.currentProgress, currentProgress) ||
                other.currentProgress == currentProgress) &&
            (identical(other.targetProgress, targetProgress) ||
                other.targetProgress == targetProgress) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.isUnlocked, isUnlocked) ||
                other.isUnlocked == isUnlocked) &&
            (identical(other.unlockedAt, unlockedAt) ||
                other.unlockedAt == unlockedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    badgeId,
    playerId,
    currentProgress,
    targetProgress,
    lastUpdated,
    isUnlocked,
    unlockedAt,
  );

  /// Create a copy of PlayerBadgeProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlayerBadgeProgressImplCopyWith<_$PlayerBadgeProgressImpl> get copyWith =>
      __$$PlayerBadgeProgressImplCopyWithImpl<_$PlayerBadgeProgressImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PlayerBadgeProgressImplToJson(this);
  }
}

abstract class _PlayerBadgeProgress implements PlayerBadgeProgress {
  const factory _PlayerBadgeProgress({
    required final String badgeId,
    required final String playerId,
    required final int currentProgress,
    required final int targetProgress,
    required final DateTime lastUpdated,
    final bool isUnlocked,
    final DateTime? unlockedAt,
  }) = _$PlayerBadgeProgressImpl;

  factory _PlayerBadgeProgress.fromJson(Map<String, dynamic> json) =
      _$PlayerBadgeProgressImpl.fromJson;

  @override
  String get badgeId;
  @override
  String get playerId;
  @override
  int get currentProgress;
  @override
  int get targetProgress;
  @override
  DateTime get lastUpdated;
  @override
  bool get isUnlocked;
  @override
  DateTime? get unlockedAt;

  /// Create a copy of PlayerBadgeProgress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlayerBadgeProgressImplCopyWith<_$PlayerBadgeProgressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PlayerBadgeCollection _$PlayerBadgeCollectionFromJson(
  Map<String, dynamic> json,
) {
  return _PlayerBadgeCollection.fromJson(json);
}

/// @nodoc
mixin _$PlayerBadgeCollection {
  String get playerId => throw _privateConstructorUsedError;
  List<PlayerBadgeProgress> get badges => throw _privateConstructorUsedError;
  int get totalXP => throw _privateConstructorUsedError;
  DateTime get lastUpdated => throw _privateConstructorUsedError;

  /// Serializes this PlayerBadgeCollection to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PlayerBadgeCollection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlayerBadgeCollectionCopyWith<PlayerBadgeCollection> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlayerBadgeCollectionCopyWith<$Res> {
  factory $PlayerBadgeCollectionCopyWith(
    PlayerBadgeCollection value,
    $Res Function(PlayerBadgeCollection) then,
  ) = _$PlayerBadgeCollectionCopyWithImpl<$Res, PlayerBadgeCollection>;
  @useResult
  $Res call({
    String playerId,
    List<PlayerBadgeProgress> badges,
    int totalXP,
    DateTime lastUpdated,
  });
}

/// @nodoc
class _$PlayerBadgeCollectionCopyWithImpl<
  $Res,
  $Val extends PlayerBadgeCollection
>
    implements $PlayerBadgeCollectionCopyWith<$Res> {
  _$PlayerBadgeCollectionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlayerBadgeCollection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? playerId = null,
    Object? badges = null,
    Object? totalXP = null,
    Object? lastUpdated = null,
  }) {
    return _then(
      _value.copyWith(
            playerId:
                null == playerId
                    ? _value.playerId
                    : playerId // ignore: cast_nullable_to_non_nullable
                        as String,
            badges:
                null == badges
                    ? _value.badges
                    : badges // ignore: cast_nullable_to_non_nullable
                        as List<PlayerBadgeProgress>,
            totalXP:
                null == totalXP
                    ? _value.totalXP
                    : totalXP // ignore: cast_nullable_to_non_nullable
                        as int,
            lastUpdated:
                null == lastUpdated
                    ? _value.lastUpdated
                    : lastUpdated // ignore: cast_nullable_to_non_nullable
                        as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PlayerBadgeCollectionImplCopyWith<$Res>
    implements $PlayerBadgeCollectionCopyWith<$Res> {
  factory _$$PlayerBadgeCollectionImplCopyWith(
    _$PlayerBadgeCollectionImpl value,
    $Res Function(_$PlayerBadgeCollectionImpl) then,
  ) = __$$PlayerBadgeCollectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String playerId,
    List<PlayerBadgeProgress> badges,
    int totalXP,
    DateTime lastUpdated,
  });
}

/// @nodoc
class __$$PlayerBadgeCollectionImplCopyWithImpl<$Res>
    extends
        _$PlayerBadgeCollectionCopyWithImpl<$Res, _$PlayerBadgeCollectionImpl>
    implements _$$PlayerBadgeCollectionImplCopyWith<$Res> {
  __$$PlayerBadgeCollectionImplCopyWithImpl(
    _$PlayerBadgeCollectionImpl _value,
    $Res Function(_$PlayerBadgeCollectionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PlayerBadgeCollection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? playerId = null,
    Object? badges = null,
    Object? totalXP = null,
    Object? lastUpdated = null,
  }) {
    return _then(
      _$PlayerBadgeCollectionImpl(
        playerId:
            null == playerId
                ? _value.playerId
                : playerId // ignore: cast_nullable_to_non_nullable
                    as String,
        badges:
            null == badges
                ? _value._badges
                : badges // ignore: cast_nullable_to_non_nullable
                    as List<PlayerBadgeProgress>,
        totalXP:
            null == totalXP
                ? _value.totalXP
                : totalXP // ignore: cast_nullable_to_non_nullable
                    as int,
        lastUpdated:
            null == lastUpdated
                ? _value.lastUpdated
                : lastUpdated // ignore: cast_nullable_to_non_nullable
                    as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PlayerBadgeCollectionImpl implements _PlayerBadgeCollection {
  const _$PlayerBadgeCollectionImpl({
    required this.playerId,
    required final List<PlayerBadgeProgress> badges,
    required this.totalXP,
    required this.lastUpdated,
  }) : _badges = badges;

  factory _$PlayerBadgeCollectionImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlayerBadgeCollectionImplFromJson(json);

  @override
  final String playerId;
  final List<PlayerBadgeProgress> _badges;
  @override
  List<PlayerBadgeProgress> get badges {
    if (_badges is EqualUnmodifiableListView) return _badges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_badges);
  }

  @override
  final int totalXP;
  @override
  final DateTime lastUpdated;

  @override
  String toString() {
    return 'PlayerBadgeCollection(playerId: $playerId, badges: $badges, totalXP: $totalXP, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlayerBadgeCollectionImpl &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            const DeepCollectionEquality().equals(other._badges, _badges) &&
            (identical(other.totalXP, totalXP) || other.totalXP == totalXP) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    playerId,
    const DeepCollectionEquality().hash(_badges),
    totalXP,
    lastUpdated,
  );

  /// Create a copy of PlayerBadgeCollection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlayerBadgeCollectionImplCopyWith<_$PlayerBadgeCollectionImpl>
  get copyWith =>
      __$$PlayerBadgeCollectionImplCopyWithImpl<_$PlayerBadgeCollectionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PlayerBadgeCollectionImplToJson(this);
  }
}

abstract class _PlayerBadgeCollection implements PlayerBadgeCollection {
  const factory _PlayerBadgeCollection({
    required final String playerId,
    required final List<PlayerBadgeProgress> badges,
    required final int totalXP,
    required final DateTime lastUpdated,
  }) = _$PlayerBadgeCollectionImpl;

  factory _PlayerBadgeCollection.fromJson(Map<String, dynamic> json) =
      _$PlayerBadgeCollectionImpl.fromJson;

  @override
  String get playerId;
  @override
  List<PlayerBadgeProgress> get badges;
  @override
  int get totalXP;
  @override
  DateTime get lastUpdated;

  /// Create a copy of PlayerBadgeCollection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlayerBadgeCollectionImplCopyWith<_$PlayerBadgeCollectionImpl>
  get copyWith => throw _privateConstructorUsedError;
}

TournamentStats _$TournamentStatsFromJson(Map<String, dynamic> json) {
  return _TournamentStats.fromJson(json);
}

/// @nodoc
mixin _$TournamentStats {
  String get playerId => throw _privateConstructorUsedError;
  int get tournamentsJoined => throw _privateConstructorUsedError;
  int get tournamentsWon => throw _privateConstructorUsedError;
  int get tournamentsCompleted => throw _privateConstructorUsedError;
  double get totalSpent => throw _privateConstructorUsedError;
  double get totalWon => throw _privateConstructorUsedError;
  int get differentCities => throw _privateConstructorUsedError;
  int get currentStreak => throw _privateConstructorUsedError;
  int get longestStreak => throw _privateConstructorUsedError;
  DateTime get lastTournamentDate => throw _privateConstructorUsedError;
  List<String> get citiesVisited => throw _privateConstructorUsedError;

  /// Serializes this TournamentStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TournamentStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TournamentStatsCopyWith<TournamentStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TournamentStatsCopyWith<$Res> {
  factory $TournamentStatsCopyWith(
    TournamentStats value,
    $Res Function(TournamentStats) then,
  ) = _$TournamentStatsCopyWithImpl<$Res, TournamentStats>;
  @useResult
  $Res call({
    String playerId,
    int tournamentsJoined,
    int tournamentsWon,
    int tournamentsCompleted,
    double totalSpent,
    double totalWon,
    int differentCities,
    int currentStreak,
    int longestStreak,
    DateTime lastTournamentDate,
    List<String> citiesVisited,
  });
}

/// @nodoc
class _$TournamentStatsCopyWithImpl<$Res, $Val extends TournamentStats>
    implements $TournamentStatsCopyWith<$Res> {
  _$TournamentStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TournamentStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? playerId = null,
    Object? tournamentsJoined = null,
    Object? tournamentsWon = null,
    Object? tournamentsCompleted = null,
    Object? totalSpent = null,
    Object? totalWon = null,
    Object? differentCities = null,
    Object? currentStreak = null,
    Object? longestStreak = null,
    Object? lastTournamentDate = null,
    Object? citiesVisited = null,
  }) {
    return _then(
      _value.copyWith(
            playerId:
                null == playerId
                    ? _value.playerId
                    : playerId // ignore: cast_nullable_to_non_nullable
                        as String,
            tournamentsJoined:
                null == tournamentsJoined
                    ? _value.tournamentsJoined
                    : tournamentsJoined // ignore: cast_nullable_to_non_nullable
                        as int,
            tournamentsWon:
                null == tournamentsWon
                    ? _value.tournamentsWon
                    : tournamentsWon // ignore: cast_nullable_to_non_nullable
                        as int,
            tournamentsCompleted:
                null == tournamentsCompleted
                    ? _value.tournamentsCompleted
                    : tournamentsCompleted // ignore: cast_nullable_to_non_nullable
                        as int,
            totalSpent:
                null == totalSpent
                    ? _value.totalSpent
                    : totalSpent // ignore: cast_nullable_to_non_nullable
                        as double,
            totalWon:
                null == totalWon
                    ? _value.totalWon
                    : totalWon // ignore: cast_nullable_to_non_nullable
                        as double,
            differentCities:
                null == differentCities
                    ? _value.differentCities
                    : differentCities // ignore: cast_nullable_to_non_nullable
                        as int,
            currentStreak:
                null == currentStreak
                    ? _value.currentStreak
                    : currentStreak // ignore: cast_nullable_to_non_nullable
                        as int,
            longestStreak:
                null == longestStreak
                    ? _value.longestStreak
                    : longestStreak // ignore: cast_nullable_to_non_nullable
                        as int,
            lastTournamentDate:
                null == lastTournamentDate
                    ? _value.lastTournamentDate
                    : lastTournamentDate // ignore: cast_nullable_to_non_nullable
                        as DateTime,
            citiesVisited:
                null == citiesVisited
                    ? _value.citiesVisited
                    : citiesVisited // ignore: cast_nullable_to_non_nullable
                        as List<String>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$TournamentStatsImplCopyWith<$Res>
    implements $TournamentStatsCopyWith<$Res> {
  factory _$$TournamentStatsImplCopyWith(
    _$TournamentStatsImpl value,
    $Res Function(_$TournamentStatsImpl) then,
  ) = __$$TournamentStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String playerId,
    int tournamentsJoined,
    int tournamentsWon,
    int tournamentsCompleted,
    double totalSpent,
    double totalWon,
    int differentCities,
    int currentStreak,
    int longestStreak,
    DateTime lastTournamentDate,
    List<String> citiesVisited,
  });
}

/// @nodoc
class __$$TournamentStatsImplCopyWithImpl<$Res>
    extends _$TournamentStatsCopyWithImpl<$Res, _$TournamentStatsImpl>
    implements _$$TournamentStatsImplCopyWith<$Res> {
  __$$TournamentStatsImplCopyWithImpl(
    _$TournamentStatsImpl _value,
    $Res Function(_$TournamentStatsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? playerId = null,
    Object? tournamentsJoined = null,
    Object? tournamentsWon = null,
    Object? tournamentsCompleted = null,
    Object? totalSpent = null,
    Object? totalWon = null,
    Object? differentCities = null,
    Object? currentStreak = null,
    Object? longestStreak = null,
    Object? lastTournamentDate = null,
    Object? citiesVisited = null,
  }) {
    return _then(
      _$TournamentStatsImpl(
        playerId:
            null == playerId
                ? _value.playerId
                : playerId // ignore: cast_nullable_to_non_nullable
                    as String,
        tournamentsJoined:
            null == tournamentsJoined
                ? _value.tournamentsJoined
                : tournamentsJoined // ignore: cast_nullable_to_non_nullable
                    as int,
        tournamentsWon:
            null == tournamentsWon
                ? _value.tournamentsWon
                : tournamentsWon // ignore: cast_nullable_to_non_nullable
                    as int,
        tournamentsCompleted:
            null == tournamentsCompleted
                ? _value.tournamentsCompleted
                : tournamentsCompleted // ignore: cast_nullable_to_non_nullable
                    as int,
        totalSpent:
            null == totalSpent
                ? _value.totalSpent
                : totalSpent // ignore: cast_nullable_to_non_nullable
                    as double,
        totalWon:
            null == totalWon
                ? _value.totalWon
                : totalWon // ignore: cast_nullable_to_non_nullable
                    as double,
        differentCities:
            null == differentCities
                ? _value.differentCities
                : differentCities // ignore: cast_nullable_to_non_nullable
                    as int,
        currentStreak:
            null == currentStreak
                ? _value.currentStreak
                : currentStreak // ignore: cast_nullable_to_non_nullable
                    as int,
        longestStreak:
            null == longestStreak
                ? _value.longestStreak
                : longestStreak // ignore: cast_nullable_to_non_nullable
                    as int,
        lastTournamentDate:
            null == lastTournamentDate
                ? _value.lastTournamentDate
                : lastTournamentDate // ignore: cast_nullable_to_non_nullable
                    as DateTime,
        citiesVisited:
            null == citiesVisited
                ? _value._citiesVisited
                : citiesVisited // ignore: cast_nullable_to_non_nullable
                    as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$TournamentStatsImpl implements _TournamentStats {
  const _$TournamentStatsImpl({
    required this.playerId,
    required this.tournamentsJoined,
    required this.tournamentsWon,
    required this.tournamentsCompleted,
    required this.totalSpent,
    required this.totalWon,
    required this.differentCities,
    required this.currentStreak,
    required this.longestStreak,
    required this.lastTournamentDate,
    final List<String> citiesVisited = const [],
  }) : _citiesVisited = citiesVisited;

  factory _$TournamentStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$TournamentStatsImplFromJson(json);

  @override
  final String playerId;
  @override
  final int tournamentsJoined;
  @override
  final int tournamentsWon;
  @override
  final int tournamentsCompleted;
  @override
  final double totalSpent;
  @override
  final double totalWon;
  @override
  final int differentCities;
  @override
  final int currentStreak;
  @override
  final int longestStreak;
  @override
  final DateTime lastTournamentDate;
  final List<String> _citiesVisited;
  @override
  @JsonKey()
  List<String> get citiesVisited {
    if (_citiesVisited is EqualUnmodifiableListView) return _citiesVisited;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_citiesVisited);
  }

  @override
  String toString() {
    return 'TournamentStats(playerId: $playerId, tournamentsJoined: $tournamentsJoined, tournamentsWon: $tournamentsWon, tournamentsCompleted: $tournamentsCompleted, totalSpent: $totalSpent, totalWon: $totalWon, differentCities: $differentCities, currentStreak: $currentStreak, longestStreak: $longestStreak, lastTournamentDate: $lastTournamentDate, citiesVisited: $citiesVisited)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TournamentStatsImpl &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.tournamentsJoined, tournamentsJoined) ||
                other.tournamentsJoined == tournamentsJoined) &&
            (identical(other.tournamentsWon, tournamentsWon) ||
                other.tournamentsWon == tournamentsWon) &&
            (identical(other.tournamentsCompleted, tournamentsCompleted) ||
                other.tournamentsCompleted == tournamentsCompleted) &&
            (identical(other.totalSpent, totalSpent) ||
                other.totalSpent == totalSpent) &&
            (identical(other.totalWon, totalWon) ||
                other.totalWon == totalWon) &&
            (identical(other.differentCities, differentCities) ||
                other.differentCities == differentCities) &&
            (identical(other.currentStreak, currentStreak) ||
                other.currentStreak == currentStreak) &&
            (identical(other.longestStreak, longestStreak) ||
                other.longestStreak == longestStreak) &&
            (identical(other.lastTournamentDate, lastTournamentDate) ||
                other.lastTournamentDate == lastTournamentDate) &&
            const DeepCollectionEquality().equals(
              other._citiesVisited,
              _citiesVisited,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    playerId,
    tournamentsJoined,
    tournamentsWon,
    tournamentsCompleted,
    totalSpent,
    totalWon,
    differentCities,
    currentStreak,
    longestStreak,
    lastTournamentDate,
    const DeepCollectionEquality().hash(_citiesVisited),
  );

  /// Create a copy of TournamentStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TournamentStatsImplCopyWith<_$TournamentStatsImpl> get copyWith =>
      __$$TournamentStatsImplCopyWithImpl<_$TournamentStatsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$TournamentStatsImplToJson(this);
  }
}

abstract class _TournamentStats implements TournamentStats {
  const factory _TournamentStats({
    required final String playerId,
    required final int tournamentsJoined,
    required final int tournamentsWon,
    required final int tournamentsCompleted,
    required final double totalSpent,
    required final double totalWon,
    required final int differentCities,
    required final int currentStreak,
    required final int longestStreak,
    required final DateTime lastTournamentDate,
    final List<String> citiesVisited,
  }) = _$TournamentStatsImpl;

  factory _TournamentStats.fromJson(Map<String, dynamic> json) =
      _$TournamentStatsImpl.fromJson;

  @override
  String get playerId;
  @override
  int get tournamentsJoined;
  @override
  int get tournamentsWon;
  @override
  int get tournamentsCompleted;
  @override
  double get totalSpent;
  @override
  double get totalWon;
  @override
  int get differentCities;
  @override
  int get currentStreak;
  @override
  int get longestStreak;
  @override
  DateTime get lastTournamentDate;
  @override
  List<String> get citiesVisited;

  /// Create a copy of TournamentStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TournamentStatsImplCopyWith<_$TournamentStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
