// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'favourite_tournament.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

FavouriteTournament _$FavouriteTournamentFromJson(Map<String, dynamic> json) {
  return _FavouriteTournament.fromJson(json);
}

/// @nodoc
mixin _$FavouriteTournament {
  String get id => throw _privateConstructorUsedError;
  String get playerId => throw _privateConstructorUsedError;
  String get tournamentId => throw _privateConstructorUsedError;
  DateTime get addedAt => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;

  /// Serializes this FavouriteTournament to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FavouriteTournament
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FavouriteTournamentCopyWith<FavouriteTournament> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FavouriteTournamentCopyWith<$Res> {
  factory $FavouriteTournamentCopyWith(
    FavouriteTournament value,
    $Res Function(FavouriteTournament) then,
  ) = _$FavouriteTournamentCopyWithImpl<$Res, FavouriteTournament>;
  @useResult
  $Res call({
    String id,
    String playerId,
    String tournamentId,
    DateTime addedAt,
    bool isActive,
  });
}

/// @nodoc
class _$FavouriteTournamentCopyWithImpl<$Res, $Val extends FavouriteTournament>
    implements $FavouriteTournamentCopyWith<$Res> {
  _$FavouriteTournamentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FavouriteTournament
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? playerId = null,
    Object? tournamentId = null,
    Object? addedAt = null,
    Object? isActive = null,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as String,
            playerId:
                null == playerId
                    ? _value.playerId
                    : playerId // ignore: cast_nullable_to_non_nullable
                        as String,
            tournamentId:
                null == tournamentId
                    ? _value.tournamentId
                    : tournamentId // ignore: cast_nullable_to_non_nullable
                        as String,
            addedAt:
                null == addedAt
                    ? _value.addedAt
                    : addedAt // ignore: cast_nullable_to_non_nullable
                        as DateTime,
            isActive:
                null == isActive
                    ? _value.isActive
                    : isActive // ignore: cast_nullable_to_non_nullable
                        as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FavouriteTournamentImplCopyWith<$Res>
    implements $FavouriteTournamentCopyWith<$Res> {
  factory _$$FavouriteTournamentImplCopyWith(
    _$FavouriteTournamentImpl value,
    $Res Function(_$FavouriteTournamentImpl) then,
  ) = __$$FavouriteTournamentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String playerId,
    String tournamentId,
    DateTime addedAt,
    bool isActive,
  });
}

/// @nodoc
class __$$FavouriteTournamentImplCopyWithImpl<$Res>
    extends _$FavouriteTournamentCopyWithImpl<$Res, _$FavouriteTournamentImpl>
    implements _$$FavouriteTournamentImplCopyWith<$Res> {
  __$$FavouriteTournamentImplCopyWithImpl(
    _$FavouriteTournamentImpl _value,
    $Res Function(_$FavouriteTournamentImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FavouriteTournament
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? playerId = null,
    Object? tournamentId = null,
    Object? addedAt = null,
    Object? isActive = null,
  }) {
    return _then(
      _$FavouriteTournamentImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as String,
        playerId:
            null == playerId
                ? _value.playerId
                : playerId // ignore: cast_nullable_to_non_nullable
                    as String,
        tournamentId:
            null == tournamentId
                ? _value.tournamentId
                : tournamentId // ignore: cast_nullable_to_non_nullable
                    as String,
        addedAt:
            null == addedAt
                ? _value.addedAt
                : addedAt // ignore: cast_nullable_to_non_nullable
                    as DateTime,
        isActive:
            null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                    as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FavouriteTournamentImpl implements _FavouriteTournament {
  const _$FavouriteTournamentImpl({
    required this.id,
    required this.playerId,
    required this.tournamentId,
    required this.addedAt,
    this.isActive = true,
  });

  factory _$FavouriteTournamentImpl.fromJson(Map<String, dynamic> json) =>
      _$$FavouriteTournamentImplFromJson(json);

  @override
  final String id;
  @override
  final String playerId;
  @override
  final String tournamentId;
  @override
  final DateTime addedAt;
  @override
  @JsonKey()
  final bool isActive;

  @override
  String toString() {
    return 'FavouriteTournament(id: $id, playerId: $playerId, tournamentId: $tournamentId, addedAt: $addedAt, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FavouriteTournamentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.tournamentId, tournamentId) ||
                other.tournamentId == tournamentId) &&
            (identical(other.addedAt, addedAt) || other.addedAt == addedAt) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, playerId, tournamentId, addedAt, isActive);

  /// Create a copy of FavouriteTournament
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FavouriteTournamentImplCopyWith<_$FavouriteTournamentImpl> get copyWith =>
      __$$FavouriteTournamentImplCopyWithImpl<_$FavouriteTournamentImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$FavouriteTournamentImplToJson(this);
  }
}

abstract class _FavouriteTournament implements FavouriteTournament {
  const factory _FavouriteTournament({
    required final String id,
    required final String playerId,
    required final String tournamentId,
    required final DateTime addedAt,
    final bool isActive,
  }) = _$FavouriteTournamentImpl;

  factory _FavouriteTournament.fromJson(Map<String, dynamic> json) =
      _$FavouriteTournamentImpl.fromJson;

  @override
  String get id;
  @override
  String get playerId;
  @override
  String get tournamentId;
  @override
  DateTime get addedAt;
  @override
  bool get isActive;

  /// Create a copy of FavouriteTournament
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FavouriteTournamentImplCopyWith<_$FavouriteTournamentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FavouriteTournamentDetails _$FavouriteTournamentDetailsFromJson(
  Map<String, dynamic> json,
) {
  return _FavouriteTournamentDetails.fromJson(json);
}

/// @nodoc
mixin _$FavouriteTournamentDetails {
  FavouriteTournament get favourite => throw _privateConstructorUsedError;
  String get tournamentName => throw _privateConstructorUsedError;
  String get tournamentLocation => throw _privateConstructorUsedError;
  DateTime get tournamentStartDate => throw _privateConstructorUsedError;
  String? get tournamentBannerImage => throw _privateConstructorUsedError;

  /// Serializes this FavouriteTournamentDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FavouriteTournamentDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FavouriteTournamentDetailsCopyWith<FavouriteTournamentDetails>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FavouriteTournamentDetailsCopyWith<$Res> {
  factory $FavouriteTournamentDetailsCopyWith(
    FavouriteTournamentDetails value,
    $Res Function(FavouriteTournamentDetails) then,
  ) =
      _$FavouriteTournamentDetailsCopyWithImpl<
        $Res,
        FavouriteTournamentDetails
      >;
  @useResult
  $Res call({
    FavouriteTournament favourite,
    String tournamentName,
    String tournamentLocation,
    DateTime tournamentStartDate,
    String? tournamentBannerImage,
  });

  $FavouriteTournamentCopyWith<$Res> get favourite;
}

/// @nodoc
class _$FavouriteTournamentDetailsCopyWithImpl<
  $Res,
  $Val extends FavouriteTournamentDetails
>
    implements $FavouriteTournamentDetailsCopyWith<$Res> {
  _$FavouriteTournamentDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FavouriteTournamentDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? favourite = null,
    Object? tournamentName = null,
    Object? tournamentLocation = null,
    Object? tournamentStartDate = null,
    Object? tournamentBannerImage = freezed,
  }) {
    return _then(
      _value.copyWith(
            favourite:
                null == favourite
                    ? _value.favourite
                    : favourite // ignore: cast_nullable_to_non_nullable
                        as FavouriteTournament,
            tournamentName:
                null == tournamentName
                    ? _value.tournamentName
                    : tournamentName // ignore: cast_nullable_to_non_nullable
                        as String,
            tournamentLocation:
                null == tournamentLocation
                    ? _value.tournamentLocation
                    : tournamentLocation // ignore: cast_nullable_to_non_nullable
                        as String,
            tournamentStartDate:
                null == tournamentStartDate
                    ? _value.tournamentStartDate
                    : tournamentStartDate // ignore: cast_nullable_to_non_nullable
                        as DateTime,
            tournamentBannerImage:
                freezed == tournamentBannerImage
                    ? _value.tournamentBannerImage
                    : tournamentBannerImage // ignore: cast_nullable_to_non_nullable
                        as String?,
          )
          as $Val,
    );
  }

  /// Create a copy of FavouriteTournamentDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FavouriteTournamentCopyWith<$Res> get favourite {
    return $FavouriteTournamentCopyWith<$Res>(_value.favourite, (value) {
      return _then(_value.copyWith(favourite: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FavouriteTournamentDetailsImplCopyWith<$Res>
    implements $FavouriteTournamentDetailsCopyWith<$Res> {
  factory _$$FavouriteTournamentDetailsImplCopyWith(
    _$FavouriteTournamentDetailsImpl value,
    $Res Function(_$FavouriteTournamentDetailsImpl) then,
  ) = __$$FavouriteTournamentDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    FavouriteTournament favourite,
    String tournamentName,
    String tournamentLocation,
    DateTime tournamentStartDate,
    String? tournamentBannerImage,
  });

  @override
  $FavouriteTournamentCopyWith<$Res> get favourite;
}

/// @nodoc
class __$$FavouriteTournamentDetailsImplCopyWithImpl<$Res>
    extends
        _$FavouriteTournamentDetailsCopyWithImpl<
          $Res,
          _$FavouriteTournamentDetailsImpl
        >
    implements _$$FavouriteTournamentDetailsImplCopyWith<$Res> {
  __$$FavouriteTournamentDetailsImplCopyWithImpl(
    _$FavouriteTournamentDetailsImpl _value,
    $Res Function(_$FavouriteTournamentDetailsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FavouriteTournamentDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? favourite = null,
    Object? tournamentName = null,
    Object? tournamentLocation = null,
    Object? tournamentStartDate = null,
    Object? tournamentBannerImage = freezed,
  }) {
    return _then(
      _$FavouriteTournamentDetailsImpl(
        favourite:
            null == favourite
                ? _value.favourite
                : favourite // ignore: cast_nullable_to_non_nullable
                    as FavouriteTournament,
        tournamentName:
            null == tournamentName
                ? _value.tournamentName
                : tournamentName // ignore: cast_nullable_to_non_nullable
                    as String,
        tournamentLocation:
            null == tournamentLocation
                ? _value.tournamentLocation
                : tournamentLocation // ignore: cast_nullable_to_non_nullable
                    as String,
        tournamentStartDate:
            null == tournamentStartDate
                ? _value.tournamentStartDate
                : tournamentStartDate // ignore: cast_nullable_to_non_nullable
                    as DateTime,
        tournamentBannerImage:
            freezed == tournamentBannerImage
                ? _value.tournamentBannerImage
                : tournamentBannerImage // ignore: cast_nullable_to_non_nullable
                    as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FavouriteTournamentDetailsImpl implements _FavouriteTournamentDetails {
  const _$FavouriteTournamentDetailsImpl({
    required this.favourite,
    required this.tournamentName,
    required this.tournamentLocation,
    required this.tournamentStartDate,
    this.tournamentBannerImage,
  });

  factory _$FavouriteTournamentDetailsImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$FavouriteTournamentDetailsImplFromJson(json);

  @override
  final FavouriteTournament favourite;
  @override
  final String tournamentName;
  @override
  final String tournamentLocation;
  @override
  final DateTime tournamentStartDate;
  @override
  final String? tournamentBannerImage;

  @override
  String toString() {
    return 'FavouriteTournamentDetails(favourite: $favourite, tournamentName: $tournamentName, tournamentLocation: $tournamentLocation, tournamentStartDate: $tournamentStartDate, tournamentBannerImage: $tournamentBannerImage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FavouriteTournamentDetailsImpl &&
            (identical(other.favourite, favourite) ||
                other.favourite == favourite) &&
            (identical(other.tournamentName, tournamentName) ||
                other.tournamentName == tournamentName) &&
            (identical(other.tournamentLocation, tournamentLocation) ||
                other.tournamentLocation == tournamentLocation) &&
            (identical(other.tournamentStartDate, tournamentStartDate) ||
                other.tournamentStartDate == tournamentStartDate) &&
            (identical(other.tournamentBannerImage, tournamentBannerImage) ||
                other.tournamentBannerImage == tournamentBannerImage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    favourite,
    tournamentName,
    tournamentLocation,
    tournamentStartDate,
    tournamentBannerImage,
  );

  /// Create a copy of FavouriteTournamentDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FavouriteTournamentDetailsImplCopyWith<_$FavouriteTournamentDetailsImpl>
  get copyWith => __$$FavouriteTournamentDetailsImplCopyWithImpl<
    _$FavouriteTournamentDetailsImpl
  >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FavouriteTournamentDetailsImplToJson(this);
  }
}

abstract class _FavouriteTournamentDetails
    implements FavouriteTournamentDetails {
  const factory _FavouriteTournamentDetails({
    required final FavouriteTournament favourite,
    required final String tournamentName,
    required final String tournamentLocation,
    required final DateTime tournamentStartDate,
    final String? tournamentBannerImage,
  }) = _$FavouriteTournamentDetailsImpl;

  factory _FavouriteTournamentDetails.fromJson(Map<String, dynamic> json) =
      _$FavouriteTournamentDetailsImpl.fromJson;

  @override
  FavouriteTournament get favourite;
  @override
  String get tournamentName;
  @override
  String get tournamentLocation;
  @override
  DateTime get tournamentStartDate;
  @override
  String? get tournamentBannerImage;

  /// Create a copy of FavouriteTournamentDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FavouriteTournamentDetailsImplCopyWith<_$FavouriteTournamentDetailsImpl>
  get copyWith => throw _privateConstructorUsedError;
}
