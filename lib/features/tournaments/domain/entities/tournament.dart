// Simple plain Dart classes for tournament entities

// Tournament Type Enum
enum TournamentType {
  oneVsOne,
  fiveVsFive,
  sevenVsSeven,
  elevenVsEleven,
}

extension TournamentTypeExtension on TournamentType {
  String get displayName {
    switch (this) {
      case TournamentType.oneVsOne:
        return '1v1';
      case TournamentType.fiveVsFive:
        return '5v5';
      case TournamentType.sevenVsSeven:
        return '7v7';
      case TournamentType.elevenVsEleven:
        return '11v11';
    }
  }

  int get playersPerTeam {
    switch (this) {
      case TournamentType.oneVsOne:
        return 1;
      case TournamentType.fiveVsFive:
        return 5;
      case TournamentType.sevenVsSeven:
        return 7;
      case TournamentType.elevenVsEleven:
        return 11;
    }
  }

  String get value {
    switch (this) {
      case TournamentType.oneVsOne:
        return '1v1';
      case TournamentType.fiveVsFive:
        return '5v5';
      case TournamentType.sevenVsSeven:
        return '7v7';
      case TournamentType.elevenVsEleven:
        return '11v11';
    }
  }

  static TournamentType fromValue(String value) {
    switch (value) {
      case '1v1':
        return TournamentType.oneVsOne;
      case '5v5':
        return TournamentType.fiveVsFive;
      case '7v7':
        return TournamentType.sevenVsSeven;
      case '11v11':
        return TournamentType.elevenVsEleven;
      default:
        return TournamentType.fiveVsFive;
    }
  }
}

// Tournament Format
class TournamentFormat {
  final TournamentType type;
  final int playersPerTeam;
  final String displayName;

  const TournamentFormat({
    required this.type,
    required this.playersPerTeam,
    required this.displayName,
  });
}

// Tournament Prize
class TournamentPrize {
  final double totalAmount;
  final String currency;
  final List<PrizeDistribution> distribution;

  const TournamentPrize({
    required this.totalAmount,
    required this.currency,
    required this.distribution,
  });

  Map<String, dynamic> toJson() {
    return {
      'totalAmount': totalAmount,
      'currency': currency,
      'distribution': distribution.map((d) => d.toJson()).toList(),
    };
  }

  factory TournamentPrize.fromJson(Map<String, dynamic> json) {
    return TournamentPrize(
      totalAmount: json['totalAmount'].toDouble(),
      currency: json['currency'],
      distribution: (json['distribution'] as List)
          .map((d) => PrizeDistribution.fromJson(d))
          .toList(),
    );
  }
}

// Prize Distribution
class PrizeDistribution {
  final int position;
  final double amount;
  final String description;

  const PrizeDistribution({
    required this.position,
    required this.amount,
    required this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'position': position,
      'amount': amount,
      'description': description,
    };
  }

  factory PrizeDistribution.fromJson(Map<String, dynamic> json) {
    return PrizeDistribution(
      position: json['position'],
      amount: json['amount'].toDouble(),
      description: json['description'],
    );
  }
}

// Tournament Location
class TournamentLocation {
  final String name;
  final String address;
  final double latitude;
  final double longitude;

  const TournamentLocation({
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory TournamentLocation.fromJson(Map<String, dynamic> json) {
    return TournamentLocation(
      name: json['name'],
      address: json['address'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
    );
  }
}

// Tournament Participant Status
enum TournamentParticipantStatus {
  pending,
  confirmed,
  declined,
  cancelled,
}

extension TournamentParticipantStatusExtension on TournamentParticipantStatus {
  String get value {
    switch (this) {
      case TournamentParticipantStatus.pending:
        return 'pending';
      case TournamentParticipantStatus.confirmed:
        return 'confirmed';
      case TournamentParticipantStatus.declined:
        return 'declined';
      case TournamentParticipantStatus.cancelled:
        return 'cancelled';
    }
  }

  static TournamentParticipantStatus fromValue(String value) {
    switch (value) {
      case 'pending':
        return TournamentParticipantStatus.pending;
      case 'confirmed':
        return TournamentParticipantStatus.confirmed;
      case 'declined':
        return TournamentParticipantStatus.declined;
      case 'cancelled':
        return TournamentParticipantStatus.cancelled;
      default:
        return TournamentParticipantStatus.pending;
    }
  }
}

class Tournament {
  final String id;
  final String name;
  final String description;
  final TournamentFormat format;
  final String location;
  final String city;
  final DateTime startDate;
  final DateTime endDate;
  final DateTime registrationDeadline;
  final int maxParticipants;
  final int currentParticipants;
  final double entryFee;
  final String currency;
  final TournamentPrize prize;
  final String organizerId;
  final String organizerName;
  final TournamentStatus status;
  final List<String> tags;
  final String bannerImage;
  final List<TournamentParticipant> participants;
  final List<String> rules;
  final TournamentLocation venueDetails;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isFeatured;
  final int viewCount;

  Tournament({
    required this.id,
    required this.name,
    required this.description,
    required this.format,
    required this.location,
    required this.city,
    required this.startDate,
    required this.endDate,
    required this.registrationDeadline,
    required this.maxParticipants,
    required this.currentParticipants,
    required this.entryFee,
    required this.currency,
    required this.prize,
    required this.organizerId,
    required this.organizerName,
    required this.status,
    required this.tags,
    required this.bannerImage,
    required this.participants,
    required this.rules,
    required this.venueDetails,
    required this.createdAt,
    required this.updatedAt,
    this.isFeatured = false,
    this.viewCount = 0,
  });

  bool get hasAvailableSlots => currentParticipants < maxParticipants;
  bool get isRegistrationOpen => DateTime.now().isBefore(registrationDeadline);
  int get availableSlots => maxParticipants - currentParticipants;

  bool get isUpcoming => startDate.isAfter(DateTime.now());
  bool get isOngoing =>
      startDate.isBefore(DateTime.now()) && endDate.isAfter(DateTime.now());
  bool get isCompleted => endDate.isBefore(DateTime.now());

  String get statusDisplayText {
    switch (status) {
      case TournamentStatus.draft:
        return 'Draft';
      case TournamentStatus.registrationOpen:
        return isRegistrationOpen ? 'Registration Open' : 'Registration Closed';
      case TournamentStatus.registrationClosed:
        return 'Registration Closed';
      case TournamentStatus.inProgress:
        return 'In Progress';
      case TournamentStatus.completed:
        return 'Completed';
      case TournamentStatus.cancelled:
        return 'Cancelled';
    }
  }

  Tournament copyWith({
    String? id,
    String? name,
    String? description,
    TournamentFormat? format,
    String? location,
    String? city,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? registrationDeadline,
    int? maxParticipants,
    int? currentParticipants,
    double? entryFee,
    String? currency,
    TournamentPrize? prize,
    String? organizerId,
    String? organizerName,
    TournamentStatus? status,
    List<String>? tags,
    String? bannerImage,
    List<TournamentParticipant>? participants,
    List<String>? rules,
    TournamentLocation? venueDetails,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isFeatured,
    int? viewCount,
  }) {
    return Tournament(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      format: format ?? this.format,
      location: location ?? this.location,
      city: city ?? this.city,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      registrationDeadline: registrationDeadline ?? this.registrationDeadline,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      currentParticipants: currentParticipants ?? this.currentParticipants,
      entryFee: entryFee ?? this.entryFee,
      currency: currency ?? this.currency,
      prize: prize ?? this.prize,
      organizerId: organizerId ?? this.organizerId,
      organizerName: organizerName ?? this.organizerName,
      status: status ?? this.status,
      tags: tags ?? this.tags,
      bannerImage: bannerImage ?? this.bannerImage,
      participants: participants ?? this.participants,
      rules: rules ?? this.rules,
      venueDetails: venueDetails ?? this.venueDetails,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isFeatured: isFeatured ?? this.isFeatured,
      viewCount: viewCount ?? this.viewCount,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'format': format.type.value, // Use the value from TournamentTypeExtension
      'location': location,
      'city': city,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'registrationDeadline': registrationDeadline.toIso8601String(),
      'maxParticipants': maxParticipants,
      'currentParticipants': currentParticipants,
      'entryFee': entryFee,
      'currency': currency,
      'prize': prize.toJson(), // Assuming TournamentPrize has a toJson method
      'organizerId': organizerId,
      'organizerName': organizerName,
      'status': status.value, // Use the value from TournamentStatus
      'tags': tags,
      'bannerImage': bannerImage,
      'participants': participants.map((p) => p.toJson()).toList(),
      'rules': rules,
      'venueDetails': venueDetails
          .toJson(), // Assuming TournamentLocation has a toJson method
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isFeatured': isFeatured,
      'viewCount': viewCount,
    };
  }

  factory Tournament.fromJson(Map<String, dynamic> json) {
    return Tournament(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      format: TournamentFormat(
        type: TournamentTypeExtension.fromValue(json['format']),
        playersPerTeam:
            TournamentTypeExtension.fromValue(json['format']).playersPerTeam,
        displayName:
            TournamentTypeExtension.fromValue(json['format']).displayName,
      ),
      location: json['location'],
      city: json['city'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      registrationDeadline: DateTime.parse(json['registrationDeadline']),
      maxParticipants: json['maxParticipants'],
      currentParticipants: json['currentParticipants'],
      entryFee: json['entryFee'].toDouble(),
      currency: json['currency'],
      prize: TournamentPrize.fromJson(
          json['prize']), // Assuming TournamentPrize has a fromJson method
      organizerId: json['organizerId'],
      organizerName: json['organizerName'],
      status: TournamentStatusExtension.fromValue(json['status']),
      tags: List<String>.from(json['tags']),
      bannerImage: json['bannerImage'],
      participants: (json['participants'] as List)
          .map((p) => TournamentParticipant.fromJson(p))
          .toList(),
      rules: List<String>.from(json['rules']),
      venueDetails: TournamentLocation.fromJson(json[
          'venueDetails']), // Assuming TournamentLocation has a fromJson method
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      isFeatured: json['isFeatured'] ?? false,
      viewCount: json['viewCount'] ?? 0,
    );
  }
}

// Tournament Status Enum
enum TournamentStatus {
  draft,
  registrationOpen,
  registrationClosed,
  inProgress,
  completed,
  cancelled,
}

extension TournamentStatusExtension on TournamentStatus {
  String get displayName {
    switch (this) {
      case TournamentStatus.draft:
        return 'Draft';
      case TournamentStatus.registrationOpen:
        return 'Registration Open';
      case TournamentStatus.registrationClosed:
        return 'Registration Closed';
      case TournamentStatus.inProgress:
        return 'In Progress';
      case TournamentStatus.completed:
        return 'Completed';
      case TournamentStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get value {
    switch (this) {
      case TournamentStatus.draft:
        return 'draft';
      case TournamentStatus.registrationOpen:
        return 'registration_open';
      case TournamentStatus.registrationClosed:
        return 'registration_closed';
      case TournamentStatus.inProgress:
        return 'in_progress';
      case TournamentStatus.completed:
        return 'completed';
      case TournamentStatus.cancelled:
        return 'cancelled';
    }
  }

  static TournamentStatus fromValue(String value) {
    switch (value) {
      case 'draft':
        return TournamentStatus.draft;
      case 'registration_open':
        return TournamentStatus.registrationOpen;
      case 'registration_closed':
        return TournamentStatus.registrationClosed;
      case 'in_progress':
        return TournamentStatus.inProgress;
      case 'completed':
        return TournamentStatus.completed;
      case 'cancelled':
        return TournamentStatus.cancelled;
      default:
        return TournamentStatus.draft;
    }
  }
}

class TournamentParticipant {
  final String playerId;
  final String playerName;
  final String? playerAvatar;
  final String? teamId;
  final String? teamName;
  final DateTime joinedAt;
  final TournamentParticipantStatus status;

  TournamentParticipant({
    required this.playerId,
    required this.playerName,
    this.playerAvatar,
    this.teamId,
    this.teamName,
    required this.joinedAt,
    required this.status,
  });

  Map<String, dynamic> toJson() {
    return {
      'playerId': playerId,
      'playerName': playerName,
      'playerAvatar': playerAvatar,
      'teamId': teamId,
      'teamName': teamName,
      'joinedAt': joinedAt.toIso8601String(),
      'status': status.value, // Use the value from TournamentParticipantStatus
    };
  }

  factory TournamentParticipant.fromJson(Map<String, dynamic> json) {
    return TournamentParticipant(
      playerId: json['playerId'],
      playerName: json['playerName'],
      playerAvatar: json['playerAvatar'],
      teamId: json['teamId'],
      teamName: json['teamName'],
      joinedAt: DateTime.parse(json['joinedAt']),
      status: TournamentParticipantStatusExtension.fromValue(json['status']),
    );
  }
}

class TournamentEligibility {
  final int minAge;
  final int maxAge;
  final String ageGroup;
  final String skillLevel;
  final String gender;
  final List<String> requiredDocuments;
  final bool requiresMedicalCertificate;
  final bool requiresInsurance;
  final List<String> restrictions;

  TournamentEligibility({
    required this.minAge,
    required this.maxAge,
    required this.ageGroup,
    required this.skillLevel,
    required this.gender,
    required this.requiredDocuments,
    required this.requiresMedicalCertificate,
    required this.requiresInsurance,
    required this.restrictions,
  });

  Map<String, dynamic> toJson() {
    return {
      'minAge': minAge,
      'maxAge': maxAge,
      'ageGroup': ageGroup,
      'skillLevel': skillLevel,
      'gender': gender,
      'requiredDocuments': requiredDocuments,
      'requiresMedicalCertificate': requiresMedicalCertificate,
      'requiresInsurance': requiresInsurance,
      'restrictions': restrictions,
    };
  }

  factory TournamentEligibility.fromJson(Map<String, dynamic> json) {
    return TournamentEligibility(
      minAge: json['minAge'],
      maxAge: json['maxAge'],
      ageGroup: json['ageGroup'],
      skillLevel: json['skillLevel'],
      gender: json['gender'],
      requiredDocuments: List<String>.from(json['requiredDocuments']),
      requiresMedicalCertificate: json['requiresMedicalCertificate'],
      requiresInsurance: json['requiresInsurance'],
      restrictions: List<String>.from(json['restrictions']),
    );
  }
}
