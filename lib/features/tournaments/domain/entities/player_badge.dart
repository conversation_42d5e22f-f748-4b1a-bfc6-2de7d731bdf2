import 'package:freezed_annotation/freezed_annotation.dart';

part 'player_badge.freezed.dart';
part 'player_badge.g.dart';

@freezed
class PlayerBadge with _$PlayerBadge {
  const factory PlayerBadge({
    required String id,
    required String name,
    required String description,
    required String iconPath,
    required BadgeCategory category,
    required BadgeRarity rarity,
    required BadgeRequirement requirement,
    @Default(0) int xpReward,
    String? color,
  }) = _PlayerBadge;

  factory PlayerBadge.fromJson(Map<String, dynamic> json) =>
      _$PlayerBadgeFromJson(json);
}

@freezed
class BadgeRequirement with _$BadgeRequirement {
  const factory BadgeRequirement({
    required BadgeType type,
    required Map<String, dynamic> criteria,
    String? description,
  }) = _BadgeRequirement;

  factory BadgeRequirement.fromJson(Map<String, dynamic> json) =>
      _$BadgeRequirementFromJson(json);
}

@freezed
class PlayerBadgeProgress with _$PlayerBadgeProgress {
  const factory PlayerBadgeProgress({
    required String badgeId,
    required String playerId,
    required int currentProgress,
    required int targetProgress,
    required DateTime lastUpdated,
    @Default(false) bool isUnlocked,
    DateTime? unlockedAt,
  }) = _PlayerBadgeProgress;

  factory PlayerBadgeProgress.fromJson(Map<String, dynamic> json) =>
      _$PlayerBadgeProgressFromJson(json);
}

@freezed
class PlayerBadgeCollection with _$PlayerBadgeCollection {
  const factory PlayerBadgeCollection({
    required String playerId,
    required List<PlayerBadgeProgress> badges,
    required int totalXP,
    required DateTime lastUpdated,
  }) = _PlayerBadgeCollection;

  factory PlayerBadgeCollection.fromJson(Map<String, dynamic> json) =>
      _$PlayerBadgeCollectionFromJson(json);
}

@freezed
class TournamentStats with _$TournamentStats {
  const factory TournamentStats({
    required String playerId,
    required int tournamentsJoined,
    required int tournamentsWon,
    required int tournamentsCompleted,
    required double totalSpent,
    required double totalWon,
    required int differentCities,
    required int currentStreak,
    required int longestStreak,
    required DateTime lastTournamentDate,
    @Default([]) List<String> citiesVisited,
  }) = _TournamentStats;

  factory TournamentStats.fromJson(Map<String, dynamic> json) =>
      _$TournamentStatsFromJson(json);
}

enum BadgeCategory {
  @JsonValue('participation')
  participation,
  @JsonValue('achievement')
  achievement,
  @JsonValue('exploration')
  exploration,
  @JsonValue('spending')
  spending,
  @JsonValue('streak')
  streak,
  @JsonValue('social')
  social,
}

enum BadgeRarity {
  @JsonValue('common')
  common,
  @JsonValue('uncommon')
  uncommon,
  @JsonValue('rare')
  rare,
  @JsonValue('epic')
  epic,
  @JsonValue('legendary')
  legendary,
}

enum BadgeType {
  @JsonValue('tournament_count')
  tournamentCount,
  @JsonValue('city_count')
  cityCount,
  @JsonValue('spending_amount')
  spendingAmount,
  @JsonValue('win_streak')
  winStreak,
  @JsonValue('format_mastery')
  formatMastery,
  @JsonValue('time_based')
  timeBased,
}

extension BadgeRarityExtension on BadgeRarity {
  String get displayName {
    switch (this) {
      case BadgeRarity.common:
        return 'Common';
      case BadgeRarity.uncommon:
        return 'Uncommon';
      case BadgeRarity.rare:
        return 'Rare';
      case BadgeRarity.epic:
        return 'Epic';
      case BadgeRarity.legendary:
        return 'Legendary';
    }
  }

  String get color {
    switch (this) {
      case BadgeRarity.common:
        return '#8E8E93';
      case BadgeRarity.uncommon:
        return '#34C759';
      case BadgeRarity.rare:
        return '#007AFF';
      case BadgeRarity.epic:
        return '#AF52DE';
      case BadgeRarity.legendary:
        return '#FF9500';
    }
  }

  int get xpMultiplier {
    switch (this) {
      case BadgeRarity.common:
        return 1;
      case BadgeRarity.uncommon:
        return 2;
      case BadgeRarity.rare:
        return 3;
      case BadgeRarity.epic:
        return 5;
      case BadgeRarity.legendary:
        return 10;
    }
  }
}

extension PlayerBadgeProgressExtension on PlayerBadgeProgress {
  double get progressPercentage {
    if (targetProgress == 0) return 0.0;
    return (currentProgress / targetProgress).clamp(0.0, 1.0);
  }

  bool get isCompleted => currentProgress >= targetProgress;
}

