// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'player_badge.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PlayerBadgeImpl _$$PlayerBadgeImplFromJson(Map<String, dynamic> json) =>
    _$PlayerBadgeImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      iconPath: json['iconPath'] as String,
      category: $enumDecode(_$BadgeCategoryEnumMap, json['category']),
      rarity: $enumDecode(_$BadgeRarityEnumMap, json['rarity']),
      requirement: BadgeRequirement.fromJson(
        json['requirement'] as Map<String, dynamic>,
      ),
      xpReward: (json['xpReward'] as num?)?.toInt() ?? 0,
      color: json['color'] as String?,
    );

Map<String, dynamic> _$$PlayerBadgeImplToJson(_$PlayerBadgeImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'iconPath': instance.iconPath,
      'category': _$BadgeCategoryEnumMap[instance.category]!,
      'rarity': _$BadgeRarityEnumMap[instance.rarity]!,
      'requirement': instance.requirement,
      'xpReward': instance.xpReward,
      'color': instance.color,
    };

const _$BadgeCategoryEnumMap = {
  BadgeCategory.participation: 'participation',
  BadgeCategory.achievement: 'achievement',
  BadgeCategory.exploration: 'exploration',
  BadgeCategory.spending: 'spending',
  BadgeCategory.streak: 'streak',
  BadgeCategory.social: 'social',
};

const _$BadgeRarityEnumMap = {
  BadgeRarity.common: 'common',
  BadgeRarity.uncommon: 'uncommon',
  BadgeRarity.rare: 'rare',
  BadgeRarity.epic: 'epic',
  BadgeRarity.legendary: 'legendary',
};

_$BadgeRequirementImpl _$$BadgeRequirementImplFromJson(
  Map<String, dynamic> json,
) => _$BadgeRequirementImpl(
  type: $enumDecode(_$BadgeTypeEnumMap, json['type']),
  criteria: json['criteria'] as Map<String, dynamic>,
  description: json['description'] as String?,
);

Map<String, dynamic> _$$BadgeRequirementImplToJson(
  _$BadgeRequirementImpl instance,
) => <String, dynamic>{
  'type': _$BadgeTypeEnumMap[instance.type]!,
  'criteria': instance.criteria,
  'description': instance.description,
};

const _$BadgeTypeEnumMap = {
  BadgeType.tournamentCount: 'tournament_count',
  BadgeType.cityCount: 'city_count',
  BadgeType.spendingAmount: 'spending_amount',
  BadgeType.winStreak: 'win_streak',
  BadgeType.formatMastery: 'format_mastery',
  BadgeType.timeBased: 'time_based',
};

_$PlayerBadgeProgressImpl _$$PlayerBadgeProgressImplFromJson(
  Map<String, dynamic> json,
) => _$PlayerBadgeProgressImpl(
  badgeId: json['badgeId'] as String,
  playerId: json['playerId'] as String,
  currentProgress: (json['currentProgress'] as num).toInt(),
  targetProgress: (json['targetProgress'] as num).toInt(),
  lastUpdated: DateTime.parse(json['lastUpdated'] as String),
  isUnlocked: json['isUnlocked'] as bool? ?? false,
  unlockedAt:
      json['unlockedAt'] == null
          ? null
          : DateTime.parse(json['unlockedAt'] as String),
);

Map<String, dynamic> _$$PlayerBadgeProgressImplToJson(
  _$PlayerBadgeProgressImpl instance,
) => <String, dynamic>{
  'badgeId': instance.badgeId,
  'playerId': instance.playerId,
  'currentProgress': instance.currentProgress,
  'targetProgress': instance.targetProgress,
  'lastUpdated': instance.lastUpdated.toIso8601String(),
  'isUnlocked': instance.isUnlocked,
  'unlockedAt': instance.unlockedAt?.toIso8601String(),
};

_$PlayerBadgeCollectionImpl _$$PlayerBadgeCollectionImplFromJson(
  Map<String, dynamic> json,
) => _$PlayerBadgeCollectionImpl(
  playerId: json['playerId'] as String,
  badges:
      (json['badges'] as List<dynamic>)
          .map((e) => PlayerBadgeProgress.fromJson(e as Map<String, dynamic>))
          .toList(),
  totalXP: (json['totalXP'] as num).toInt(),
  lastUpdated: DateTime.parse(json['lastUpdated'] as String),
);

Map<String, dynamic> _$$PlayerBadgeCollectionImplToJson(
  _$PlayerBadgeCollectionImpl instance,
) => <String, dynamic>{
  'playerId': instance.playerId,
  'badges': instance.badges,
  'totalXP': instance.totalXP,
  'lastUpdated': instance.lastUpdated.toIso8601String(),
};

_$TournamentStatsImpl _$$TournamentStatsImplFromJson(
  Map<String, dynamic> json,
) => _$TournamentStatsImpl(
  playerId: json['playerId'] as String,
  tournamentsJoined: (json['tournamentsJoined'] as num).toInt(),
  tournamentsWon: (json['tournamentsWon'] as num).toInt(),
  tournamentsCompleted: (json['tournamentsCompleted'] as num).toInt(),
  totalSpent: (json['totalSpent'] as num).toDouble(),
  totalWon: (json['totalWon'] as num).toDouble(),
  differentCities: (json['differentCities'] as num).toInt(),
  currentStreak: (json['currentStreak'] as num).toInt(),
  longestStreak: (json['longestStreak'] as num).toInt(),
  lastTournamentDate: DateTime.parse(json['lastTournamentDate'] as String),
  citiesVisited:
      (json['citiesVisited'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
);

Map<String, dynamic> _$$TournamentStatsImplToJson(
  _$TournamentStatsImpl instance,
) => <String, dynamic>{
  'playerId': instance.playerId,
  'tournamentsJoined': instance.tournamentsJoined,
  'tournamentsWon': instance.tournamentsWon,
  'tournamentsCompleted': instance.tournamentsCompleted,
  'totalSpent': instance.totalSpent,
  'totalWon': instance.totalWon,
  'differentCities': instance.differentCities,
  'currentStreak': instance.currentStreak,
  'longestStreak': instance.longestStreak,
  'lastTournamentDate': instance.lastTournamentDate.toIso8601String(),
  'citiesVisited': instance.citiesVisited,
};
