import 'tournament.dart';

// Simple plain Dart classes for tournament filters
class TournamentFilter {
  final List<TournamentType> formats;
  final List<String> locations;
  final DateRangeFilter? dateRange;
  final PriceRangeFilter? entryFeeRange;
  final PriceRangeFilter? prizeRange;
  final bool availableSlotsOnly;
  final List<TournamentStatus> statuses;
  final List<String> organizers;
  final TournamentSortBy sortBy;
  final SortOrder sortOrder;

  const TournamentFilter({
    this.formats = const [],
    this.locations = const [],
    this.dateRange,
    this.entryFeeRange,
    this.prizeRange,
    this.availableSlotsOnly = false,
    this.statuses = const [],
    this.organizers = const [],
    this.sortBy = TournamentSortBy.soonestFirst,
    this.sortOrder = SortOrder.ascending,
  });

  bool get isEmpty =>
      formats.isEmpty &&
      locations.isEmpty &&
      dateRange == null &&
      entryFeeRange == null &&
      prizeRange == null &&
      !availableSlotsOnly &&
      statuses.isEmpty &&
      organizers.isEmpty;

  bool get hasActiveFilters => !isEmpty;

  int get activeFilterCount {
    int count = 0;
    if (formats.isNotEmpty) count++;
    if (locations.isNotEmpty) count++;
    if (dateRange != null) count++;
    if (entryFeeRange != null) count++;
    if (prizeRange != null) count++;
    if (availableSlotsOnly) count++;
    if (statuses.isNotEmpty) count++;
    if (organizers.isNotEmpty) count++;
    return count;
  }

  TournamentFilter copyWith({
    List<TournamentType>? formats,
    List<String>? locations,
    DateRangeFilter? dateRange,
    PriceRangeFilter? entryFeeRange,
    PriceRangeFilter? prizeRange,
    bool? availableSlotsOnly,
    List<TournamentStatus>? statuses,
    List<String>? organizers,
    TournamentSortBy? sortBy,
    SortOrder? sortOrder,
  }) {
    return TournamentFilter(
      formats: formats ?? this.formats,
      locations: locations ?? this.locations,
      dateRange: dateRange ?? this.dateRange,
      entryFeeRange: entryFeeRange ?? this.entryFeeRange,
      prizeRange: prizeRange ?? this.prizeRange,
      availableSlotsOnly: availableSlotsOnly ?? this.availableSlotsOnly,
      statuses: statuses ?? this.statuses,
      organizers: organizers ?? this.organizers,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  TournamentFilter clearAll() => TournamentFilter();

  TournamentFilter clearFormats() => copyWith(formats: []);
  TournamentFilter clearLocations() => copyWith(locations: []);
  TournamentFilter clearDateRange() => copyWith(dateRange: null);
  TournamentFilter clearEntryFeeRange() => copyWith(entryFeeRange: null);
  TournamentFilter clearPrizeRange() => copyWith(prizeRange: null);
  TournamentFilter clearAvailableSlots() => copyWith(availableSlotsOnly: false);
  TournamentFilter clearStatuses() => copyWith(statuses: []);
  TournamentFilter clearOrganizers() => copyWith(organizers: []);

  Map<String, dynamic> toJson() {
    return {
      'formats': formats.map((f) => f.value).toList(),
      'locations': locations,
      'dateRange': dateRange?.toJson(),
      'entryFeeRange': entryFeeRange?.toJson(),
      'prizeRange': prizeRange?.toJson(),
      'availableSlotsOnly': availableSlotsOnly,
      'statuses': statuses,
      'organizers': organizers,
      'sortBy': sortBy.index,
      'sortOrder': sortOrder.index,
    };
  }

  factory TournamentFilter.fromJson(Map<String, dynamic> json) {
    return TournamentFilter(
      formats: (json['formats'] as List<dynamic>?)
              ?.map((e) => TournamentTypeExtension.fromValue(e as String))
              .toList() ??
          [],
      locations: List<String>.from(json['locations'] ?? []),
      dateRange: json['dateRange'] != null
          ? DateRangeFilter.fromJson(json['dateRange'])
          : null,
      entryFeeRange: json['entryFeeRange'] != null
          ? PriceRangeFilter.fromJson(json['entryFeeRange'])
          : null,
      prizeRange: json['prizeRange'] != null
          ? PriceRangeFilter.fromJson(json['prizeRange'])
          : null,
      availableSlotsOnly: json['availableSlotsOnly'] ?? false,
      statuses: (json['statuses'] as List<dynamic>?)
              ?.map((e) => TournamentStatus.values[e as int])
              .toList() ??
          [],
      organizers: List<String>.from(json['organizers'] ?? []),
      sortBy: TournamentSortBy.values[json['sortBy'] ?? 0],
      sortOrder: SortOrder.values[json['sortOrder'] ?? 0],
    );
  }
}

class DateRangeFilter {
  final DateTime? startDate;
  final DateTime? endDate;

  const DateRangeFilter({this.startDate, this.endDate});

  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
    };
  }

  factory DateRangeFilter.fromJson(Map<String, dynamic> json) {
    return DateRangeFilter(
      startDate:
          json['startDate'] != null ? DateTime.parse(json['startDate']) : null,
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
    );
  }
}

class PriceRangeFilter {
  final double? minAmount;
  final double? maxAmount;

  const PriceRangeFilter({this.minAmount, this.maxAmount});

  Map<String, dynamic> toJson() {
    return {
      'minAmount': minAmount,
      'maxAmount': maxAmount,
    };
  }

  factory PriceRangeFilter.fromJson(Map<String, dynamic> json) {
    return PriceRangeFilter(
      minAmount: json['minAmount']?.toDouble(),
      maxAmount: json['maxAmount']?.toDouble(),
    );
  }
}

class TournamentSearchQuery {
  final String searchTerm;
  final TournamentFilter filter;
  final int limit;
  final int offset;

  const TournamentSearchQuery({
    this.searchTerm = '',
    this.filter = const TournamentFilter(),
    this.limit = 20,
    this.offset = 0,
  });

  Map<String, dynamic> toJson() {
    return {
      'searchTerm': searchTerm,
      'filter': filter,
      'limit': limit,
      'offset': offset,
    };
  }

  factory TournamentSearchQuery.fromJson(Map<String, dynamic> json) {
    return TournamentSearchQuery(
      searchTerm: json['searchTerm'] ?? '',
      filter: TournamentFilter.fromJson(json['filter'] ?? {}),
      limit: json['limit'] ?? 20,
      offset: json['offset'] ?? 0,
    );
  }
}

class TournamentSearchResult {
  final List<Tournament> tournaments;
  final int totalCount;
  final bool hasMore;
  final int currentPage;
  final TournamentSearchQuery query;

  TournamentSearchResult({
    required this.tournaments,
    required this.totalCount,
    required this.hasMore,
    required this.currentPage,
    required this.query,
  });

  Map<String, dynamic> toJson() {
    return {
      'tournaments': tournaments.map((t) => t.toJson()).toList(),
      'totalCount': totalCount,
      'hasMore': hasMore,
      'currentPage': currentPage,
      'query': query.toJson(),
    };
  }

  factory TournamentSearchResult.fromJson(Map<String, dynamic> json) {
    return TournamentSearchResult(
      tournaments: (json['tournaments'] as List)
          .map((t) => Tournament.fromJson(t))
          .toList(),
      totalCount: json['totalCount'],
      hasMore: json['hasMore'],
      currentPage: json['currentPage'],
      query: TournamentSearchQuery.fromJson(json['query']),
    );
  }
}

enum TournamentSortBy {
  soonestFirst,
  latestFirst,
  lowestFee,
  highestFee,
  highestPrize,
  lowestPrize,
  mostPopular,
  availableSlots,
  nearestLocation,
  createdDate,
}

enum SortOrder {
  ascending,
  descending,
}

extension TournamentSortByExtension on TournamentSortBy {
  String get displayName {
    switch (this) {
      case TournamentSortBy.soonestFirst:
        return 'Soonest First';
      case TournamentSortBy.latestFirst:
        return 'Latest First';
      case TournamentSortBy.lowestFee:
        return 'Lowest Fee';
      case TournamentSortBy.highestFee:
        return 'Highest Fee';
      case TournamentSortBy.highestPrize:
        return 'Highest Prize';
      case TournamentSortBy.lowestPrize:
        return 'Lowest Prize';
      case TournamentSortBy.mostPopular:
        return 'Most Popular';
      case TournamentSortBy.availableSlots:
        return 'Available Slots';
      case TournamentSortBy.nearestLocation:
        return 'Nearest Location';
      case TournamentSortBy.createdDate:
        return 'Recently Added';
    }
  }

  String get description {
    switch (this) {
      case TournamentSortBy.soonestFirst:
        return 'Show tournaments starting soon first';
      case TournamentSortBy.latestFirst:
        return 'Show latest tournaments first';
      case TournamentSortBy.lowestFee:
        return 'Show cheapest tournaments first';
      case TournamentSortBy.highestFee:
        return 'Show most expensive tournaments first';
      case TournamentSortBy.highestPrize:
        return 'Show highest prize tournaments first';
      case TournamentSortBy.lowestPrize:
        return 'Show lowest prize tournaments first';
      case TournamentSortBy.mostPopular:
        return 'Show most popular tournaments first';
      case TournamentSortBy.availableSlots:
        return 'Show tournaments with most available slots';
      case TournamentSortBy.nearestLocation:
        return 'Show nearest tournaments first';
      case TournamentSortBy.createdDate:
        return 'Show recently added tournaments first';
    }
  }
}
