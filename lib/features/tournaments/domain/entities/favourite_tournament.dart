import 'package:freezed_annotation/freezed_annotation.dart';

part 'favourite_tournament.freezed.dart';
part 'favourite_tournament.g.dart';

@freezed
class FavouriteTournament with _$FavouriteTournament {
  const factory FavouriteTournament({
    required String id,
    required String playerId,
    required String tournamentId,
    required DateTime addedAt,
    @Default(true) bool isActive,
  }) = _FavouriteTournament;

  factory FavouriteTournament.fromJson(Map<String, dynamic> json) =>
      _$FavouriteTournamentFromJson(json);
}

@freezed
class FavouriteTournamentDetails with _$FavouriteTournamentDetails {
  const factory FavouriteTournamentDetails({
    required FavouriteTournament favourite,
    required String tournamentName,
    required String tournamentLocation,
    required DateTime tournamentStartDate,
    String? tournamentBannerImage,
  }) = _FavouriteTournamentDetails;

  factory FavouriteTournamentDetails.fromJson(Map<String, dynamic> json) =>
      _$FavouriteTournamentDetailsFromJson(json);
}

