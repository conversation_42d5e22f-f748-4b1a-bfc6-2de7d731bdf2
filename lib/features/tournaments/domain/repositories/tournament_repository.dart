import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../entities/tournament.dart';
import '../entities/tournament_filter.dart';
import '../entities/favourite_tournament.dart';
import '../entities/player_badge.dart';

abstract interface class TournamentRepository {
  /// Get tournaments with optional filtering and pagination
  Future<Either<AppError, TournamentSearchResult>> getTournaments({
    TournamentSearchQuery? query,
  });

  /// Get a single tournament by ID
  Future<Either<AppError, Tournament>> getTournamentById(String id);

  /// Join a tournament
  Future<Either<AppError, Tournament>> joinTournament(
    String tournamentId,
    String playerId,
  );

  /// Leave a tournament
  Future<Either<AppError, Tournament>> leaveTournament(
    String tournamentId,
    String playerId,
  );

  /// Get featured tournaments
  Future<Either<AppError, List<Tournament>>> getFeaturedTournaments({
    int limit = 5,
  });

  /// Get nearby tournaments based on location
  Future<Either<AppError, List<Tournament>>> getNearbyTournaments({
    required double latitude,
    required double longitude,
    double radiusKm = 50.0,
    int limit = 20,
  });

  /// Search tournaments by name or tags
  Future<Either<AppError, List<Tournament>>> searchTournaments({
    required String searchTerm,
    TournamentFilter? filter,
    int limit = 20,
    int offset = 0,
  });

  /// Get tournaments by organizer
  Future<Either<AppError, List<Tournament>>> getTournamentsByOrganizer({
    required String organizerId,
    int limit = 20,
    int offset = 0,
  });

  /// Get tournaments the player has joined
  Future<Either<AppError, List<Tournament>>> getPlayerTournaments({
    required String playerId,
    int limit = 20,
    int offset = 0,
  });

  /// Favourite Tournaments
  Future<Either<AppError, List<FavouriteTournamentDetails>>>
  getFavouriteTournaments(String playerId);

  Future<Either<AppError, FavouriteTournament>> addToFavourites({
    required String playerId,
    required String tournamentId,
  });

  Future<Either<AppError, void>> removeFromFavourites({
    required String playerId,
    required String tournamentId,
  });

  Future<Either<AppError, bool>> isTournamentFavourite({
    required String playerId,
    required String tournamentId,
  });

  /// Badge System
  Future<Either<AppError, List<PlayerBadge>>> getAvailableBadges();

  Future<Either<AppError, PlayerBadgeCollection>> getPlayerBadges(
    String playerId,
  );

  Future<Either<AppError, TournamentStats>> getPlayerTournamentStats(
    String playerId,
  );

  Future<Either<AppError, PlayerBadgeProgress>> updateBadgeProgress({
    required String playerId,
    required String badgeId,
    required int progress,
  });

  Future<Either<AppError, List<PlayerBadge>>> checkForNewBadges({
    required String playerId,
    required TournamentStats stats,
  });

  /// Tournament Statistics
  Future<Either<AppError, Map<String, dynamic>>> getTournamentAnalytics();

  Future<Either<AppError, List<String>>> getPopularLocations({int limit = 10});

  Future<Either<AppError, List<String>>> getPopularOrganizers({int limit = 10});
}

