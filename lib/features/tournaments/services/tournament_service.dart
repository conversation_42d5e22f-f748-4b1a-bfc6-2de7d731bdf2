import '../domain/entities/tournament.dart';
import '../domain/entities/tournament_filter.dart';

class TournamentService {
  static final List<Tournament> _mockTournaments = [
    Tournament(
      id: '1',
      name: 'Summer Cup 2024',
      description:
          'Annual summer football tournament for all skill levels. Join us for an exciting competition with great prizes!',
      format: const TournamentFormat(
        type: TournamentType.fiveVsFive,
        playersPerTeam: 5,
        displayName: '5v5',
      ),
      location: 'Kathmandu Sports Complex',
      city: 'Kathmandu',
      startDate: DateTime.now().add(const Duration(days: 30)),
      endDate: DateTime.now().add(const Duration(days: 32)),
      registrationDeadline: DateTime.now().add(const Duration(days: 25)),
      maxParticipants: 16,
      currentParticipants: 12,
      entryFee: 500.0,
      currency: 'NPR',
      prize: const TournamentPrize(
        totalAmount: 50000.0,
        currency: 'NPR',
        distribution: [
          PrizeDistribution(
              position: 1, amount: 25000, description: '1st Place'),
          PrizeDistribution(
              position: 2, amount: 15000, description: '2nd Place'),
          PrizeDistribution(
              position: 3, amount: 10000, description: '3rd Place'),
        ],
      ),
      organizerId: 'org_1',
      organizerName: 'Kathmandu Football Association',
      status: TournamentStatus.registrationOpen,
      tags: ['Summer', '5v5', 'Beginner Friendly'],
      bannerImage:
          'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800',
      participants: [],
      rules: [
        'Teams must have 5-7 players',
        'Matches are 20 minutes each',
        'No professional players allowed',
        'Fair play is mandatory',
      ],
      venueDetails: const TournamentLocation(
        name: 'Kathmandu Sports Complex',
        address: 'Kathmandu, Nepal',
        latitude: 27.7172,
        longitude: 85.3240,
      ),
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
      updatedAt: DateTime.now(),
    ),
    Tournament(
      id: '2',
      name: 'Youth Championship Under-18',
      description:
          'Exclusive tournament for players under 18 years old. Show your skills and compete with the best young talents!',
      format: const TournamentFormat(
        type: TournamentType.sevenVsSeven,
        playersPerTeam: 7,
        displayName: '7v7',
      ),
      location: 'Pokhara Stadium',
      city: 'Pokhara',
      startDate: DateTime.now().add(const Duration(days: 45)),
      endDate: DateTime.now().add(const Duration(days: 47)),
      registrationDeadline: DateTime.now().add(const Duration(days: 40)),
      maxParticipants: 12,
      currentParticipants: 8,
      entryFee: 300.0,
      currency: 'NPR',
      prize: const TournamentPrize(
        totalAmount: 25000.0,
        currency: 'NPR',
        distribution: [
          PrizeDistribution(
              position: 1, amount: 15000, description: '1st Place'),
          PrizeDistribution(
              position: 2, amount: 10000, description: '2nd Place'),
        ],
      ),
      organizerId: 'org_2',
      organizerName: 'Pokhara Youth Sports Club',
      status: TournamentStatus.registrationOpen,
      tags: ['Youth', 'Under-18', '7v7'],
      bannerImage:
          'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800',
      participants: [],
      rules: [
        'Only players under 18 years old',
        'Teams must have 7-9 players',
        'Matches are 25 minutes each',
        'School ID required for verification',
      ],
      venueDetails: const TournamentLocation(
        name: 'Pokhara Stadium',
        address: 'Pokhara, Nepal',
        latitude: 28.2096,
        longitude: 83.9856,
      ),
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now(),
    ),
    Tournament(
      id: '3',
      name: 'Professional League',
      description:
          'High-level competition for experienced players. Compete with the best and win prestigious prizes!',
      format: const TournamentFormat(
        type: TournamentType.elevenVsEleven,
        playersPerTeam: 11,
        displayName: '11v11',
      ),
      location: 'Dasarath Rangasala',
      city: 'Kathmandu',
      startDate: DateTime.now().add(const Duration(days: 60)),
      endDate: DateTime.now().add(const Duration(days: 90)),
      registrationDeadline: DateTime.now().add(const Duration(days: 50)),
      maxParticipants: 8,
      currentParticipants: 6,
      entryFee: 2000.0,
      currency: 'NPR',
      prize: const TournamentPrize(
        totalAmount: 200000.0,
        currency: 'NPR',
        distribution: [
          PrizeDistribution(
              position: 1, amount: 100000, description: '1st Place'),
          PrizeDistribution(
              position: 2, amount: 60000, description: '2nd Place'),
          PrizeDistribution(
              position: 3, amount: 40000, description: '3rd Place'),
        ],
      ),
      organizerId: 'org_3',
      organizerName: 'Nepal Football Association',
      status: TournamentStatus.registrationOpen,
      tags: ['Professional', '11v11', 'High Level'],
      bannerImage:
          'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800',
      participants: [],
      rules: [
        'Professional players welcome',
        'Teams must have 11-15 players',
        'Matches are 90 minutes each',
        'Professional referees',
      ],
      venueDetails: const TournamentLocation(
        name: 'Dasarath Rangasala',
        address: 'Kathmandu, Nepal',
        latitude: 27.7172,
        longitude: 85.3240,
      ),
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now(),
    ),
    Tournament(
      id: '4',
      name: 'Women\'s Championship',
      description:
          'Empowering women through football. Join this exclusive tournament for female players!',
      format: const TournamentFormat(
        type: TournamentType.sevenVsSeven,
        playersPerTeam: 7,
        displayName: '7v7',
      ),
      location: 'Lalitpur Sports Ground',
      city: 'Lalitpur',
      startDate: DateTime.now().add(const Duration(days: 75)),
      endDate: DateTime.now().add(const Duration(days: 77)),
      registrationDeadline: DateTime.now().add(const Duration(days: 70)),
      maxParticipants: 10,
      currentParticipants: 4,
      entryFee: 400.0,
      currency: 'NPR',
      prize: const TournamentPrize(
        totalAmount: 30000.0,
        currency: 'NPR',
        distribution: [
          PrizeDistribution(
              position: 1, amount: 18000, description: '1st Place'),
          PrizeDistribution(
              position: 2, amount: 12000, description: '2nd Place'),
        ],
      ),
      organizerId: 'org_4',
      organizerName: 'Women\'s Football Association',
      status: TournamentStatus.registrationOpen,
      tags: ['Women', '7v7', 'Empowerment'],
      bannerImage:
          'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800',
      participants: [],
      rules: [
        'Only female players allowed',
        'Teams must have 7-9 players',
        'Matches are 25 minutes each',
        'Promoting women in sports',
      ],
      venueDetails: const TournamentLocation(
        name: 'Lalitpur Sports Ground',
        address: 'Lalitpur, Nepal',
        latitude: 27.6667,
        longitude: 85.3333,
      ),
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now(),
    ),
    Tournament(
      id: '5',
      name: 'Under-16 Development League',
      description:
          'Development tournament for young talents under 16. Focus on skill development and fair play!',
      format: const TournamentFormat(
        type: TournamentType.fiveVsFive,
        playersPerTeam: 5,
        displayName: '5v5',
      ),
      location: 'Bhaktapur Sports Complex',
      city: 'Bhaktapur',
      startDate: DateTime.now().add(const Duration(days: 90)),
      endDate: DateTime.now().add(const Duration(days: 92)),
      registrationDeadline: DateTime.now().add(const Duration(days: 85)),
      maxParticipants: 14,
      currentParticipants: 10,
      entryFee: 200.0,
      currency: 'NPR',
      prize: const TournamentPrize(
        totalAmount: 15000.0,
        currency: 'NPR',
        distribution: [
          PrizeDistribution(
              position: 1, amount: 9000, description: '1st Place'),
          PrizeDistribution(
              position: 2, amount: 6000, description: '2nd Place'),
        ],
      ),
      organizerId: 'org_5',
      organizerName: 'Youth Development Association',
      status: TournamentStatus.registrationOpen,
      tags: ['Under-16', '5v5', 'Development'],
      bannerImage:
          'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800',
      participants: [],
      rules: [
        'Only players under 16 years old',
        'Teams must have 5-7 players',
        'Matches are 20 minutes each',
        'Focus on development, not competition',
      ],
      venueDetails: const TournamentLocation(
        name: 'Bhaktapur Sports Complex',
        address: 'Bhaktapur, Nepal',
        latitude: 27.6710,
        longitude: 85.4298,
      ),
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
      updatedAt: DateTime.now(),
    ),
  ];

  Future<List<Tournament>> getTournaments({
    TournamentFilter? filter,
    String? searchTerm,
    int limit = 20,
    int offset = 0,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    List<Tournament> filteredTournaments = List.from(_mockTournaments);

    // Apply search filter
    if (searchTerm != null && searchTerm.isNotEmpty) {
      filteredTournaments = filteredTournaments.where((tournament) {
        return tournament.name
                .toLowerCase()
                .contains(searchTerm.toLowerCase()) ||
            tournament.description
                .toLowerCase()
                .contains(searchTerm.toLowerCase()) ||
            tournament.location
                .toLowerCase()
                .contains(searchTerm.toLowerCase()) ||
            tournament.city.toLowerCase().contains(searchTerm.toLowerCase()) ||
            tournament.tags.any(
                (tag) => tag.toLowerCase().contains(searchTerm.toLowerCase()));
      }).toList();
    }

    // Apply filters
    if (filter != null) {
      // Format filter
      if (filter.formats.isNotEmpty) {
        filteredTournaments = filteredTournaments.where((tournament) {
          return filter.formats.contains(tournament.format.type);
        }).toList();
      }

      // Location filter
      if (filter.locations.isNotEmpty) {
        filteredTournaments = filteredTournaments.where((tournament) {
          return filter.locations.contains(tournament.city);
        }).toList();
      }

      // Date range filter
      if (filter.dateRange != null) {
        filteredTournaments = filteredTournaments.where((tournament) {
          if (filter.dateRange!.startDate != null &&
              tournament.startDate.isBefore(filter.dateRange!.startDate!)) {
            return false;
          }
          if (filter.dateRange!.endDate != null &&
              tournament.endDate.isAfter(filter.dateRange!.endDate!)) {
            return false;
          }
          return true;
        }).toList();
      }

      // Entry fee range filter
      if (filter.entryFeeRange != null) {
        filteredTournaments = filteredTournaments.where((tournament) {
          if (filter.entryFeeRange!.minAmount != null &&
              tournament.entryFee < filter.entryFeeRange!.minAmount!) {
            return false;
          }
          if (filter.entryFeeRange!.maxAmount != null &&
              tournament.entryFee > filter.entryFeeRange!.maxAmount!) {
            return false;
          }
          return true;
        }).toList();
      }

      // Available slots filter
      if (filter.availableSlotsOnly) {
        filteredTournaments = filteredTournaments.where((tournament) {
          return tournament.currentParticipants < tournament.maxParticipants;
        }).toList();
      }

      // Status filter
      if (filter.statuses.isNotEmpty) {
        filteredTournaments = filteredTournaments.where((tournament) {
          return filter.statuses.contains(tournament.status);
        }).toList();
      }

      // Organizer filter
      if (filter.organizers.isNotEmpty) {
        filteredTournaments = filteredTournaments.where((tournament) {
          return filter.organizers.contains(tournament.organizerName);
        }).toList();
      }
    }

    // Apply sorting
    if (filter?.sortBy != null) {
      switch (filter!.sortBy) {
        case TournamentSortBy.soonestFirst:
          filteredTournaments
              .sort((a, b) => a.startDate.compareTo(b.startDate));
          break;
        case TournamentSortBy.latestFirst:
          filteredTournaments
              .sort((a, b) => b.startDate.compareTo(a.startDate));
          break;
        case TournamentSortBy.lowestFee:
          filteredTournaments.sort((a, b) => a.entryFee.compareTo(b.entryFee));
          break;
        case TournamentSortBy.highestFee:
          filteredTournaments.sort((a, b) => b.entryFee.compareTo(a.entryFee));
          break;
        case TournamentSortBy.highestPrize:
          filteredTournaments.sort(
              (a, b) => b.prize.totalAmount.compareTo(a.prize.totalAmount));
          break;
        case TournamentSortBy.lowestPrize:
          filteredTournaments.sort(
              (a, b) => a.prize.totalAmount.compareTo(b.prize.totalAmount));
          break;
        case TournamentSortBy.mostPopular:
          filteredTournaments.sort(
              (a, b) => b.currentParticipants.compareTo(a.currentParticipants));
          break;
        case TournamentSortBy.availableSlots:
          filteredTournaments.sort((a, b) {
            final aSlots = a.maxParticipants - a.currentParticipants;
            final bSlots = b.maxParticipants - b.currentParticipants;
            return bSlots.compareTo(aSlots);
          });
          break;
        case TournamentSortBy.createdDate:
          filteredTournaments
              .sort((a, b) => b.createdAt.compareTo(a.createdAt));
          break;
        case TournamentSortBy.nearestLocation:
          // For demo, just sort by name
          filteredTournaments.sort((a, b) => a.city.compareTo(b.city));
          break;
      }
    }

    // Apply pagination
    final startIndex = offset;
    final endIndex = (offset + limit).clamp(0, filteredTournaments.length);

    return filteredTournaments.sublist(startIndex, endIndex);
  }

  Future<bool> joinTournament(String tournamentId, String playerId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1000));

    final tournament = _mockTournaments.firstWhere(
      (t) => t.id == tournamentId,
      orElse: () => throw Exception('Tournament not found'),
    );

    // Check if tournament is full
    if (tournament.currentParticipants >= tournament.maxParticipants) {
      throw Exception('Tournament is full');
    }

    // Check if registration deadline has passed
    if (DateTime.now().isAfter(tournament.registrationDeadline)) {
      throw Exception('Registration deadline has passed');
    }

    // In a real app, you would update the tournament in the database
    // For now, we'll just return success
    return true;
  }

  Future<List<Tournament>> getFeaturedTournaments() async {
    await Future.delayed(const Duration(milliseconds: 300));
    // For now, return first 3 tournaments as featured since isFeatured property doesn't exist
    return _mockTournaments.take(3).toList();
  }

  Future<Tournament?> getTournamentById(String id) async {
    await Future.delayed(const Duration(milliseconds: 200));
    try {
      return _mockTournaments.firstWhere((t) => t.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<List<String>> getAvailableFormats() async {
    await Future.delayed(const Duration(milliseconds: 100));
    return ['5v5', '7v7', '11v11'];
  }

  Future<List<String>> getAvailableLocations() async {
    await Future.delayed(const Duration(milliseconds: 100));
    return ['Kathmandu', 'Pokhara', 'Lalitpur', 'Bhaktapur'];
  }

  Future<List<String>> getAvailableOrganizers() async {
    await Future.delayed(const Duration(milliseconds: 100));
    return [
      'Kathmandu Football Association',
      'Pokhara Youth Sports Club',
      'Nepal Football Association',
      'Women\'s Football Association',
      'Youth Development Association',
    ];
  }
}
