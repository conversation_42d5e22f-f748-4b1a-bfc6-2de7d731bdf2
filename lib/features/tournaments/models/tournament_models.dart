class Tournament {
  final String id;
  final String name;
  final String description;
  final String format; // 5v5, 7v7, 11v11
  final String location;
  final String city;
  final DateTime startDate;
  final DateTime endDate;
  final DateTime registrationDeadline;
  final int maxParticipants;
  final int currentParticipants;
  final double entryFee;
  final String currency;
  final double prizeAmount;
  final String organizerName;
  final String organizerEmail;
  final String
      status; // draft, registration_open, registration_closed, in_progress, completed, cancelled
  final List<String> tags;
  final String bannerImage;
  final List<TournamentParticipant> participants;
  final List<String> rules;
  final TournamentEligibility eligibility;
  final bool isFeatured;
  final DateTime createdAt;
  final DateTime updatedAt;

  Tournament({
    required this.id,
    required this.name,
    required this.description,
    required this.format,
    required this.location,
    required this.city,
    required this.startDate,
    required this.endDate,
    required this.registrationDeadline,
    required this.maxParticipants,
    required this.currentParticipants,
    required this.entryFee,
    required this.currency,
    required this.prizeAmount,
    required this.organizerName,
    required this.organizerEmail,
    required this.status,
    required this.tags,
    required this.bannerImage,
    required this.participants,
    required this.rules,
    required this.eligibility,
    required this.isFeatured,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get hasAvailableSlots => currentParticipants < maxParticipants;
  int get availableSlots => maxParticipants - currentParticipants;
  bool get isRegistrationOpen =>
      status == 'registration_open' &&
      DateTime.now().isBefore(registrationDeadline);

  String get statusDisplayText {
    switch (status) {
      case 'draft':
        return 'Draft';
      case 'registration_open':
        return isRegistrationOpen ? 'Registration Open' : 'Registration Closed';
      case 'registration_closed':
        return 'Registration Closed';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }

  bool get isEligibleForUser {
    // This would be implemented based on user profile
    // For now, return true as placeholder
    return true;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'format': format,
      'location': location,
      'city': city,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'registrationDeadline': registrationDeadline.toIso8601String(),
      'maxParticipants': maxParticipants,
      'currentParticipants': currentParticipants,
      'entryFee': entryFee,
      'currency': currency,
      'prizeAmount': prizeAmount,
      'organizerName': organizerName,
      'organizerEmail': organizerEmail,
      'status': status,
      'tags': tags,
      'bannerImage': bannerImage,
      'participants': participants.map((p) => p.toJson()).toList(),
      'rules': rules,
      'eligibility': eligibility.toJson(),
      'isFeatured': isFeatured,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Tournament.fromJson(Map<String, dynamic> json) {
    return Tournament(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      format: json['format'],
      location: json['location'],
      city: json['city'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      registrationDeadline: DateTime.parse(json['registrationDeadline']),
      maxParticipants: json['maxParticipants'],
      currentParticipants: json['currentParticipants'],
      entryFee: json['entryFee'].toDouble(),
      currency: json['currency'],
      prizeAmount: json['prizeAmount'].toDouble(),
      organizerName: json['organizerName'],
      organizerEmail: json['organizerEmail'],
      status: json['status'],
      tags: List<String>.from(json['tags']),
      bannerImage: json['bannerImage'],
      participants: (json['participants'] as List)
          .map((p) => TournamentParticipant.fromJson(p))
          .toList(),
      rules: List<String>.from(json['rules']),
      eligibility: TournamentEligibility.fromJson(json['eligibility']),
      isFeatured: json['isFeatured'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}

class TournamentParticipant {
  final String id;
  final String name;
  final String teamName;
  final String position;
  final int age;
  final String status; // registered, confirmed, paid
  final DateTime registeredAt;
  final String? profileImage;

  TournamentParticipant({
    required this.id,
    required this.name,
    required this.teamName,
    required this.position,
    required this.age,
    required this.status,
    required this.registeredAt,
    this.profileImage,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'teamName': teamName,
      'position': position,
      'age': age,
      'status': status,
      'registeredAt': registeredAt.toIso8601String(),
      'profileImage': profileImage,
    };
  }

  factory TournamentParticipant.fromJson(Map<String, dynamic> json) {
    return TournamentParticipant(
      id: json['id'],
      name: json['name'],
      teamName: json['teamName'],
      position: json['position'],
      age: json['age'],
      status: json['status'],
      registeredAt: DateTime.parse(json['registeredAt']),
      profileImage: json['profileImage'],
    );
  }
}

class TournamentEligibility {
  final int minAge;
  final int maxAge;
  final String ageGroup; // Under 16, Under 18, Open, etc.
  final String skillLevel; // Beginner, Intermediate, Advanced, Professional
  final String gender; // Male, Female, Mixed
  final List<String> requiredDocuments;
  final bool requiresMedicalCertificate;
  final bool requiresInsurance;
  final List<String> restrictions;

  TournamentEligibility({
    required this.minAge,
    required this.maxAge,
    required this.ageGroup,
    required this.skillLevel,
    required this.gender,
    required this.requiredDocuments,
    required this.requiresMedicalCertificate,
    required this.requiresInsurance,
    required this.restrictions,
  });

  Map<String, dynamic> toJson() {
    return {
      'minAge': minAge,
      'maxAge': maxAge,
      'ageGroup': ageGroup,
      'skillLevel': skillLevel,
      'gender': gender,
      'requiredDocuments': requiredDocuments,
      'requiresMedicalCertificate': requiresMedicalCertificate,
      'requiresInsurance': requiresInsurance,
      'restrictions': restrictions,
    };
  }

  factory TournamentEligibility.fromJson(Map<String, dynamic> json) {
    return TournamentEligibility(
      minAge: json['minAge'],
      maxAge: json['maxAge'],
      ageGroup: json['ageGroup'],
      skillLevel: json['skillLevel'],
      gender: json['gender'],
      requiredDocuments: List<String>.from(json['requiredDocuments']),
      requiresMedicalCertificate: json['requiresMedicalCertificate'],
      requiresInsurance: json['requiresInsurance'],
      restrictions: List<String>.from(json['restrictions']),
    );
  }
}

class TournamentFilter {
  final List<String> formats;
  final List<String> locations;
  final List<String> statuses;
  final bool availableSlotsOnly;
  final String sortBy;
  final String sortOrder;
  final DateRangeFilter? dateRange;
  final PriceRangeFilter? entryFeeRange;
  final PriceRangeFilter? prizeRange;
  final String? ageGroup;
  final String? skillLevel;
  final String? gender;

  TournamentFilter({
    this.formats = const [],
    this.locations = const [],
    this.statuses = const [],
    this.availableSlotsOnly = false,
    this.sortBy = 'startDate',
    this.sortOrder = 'asc',
    this.dateRange,
    this.entryFeeRange,
    this.prizeRange,
    this.ageGroup,
    this.skillLevel,
    this.gender,
  });

  bool get hasActiveFilters {
    return formats.isNotEmpty ||
        locations.isNotEmpty ||
        statuses.isNotEmpty ||
        availableSlotsOnly ||
        dateRange != null ||
        entryFeeRange != null ||
        prizeRange != null ||
        ageGroup != null ||
        skillLevel != null ||
        gender != null;
  }

  int get activeFilterCount {
    int count = 0;
    if (formats.isNotEmpty) count++;
    if (locations.isNotEmpty) count++;
    if (statuses.isNotEmpty) count++;
    if (availableSlotsOnly) count++;
    if (dateRange != null) count++;
    if (entryFeeRange != null) count++;
    if (prizeRange != null) count++;
    if (ageGroup != null) count++;
    if (skillLevel != null) count++;
    if (gender != null) count++;
    return count;
  }

  TournamentFilter copyWith({
    List<String>? formats,
    List<String>? locations,
    List<String>? statuses,
    bool? availableSlotsOnly,
    String? sortBy,
    String? sortOrder,
    DateRangeFilter? dateRange,
    PriceRangeFilter? entryFeeRange,
    PriceRangeFilter? prizeRange,
    String? ageGroup,
    String? skillLevel,
    String? gender,
  }) {
    return TournamentFilter(
      formats: formats ?? this.formats,
      locations: locations ?? this.locations,
      statuses: statuses ?? this.statuses,
      availableSlotsOnly: availableSlotsOnly ?? this.availableSlotsOnly,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      dateRange: dateRange ?? this.dateRange,
      entryFeeRange: entryFeeRange ?? this.entryFeeRange,
      prizeRange: prizeRange ?? this.prizeRange,
      ageGroup: ageGroup ?? this.ageGroup,
      skillLevel: skillLevel ?? this.skillLevel,
      gender: gender ?? this.gender,
    );
  }

  TournamentFilter clear() {
    return TournamentFilter();
  }
}

class DateRangeFilter {
  final DateTime startDate;
  final DateTime endDate;

  DateRangeFilter({
    required this.startDate,
    required this.endDate,
  });
}

class PriceRangeFilter {
  final double minPrice;
  final double maxPrice;

  PriceRangeFilter({
    required this.minPrice,
    required this.maxPrice,
  });
}

class TournamentSearchQuery {
  final String searchTerm;
  final TournamentFilter filter;
  final int limit;
  final int offset;

  TournamentSearchQuery({
    required this.searchTerm,
    required this.filter,
    this.limit = 20,
    this.offset = 0,
  });
}
