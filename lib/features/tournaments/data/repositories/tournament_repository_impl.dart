import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../domain/entities/tournament.dart';
import '../../domain/entities/tournament_filter.dart';
import '../../domain/entities/favourite_tournament.dart';
import '../../domain/entities/player_badge.dart';
import '../../domain/repositories/tournament_repository.dart';
import '../datasources/tournament_datasource.dart';

class TournamentRepositoryImpl implements TournamentRepository {
  final TournamentDataSource dataSource;

  const TournamentRepositoryImpl(this.dataSource);

  @override
  Future<Either<AppError, TournamentSearchResult>> getTournaments({
    TournamentSearchQuery? query,
  }) async {
    try {
      final tournaments = await dataSource.getTournaments(query: query);

      // Calculate total count and pagination info
      final totalTournaments =
          await dataSource.getTournaments(); // Get all for count
      final hasMore = query != null
          ? (query.offset + query.limit) < totalTournaments.length
          : false;
      final currentPage =
          query != null ? (query.offset / query.limit).floor() + 1 : 1;

      final result = TournamentSearchResult(
        tournaments: tournaments,
        totalCount: totalTournaments.length,
        hasMore: hasMore,
        currentPage: currentPage,
        query: query ?? const TournamentSearchQuery(),
      );

      return Right(result);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, Tournament>> getTournamentById(String id) async {
    try {
      final tournament = await dataSource.getTournamentDetails(id);
      if (tournament == null) {
        return Left(AppError('Tournament not found'));
      }
      return Right(tournament);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, Tournament>> joinTournament(
    String tournamentId,
    String playerId,
  ) async {
    try {
      final tournament = await dataSource.joinTournament(
        tournamentId,
        playerId,
      );
      return Right(tournament);
    } catch (e) {
      if (e.toString().contains('No available slots')) {
        return Left(AppError('No available slots'));
      } else if (e.toString().contains('Registration is closed')) {
        return Left(AppError('Registration is closed'));
      }
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, Tournament>> leaveTournament(
    String tournamentId,
    String playerId,
  ) async {
    try {
      final tournament = await dataSource.leaveTournament(
        tournamentId,
        playerId,
      );
      return Right(tournament);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<Tournament>>> getFeaturedTournaments({
    int limit = 5,
  }) async {
    try {
      final tournaments = await dataSource.getFeaturedTournaments(limit: limit);
      return Right(tournaments);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<Tournament>>> getNearbyTournaments({
    required double latitude,
    required double longitude,
    double radiusKm = 50.0,
    int limit = 20,
  }) async {
    try {
      final tournaments = await dataSource.getNearbyTournaments(
        latitude: latitude,
        longitude: longitude,
        radiusKm: radiusKm,
      );
      return Right(tournaments);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<Tournament>>> searchTournaments({
    required String searchTerm,
    TournamentFilter? filter,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      // Use getTournaments with query for search functionality
      final query = TournamentSearchQuery(
        searchTerm: searchTerm,
        filter: filter ?? const TournamentFilter(),
        limit: limit,
        offset: offset,
      );
      final tournaments = await dataSource.getTournaments(query: query);
      return Right(tournaments);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<Tournament>>> getTournamentsByOrganizer({
    required String organizerId,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      // Use getTournaments with organizer filter
      final filter = TournamentFilter(organizers: [organizerId]);
      final query = TournamentSearchQuery(
        searchTerm: '',
        filter: filter,
        limit: limit,
        offset: offset,
      );
      final tournaments = await dataSource.getTournaments(query: query);
      return Right(tournaments);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<Tournament>>> getPlayerTournaments({
    required String playerId,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      // Get all tournaments and filter by participant
      final allTournaments = await dataSource.getTournaments();
      final playerTournaments = allTournaments
          .where((t) => t.participants.any((p) => p.playerId == playerId))
          .skip(offset)
          .take(limit)
          .toList();

      return Right(playerTournaments);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<FavouriteTournamentDetails>>>
      getFavouriteTournaments(String playerId) async {
    try {
      final favourites = await dataSource.getFavouriteTournaments(playerId);
      return Right(favourites);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, FavouriteTournament>> addToFavourites({
    required String playerId,
    required String tournamentId,
  }) async {
    try {
      await dataSource.addToFavourites(tournamentId, playerId);
      // Return a mock FavouriteTournament since the data source doesn't return one
      final favourite = FavouriteTournament(
        id: 'fav_${DateTime.now().millisecondsSinceEpoch}',
        playerId: playerId,
        tournamentId: tournamentId,
        addedAt: DateTime.now(),
        isActive: true,
      );
      return Right(favourite);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> removeFromFavourites({
    required String playerId,
    required String tournamentId,
  }) async {
    try {
      await dataSource.removeFromFavourites(tournamentId, playerId);
      return const Right(null);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, bool>> isTournamentFavourite({
    required String playerId,
    required String tournamentId,
  }) async {
    try {
      // Check if tournament is in player's favourites
      final favourites = await dataSource.getFavouriteTournaments(playerId);
      final isFavourite =
          favourites.any((f) => f.favourite.tournamentId == tournamentId);
      return Right(isFavourite);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<PlayerBadge>>> getAvailableBadges() async {
    try {
      // Return empty list since getAvailableBadges is not implemented in data source
      return const Right([]);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, PlayerBadgeCollection>> getPlayerBadges(
    String playerId,
  ) async {
    try {
      final badges = await dataSource.getPlayerBadges(playerId);
      return Right(badges);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, TournamentStats>> getPlayerTournamentStats(
    String playerId,
  ) async {
    try {
      final stats = await dataSource.getPlayerTournamentStats(playerId);
      return Right(stats);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, PlayerBadgeProgress>> updateBadgeProgress({
    required String playerId,
    required String badgeId,
    required int progress,
  }) async {
    try {
      // This would typically update the backend and return updated progress
      // For mock implementation, we'll just return a mock progress
      final updatedProgress = PlayerBadgeProgress(
        badgeId: badgeId,
        playerId: playerId,
        currentProgress: progress,
        targetProgress: 10, // Mock target
        lastUpdated: DateTime.now(),
        isUnlocked: progress >= 10,
        unlockedAt: progress >= 10 ? DateTime.now() : null,
      );
      return Right(updatedProgress);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<PlayerBadge>>> checkForNewBadges({
    required String playerId,
    required TournamentStats stats,
  }) async {
    try {
      final newBadges = await dataSource.checkForNewBadges(playerId);
      return Right(newBadges);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, Map<String, dynamic>>>
      getTournamentAnalytics() async {
    try {
      // Mock analytics data
      final analytics = {
        'total_tournaments': 50,
        'active_tournaments': 25,
        'total_participants': 500,
        'popular_formats': ['5v5', '7v7', '11v11'],
        'average_entry_fee': 1500.0,
        'total_prize_money': 150000.0,
      };
      return Right(analytics);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<String>>> getPopularLocations({
    int limit = 10,
  }) async {
    try {
      final locations = [
        'Kathmandu',
        'Pokhara',
        'Chitwan',
        'Dharan',
        'Lalitpur',
        'Butwal',
        'Biratnagar',
        'Janakpur',
        'Nepalgunj',
        'Bharatpur',
      ].take(limit).toList();
      return Right(locations);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<String>>> getPopularOrganizers({
    int limit = 10,
  }) async {
    try {
      final organizers = [
        'Nepal Football Academy',
        'Kathmandu Sports Club',
        'Pokhara Football Federation',
        'Chitwan United',
        'Lalitpur Football Association',
        'Dharan Sports Academy',
        'Butwal Football Club',
        'Biratnagar United',
      ].take(limit).toList();
      return Right(organizers);
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }
}
