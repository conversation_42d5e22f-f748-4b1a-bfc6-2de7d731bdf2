import '../../domain/entities/tournament.dart';
import '../../domain/entities/tournament_filter.dart';
import '../../domain/entities/favourite_tournament.dart';
import '../../domain/entities/player_badge.dart';

abstract class TournamentDataSource {
  Future<List<Tournament>> getTournaments({
    TournamentSearchQuery? query,
  });
  Future<Tournament> getTournamentDetails(String id);
  Future<Tournament> joinTournament(String tournamentId, String playerId);
  Future<Tournament> leaveTournament(String tournamentId, String playerId);
  Future<List<Tournament>> getFeaturedTournaments({int? limit});
  Future<List<Tournament>> getNearbyTournaments({
    required double latitude,
    required double longitude,
    double radiusKm = 50.0,
  });
  Future<List<FavouriteTournamentDetails>> getFavouriteTournaments(
      String playerId);
  Future<void> addToFavourites(String tournamentId, String playerId);
  Future<void> removeFromFavourites(String tournamentId, String playerId);
  Future<PlayerBadgeCollection> getPlayerBadges(String playerId);
  Future<TournamentStats> getPlayerTournamentStats(String playerId);
  Future<List<PlayerBadge>> checkForNewBadges(String playerId);
  Future<List<String>> getPopularLocations();
  Future<List<String>> getPopularOrganizers();
}

class TournamentDataSourceImpl implements TournamentDataSource {
  // Mock data for now to get the app building
  final List<Tournament> _mockTournaments = [
    Tournament(
      id: '1',
      name: 'Kathmandu Football Championship',
      description: 'Annual football tournament in Kathmandu',
      format: const TournamentFormat(
        type: TournamentType.elevenVsEleven,
        playersPerTeam: 11,
        displayName: '11v11',
      ),
      location: 'Kathmandu, Nepal',
      city: 'Kathmandu',
      startDate: DateTime.now().add(const Duration(days: 7)),
      endDate: DateTime.now().add(const Duration(days: 14)),
      registrationDeadline: DateTime.now().add(const Duration(days: 5)),
      entryFee: 1000,
      currency: 'NPR',
      prize: const TournamentPrize(
        totalAmount: 50000,
        currency: 'NPR',
        distribution: [
          PrizeDistribution(
              position: 1, amount: 25000, description: '1st Place'),
          PrizeDistribution(
              position: 2, amount: 15000, description: '2nd Place'),
          PrizeDistribution(
              position: 3, amount: 10000, description: '3rd Place'),
        ],
      ),
      maxParticipants: 16,
      currentParticipants: 8,
      organizerId: 'org_1',
      organizerName: 'Nepal Football Association',
      status: TournamentStatus.registrationOpen,
      tags: ['football', 'championship', 'kathmandu'],
      bannerImage: 'https://example.com/banner1.jpg',
      participants: [],
      rules: ['Standard FIFA rules apply', 'Teams must have 11 players'],
      venueDetails: const TournamentLocation(
        name: 'Kathmandu Stadium',
        address: 'Kathmandu, Nepal',
        latitude: 27.7172,
        longitude: 85.3240,
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Tournament(
      id: '2',
      name: 'Pokhara 5v5 Tournament',
      description: 'Fast-paced 5v5 football tournament',
      format: const TournamentFormat(
        type: TournamentType.fiveVsFive,
        playersPerTeam: 5,
        displayName: '5v5',
      ),
      location: 'Pokhara, Nepal',
      city: 'Pokhara',
      startDate: DateTime.now().add(const Duration(days: 14)),
      endDate: DateTime.now().add(const Duration(days: 16)),
      registrationDeadline: DateTime.now().add(const Duration(days: 10)),
      entryFee: 500,
      currency: 'NPR',
      prize: const TournamentPrize(
        totalAmount: 25000,
        currency: 'NPR',
        distribution: [
          PrizeDistribution(
              position: 1, amount: 15000, description: '1st Place'),
          PrizeDistribution(
              position: 2, amount: 10000, description: '2nd Place'),
        ],
      ),
      maxParticipants: 32,
      currentParticipants: 24,
      organizerId: 'org_2',
      organizerName: 'Pokhara Sports Club',
      status: TournamentStatus.registrationOpen,
      tags: ['football', '5v5', 'pokhara'],
      bannerImage: 'https://example.com/banner2.jpg',
      participants: [],
      rules: ['5v5 format', 'Quick matches'],
      venueDetails: const TournamentLocation(
        name: 'Pokhara Sports Complex',
        address: 'Pokhara, Nepal',
        latitude: 28.2096,
        longitude: 83.9856,
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];

  @override
  Future<List<Tournament>> getTournaments({
    TournamentSearchQuery? query,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return _mockTournaments;
  }

  @override
  Future<Tournament> getTournamentDetails(String id) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final tournament = _mockTournaments.firstWhere(
      (t) => t.id == id,
      orElse: () => throw Exception('Tournament not found'),
    );
    return tournament;
  }

  @override
  Future<Tournament> joinTournament(
      String tournamentId, String playerId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final tournament = _mockTournaments.firstWhere((t) => t.id == tournamentId);
    // Mock joining - increment participants
    final updatedTournament = Tournament(
      id: tournament.id,
      name: tournament.name,
      description: tournament.description,
      format: tournament.format,
      location: tournament.location,
      city: tournament.city,
      startDate: tournament.startDate,
      endDate: tournament.endDate,
      registrationDeadline: tournament.registrationDeadline,
      entryFee: tournament.entryFee,
      currency: tournament.currency,
      prize: tournament.prize,
      maxParticipants: tournament.maxParticipants,
      currentParticipants: tournament.currentParticipants + 1,
      organizerId: tournament.organizerId,
      organizerName: tournament.organizerName,
      status: tournament.status,
      tags: tournament.tags,
      bannerImage: tournament.bannerImage,
      participants: tournament.participants,
      rules: tournament.rules,
      venueDetails: tournament.venueDetails,
      createdAt: tournament.createdAt,
      updatedAt: DateTime.now(),
    );
    return updatedTournament;
  }

  @override
  Future<Tournament> leaveTournament(
      String tournamentId, String playerId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final tournament = _mockTournaments.firstWhere((t) => t.id == tournamentId);
    // Mock leaving - decrement participants
    final updatedTournament = Tournament(
      id: tournament.id,
      name: tournament.name,
      description: tournament.description,
      format: tournament.format,
      location: tournament.location,
      city: tournament.city,
      startDate: tournament.startDate,
      endDate: tournament.endDate,
      registrationDeadline: tournament.registrationDeadline,
      entryFee: tournament.entryFee,
      currency: tournament.currency,
      prize: tournament.prize,
      maxParticipants: tournament.maxParticipants,
      currentParticipants: (tournament.currentParticipants - 1)
          .clamp(0, tournament.maxParticipants),
      organizerId: tournament.organizerId,
      organizerName: tournament.organizerName,
      status: tournament.status,
      tags: tournament.tags,
      bannerImage: tournament.bannerImage,
      participants: tournament.participants,
      rules: tournament.rules,
      venueDetails: tournament.venueDetails,
      createdAt: tournament.createdAt,
      updatedAt: DateTime.now(),
    );
    return updatedTournament;
  }

  @override
  Future<List<Tournament>> getFeaturedTournaments({int? limit}) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final featured = _mockTournaments.take(limit ?? 5).toList();
    return featured;
  }

  @override
  Future<List<Tournament>> getNearbyTournaments({
    required double latitude,
    required double longitude,
    double radiusKm = 50.0,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));
    return _mockTournaments;
  }

  @override
  Future<List<FavouriteTournamentDetails>> getFavouriteTournaments(
      String playerId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return [];
  }

  @override
  Future<void> addToFavourites(String tournamentId, String playerId) async {
    await Future.delayed(const Duration(milliseconds: 200));
  }

  @override
  Future<void> removeFromFavourites(
      String tournamentId, String playerId) async {
    await Future.delayed(const Duration(milliseconds: 200));
  }

  @override
  Future<PlayerBadgeCollection> getPlayerBadges(String playerId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return PlayerBadgeCollection(
      playerId: playerId,
      badges: [],
      totalXP: 0,
      lastUpdated: DateTime.now(),
    );
  }

  @override
  Future<TournamentStats> getPlayerTournamentStats(String playerId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return TournamentStats(
      playerId: playerId,
      tournamentsJoined: 0,
      tournamentsCompleted: 0,
      tournamentsWon: 0,
      totalSpent: 0,
      totalWon: 0,
      differentCities: 0,
      currentStreak: 0,
      longestStreak: 0,
      lastTournamentDate: DateTime.now(),
    );
  }

  @override
  Future<List<PlayerBadge>> checkForNewBadges(String playerId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return [];
  }

  @override
  Future<List<String>> getPopularLocations() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return ['Kathmandu', 'Pokhara', 'Lalitpur', 'Bhaktapur'];
  }

  @override
  Future<List<String>> getPopularOrganizers() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return ['Nepal Football Association', 'Pokhara Sports Club'];
  }
}
