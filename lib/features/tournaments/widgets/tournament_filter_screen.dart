import 'package:flutter/material.dart';
import '../models/tournament_models.dart';
import '../services/tournament_service.dart';
import '../domain/entities/tournament.dart';
import '../../../../utils/color.dart';

class TournamentFilterScreen extends StatefulWidget {
  final TournamentFilter currentFilter;
  final Function(TournamentFilter) onApplyFilter;

  const TournamentFilterScreen({
    super.key,
    required this.currentFilter,
    required this.onApplyFilter,
  });

  @override
  State<TournamentFilterScreen> createState() => _TournamentFilterScreenState();
}

class _TournamentFilterScreenState extends State<TournamentFilterScreen> {
  late TournamentFilter _filter;
  final TournamentService _tournamentService = TournamentService();

  List<String> _availableFormats = [];
  List<String> _availableLocations = [];
  List<String> _availableStatuses = [];
  List<String> _availableAgeGroups = [];
  List<String> _availableSkillLevels = [];
  List<String> _availableGenders = [];

  @override
  void initState() {
    super.initState();
    _filter = widget.currentFilter;
    _loadFilterOptions();
  }

  Future<void> _loadFilterOptions() async {
    _availableFormats = await _tournamentService.getAvailableFormats();
    _availableLocations = await _tournamentService.getAvailableLocations();
    // For now, use hardcoded values for missing methods
    _availableStatuses = [
      'registration_open',
      'registration_closed',
      'in_progress',
      'completed',
      'cancelled'
    ];
    _availableAgeGroups = ['U18', 'U21', 'Open', 'Senior'];
    _availableSkillLevels = [
      'Beginner',
      'Intermediate',
      'Advanced',
      'Professional'
    ];
    _availableGenders = ['Male', 'Female', 'Mixed'];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PrimeryColor,
      appBar: AppBar(
        backgroundColor: PrimeryColor,
        elevation: 0,
        title: const Text(
          'Filter Tournaments',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          TextButton(
            onPressed: _clearAllFilters,
            child: const Text(
              'Clear All',
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Color(0xffDA22FF),
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Format Filter
                  _buildFilterSection(
                    'Tournament Format',
                    _availableFormats,
                    _filter.formats,
                    (formats) => _updateFilter(formats: formats),
                  ),
                  const SizedBox(height: 24),

                  // Location Filter
                  _buildFilterSection(
                    'Location',
                    _availableLocations,
                    _filter.locations,
                    (locations) => _updateFilter(locations: locations),
                  ),
                  const SizedBox(height: 24),

                  // Status Filter
                  _buildFilterSection(
                    'Status',
                    _availableStatuses
                        .map((s) => _getStatusDisplayText(s))
                        .toList(),
                    _filter.statuses
                        .map((s) => _getStatusDisplayText(s))
                        .toList(),
                    (statuses) => _updateFilter(
                      statuses: statuses
                          .map((s) => _getStatusFromDisplayText(s))
                          .toList(),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Age Group Filter
                  _buildFilterSection(
                    'Age Group',
                    _availableAgeGroups,
                    _filter.ageGroup != null ? [_filter.ageGroup!] : [],
                    (ageGroups) => _updateFilter(
                      ageGroup: ageGroups.isNotEmpty ? ageGroups.first : null,
                    ),
                    singleSelection: true,
                  ),
                  const SizedBox(height: 24),

                  // Skill Level Filter
                  _buildFilterSection(
                    'Skill Level',
                    _availableSkillLevels,
                    _filter.skillLevel != null ? [_filter.skillLevel!] : [],
                    (skillLevels) => _updateFilter(
                      skillLevel:
                          skillLevels.isNotEmpty ? skillLevels.first : null,
                    ),
                    singleSelection: true,
                  ),
                  const SizedBox(height: 24),

                  // Gender Filter
                  _buildFilterSection(
                    'Gender',
                    _availableGenders,
                    _filter.gender != null ? [_filter.gender!] : [],
                    (genders) => _updateFilter(
                      gender: genders.isNotEmpty ? genders.first : null,
                    ),
                    singleSelection: true,
                  ),
                  const SizedBox(height: 24),

                  // Available Slots Only
                  _buildSwitchFilter(
                    'Available Slots Only',
                    'Show only tournaments with available slots',
                    _filter.availableSlotsOnly,
                    (value) => _updateFilter(availableSlotsOnly: value),
                  ),
                  const SizedBox(height: 24),

                  // Entry Fee Range
                  _buildPriceRangeFilter(
                    'Entry Fee Range',
                    'NPR',
                    _filter.entryFeeRange,
                    (range) => _updateFilter(entryFeeRange: range),
                  ),
                  const SizedBox(height: 24),

                  // Prize Range
                  _buildPriceRangeFilter(
                    'Prize Pool Range',
                    'NPR',
                    _filter.prizeRange,
                    (range) => _updateFilter(prizeRange: range),
                  ),
                  const SizedBox(height: 24),

                  // Sort Options
                  _buildSortOptions(),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),

          // Apply Button
          Container(
            padding: const EdgeInsets.all(20),
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _applyFilter,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff00D4AA),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'Apply Filters (${_filter.activeFilterCount})',
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(
    String title,
    List<String> options,
    List<String> selectedOptions,
    Function(List<String>) onChanged, {
    bool singleSelection = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: options.map((option) {
            final isSelected = selectedOptions.contains(option);
            return FilterChip(
              label: Text(
                option,
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: isSelected ? Colors.white : Colors.white70,
                  fontSize: 14,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                List<String> newSelection;
                if (singleSelection) {
                  newSelection = selected ? [option] : [];
                } else {
                  if (selected) {
                    newSelection = [...selectedOptions, option];
                  } else {
                    newSelection = selectedOptions
                        .where((item) => item != option)
                        .toList();
                  }
                }
                onChanged(newSelection);
              },
              selectedColor: const Color(0xff00D4AA),
              backgroundColor: Colors.white.withOpacity(0.1),
              checkmarkColor: Colors.white,
              side: BorderSide(
                color: isSelected
                    ? const Color(0xff00D4AA)
                    : Colors.white.withOpacity(0.3),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSwitchFilter(
    String title,
    String subtitle,
    bool value,
    Function(bool) onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xff00D4AA),
            activeTrackColor: const Color(0xff00D4AA).withOpacity(0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRangeFilter(
    String title,
    String currency,
    PriceRangeFilter? currentRange,
    Function(PriceRangeFilter?) onChanged,
  ) {
    double minValue = currentRange?.minPrice ?? 0;
    double maxValue = currentRange?.maxPrice ?? 5000;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () => onChanged(null),
                child: const Text(
                  'Clear',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Color(0xffDA22FF),
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          RangeSlider(
            values: RangeValues(minValue, maxValue),
            min: 0,
            max: 5000,
            divisions: 50,
            activeColor: const Color(0xff00D4AA),
            inactiveColor: Colors.white.withOpacity(0.3),
            labels: RangeLabels(
              '$currency ${minValue.toInt()}',
              '$currency ${maxValue.toInt()}',
            ),
            onChanged: (values) {
              setState(() {
                minValue = values.start;
                maxValue = values.end;
              });
            },
            onChangeEnd: (values) {
              onChanged(PriceRangeFilter(
                minPrice: values.start,
                maxPrice: values.end,
              ));
            },
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '$currency ${minValue.toInt()}',
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
              Text(
                '$currency ${maxValue.toInt()}',
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSortOptions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Sort By',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildSortChip('Start Date', 'startDate'),
              _buildSortChip('Entry Fee', 'entryFee'),
              _buildSortChip('Prize Pool', 'prizeAmount'),
              _buildSortChip('Name', 'name'),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSortOrderChip('Ascending', 'asc'),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSortOrderChip('Descending', 'desc'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSortChip(String label, String value) {
    final isSelected = _filter.sortBy == value;
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          fontFamily: 'Gilroy_Medium',
          color: isSelected ? Colors.white : Colors.white70,
          fontSize: 14,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          _updateFilter(sortBy: value);
        }
      },
      selectedColor: const Color(0xff00D4AA),
      backgroundColor: Colors.white.withOpacity(0.1),
      checkmarkColor: Colors.white,
      side: BorderSide(
        color: isSelected
            ? const Color(0xff00D4AA)
            : Colors.white.withOpacity(0.3),
      ),
    );
  }

  Widget _buildSortOrderChip(String label, String value) {
    final isSelected = _filter.sortOrder == value;
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          fontFamily: 'Gilroy_Medium',
          color: isSelected ? Colors.white : Colors.white70,
          fontSize: 14,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          _updateFilter(sortOrder: value);
        }
      },
      selectedColor: const Color(0xff00D4AA),
      backgroundColor: Colors.white.withOpacity(0.1),
      checkmarkColor: Colors.white,
      side: BorderSide(
        color: isSelected
            ? const Color(0xff00D4AA)
            : Colors.white.withOpacity(0.3),
      ),
    );
  }

  void _updateFilter({
    List<String>? formats,
    List<String>? locations,
    List<String>? statuses,
    bool? availableSlotsOnly,
    String? sortBy,
    String? sortOrder,
    DateRangeFilter? dateRange,
    PriceRangeFilter? entryFeeRange,
    PriceRangeFilter? prizeRange,
    String? ageGroup,
    String? skillLevel,
    String? gender,
  }) {
    setState(() {
      _filter = _filter.copyWith(
        formats: formats,
        locations: locations,
        statuses: statuses,
        availableSlotsOnly: availableSlotsOnly,
        sortBy: sortBy,
        sortOrder: sortOrder,
        dateRange: dateRange,
        entryFeeRange: entryFeeRange,
        prizeRange: prizeRange,
        ageGroup: ageGroup,
        skillLevel: skillLevel,
        gender: gender,
      );
    });
  }

  void _clearAllFilters() {
    setState(() {
      _filter = TournamentFilter();
    });
  }

  void _applyFilter() {
    widget.onApplyFilter(_filter);
    Navigator.pop(context);
  }

  String _getStatusDisplayText(String status) {
    switch (status) {
      case 'registration_open':
        return 'Registration Open';
      case 'registration_closed':
        return 'Registration Closed';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }

  String _getStatusFromDisplayText(String displayText) {
    switch (displayText) {
      case 'Registration Open':
        return 'registration_open';
      case 'Registration Closed':
        return 'registration_closed';
      case 'In Progress':
        return 'in_progress';
      case 'Completed':
        return 'completed';
      case 'Cancelled':
        return 'cancelled';
      default:
        return displayText;
    }
  }
}
