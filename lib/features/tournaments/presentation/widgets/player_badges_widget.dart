import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../tournaments_providers.dart';
import '../../domain/entities/player_badge.dart';
import '../logic/tournament_state.dart';
import 'badge_unlock_animation.dart';

class PlayerBadgesWidget extends ConsumerStatefulWidget {
  const PlayerBadgesWidget({super.key});

  @override
  ConsumerState<PlayerBadgesWidget> createState() => _PlayerBadgesWidgetState();
}

class _PlayerBadgesWidgetState extends ConsumerState<PlayerBadgesWidget>
    with TickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
    // Load badge system data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(badgeSystemStateProvider.notifier).loadBadgeSystem();
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(badgeSystemStateProvider);

    return state.when(
      initial: () => _buildLoadingState(),
      loading: () => _buildLoadingState(),
      loaded: (playerBadges, stats, availableBadges, newlyUnlockedBadges) {
        // Show badge unlock animation if there are new badges
        if (newlyUnlockedBadges.isNotEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _showBadgeUnlockAnimation(newlyUnlockedBadges.first);
          });
        }

        return _buildLoadedState(playerBadges, stats);
      },
      error: (message) => _buildErrorState(message),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildErrorState(String message) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Center(
        child: Text(
          'Error loading badges: $message',
          style: TextStyle(color: Colors.red, fontSize: 14.sp),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildLoadedState(
    PlayerBadgeCollection playerBadges,
    TournamentStats stats,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildXPHeader(playerBadges.totalXP, theme),
          SizedBox(height: 24.h),
          _buildStatsOverview(stats, theme),
          SizedBox(height: 24.h),
          _buildBadgesSection(playerBadges, theme),
        ],
      ),
    );
  }

  Widget _buildXPHeader(int totalXP, ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          Icon(Icons.emoji_events, color: Colors.white, size: 32.sp),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Total XP',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14.sp,
                  ),
                ),
                Text(
                  totalXP.toString(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 28.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          _buildXPLevel(totalXP),
        ],
      ),
    );
  }

  Widget _buildXPLevel(int totalXP) {
    final level = (totalXP / 100).floor() + 1;
    final currentLevelXP = totalXP % 100;
    final progress = currentLevelXP / 100;

    return Column(
      children: [
        Text(
          'Level $level',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 4.h),
        SizedBox(
          width: 60.w,
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.white.withOpacity(0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
        SizedBox(height: 2.h),
        Text(
          '$currentLevelXP/100',
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 11.sp,
          ),
        ),
      ],
    );
  }

  Widget _buildStatsOverview(TournamentStats stats, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Tournament Journey',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Tournaments\nJoined',
                stats.tournamentsJoined.toString(),
                Icons.sports_soccer,
                Colors.blue,
                theme,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildStatCard(
                'Tournaments\nWon',
                stats.tournamentsWon.toString(),
                Icons.emoji_events,
                Colors.orange,
                theme,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Cities\nVisited',
                stats.differentCities.toString(),
                Icons.location_on,
                Colors.green,
                theme,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildStatCard(
                'Current\nStreak',
                stats.currentStreak.toString(),
                Icons.whatshot,
                Colors.red,
                theme,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String label,
    String value,
    IconData icon,
    Color color,
    ThemeData theme,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 8.h),
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBadgesSection(
    PlayerBadgeCollection playerBadges,
    ThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Badges',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '${playerBadges.badges.where((b) => b.isUnlocked).length}/${playerBadges.badges.length}',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: 0.8,
            crossAxisSpacing: 12.w,
            mainAxisSpacing: 12.h,
          ),
          itemCount: playerBadges.badges.length,
          itemBuilder: (context, index) {
            final badgeProgress = playerBadges.badges[index];
            return _buildBadgeCard(badgeProgress, theme);
          },
        ),
      ],
    );
  }

  Widget _buildBadgeCard(PlayerBadgeProgress badgeProgress, ThemeData theme) {
    // In a real app, you'd fetch the badge details by ID
    final badgeColor = badgeProgress.isUnlocked ? Colors.orange : Colors.grey;

    return GestureDetector(
      onTap: () => _showBadgeDetails(badgeProgress),
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color:
                badgeProgress.isUnlocked
                    ? Colors.orange.withOpacity(0.3)
                    : theme.colorScheme.outline.withOpacity(0.2),
            width: badgeProgress.isUnlocked ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            // Badge icon
            Container(
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                color: badgeColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                badgeProgress.isUnlocked ? Icons.emoji_events : Icons.lock,
                color: badgeColor,
                size: 24.sp,
              ),
            ),
            SizedBox(height: 8.h),

            // Badge name
            Text(
              _getBadgeName(badgeProgress.badgeId),
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color:
                    badgeProgress.isUnlocked
                        ? theme.colorScheme.onSurface
                        : theme.colorScheme.onSurface.withOpacity(0.5),
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            if (!badgeProgress.isUnlocked) ...[
              SizedBox(height: 4.h),
              LinearProgressIndicator(
                value: badgeProgress.progressPercentage,
                backgroundColor: Colors.grey.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation<Color>(badgeColor),
              ),
              SizedBox(height: 2.h),
              Text(
                '${badgeProgress.currentProgress}/${badgeProgress.targetProgress}',
                style: TextStyle(
                  fontSize: 10.sp,
                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getBadgeName(String badgeId) {
    // In a real app, you'd have a mapping or fetch from the available badges
    switch (badgeId) {
      case 'badge_1':
        return 'Tournament Newbie';
      case 'badge_2':
        return 'Road Warrior';
      case 'badge_3':
        return 'Big Spender';
      case 'badge_4':
        return 'Consistent Player';
      case 'badge_5':
        return 'Tournament Master';
      default:
        return 'Mystery Badge';
    }
  }

  void _showBadgeDetails(PlayerBadgeProgress badgeProgress) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(_getBadgeName(badgeProgress.badgeId)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  badgeProgress.isUnlocked ? Icons.emoji_events : Icons.lock,
                  size: 64.sp,
                  color: badgeProgress.isUnlocked ? Colors.orange : Colors.grey,
                ),
                SizedBox(height: 16.h),
                Text(
                  badgeProgress.isUnlocked
                      ? 'Congratulations! You\'ve unlocked this badge.'
                      : 'Progress: ${badgeProgress.currentProgress}/${badgeProgress.targetProgress}',
                  textAlign: TextAlign.center,
                ),
                if (!badgeProgress.isUnlocked) ...[
                  SizedBox(height: 12.h),
                  LinearProgressIndicator(
                    value: badgeProgress.progressPercentage,
                  ),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  void _showBadgeUnlockAnimation(PlayerBadge badge) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => BadgeUnlockAnimation(
            badge: badge,
            onAnimationComplete: () {
              Navigator.of(context).pop();
              ref.read(badgeSystemStateProvider.notifier).clearNewBadges();
            },
          ),
    );
  }
}
