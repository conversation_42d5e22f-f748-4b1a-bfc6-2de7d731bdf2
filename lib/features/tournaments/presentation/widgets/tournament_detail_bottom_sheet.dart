import 'package:flutter/material.dart';
import '../../domain/entities/tournament.dart';
import '../../services/tournament_service.dart';
import '../../../../utils/color.dart';

class TournamentDetailBottomSheet extends StatefulWidget {
  final Tournament tournament;

  const TournamentDetailBottomSheet({
    super.key,
    required this.tournament,
  });

  @override
  State<TournamentDetailBottomSheet> createState() =>
      _TournamentDetailBottomSheetState();
}

class _TournamentDetailBottomSheetState
    extends State<TournamentDetailBottomSheet> {
  bool _isJoining = false;
  bool _hasAcceptedTerms = false;
  final TournamentService _tournamentService = TournamentService();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        color: PrimeryColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tournament header
                  _buildTournamentHeader(),
                  const SizedBox(height: 20),

                  // Tournament image
                  _buildTournamentImage(),
                  const SizedBox(height: 20),

                  // Basic info
                  _buildBasicInfo(),
                  const SizedBox(height: 20),

                  // Eligibility section
                  _buildEligibilitySection(),
                  const SizedBox(height: 20),

                  // Rules section
                  _buildRulesSection(),
                  const SizedBox(height: 20),

                  // Organizer info
                  _buildOrganizerInfo(),
                  const SizedBox(height: 20),

                  // Terms and conditions
                  _buildTermsAndConditions(),
                  const SizedBox(height: 30),
                ],
              ),
            ),
          ),

          // Join button
          _buildJoinButton(),
        ],
      ),
    );
  }

  Widget _buildTournamentHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                widget.tournament.name,
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getStatusColor().withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: _getStatusColor()),
              ),
              child: Text(
                widget.tournament.statusDisplayText,
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: _getStatusColor(),
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          widget.tournament.description,
          style: TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: Colors.white.withOpacity(0.8),
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  Widget _buildTournamentImage() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        image: DecorationImage(
          image: NetworkImage(widget.tournament.bannerImage),
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Column(
        children: [
          _buildInfoRow('Format', _formatDisplay()),
          _buildInfoRow('Location', _locationDisplay()),
          _buildInfoRow('Start Date', _formatDate(widget.tournament.startDate)),
          _buildInfoRow('End Date', _formatDate(widget.tournament.endDate)),
          _buildInfoRow('Registration Deadline',
              _formatDate(widget.tournament.registrationDeadline)),
          _buildInfoRow('Entry Fee',
              '${widget.tournament.currency} ${widget.tournament.entryFee}'),
          _buildInfoRow('Prize Pool',
              '${widget.tournament.currency} ${widget.tournament.prize.totalAmount}'),
          _buildInfoRow('Participants',
              '${widget.tournament.currentParticipants}/${widget.tournament.maxParticipants}'),
        ],
      ),
    );
  }

  Widget _buildEligibilitySection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            const Color(0xff00D4AA).withOpacity(0.1), // Updated to sporty green
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
            color: const Color(0xff00D4AA)
                .withOpacity(0.3)), // Updated to sporty green
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.info,
                  color: Color(0xff00D4AA)), // Updated to sporty green
              const SizedBox(width: 8),
              const Text(
                'Tournament Information',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildEligibilityRow('Format', widget.tournament.format.displayName),
          _buildEligibilityRow('Location', widget.tournament.location),
          _buildEligibilityRow('Organizer', widget.tournament.organizerName),
          _buildEligibilityRow('Status', widget.tournament.statusDisplayText),
        ],
      ),
    );
  }

  Widget _buildRulesSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.rule, color: Colors.white),
              const SizedBox(width: 8),
              const Text(
                'Tournament Rules',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...widget.tournament.rules.asMap().entries.map(
                (entry) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: const Color(0xffDA22FF),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: Text(
                            '${entry.key + 1}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          entry.value,
                          style: TextStyle(
                            fontFamily: 'Gilroy_Medium',
                            color: Colors.white.withOpacity(0.8),
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildOrganizerInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.person, color: Colors.white),
              const SizedBox(width: 8),
              const Text(
                'Organizer Information',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow('Name', widget.tournament.organizerName),
        ],
      ),
    );
  }

  Widget _buildTermsAndConditions() {
    return Row(
      children: [
        Checkbox(
          value: _hasAcceptedTerms,
          onChanged: (value) {
            setState(() {
              _hasAcceptedTerms = value ?? false;
            });
          },
          activeColor: const Color(0xff00D4AA), // Updated to sporty green
          checkColor: Colors.white,
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _hasAcceptedTerms = !_hasAcceptedTerms;
              });
            },
            child: Text(
              'I accept the tournament rules and eligibility requirements',
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white.withOpacity(0.8),
                fontSize: 14,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildJoinButton() {
    final canJoin = widget.tournament.isRegistrationOpen &&
        widget.tournament.hasAvailableSlots &&
        _hasAcceptedTerms;

    return Container(
      padding: const EdgeInsets.all(20),
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: ElevatedButton(
          onPressed: canJoin && !_isJoining ? _joinTournament : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: canJoin
                ? const Color(0xff00D4AA)
                : Colors.grey, // Updated to sporty green
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: 0,
          ),
          child: _isJoining
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : Text(
                  widget.tournament.isRegistrationOpen
                      ? 'Join Tournament'
                      : 'Registration Closed',
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white.withOpacity(0.6),
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEligibilityRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white.withOpacity(0.6),
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (widget.tournament.status) {
      case TournamentStatus.registrationOpen:
        return Colors.green;
      case TournamentStatus.registrationClosed:
        return Colors.orange;
      case TournamentStatus.inProgress:
        return Colors.blue;
      case TournamentStatus.completed:
        return Colors.grey;
      case TournamentStatus.cancelled:
        return Colors.red;
      case TournamentStatus.draft:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _joinTournament() async {
    setState(() {
      _isJoining = true;
    });

    try {
      await _tournamentService.joinTournament(widget.tournament.id, 'user123');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Successfully joined tournament!',
              style: TextStyle(fontFamily: 'Gilroy_Medium'),
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to join tournament: ${e.toString()}',
              style: const TextStyle(fontFamily: 'Gilroy_Medium'),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isJoining = false;
        });
      }
    }
  }

  String _formatDisplay() {
    return widget.tournament.format.displayName;
  }

  String _locationDisplay() {
    return widget.tournament.location;
  }
}
