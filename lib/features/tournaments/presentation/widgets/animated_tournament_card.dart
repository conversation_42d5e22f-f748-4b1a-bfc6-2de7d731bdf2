import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../domain/entities/tournament.dart';
import 'tournament_card.dart';

class AnimatedTournamentCard extends StatefulWidget {
  final Tournament tournament;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteTap;
  final bool isFavorite;
  final bool showFavoriteButton;
  final int index;
  final Duration animationDelay;

  const AnimatedTournamentCard({
    super.key,
    required this.tournament,
    this.onTap,
    this.onFavoriteTap,
    this.isFavorite = false,
    this.showFavoriteButton = true,
    this.index = 0,
    this.animationDelay = const Duration(milliseconds: 100),
  });

  @override
  State<AnimatedTournamentCard> createState() => _AnimatedTournamentCardState();
}

class _AnimatedTournamentCardState extends State<AnimatedTournamentCard>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _slideController = AnimationController(
      duration: Duration(milliseconds: 600 + (widget.index * 100)),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));

    // Start animation with delay
    Future.delayed(widget.animationDelay * widget.index, () {
      if (mounted) {
        _slideController.forward();
      }
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: GestureDetector(
            onTapDown: (_) => _scaleController.reverse(),
            onTapUp: (_) => _scaleController.forward(),
            onTapCancel: () => _scaleController.forward(),
            child: TournamentCard(
              tournament: widget.tournament,
              onTap: widget.onTap,
              onFavoriteTap: widget.onFavoriteTap,
              isFavorite: widget.isFavorite,
              showFavoriteButton: widget.showFavoriteButton,
            ),
          ),
        ),
      ),
    );
  }
}

class TournamentListAnimator extends StatefulWidget {
  final List<Tournament> tournaments;
  final Widget Function(Tournament tournament, int index) itemBuilder;
  final Duration staggerDelay;

  const TournamentListAnimator({
    super.key,
    required this.tournaments,
    required this.itemBuilder,
    this.staggerDelay = const Duration(milliseconds: 100),
  });

  @override
  State<TournamentListAnimator> createState() => _TournamentListAnimatorState();
}

class _TournamentListAnimatorState extends State<TournamentListAnimator>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: Duration(
        milliseconds:
            300 +
            (widget.tournaments.length * widget.staggerDelay.inMilliseconds),
      ),
      vsync: this,
    );

    _animations = List.generate(
      widget.tournaments.length,
      (index) => Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(
            (index * 0.1).clamp(0.0, 1.0),
            ((index * 0.1) + 0.3).clamp(0.0, 1.0),
            curve: Curves.easeOutCubic,
          ),
        ),
      ),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children:
          widget.tournaments.asMap().entries.map((entry) {
            final index = entry.key;
            final tournament = entry.value;

            return AnimatedBuilder(
              animation: _animations[index],
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, (1 - _animations[index].value) * 50),
                  child: Opacity(
                    opacity: _animations[index].value,
                    child: widget.itemBuilder(tournament, index),
                  ),
                );
              },
            );
          }).toList(),
    );
  }
}
