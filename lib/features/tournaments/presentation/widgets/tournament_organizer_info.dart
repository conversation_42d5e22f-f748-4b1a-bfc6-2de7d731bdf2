import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';

class TournamentOrganizerInfo extends StatelessWidget {
  final dynamic tournament;

  const TournamentOrganizerInfo({super.key, required this.tournament});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Organizer',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: Row(
            children: [
              // Organizer avatar
              CircleAvatar(
                radius: 32.r,
                backgroundImage:
                    tournament.organizerAvatar != null
                        ? CachedNetworkImageProvider(
                          tournament.organizerAvatar!,
                        )
                        : null,
                backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                child:
                    tournament.organizerAvatar == null
                        ? Icon(
                          Icons.business,
                          size: 32.sp,
                          color: theme.colorScheme.primary,
                        )
                        : null,
              ),

              SizedBox(width: 16.w),

              // Organizer info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tournament.organizerName,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'Tournament Organizer',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Icon(Icons.verified, size: 16.sp, color: Colors.green),
                        SizedBox(width: 4.w),
                        Text(
                          'Verified Organizer',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.green,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Action buttons
              Column(
                children: [
                  IconButton(
                    onPressed: () => _contactOrganizer(context),
                    icon: Icon(Icons.message, color: theme.colorScheme.primary),
                    style: IconButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary.withOpacity(
                        0.1,
                      ),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  IconButton(
                    onPressed: () => _viewOrganizerProfile(context),
                    icon: Icon(Icons.person, color: theme.colorScheme.primary),
                    style: IconButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary.withOpacity(
                        0.1,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        SizedBox(height: 16.h),

        // Tournament dates
        _buildTournamentDates(tournament, theme),
      ],
    );
  }

  Widget _buildTournamentDates(dynamic tournament, ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          _buildDateRow(
            'Registration Deadline',
            tournament.registrationDeadline,
            Icons.app_registration,
            theme,
          ),
          SizedBox(height: 12.h),
          _buildDateRow(
            'Tournament Start',
            tournament.startDate,
            Icons.play_arrow,
            theme,
          ),
          SizedBox(height: 12.h),
          _buildDateRow(
            'Tournament End',
            tournament.endDate,
            Icons.flag,
            theme,
          ),
        ],
      ),
    );
  }

  Widget _buildDateRow(
    String label,
    DateTime date,
    IconData icon,
    ThemeData theme,
  ) {
    final now = DateTime.now();
    final isUpcoming = date.isAfter(now);
    final isPast = date.isBefore(now);

    Color iconColor = theme.colorScheme.primary;
    if (isPast) {
      iconColor = Colors.grey;
    } else if (date.difference(now).inDays <= 1) {
      iconColor = Colors.orange;
    }

    return Row(
      children: [
        Icon(icon, size: 20.sp, color: iconColor),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
              Text(
                _formatDateTime(date),
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: iconColor,
                ),
              ),
            ],
          ),
        ),
        if (isUpcoming)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Text(
              _getTimeRemaining(date),
              style: TextStyle(
                color: iconColor,
                fontSize: 11.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _getTimeRemaining(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now);

    if (difference.inDays > 0) {
      return '${difference.inDays}d left';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h left';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m left';
    } else {
      return 'Now';
    }
  }

  void _contactOrganizer(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Contact organizer feature coming soon!')),
    );
  }

  void _viewOrganizerProfile(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Organizer profile feature coming soon!')),
    );
  }
}
