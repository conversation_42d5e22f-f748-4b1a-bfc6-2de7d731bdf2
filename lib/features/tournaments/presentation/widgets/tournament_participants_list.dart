import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../domain/entities/tournament.dart';

class TournamentParticipantsList extends StatelessWidget {
  final dynamic tournament;

  const TournamentParticipantsList({super.key, required this.tournament});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (tournament.participants.isEmpty) {
      return _buildEmptyState(theme);
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: tournament.participants.length,
      itemBuilder: (context, index) {
        final participant = tournament.participants[index];
        return _buildParticipantCard(participant, theme);
      },
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people_outline, size: 64.sp, color: Colors.grey[400]),
          Sized<PERSON><PERSON>(height: 16.h),
          Text(
            'No participants yet',
            style: TextStyle(fontSize: 18.sp, color: Colors.grey[600]),
          ),
          SizedBox(height: 8.h),
          Text(
            'Be the first to join this tournament!',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildParticipantCard(dynamic participant, ThemeData theme) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          // Avatar
          CircleAvatar(
            radius: 24.r,
            backgroundImage: participant.playerAvatar != null
                ? CachedNetworkImageProvider(participant.playerAvatar!)
                : null,
            backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
            child: participant.playerAvatar == null
                ? Text(
                    participant.playerName[0].toUpperCase(),
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  )
                : null,
          ),

          SizedBox(width: 12.w),

          // Participant info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  participant.playerName,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (participant.teamName != null) ...[
                  SizedBox(height: 2.h),
                  Text(
                    participant.teamName!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
                SizedBox(height: 4.h),
                Text(
                  'Joined ${_formatJoinDate(participant.joinedAt)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                  ),
                ),
              ],
            ),
          ),

          // Status badge
          _buildStatusBadge(participant, theme),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(dynamic participant, ThemeData theme) {
    Color badgeColor;
    String statusText;

    switch (participant.status) {
      case 'registered':
        badgeColor = Colors.blue;
        statusText = 'Registered';
        break;
      case 'confirmed':
        badgeColor = Colors.green;
        statusText = 'Confirmed';
        break;
      case 'checked_in':
        badgeColor = Colors.purple;
        statusText = 'Checked In';
        break;
      case 'eliminated':
        badgeColor = Colors.red;
        statusText = 'Eliminated';
        break;
      case 'withdrawn':
        badgeColor = Colors.grey;
        statusText = 'Withdrawn';
        break;
      default:
        badgeColor = Colors.grey;
        statusText = 'Unknown';
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: badgeColor,
          fontSize: 11.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  String _formatJoinDate(DateTime joinDate) {
    final now = DateTime.now();
    final difference = now.difference(joinDate);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
}
