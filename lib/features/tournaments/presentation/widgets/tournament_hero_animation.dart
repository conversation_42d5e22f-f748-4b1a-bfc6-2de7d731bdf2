import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../domain/entities/tournament.dart';

class TournamentHeroAnimation extends StatelessWidget {
  final Tournament tournament;
  final Widget child;
  final String? heroTag;

  const TournamentHeroAnimation({
    super.key,
    required this.tournament,
    required this.child,
    this.heroTag,
  });

  @override
  Widget build(BuildContext context) {
    final tag = heroTag ?? 'tournament_${tournament.id}';

    return Hero(
      tag: tag,
      child: Material(color: Colors.transparent, child: child),
      flightShuttleBuilder: (
        flightContext,
        animation,
        flightDirection,
        fromHeroContext,
        toHeroContext,
      ) {
        return _buildFlightShuttle(
          animation,
          flightDirection == HeroFlightDirection.push,
        );
      },
    );
  }

  Widget _buildFlightShuttle(Animation<double> animation, bool isPush) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final theme = Theme.of(context);

        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(
              Tween<double>(begin: 16.0, end: 0.0).evaluate(animation).r,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(
                  Tween<double>(begin: 0.1, end: 0.3).evaluate(animation),
                ),
                blurRadius: Tween<double>(
                  begin: 8.0,
                  end: 20.0,
                ).evaluate(animation),
                spreadRadius: Tween<double>(
                  begin: 0.0,
                  end: 5.0,
                ).evaluate(animation),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(
              Tween<double>(begin: 16.0, end: 0.0).evaluate(animation).r,
            ),
            child: Stack(
              fit: StackFit.expand,
              children: [
                // Background image
                CachedNetworkImage(
                  imageUrl: tournament.bannerImage,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => _buildImagePlaceholder(theme),
                  errorWidget: (context, url, error) =>
                      _buildImagePlaceholder(theme),
                ),

                // Gradient overlay
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(
                          Tween<double>(
                            begin: 0.3,
                            end: 0.7,
                          ).evaluate(animation),
                        ),
                      ],
                    ),
                  ),
                ),

                // Tournament info
                Positioned(
                  bottom: Tween<double>(
                    begin: 16.0,
                    end: 60.0,
                  ).evaluate(animation),
                  left: 16.0,
                  right: 16.0,
                  child: Opacity(
                    opacity: Tween<double>(begin: 0.0, end: 1.0).evaluate(
                      CurvedAnimation(
                        parent: animation,
                        curve: const Interval(0.5, 1.0),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          tournament.name,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: Tween<double>(
                              begin: 16.0,
                              end: 24.0,
                            ).evaluate(animation).sp,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 8.h),
                        Row(
                          children: [
                            _buildInfoChip(
                              Icons.sports_soccer,
                              _formatDisplay(),
                              animation,
                            ),
                            SizedBox(width: 8.w),
                            _buildInfoChip(
                              Icons.location_on,
                              _locationDisplay(),
                              animation,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildImagePlaceholder(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withOpacity(0.8),
            theme.colorScheme.primary,
          ],
        ),
      ),
      child: Center(
        child: Icon(Icons.sports_soccer, size: 80.sp, color: Colors.white),
      ),
    );
  }

  Widget _buildInfoChip(
    IconData icon,
    String label,
    Animation<double> animation,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(
          Tween<double>(begin: 0.2, end: 0.4).evaluate(animation),
        ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14.sp, color: Colors.white),
          SizedBox(width: 4.w),
          Text(
            label,
            style: TextStyle(
              color: Colors.white,
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

class TournamentImageHero extends StatelessWidget {
  final Tournament tournament;
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;

  const TournamentImageHero({
    super.key,
    required this.tournament,
    this.width,
    this.height,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Hero(
      tag: 'tournament_image_${tournament.id}',
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: borderRadius ?? BorderRadius.circular(12.r),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary.withOpacity(0.8),
              theme.colorScheme.primary,
            ],
          ),
        ),
        child: ClipRRect(
          borderRadius: borderRadius ?? BorderRadius.circular(12.r),
          child: CachedNetworkImage(
            imageUrl: tournament.bannerImage,
            fit: BoxFit.cover,
            placeholder: (context, url) => _buildImagePlaceholder(theme),
            errorWidget: (context, url, error) => _buildImagePlaceholder(theme),
          ),
        ),
      ),
    );
  }

  Widget _buildImagePlaceholder(ThemeData theme) {
    return Center(
      child: Icon(
        Icons.sports_soccer,
        size: (height ?? 100.h) * 0.4,
        color: Colors.white,
      ),
    );
  }
}

class TournamentPageTransition extends StatelessWidget {
  final Widget child;
  final Animation<double> animation;

  const TournamentPageTransition({
    super.key,
    required this.child,
    required this.animation,
  });

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: animation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0.0, 0.1),
          end: Offset.zero,
        ).animate(
          CurvedAnimation(parent: animation, curve: Curves.easeOutCubic),
        ),
        child: child,
      ),
    );
  }
}

extension _TournamentHeroHelpers on TournamentHeroAnimation {
  String _formatDisplay() {
    return tournament.format.displayName;
  }

  String _locationDisplay() {
    return tournament.location;
  }
}
