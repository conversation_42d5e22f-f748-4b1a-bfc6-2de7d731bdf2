import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../domain/entities/tournament.dart';
import 'tournament_detail_bottom_sheet.dart';

class TournamentCard extends ConsumerWidget {
  final Tournament tournament;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteTap;
  final bool isFavorite;
  final bool showFavoriteButton;

  const TournamentCard({
    super.key,
    required this.tournament,
    this.onTap,
    this.onFavoriteTap,
    this.isFavorite = false,
    this.showFavoriteButton = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    return GestureDetector(
      onTap: onTap ?? () => _showTournamentDetails(context),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withOpacity(0.1)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tournament image
            Container(
              height: 160,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(16)),
                image: DecorationImage(
                  image: NetworkImage(tournament.bannerImage),
                  fit: BoxFit.cover,
                ),
              ),
              child: Stack(
                children: [
                  // Status badge
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor().withOpacity(0.9),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        tournament.statusDisplayText,
                        style: const TextStyle(
                          fontFamily: 'Gilroy_Bold',
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  // Featured badge - removed as isFeatured property doesn't exist
                ],
              ),
            ),

            // Tournament info
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and format
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          tournament.name,
                          style: const TextStyle(
                            fontFamily: 'Gilroy_Bold',
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: accentColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: accentColor),
                        ),
                        child: Text(
                          _formatDisplay(),
                          style: TextStyle(
                            fontFamily: 'Gilroy_Bold',
                            color: accentColor,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Organizer
                  Row(
                    children: [
                      Icon(
                        Icons.person,
                        color: greyColor,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        tournament.organizerName,
                        style: TextStyle(
                          fontFamily: 'Gilroy_Medium',
                          color: greyColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Location and dates
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        color: greyColor,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          '${tournament.city}, ${tournament.venueDetails.name}',
                          style: TextStyle(
                            fontFamily: 'Gilroy_Medium',
                            color: greyColor,
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        color: greyColor,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${_formatDate(tournament.startDate)} - ${_formatDate(tournament.endDate)}',
                        style: TextStyle(
                          fontFamily: 'Gilroy_Medium',
                          color: greyColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Tournament details grid
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoChip(
                          'Teams',
                          '${tournament.maxParticipants}',
                          Icons.group,
                          accentColor,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildInfoChip(
                          'Format',
                          tournament.format.displayName,
                          Icons.sports_soccer,
                          accentColor,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildInfoChip(
                          'Entry',
                          tournament.entryFee > 0
                              ? '₹${tournament.entryFee}'
                              : 'Free',
                          Icons.monetization_on,
                          accentColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _canJoin() ? () {} : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: accentColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: Text(
                            _joinButtonText,
                            style: const TextStyle(
                              fontFamily: 'Gilroy_Bold',
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      if (showFavoriteButton) ...[
                        const SizedBox(width: 8),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.2),
                            ),
                          ),
                          child: IconButton(
                            onPressed: onFavoriteTap,
                            icon: Icon(
                              isFavorite
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              color: isFavorite ? Colors.red : greyColor,
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.7),
              fontSize: 10,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (tournament.status) {
      case TournamentStatus.registrationOpen:
        return Colors.green;
      case TournamentStatus.registrationClosed:
        return Colors.orange;
      case TournamentStatus.inProgress:
        return Colors.blue;
      case TournamentStatus.completed:
        return Colors.grey;
      case TournamentStatus.cancelled:
        return Colors.red;
      case TournamentStatus.draft:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final isSameDay =
        now.year == date.year && now.month == date.month && now.day == date.day;
    if (isSameDay) return 'Today';
    return '${date.day}/${date.month}/${date.year}';
  }

  String get _joinButtonText {
    if (tournament.status == TournamentStatus.registrationOpen) {
      return 'Join Tournament';
    } else if (tournament.status == TournamentStatus.registrationClosed) {
      return 'Registration Closed';
    } else if (tournament.status == TournamentStatus.inProgress) {
      return 'In Progress';
    } else if (tournament.status == TournamentStatus.completed) {
      return 'Completed';
    } else if (tournament.status == TournamentStatus.cancelled) {
      return 'Cancelled';
    } else {
      return 'View Details';
    }
  }

  bool _canJoin() {
    return tournament.status == TournamentStatus.registrationOpen;
  }

  String _formatDisplay() {
    return tournament.format.displayName;
  }

  void _showTournamentDetails(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => TournamentDetailBottomSheet(tournament: tournament),
    );
  }
}

class TournamentCardShimmer extends StatelessWidget {
  const TournamentCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.05)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image placeholder
          Container(
            height: 160,
            width: double.infinity,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
              color: Colors.black12,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(height: 16, width: 180, color: Colors.black12),
                const SizedBox(height: 8),
                Container(height: 14, width: 240, color: Colors.black12),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: Container(height: 36, color: Colors.black12),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
