import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/tournament_filter.dart';
import '../../domain/entities/tournament.dart';
import '../../domain/usecases/tournament_usecases.dart';
import 'tournament_state.dart';

class TournamentListNotifier extends StateNotifier<TournamentListState> {
  final GetTournamentsUseCase getTournamentsUseCase;
  final SearchTournamentsUseCase searchTournamentsUseCase;
  final GetFeaturedTournamentsUseCase getFeaturedTournamentsUseCase;
  final GetNearbyTournamentsUseCase getNearbyTournamentsUseCase;

  TournamentListNotifier({
    required this.getTournamentsUseCase,
    required this.searchTournamentsUseCase,
    required this.getFeaturedTournamentsUseCase,
    required this.getNearbyTournamentsUseCase,
  }) : super(const TournamentListState.initial());

  Future<void> loadTournaments({TournamentSearchQuery? query}) async {
    if (state.maybeWhen(
      loading: () => true,
      orElse: () => false,
    )) return;

    state = const TournamentListState.loading();

    final result = await getTournamentsUseCase(
      GetTournamentsParams(query: query),
    );

    result.fold(
      (error) => state = TournamentListState.error(error.message),
      (searchResult) => state = TournamentListState.loaded(
        tournaments: searchResult.tournaments,
        hasMore: searchResult.hasMore,
        currentPage: searchResult.currentPage,
        query: searchResult.query,
      ),
    );
  }

  Future<void> loadMoreTournaments() async {
    final currentState = state;
    final hasMore = currentState.maybeWhen(
      loaded: (_, hasMore, __, ___) => hasMore,
      orElse: () => false,
    );
    if (!hasMore) return;

    state = const TournamentListState.loadingMore();

    final nextQuery = currentState.maybeWhen(
      loaded: (_, __, ___, query) => TournamentSearchQuery(
        searchTerm: query.searchTerm,
        filter: query.filter,
        limit: query.limit,
        offset: query.offset + query.limit,
      ),
      orElse: () => const TournamentSearchQuery(),
    );

    final result = await getTournamentsUseCase(
      GetTournamentsParams(query: nextQuery),
    );

    result.fold(
      (error) => state = TournamentListState.error(error.message),
      (searchResult) {
        final existingTournaments = currentState.maybeWhen(
          loaded: (tournaments, _, __, ___) => tournaments,
          orElse: () => <Tournament>[],
        );
        state = TournamentListState.loaded(
          tournaments: [
            ...existingTournaments,
            ...searchResult.tournaments,
          ],
          hasMore: searchResult.hasMore,
          currentPage: searchResult.currentPage,
          query: searchResult.query,
        );
      },
    );
  }

  Future<void> refreshTournaments() async {
    final currentState = state;
    currentState.when(
      loaded: (tournaments, hasMore, currentPage, query) async {
        await loadTournaments(
            query: TournamentSearchQuery(
          searchTerm: query.searchTerm,
          filter: query.filter,
          limit: query.limit,
          offset: 0,
        ));
      },
      initial: () async {
        await loadTournaments();
      },
      loading: () async {
        await loadTournaments();
      },
      loadingMore: () async {
        await loadTournaments();
      },
      error: (message) async {
        await loadTournaments();
      },
    );
  }

  Future<void> applyFilter(TournamentFilter filter) async {
    final currentState = state;
    final query = currentState.maybeWhen(
      loaded: (_, __, ___, query) => TournamentSearchQuery(
        searchTerm: query.searchTerm,
        filter: filter,
        limit: query.limit,
        offset: 0,
      ),
      orElse: () => TournamentSearchQuery(filter: filter),
    );

    await loadTournaments(query: query);
  }

  Future<void> search(String searchTerm) async {
    final currentState = state;
    final query = currentState.maybeWhen(
      loaded: (_, __, ___, query) => TournamentSearchQuery(
        searchTerm: searchTerm,
        filter: query.filter,
        limit: query.limit,
        offset: 0,
      ),
      orElse: () => TournamentSearchQuery(searchTerm: searchTerm),
    );

    await loadTournaments(query: query);
  }

  Future<void> loadFeaturedTournaments() async {
    state = const TournamentListState.loading();

    final result = await getFeaturedTournamentsUseCase(
      const GetFeaturedTournamentsParams(limit: 10),
    );

    result.fold(
      (error) => state = TournamentListState.error(error.message),
      (tournaments) => state = TournamentListState.loaded(
        tournaments: tournaments,
        hasMore: false,
        currentPage: 1,
        query: const TournamentSearchQuery(),
      ),
    );
  }

  Future<void> loadNearbyTournaments({
    required double latitude,
    required double longitude,
    double radiusKm = 50.0,
  }) async {
    state = const TournamentListState.loading();

    final result = await getNearbyTournamentsUseCase(
      GetNearbyTournamentsParams(
        latitude: latitude,
        longitude: longitude,
        radiusKm: radiusKm,
      ),
    );

    result.fold(
      (error) => state = TournamentListState.error(error.message),
      (tournaments) => state = TournamentListState.loaded(
        tournaments: tournaments,
        hasMore: false,
        currentPage: 1,
        query: const TournamentSearchQuery(),
      ),
    );
  }
}

class TournamentDetailsNotifier extends StateNotifier<TournamentDetailsState> {
  final GetTournamentDetailsUseCase getTournamentDetailsUseCase;
  final JoinTournamentUseCase joinTournamentUseCase;
  final LeaveTournamentUseCase leaveTournamentUseCase;
  final AddToFavouritesUseCase addToFavouritesUseCase;
  final RemoveFromFavouritesUseCase removeFromFavouritesUseCase;

  final String tournamentId;
  final String currentPlayerId;

  TournamentDetailsNotifier({
    required this.tournamentId,
    required this.currentPlayerId,
    required this.getTournamentDetailsUseCase,
    required this.joinTournamentUseCase,
    required this.leaveTournamentUseCase,
    required this.addToFavouritesUseCase,
    required this.removeFromFavouritesUseCase,
  }) : super(const TournamentDetailsState.initial());

  Future<void> loadTournamentDetails() async {
    state = const TournamentDetailsState.loading();

    final result = await getTournamentDetailsUseCase(tournamentId);

    result.fold(
      (error) => state = TournamentDetailsState.error(error.message),
      (tournament) {
        final isParticipant = tournament.participants.any(
          (p) => p.playerId == currentPlayerId,
        );

        state = TournamentDetailsState.loaded(
          tournament: tournament,
          isFavourite: false, // This should be checked from favourites
          isParticipant: isParticipant,
        );
      },
    );
  }

  Future<void> joinTournament() async {
    final currentState = state;
    final isLoaded = currentState.maybeWhen(
      loaded: (_, __, ___) => true,
      orElse: () => false,
    );
    if (!isLoaded) return;

    final result = await joinTournamentUseCase(
      JoinTournamentParams(
        tournamentId: tournamentId,
        playerId: currentPlayerId,
      ),
    );

    result.fold(
      (error) => state = TournamentDetailsState.error(error.message),
      (updatedTournament) => currentState.maybeWhen(
        loaded: (tournament, isFavourite, isParticipant) =>
            state = TournamentDetailsState.loaded(
          tournament: updatedTournament,
          isFavourite: isFavourite,
          isParticipant: true,
        ),
        orElse: () => state = TournamentDetailsState.error('Invalid state'),
      ),
    );
  }

  Future<void> leaveTournament() async {
    final currentState = state;
    final isLoaded = currentState.maybeWhen(
      loaded: (_, __, ___) => true,
      orElse: () => false,
    );
    if (!isLoaded) return;

    final result = await leaveTournamentUseCase(
      LeaveTournamentParams(
        tournamentId: tournamentId,
        playerId: currentPlayerId,
      ),
    );

    result.fold(
      (error) => state = TournamentDetailsState.error(error.message),
      (updatedTournament) => currentState.maybeWhen(
        loaded: (tournament, isFavourite, isParticipant) =>
            state = TournamentDetailsState.loaded(
          tournament: updatedTournament,
          isFavourite: isFavourite,
          isParticipant: false,
        ),
        orElse: () => state = TournamentDetailsState.error('Invalid state'),
      ),
    );
  }

  Future<void> toggleFavourite() async {
    final currentState = state;
    final isLoaded = currentState.maybeWhen(
      loaded: (_, __, ___) => true,
      orElse: () => false,
    );
    if (!isLoaded) return;

    final isFavourite = currentState.maybeWhen(
      loaded: (_, isFav, __) => isFav,
      orElse: () => false,
    );

    if (isFavourite) {
      await removeFromFavouritesUseCase(
        RemoveFromFavouritesParams(
          playerId: currentPlayerId,
          tournamentId: tournamentId,
        ),
      );
      currentState.maybeWhen(
        loaded: (tournament, _, isParticipant) =>
            state = TournamentDetailsState.loaded(
          tournament: tournament,
          isFavourite: false,
          isParticipant: isParticipant,
        ),
        orElse: () => state = TournamentDetailsState.error('Invalid state'),
      );
    } else {
      await addToFavouritesUseCase(
        AddToFavouritesParams(
          playerId: currentPlayerId,
          tournamentId: tournamentId,
        ),
      );
      currentState.maybeWhen(
        loaded: (tournament, _, isParticipant) =>
            state = TournamentDetailsState.loaded(
          tournament: tournament,
          isFavourite: true,
          isParticipant: isParticipant,
        ),
        orElse: () => state = TournamentDetailsState.error('Invalid state'),
      );
    }
  }
}

class FavouriteTournamentsNotifier
    extends StateNotifier<FavouriteTournamentsState> {
  final GetFavouriteTournamentsUseCase getFavouriteTournamentsUseCase;
  final RemoveFromFavouritesUseCase removeFromFavouritesUseCase;

  final String playerId;

  FavouriteTournamentsNotifier({
    required this.playerId,
    required this.getFavouriteTournamentsUseCase,
    required this.removeFromFavouritesUseCase,
  }) : super(const FavouriteTournamentsState.initial());

  Future<void> loadFavourites() async {
    state = const FavouriteTournamentsState.loading();

    final result = await getFavouriteTournamentsUseCase(playerId);

    result.fold(
      (error) => state = FavouriteTournamentsState.error(error.message),
      (favourites) => state = FavouriteTournamentsState.loaded(favourites),
    );
  }

  Future<void> removeFavourite(String tournamentId) async {
    final result = await removeFromFavouritesUseCase(
      RemoveFromFavouritesParams(
        playerId: playerId,
        tournamentId: tournamentId,
      ),
    );

    result.fold(
      (error) => state = FavouriteTournamentsState.error(error.message),
      (_) => loadFavourites(), // Reload favourites
    );
  }
}

class BadgeSystemNotifier extends StateNotifier<BadgeSystemState> {
  final GetPlayerBadgesUseCase getPlayerBadgesUseCase;
  final GetPlayerTournamentStatsUseCase getPlayerTournamentStatsUseCase;
  final CheckForNewBadgesUseCase checkForNewBadgesUseCase;

  final String playerId;

  BadgeSystemNotifier({
    required this.playerId,
    required this.getPlayerBadgesUseCase,
    required this.getPlayerTournamentStatsUseCase,
    required this.checkForNewBadgesUseCase,
  }) : super(const BadgeSystemState.initial());

  Future<void> loadBadgeSystem() async {
    state = const BadgeSystemState.loading();

    final badgesResult = await getPlayerBadgesUseCase(playerId);
    final statsResult = await getPlayerTournamentStatsUseCase(playerId);

    if (badgesResult.isLeft() || statsResult.isLeft()) {
      final error = badgesResult.isLeft()
          ? badgesResult.fold((l) => l.message, (r) => '')
          : statsResult.fold((l) => l.message, (r) => '');
      state = BadgeSystemState.error(error);
      return;
    }

    final badges = badgesResult.fold(
      (error) => throw Exception(error.message),
      (badges) => badges,
    );
    final stats = statsResult.fold(
      (error) => throw Exception(error.message),
      (stats) => stats,
    );

    state = BadgeSystemState.loaded(
      playerBadges: badges,
      stats: stats,
      availableBadges: [], // This would be loaded separately
    );
  }

  Future<void> checkForNewBadges() async {
    final currentState = state;
    final isLoaded = currentState.maybeWhen(
      loaded: (_, __, ___, ____) => true,
      orElse: () => false,
    );
    if (!isLoaded) return;

    final stats = currentState.maybeWhen(
      loaded: (_, stats, __, ___) => stats,
      orElse: () => null,
    );
    if (stats == null) return;

    final result = await checkForNewBadgesUseCase(
      CheckForNewBadgesParams(playerId: playerId, stats: stats),
    );

    result.fold(
      (error) => {}, // Silently fail for new badge checks
      (newBadges) => currentState.maybeWhen(
        loaded: (playerBadges, stats, availableBadges, newlyUnlockedBadges) =>
            state = BadgeSystemState.loaded(
          playerBadges: playerBadges,
          stats: stats,
          availableBadges: availableBadges,
          newlyUnlockedBadges: newBadges,
        ),
        orElse: () => state = BadgeSystemState.error('Invalid state'),
      ),
    );
  }

  void clearNewBadges() {
    final currentState = state;
    currentState.maybeWhen(
      loaded: (playerBadges, stats, availableBadges, newlyUnlockedBadges) =>
          state = BadgeSystemState.loaded(
        playerBadges: playerBadges,
        stats: stats,
        availableBadges: availableBadges,
        newlyUnlockedBadges: [],
      ),
      orElse: () => {},
    );
  }
}

class TournamentFilterNotifier extends StateNotifier<TournamentFilterState> {
  TournamentFilterNotifier()
      : super(TournamentFilterState(filter: TournamentFilter()));

  void updateFilter(TournamentFilter filter) {
    state = state.copyWith(filter: filter);
  }

  void applyFilter() {
    state = state.copyWith(isApplied: true);
  }

  void clearFilter() {
    state = const TournamentFilterState(filter: TournamentFilter());
  }

  void resetApplied() {
    state = state.copyWith(isApplied: false);
  }

  // Specific filter methods
  void updateFormats(List<TournamentType> formats) {
    updateFilter(state.filter.copyWith(formats: formats));
  }

  void updateLocations(List<String> locations) {
    updateFilter(state.filter.copyWith(locations: locations));
  }

  void updateDateRange(DateRangeFilter? dateRange) {
    updateFilter(state.filter.copyWith(dateRange: dateRange));
  }

  void updateEntryFeeRange(PriceRangeFilter? entryFeeRange) {
    updateFilter(state.filter.copyWith(entryFeeRange: entryFeeRange));
  }

  void updatePrizeRange(PriceRangeFilter? prizeRange) {
    updateFilter(state.filter.copyWith(prizeRange: prizeRange));
  }

  void updateAvailableSlotsOnly(bool availableSlotsOnly) {
    updateFilter(state.filter.copyWith(availableSlotsOnly: availableSlotsOnly));
  }

  void updateSortBy(TournamentSortBy sortBy) {
    updateFilter(state.filter.copyWith(sortBy: sortBy));
  }

  void updateSortOrder(SortOrder sortOrder) {
    updateFilter(state.filter.copyWith(sortOrder: sortOrder));
  }
}
