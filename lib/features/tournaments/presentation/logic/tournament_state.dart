import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/tournament.dart';
import '../../domain/entities/tournament_filter.dart';
import '../../domain/entities/favourite_tournament.dart';
import '../../domain/entities/player_badge.dart';

part 'tournament_state.freezed.dart';

// Tournament List State
@freezed
class TournamentListState with _$TournamentListState {
  const factory TournamentListState.initial() = _Initial;
  const factory TournamentListState.loading() = _Loading;
  const factory TournamentListState.loadingMore() = _LoadingMore;
  const factory TournamentListState.loaded({
    required List<Tournament> tournaments,
    required bool hasMore,
    required int currentPage,
    required TournamentSearchQuery query,
  }) = _Loaded;
  const factory TournamentListState.error(String message) = _Error;
}

// Tournament Details State
@freezed
class TournamentDetailsState with _$TournamentDetailsState {
  const factory TournamentDetailsState.initial() = _DetailsInitial;
  const factory TournamentDetailsState.loading() = _DetailsLoading;
  const factory TournamentDetailsState.loaded({
    required Tournament tournament,
    required bool isFavourite,
    required bool isParticipant,
  }) = _DetailsLoaded;
  const factory TournamentDetailsState.error(String message) = _DetailsError;
}

// Join Tournament State
@freezed
class JoinTournamentState with _$JoinTournamentState {
  const factory JoinTournamentState.initial() = _JoinInitial;
  const factory JoinTournamentState.loading() = _JoinLoading;
  const factory JoinTournamentState.success(Tournament tournament) =
      _JoinSuccess;
  const factory JoinTournamentState.error(String message) = _JoinError;
}

// Favourite Tournaments State
@freezed
class FavouriteTournamentsState with _$FavouriteTournamentsState {
  const factory FavouriteTournamentsState.initial() = _FavouriteInitial;
  const factory FavouriteTournamentsState.loading() = _FavouriteLoading;
  const factory FavouriteTournamentsState.loaded(
    List<FavouriteTournamentDetails> favourites,
  ) = _FavouriteLoaded;
  const factory FavouriteTournamentsState.error(String message) =
      _FavouriteError;
}

// Badge System State
@freezed
class BadgeSystemState with _$BadgeSystemState {
  const factory BadgeSystemState.initial() = _BadgeInitial;
  const factory BadgeSystemState.loading() = _BadgeLoading;
  const factory BadgeSystemState.loaded({
    required PlayerBadgeCollection playerBadges,
    required TournamentStats stats,
    required List<PlayerBadge> availableBadges,
    @Default([]) List<PlayerBadge> newlyUnlockedBadges,
  }) = _BadgeLoaded;
  const factory BadgeSystemState.error(String message) = _BadgeError;
}

// Filter State
@freezed
class TournamentFilterState with _$TournamentFilterState {
  const factory TournamentFilterState({
    required TournamentFilter filter,
    @Default(false) bool isApplied,
  }) = _TournamentFilterState;
}

// Search State
@freezed
class TournamentSearchState with _$TournamentSearchState {
  const factory TournamentSearchState.initial() = _SearchInitial;
  const factory TournamentSearchState.searching() = _Searching;
  const factory TournamentSearchState.loaded({
    required List<Tournament> results,
    required String searchTerm,
    required bool hasMore,
  }) = _SearchLoaded;
  const factory TournamentSearchState.error(String message) = _SearchError;
}
