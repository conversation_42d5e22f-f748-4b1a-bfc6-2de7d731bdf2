// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tournament_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$TournamentListState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loadingMore,
    required TResult Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )
    loaded,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loadingMore,
    TResult? Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loadingMore,
    TResult Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadingMore value) loadingMore,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadingMore value)? loadingMore,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadingMore value)? loadingMore,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TournamentListStateCopyWith<$Res> {
  factory $TournamentListStateCopyWith(
    TournamentListState value,
    $Res Function(TournamentListState) then,
  ) = _$TournamentListStateCopyWithImpl<$Res, TournamentListState>;
}

/// @nodoc
class _$TournamentListStateCopyWithImpl<$Res, $Val extends TournamentListState>
    implements $TournamentListStateCopyWith<$Res> {
  _$TournamentListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TournamentListState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
    _$InitialImpl value,
    $Res Function(_$InitialImpl) then,
  ) = __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$TournamentListStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
    _$InitialImpl _value,
    $Res Function(_$InitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentListState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'TournamentListState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loadingMore,
    required TResult Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loadingMore,
    TResult? Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loadingMore,
    TResult Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadingMore value) loadingMore,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadingMore value)? loadingMore,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadingMore value)? loadingMore,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements TournamentListState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
    _$LoadingImpl value,
    $Res Function(_$LoadingImpl) then,
  ) = __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$TournamentListStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
    _$LoadingImpl _value,
    $Res Function(_$LoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentListState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'TournamentListState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loadingMore,
    required TResult Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loadingMore,
    TResult? Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loadingMore,
    TResult Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadingMore value) loadingMore,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadingMore value)? loadingMore,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadingMore value)? loadingMore,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements TournamentListState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadingMoreImplCopyWith<$Res> {
  factory _$$LoadingMoreImplCopyWith(
    _$LoadingMoreImpl value,
    $Res Function(_$LoadingMoreImpl) then,
  ) = __$$LoadingMoreImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingMoreImplCopyWithImpl<$Res>
    extends _$TournamentListStateCopyWithImpl<$Res, _$LoadingMoreImpl>
    implements _$$LoadingMoreImplCopyWith<$Res> {
  __$$LoadingMoreImplCopyWithImpl(
    _$LoadingMoreImpl _value,
    $Res Function(_$LoadingMoreImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentListState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingMoreImpl implements _LoadingMore {
  const _$LoadingMoreImpl();

  @override
  String toString() {
    return 'TournamentListState.loadingMore()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingMoreImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loadingMore,
    required TResult Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return loadingMore();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loadingMore,
    TResult? Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return loadingMore?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loadingMore,
    TResult Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loadingMore != null) {
      return loadingMore();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadingMore value) loadingMore,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return loadingMore(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadingMore value)? loadingMore,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return loadingMore?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadingMore value)? loadingMore,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loadingMore != null) {
      return loadingMore(this);
    }
    return orElse();
  }
}

abstract class _LoadingMore implements TournamentListState {
  const factory _LoadingMore() = _$LoadingMoreImpl;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
    _$LoadedImpl value,
    $Res Function(_$LoadedImpl) then,
  ) = __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({
    List<Tournament> tournaments,
    bool hasMore,
    int currentPage,
    TournamentSearchQuery query,
  });
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$TournamentListStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
    _$LoadedImpl _value,
    $Res Function(_$LoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tournaments = null,
    Object? hasMore = null,
    Object? currentPage = null,
    Object? query = null,
  }) {
    return _then(
      _$LoadedImpl(
        tournaments:
            null == tournaments
                ? _value._tournaments
                : tournaments // ignore: cast_nullable_to_non_nullable
                    as List<Tournament>,
        hasMore:
            null == hasMore
                ? _value.hasMore
                : hasMore // ignore: cast_nullable_to_non_nullable
                    as bool,
        currentPage:
            null == currentPage
                ? _value.currentPage
                : currentPage // ignore: cast_nullable_to_non_nullable
                    as int,
        query:
            null == query
                ? _value.query
                : query // ignore: cast_nullable_to_non_nullable
                    as TournamentSearchQuery,
      ),
    );
  }
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl({
    required final List<Tournament> tournaments,
    required this.hasMore,
    required this.currentPage,
    required this.query,
  }) : _tournaments = tournaments;

  final List<Tournament> _tournaments;
  @override
  List<Tournament> get tournaments {
    if (_tournaments is EqualUnmodifiableListView) return _tournaments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tournaments);
  }

  @override
  final bool hasMore;
  @override
  final int currentPage;
  @override
  final TournamentSearchQuery query;

  @override
  String toString() {
    return 'TournamentListState.loaded(tournaments: $tournaments, hasMore: $hasMore, currentPage: $currentPage, query: $query)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            const DeepCollectionEquality().equals(
              other._tournaments,
              _tournaments,
            ) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_tournaments),
    hasMore,
    currentPage,
    query,
  );

  /// Create a copy of TournamentListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loadingMore,
    required TResult Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(tournaments, hasMore, currentPage, query);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loadingMore,
    TResult? Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(tournaments, hasMore, currentPage, query);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loadingMore,
    TResult Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(tournaments, hasMore, currentPage, query);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadingMore value) loadingMore,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadingMore value)? loadingMore,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadingMore value)? loadingMore,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements TournamentListState {
  const factory _Loaded({
    required final List<Tournament> tournaments,
    required final bool hasMore,
    required final int currentPage,
    required final TournamentSearchQuery query,
  }) = _$LoadedImpl;

  List<Tournament> get tournaments;
  bool get hasMore;
  int get currentPage;
  TournamentSearchQuery get query;

  /// Create a copy of TournamentListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
    _$ErrorImpl value,
    $Res Function(_$ErrorImpl) then,
  ) = __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$TournamentListStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
    _$ErrorImpl _value,
    $Res Function(_$ErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$ErrorImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                as String,
      ),
    );
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'TournamentListState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of TournamentListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loadingMore,
    required TResult Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loadingMore,
    TResult? Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loadingMore,
    TResult Function(
      List<Tournament> tournaments,
      bool hasMore,
      int currentPage,
      TournamentSearchQuery query,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadingMore value) loadingMore,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadingMore value)? loadingMore,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadingMore value)? loadingMore,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements TournamentListState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of TournamentListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$TournamentDetailsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )
    loaded,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DetailsInitial value) initial,
    required TResult Function(_DetailsLoading value) loading,
    required TResult Function(_DetailsLoaded value) loaded,
    required TResult Function(_DetailsError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DetailsInitial value)? initial,
    TResult? Function(_DetailsLoading value)? loading,
    TResult? Function(_DetailsLoaded value)? loaded,
    TResult? Function(_DetailsError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DetailsInitial value)? initial,
    TResult Function(_DetailsLoading value)? loading,
    TResult Function(_DetailsLoaded value)? loaded,
    TResult Function(_DetailsError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TournamentDetailsStateCopyWith<$Res> {
  factory $TournamentDetailsStateCopyWith(
    TournamentDetailsState value,
    $Res Function(TournamentDetailsState) then,
  ) = _$TournamentDetailsStateCopyWithImpl<$Res, TournamentDetailsState>;
}

/// @nodoc
class _$TournamentDetailsStateCopyWithImpl<
  $Res,
  $Val extends TournamentDetailsState
>
    implements $TournamentDetailsStateCopyWith<$Res> {
  _$TournamentDetailsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TournamentDetailsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$DetailsInitialImplCopyWith<$Res> {
  factory _$$DetailsInitialImplCopyWith(
    _$DetailsInitialImpl value,
    $Res Function(_$DetailsInitialImpl) then,
  ) = __$$DetailsInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DetailsInitialImplCopyWithImpl<$Res>
    extends _$TournamentDetailsStateCopyWithImpl<$Res, _$DetailsInitialImpl>
    implements _$$DetailsInitialImplCopyWith<$Res> {
  __$$DetailsInitialImplCopyWithImpl(
    _$DetailsInitialImpl _value,
    $Res Function(_$DetailsInitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentDetailsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DetailsInitialImpl implements _DetailsInitial {
  const _$DetailsInitialImpl();

  @override
  String toString() {
    return 'TournamentDetailsState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DetailsInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DetailsInitial value) initial,
    required TResult Function(_DetailsLoading value) loading,
    required TResult Function(_DetailsLoaded value) loaded,
    required TResult Function(_DetailsError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DetailsInitial value)? initial,
    TResult? Function(_DetailsLoading value)? loading,
    TResult? Function(_DetailsLoaded value)? loaded,
    TResult? Function(_DetailsError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DetailsInitial value)? initial,
    TResult Function(_DetailsLoading value)? loading,
    TResult Function(_DetailsLoaded value)? loaded,
    TResult Function(_DetailsError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _DetailsInitial implements TournamentDetailsState {
  const factory _DetailsInitial() = _$DetailsInitialImpl;
}

/// @nodoc
abstract class _$$DetailsLoadingImplCopyWith<$Res> {
  factory _$$DetailsLoadingImplCopyWith(
    _$DetailsLoadingImpl value,
    $Res Function(_$DetailsLoadingImpl) then,
  ) = __$$DetailsLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DetailsLoadingImplCopyWithImpl<$Res>
    extends _$TournamentDetailsStateCopyWithImpl<$Res, _$DetailsLoadingImpl>
    implements _$$DetailsLoadingImplCopyWith<$Res> {
  __$$DetailsLoadingImplCopyWithImpl(
    _$DetailsLoadingImpl _value,
    $Res Function(_$DetailsLoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentDetailsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DetailsLoadingImpl implements _DetailsLoading {
  const _$DetailsLoadingImpl();

  @override
  String toString() {
    return 'TournamentDetailsState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DetailsLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DetailsInitial value) initial,
    required TResult Function(_DetailsLoading value) loading,
    required TResult Function(_DetailsLoaded value) loaded,
    required TResult Function(_DetailsError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DetailsInitial value)? initial,
    TResult? Function(_DetailsLoading value)? loading,
    TResult? Function(_DetailsLoaded value)? loaded,
    TResult? Function(_DetailsError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DetailsInitial value)? initial,
    TResult Function(_DetailsLoading value)? loading,
    TResult Function(_DetailsLoaded value)? loaded,
    TResult Function(_DetailsError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _DetailsLoading implements TournamentDetailsState {
  const factory _DetailsLoading() = _$DetailsLoadingImpl;
}

/// @nodoc
abstract class _$$DetailsLoadedImplCopyWith<$Res> {
  factory _$$DetailsLoadedImplCopyWith(
    _$DetailsLoadedImpl value,
    $Res Function(_$DetailsLoadedImpl) then,
  ) = __$$DetailsLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Tournament tournament, bool isFavourite, bool isParticipant});
}

/// @nodoc
class __$$DetailsLoadedImplCopyWithImpl<$Res>
    extends _$TournamentDetailsStateCopyWithImpl<$Res, _$DetailsLoadedImpl>
    implements _$$DetailsLoadedImplCopyWith<$Res> {
  __$$DetailsLoadedImplCopyWithImpl(
    _$DetailsLoadedImpl _value,
    $Res Function(_$DetailsLoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tournament = null,
    Object? isFavourite = null,
    Object? isParticipant = null,
  }) {
    return _then(
      _$DetailsLoadedImpl(
        tournament:
            null == tournament
                ? _value.tournament
                : tournament // ignore: cast_nullable_to_non_nullable
                    as Tournament,
        isFavourite:
            null == isFavourite
                ? _value.isFavourite
                : isFavourite // ignore: cast_nullable_to_non_nullable
                    as bool,
        isParticipant:
            null == isParticipant
                ? _value.isParticipant
                : isParticipant // ignore: cast_nullable_to_non_nullable
                    as bool,
      ),
    );
  }
}

/// @nodoc

class _$DetailsLoadedImpl implements _DetailsLoaded {
  const _$DetailsLoadedImpl({
    required this.tournament,
    required this.isFavourite,
    required this.isParticipant,
  });

  @override
  final Tournament tournament;
  @override
  final bool isFavourite;
  @override
  final bool isParticipant;

  @override
  String toString() {
    return 'TournamentDetailsState.loaded(tournament: $tournament, isFavourite: $isFavourite, isParticipant: $isParticipant)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DetailsLoadedImpl &&
            (identical(other.tournament, tournament) ||
                other.tournament == tournament) &&
            (identical(other.isFavourite, isFavourite) ||
                other.isFavourite == isFavourite) &&
            (identical(other.isParticipant, isParticipant) ||
                other.isParticipant == isParticipant));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, tournament, isFavourite, isParticipant);

  /// Create a copy of TournamentDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DetailsLoadedImplCopyWith<_$DetailsLoadedImpl> get copyWith =>
      __$$DetailsLoadedImplCopyWithImpl<_$DetailsLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(tournament, isFavourite, isParticipant);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(tournament, isFavourite, isParticipant);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(tournament, isFavourite, isParticipant);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DetailsInitial value) initial,
    required TResult Function(_DetailsLoading value) loading,
    required TResult Function(_DetailsLoaded value) loaded,
    required TResult Function(_DetailsError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DetailsInitial value)? initial,
    TResult? Function(_DetailsLoading value)? loading,
    TResult? Function(_DetailsLoaded value)? loaded,
    TResult? Function(_DetailsError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DetailsInitial value)? initial,
    TResult Function(_DetailsLoading value)? loading,
    TResult Function(_DetailsLoaded value)? loaded,
    TResult Function(_DetailsError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _DetailsLoaded implements TournamentDetailsState {
  const factory _DetailsLoaded({
    required final Tournament tournament,
    required final bool isFavourite,
    required final bool isParticipant,
  }) = _$DetailsLoadedImpl;

  Tournament get tournament;
  bool get isFavourite;
  bool get isParticipant;

  /// Create a copy of TournamentDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DetailsLoadedImplCopyWith<_$DetailsLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DetailsErrorImplCopyWith<$Res> {
  factory _$$DetailsErrorImplCopyWith(
    _$DetailsErrorImpl value,
    $Res Function(_$DetailsErrorImpl) then,
  ) = __$$DetailsErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$DetailsErrorImplCopyWithImpl<$Res>
    extends _$TournamentDetailsStateCopyWithImpl<$Res, _$DetailsErrorImpl>
    implements _$$DetailsErrorImplCopyWith<$Res> {
  __$$DetailsErrorImplCopyWithImpl(
    _$DetailsErrorImpl _value,
    $Res Function(_$DetailsErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$DetailsErrorImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                as String,
      ),
    );
  }
}

/// @nodoc

class _$DetailsErrorImpl implements _DetailsError {
  const _$DetailsErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'TournamentDetailsState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DetailsErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of TournamentDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DetailsErrorImplCopyWith<_$DetailsErrorImpl> get copyWith =>
      __$$DetailsErrorImplCopyWithImpl<_$DetailsErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      Tournament tournament,
      bool isFavourite,
      bool isParticipant,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DetailsInitial value) initial,
    required TResult Function(_DetailsLoading value) loading,
    required TResult Function(_DetailsLoaded value) loaded,
    required TResult Function(_DetailsError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DetailsInitial value)? initial,
    TResult? Function(_DetailsLoading value)? loading,
    TResult? Function(_DetailsLoaded value)? loaded,
    TResult? Function(_DetailsError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DetailsInitial value)? initial,
    TResult Function(_DetailsLoading value)? loading,
    TResult Function(_DetailsLoaded value)? loaded,
    TResult Function(_DetailsError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _DetailsError implements TournamentDetailsState {
  const factory _DetailsError(final String message) = _$DetailsErrorImpl;

  String get message;

  /// Create a copy of TournamentDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DetailsErrorImplCopyWith<_$DetailsErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$JoinTournamentState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(Tournament tournament) success,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Tournament tournament)? success,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Tournament tournament)? success,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_JoinInitial value) initial,
    required TResult Function(_JoinLoading value) loading,
    required TResult Function(_JoinSuccess value) success,
    required TResult Function(_JoinError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_JoinInitial value)? initial,
    TResult? Function(_JoinLoading value)? loading,
    TResult? Function(_JoinSuccess value)? success,
    TResult? Function(_JoinError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_JoinInitial value)? initial,
    TResult Function(_JoinLoading value)? loading,
    TResult Function(_JoinSuccess value)? success,
    TResult Function(_JoinError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JoinTournamentStateCopyWith<$Res> {
  factory $JoinTournamentStateCopyWith(
    JoinTournamentState value,
    $Res Function(JoinTournamentState) then,
  ) = _$JoinTournamentStateCopyWithImpl<$Res, JoinTournamentState>;
}

/// @nodoc
class _$JoinTournamentStateCopyWithImpl<$Res, $Val extends JoinTournamentState>
    implements $JoinTournamentStateCopyWith<$Res> {
  _$JoinTournamentStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JoinTournamentState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$JoinInitialImplCopyWith<$Res> {
  factory _$$JoinInitialImplCopyWith(
    _$JoinInitialImpl value,
    $Res Function(_$JoinInitialImpl) then,
  ) = __$$JoinInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$JoinInitialImplCopyWithImpl<$Res>
    extends _$JoinTournamentStateCopyWithImpl<$Res, _$JoinInitialImpl>
    implements _$$JoinInitialImplCopyWith<$Res> {
  __$$JoinInitialImplCopyWithImpl(
    _$JoinInitialImpl _value,
    $Res Function(_$JoinInitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of JoinTournamentState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$JoinInitialImpl implements _JoinInitial {
  const _$JoinInitialImpl();

  @override
  String toString() {
    return 'JoinTournamentState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$JoinInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(Tournament tournament) success,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Tournament tournament)? success,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Tournament tournament)? success,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_JoinInitial value) initial,
    required TResult Function(_JoinLoading value) loading,
    required TResult Function(_JoinSuccess value) success,
    required TResult Function(_JoinError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_JoinInitial value)? initial,
    TResult? Function(_JoinLoading value)? loading,
    TResult? Function(_JoinSuccess value)? success,
    TResult? Function(_JoinError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_JoinInitial value)? initial,
    TResult Function(_JoinLoading value)? loading,
    TResult Function(_JoinSuccess value)? success,
    TResult Function(_JoinError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _JoinInitial implements JoinTournamentState {
  const factory _JoinInitial() = _$JoinInitialImpl;
}

/// @nodoc
abstract class _$$JoinLoadingImplCopyWith<$Res> {
  factory _$$JoinLoadingImplCopyWith(
    _$JoinLoadingImpl value,
    $Res Function(_$JoinLoadingImpl) then,
  ) = __$$JoinLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$JoinLoadingImplCopyWithImpl<$Res>
    extends _$JoinTournamentStateCopyWithImpl<$Res, _$JoinLoadingImpl>
    implements _$$JoinLoadingImplCopyWith<$Res> {
  __$$JoinLoadingImplCopyWithImpl(
    _$JoinLoadingImpl _value,
    $Res Function(_$JoinLoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of JoinTournamentState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$JoinLoadingImpl implements _JoinLoading {
  const _$JoinLoadingImpl();

  @override
  String toString() {
    return 'JoinTournamentState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$JoinLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(Tournament tournament) success,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Tournament tournament)? success,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Tournament tournament)? success,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_JoinInitial value) initial,
    required TResult Function(_JoinLoading value) loading,
    required TResult Function(_JoinSuccess value) success,
    required TResult Function(_JoinError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_JoinInitial value)? initial,
    TResult? Function(_JoinLoading value)? loading,
    TResult? Function(_JoinSuccess value)? success,
    TResult? Function(_JoinError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_JoinInitial value)? initial,
    TResult Function(_JoinLoading value)? loading,
    TResult Function(_JoinSuccess value)? success,
    TResult Function(_JoinError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _JoinLoading implements JoinTournamentState {
  const factory _JoinLoading() = _$JoinLoadingImpl;
}

/// @nodoc
abstract class _$$JoinSuccessImplCopyWith<$Res> {
  factory _$$JoinSuccessImplCopyWith(
    _$JoinSuccessImpl value,
    $Res Function(_$JoinSuccessImpl) then,
  ) = __$$JoinSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Tournament tournament});
}

/// @nodoc
class __$$JoinSuccessImplCopyWithImpl<$Res>
    extends _$JoinTournamentStateCopyWithImpl<$Res, _$JoinSuccessImpl>
    implements _$$JoinSuccessImplCopyWith<$Res> {
  __$$JoinSuccessImplCopyWithImpl(
    _$JoinSuccessImpl _value,
    $Res Function(_$JoinSuccessImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of JoinTournamentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? tournament = null}) {
    return _then(
      _$JoinSuccessImpl(
        null == tournament
            ? _value.tournament
            : tournament // ignore: cast_nullable_to_non_nullable
                as Tournament,
      ),
    );
  }
}

/// @nodoc

class _$JoinSuccessImpl implements _JoinSuccess {
  const _$JoinSuccessImpl(this.tournament);

  @override
  final Tournament tournament;

  @override
  String toString() {
    return 'JoinTournamentState.success(tournament: $tournament)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JoinSuccessImpl &&
            (identical(other.tournament, tournament) ||
                other.tournament == tournament));
  }

  @override
  int get hashCode => Object.hash(runtimeType, tournament);

  /// Create a copy of JoinTournamentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JoinSuccessImplCopyWith<_$JoinSuccessImpl> get copyWith =>
      __$$JoinSuccessImplCopyWithImpl<_$JoinSuccessImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(Tournament tournament) success,
    required TResult Function(String message) error,
  }) {
    return success(tournament);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Tournament tournament)? success,
    TResult? Function(String message)? error,
  }) {
    return success?.call(tournament);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Tournament tournament)? success,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(tournament);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_JoinInitial value) initial,
    required TResult Function(_JoinLoading value) loading,
    required TResult Function(_JoinSuccess value) success,
    required TResult Function(_JoinError value) error,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_JoinInitial value)? initial,
    TResult? Function(_JoinLoading value)? loading,
    TResult? Function(_JoinSuccess value)? success,
    TResult? Function(_JoinError value)? error,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_JoinInitial value)? initial,
    TResult Function(_JoinLoading value)? loading,
    TResult Function(_JoinSuccess value)? success,
    TResult Function(_JoinError value)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class _JoinSuccess implements JoinTournamentState {
  const factory _JoinSuccess(final Tournament tournament) = _$JoinSuccessImpl;

  Tournament get tournament;

  /// Create a copy of JoinTournamentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JoinSuccessImplCopyWith<_$JoinSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$JoinErrorImplCopyWith<$Res> {
  factory _$$JoinErrorImplCopyWith(
    _$JoinErrorImpl value,
    $Res Function(_$JoinErrorImpl) then,
  ) = __$$JoinErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$JoinErrorImplCopyWithImpl<$Res>
    extends _$JoinTournamentStateCopyWithImpl<$Res, _$JoinErrorImpl>
    implements _$$JoinErrorImplCopyWith<$Res> {
  __$$JoinErrorImplCopyWithImpl(
    _$JoinErrorImpl _value,
    $Res Function(_$JoinErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of JoinTournamentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$JoinErrorImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                as String,
      ),
    );
  }
}

/// @nodoc

class _$JoinErrorImpl implements _JoinError {
  const _$JoinErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'JoinTournamentState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JoinErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of JoinTournamentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JoinErrorImplCopyWith<_$JoinErrorImpl> get copyWith =>
      __$$JoinErrorImplCopyWithImpl<_$JoinErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(Tournament tournament) success,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Tournament tournament)? success,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Tournament tournament)? success,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_JoinInitial value) initial,
    required TResult Function(_JoinLoading value) loading,
    required TResult Function(_JoinSuccess value) success,
    required TResult Function(_JoinError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_JoinInitial value)? initial,
    TResult? Function(_JoinLoading value)? loading,
    TResult? Function(_JoinSuccess value)? success,
    TResult? Function(_JoinError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_JoinInitial value)? initial,
    TResult Function(_JoinLoading value)? loading,
    TResult Function(_JoinSuccess value)? success,
    TResult Function(_JoinError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _JoinError implements JoinTournamentState {
  const factory _JoinError(final String message) = _$JoinErrorImpl;

  String get message;

  /// Create a copy of JoinTournamentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JoinErrorImplCopyWith<_$JoinErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$FavouriteTournamentsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<FavouriteTournamentDetails> favourites)
    loaded,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<FavouriteTournamentDetails> favourites)? loaded,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<FavouriteTournamentDetails> favourites)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FavouriteInitial value) initial,
    required TResult Function(_FavouriteLoading value) loading,
    required TResult Function(_FavouriteLoaded value) loaded,
    required TResult Function(_FavouriteError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FavouriteInitial value)? initial,
    TResult? Function(_FavouriteLoading value)? loading,
    TResult? Function(_FavouriteLoaded value)? loaded,
    TResult? Function(_FavouriteError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FavouriteInitial value)? initial,
    TResult Function(_FavouriteLoading value)? loading,
    TResult Function(_FavouriteLoaded value)? loaded,
    TResult Function(_FavouriteError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FavouriteTournamentsStateCopyWith<$Res> {
  factory $FavouriteTournamentsStateCopyWith(
    FavouriteTournamentsState value,
    $Res Function(FavouriteTournamentsState) then,
  ) = _$FavouriteTournamentsStateCopyWithImpl<$Res, FavouriteTournamentsState>;
}

/// @nodoc
class _$FavouriteTournamentsStateCopyWithImpl<
  $Res,
  $Val extends FavouriteTournamentsState
>
    implements $FavouriteTournamentsStateCopyWith<$Res> {
  _$FavouriteTournamentsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FavouriteTournamentsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$FavouriteInitialImplCopyWith<$Res> {
  factory _$$FavouriteInitialImplCopyWith(
    _$FavouriteInitialImpl value,
    $Res Function(_$FavouriteInitialImpl) then,
  ) = __$$FavouriteInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FavouriteInitialImplCopyWithImpl<$Res>
    extends
        _$FavouriteTournamentsStateCopyWithImpl<$Res, _$FavouriteInitialImpl>
    implements _$$FavouriteInitialImplCopyWith<$Res> {
  __$$FavouriteInitialImplCopyWithImpl(
    _$FavouriteInitialImpl _value,
    $Res Function(_$FavouriteInitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FavouriteTournamentsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FavouriteInitialImpl implements _FavouriteInitial {
  const _$FavouriteInitialImpl();

  @override
  String toString() {
    return 'FavouriteTournamentsState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$FavouriteInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<FavouriteTournamentDetails> favourites)
    loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<FavouriteTournamentDetails> favourites)? loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<FavouriteTournamentDetails> favourites)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FavouriteInitial value) initial,
    required TResult Function(_FavouriteLoading value) loading,
    required TResult Function(_FavouriteLoaded value) loaded,
    required TResult Function(_FavouriteError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FavouriteInitial value)? initial,
    TResult? Function(_FavouriteLoading value)? loading,
    TResult? Function(_FavouriteLoaded value)? loaded,
    TResult? Function(_FavouriteError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FavouriteInitial value)? initial,
    TResult Function(_FavouriteLoading value)? loading,
    TResult Function(_FavouriteLoaded value)? loaded,
    TResult Function(_FavouriteError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _FavouriteInitial implements FavouriteTournamentsState {
  const factory _FavouriteInitial() = _$FavouriteInitialImpl;
}

/// @nodoc
abstract class _$$FavouriteLoadingImplCopyWith<$Res> {
  factory _$$FavouriteLoadingImplCopyWith(
    _$FavouriteLoadingImpl value,
    $Res Function(_$FavouriteLoadingImpl) then,
  ) = __$$FavouriteLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FavouriteLoadingImplCopyWithImpl<$Res>
    extends
        _$FavouriteTournamentsStateCopyWithImpl<$Res, _$FavouriteLoadingImpl>
    implements _$$FavouriteLoadingImplCopyWith<$Res> {
  __$$FavouriteLoadingImplCopyWithImpl(
    _$FavouriteLoadingImpl _value,
    $Res Function(_$FavouriteLoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FavouriteTournamentsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FavouriteLoadingImpl implements _FavouriteLoading {
  const _$FavouriteLoadingImpl();

  @override
  String toString() {
    return 'FavouriteTournamentsState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$FavouriteLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<FavouriteTournamentDetails> favourites)
    loaded,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<FavouriteTournamentDetails> favourites)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<FavouriteTournamentDetails> favourites)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FavouriteInitial value) initial,
    required TResult Function(_FavouriteLoading value) loading,
    required TResult Function(_FavouriteLoaded value) loaded,
    required TResult Function(_FavouriteError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FavouriteInitial value)? initial,
    TResult? Function(_FavouriteLoading value)? loading,
    TResult? Function(_FavouriteLoaded value)? loaded,
    TResult? Function(_FavouriteError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FavouriteInitial value)? initial,
    TResult Function(_FavouriteLoading value)? loading,
    TResult Function(_FavouriteLoaded value)? loaded,
    TResult Function(_FavouriteError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _FavouriteLoading implements FavouriteTournamentsState {
  const factory _FavouriteLoading() = _$FavouriteLoadingImpl;
}

/// @nodoc
abstract class _$$FavouriteLoadedImplCopyWith<$Res> {
  factory _$$FavouriteLoadedImplCopyWith(
    _$FavouriteLoadedImpl value,
    $Res Function(_$FavouriteLoadedImpl) then,
  ) = __$$FavouriteLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<FavouriteTournamentDetails> favourites});
}

/// @nodoc
class __$$FavouriteLoadedImplCopyWithImpl<$Res>
    extends _$FavouriteTournamentsStateCopyWithImpl<$Res, _$FavouriteLoadedImpl>
    implements _$$FavouriteLoadedImplCopyWith<$Res> {
  __$$FavouriteLoadedImplCopyWithImpl(
    _$FavouriteLoadedImpl _value,
    $Res Function(_$FavouriteLoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FavouriteTournamentsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? favourites = null}) {
    return _then(
      _$FavouriteLoadedImpl(
        null == favourites
            ? _value._favourites
            : favourites // ignore: cast_nullable_to_non_nullable
                as List<FavouriteTournamentDetails>,
      ),
    );
  }
}

/// @nodoc

class _$FavouriteLoadedImpl implements _FavouriteLoaded {
  const _$FavouriteLoadedImpl(final List<FavouriteTournamentDetails> favourites)
    : _favourites = favourites;

  final List<FavouriteTournamentDetails> _favourites;
  @override
  List<FavouriteTournamentDetails> get favourites {
    if (_favourites is EqualUnmodifiableListView) return _favourites;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_favourites);
  }

  @override
  String toString() {
    return 'FavouriteTournamentsState.loaded(favourites: $favourites)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FavouriteLoadedImpl &&
            const DeepCollectionEquality().equals(
              other._favourites,
              _favourites,
            ));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_favourites),
  );

  /// Create a copy of FavouriteTournamentsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FavouriteLoadedImplCopyWith<_$FavouriteLoadedImpl> get copyWith =>
      __$$FavouriteLoadedImplCopyWithImpl<_$FavouriteLoadedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<FavouriteTournamentDetails> favourites)
    loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(favourites);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<FavouriteTournamentDetails> favourites)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(favourites);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<FavouriteTournamentDetails> favourites)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(favourites);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FavouriteInitial value) initial,
    required TResult Function(_FavouriteLoading value) loading,
    required TResult Function(_FavouriteLoaded value) loaded,
    required TResult Function(_FavouriteError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FavouriteInitial value)? initial,
    TResult? Function(_FavouriteLoading value)? loading,
    TResult? Function(_FavouriteLoaded value)? loaded,
    TResult? Function(_FavouriteError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FavouriteInitial value)? initial,
    TResult Function(_FavouriteLoading value)? loading,
    TResult Function(_FavouriteLoaded value)? loaded,
    TResult Function(_FavouriteError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _FavouriteLoaded implements FavouriteTournamentsState {
  const factory _FavouriteLoaded(
    final List<FavouriteTournamentDetails> favourites,
  ) = _$FavouriteLoadedImpl;

  List<FavouriteTournamentDetails> get favourites;

  /// Create a copy of FavouriteTournamentsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FavouriteLoadedImplCopyWith<_$FavouriteLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FavouriteErrorImplCopyWith<$Res> {
  factory _$$FavouriteErrorImplCopyWith(
    _$FavouriteErrorImpl value,
    $Res Function(_$FavouriteErrorImpl) then,
  ) = __$$FavouriteErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$FavouriteErrorImplCopyWithImpl<$Res>
    extends _$FavouriteTournamentsStateCopyWithImpl<$Res, _$FavouriteErrorImpl>
    implements _$$FavouriteErrorImplCopyWith<$Res> {
  __$$FavouriteErrorImplCopyWithImpl(
    _$FavouriteErrorImpl _value,
    $Res Function(_$FavouriteErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FavouriteTournamentsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$FavouriteErrorImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                as String,
      ),
    );
  }
}

/// @nodoc

class _$FavouriteErrorImpl implements _FavouriteError {
  const _$FavouriteErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'FavouriteTournamentsState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FavouriteErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of FavouriteTournamentsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FavouriteErrorImplCopyWith<_$FavouriteErrorImpl> get copyWith =>
      __$$FavouriteErrorImplCopyWithImpl<_$FavouriteErrorImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<FavouriteTournamentDetails> favourites)
    loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<FavouriteTournamentDetails> favourites)? loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<FavouriteTournamentDetails> favourites)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FavouriteInitial value) initial,
    required TResult Function(_FavouriteLoading value) loading,
    required TResult Function(_FavouriteLoaded value) loaded,
    required TResult Function(_FavouriteError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FavouriteInitial value)? initial,
    TResult? Function(_FavouriteLoading value)? loading,
    TResult? Function(_FavouriteLoaded value)? loaded,
    TResult? Function(_FavouriteError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FavouriteInitial value)? initial,
    TResult Function(_FavouriteLoading value)? loading,
    TResult Function(_FavouriteLoaded value)? loaded,
    TResult Function(_FavouriteError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _FavouriteError implements FavouriteTournamentsState {
  const factory _FavouriteError(final String message) = _$FavouriteErrorImpl;

  String get message;

  /// Create a copy of FavouriteTournamentsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FavouriteErrorImplCopyWith<_$FavouriteErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$BadgeSystemState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )
    loaded,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_BadgeInitial value) initial,
    required TResult Function(_BadgeLoading value) loading,
    required TResult Function(_BadgeLoaded value) loaded,
    required TResult Function(_BadgeError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_BadgeInitial value)? initial,
    TResult? Function(_BadgeLoading value)? loading,
    TResult? Function(_BadgeLoaded value)? loaded,
    TResult? Function(_BadgeError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_BadgeInitial value)? initial,
    TResult Function(_BadgeLoading value)? loading,
    TResult Function(_BadgeLoaded value)? loaded,
    TResult Function(_BadgeError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BadgeSystemStateCopyWith<$Res> {
  factory $BadgeSystemStateCopyWith(
    BadgeSystemState value,
    $Res Function(BadgeSystemState) then,
  ) = _$BadgeSystemStateCopyWithImpl<$Res, BadgeSystemState>;
}

/// @nodoc
class _$BadgeSystemStateCopyWithImpl<$Res, $Val extends BadgeSystemState>
    implements $BadgeSystemStateCopyWith<$Res> {
  _$BadgeSystemStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BadgeSystemState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$BadgeInitialImplCopyWith<$Res> {
  factory _$$BadgeInitialImplCopyWith(
    _$BadgeInitialImpl value,
    $Res Function(_$BadgeInitialImpl) then,
  ) = __$$BadgeInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$BadgeInitialImplCopyWithImpl<$Res>
    extends _$BadgeSystemStateCopyWithImpl<$Res, _$BadgeInitialImpl>
    implements _$$BadgeInitialImplCopyWith<$Res> {
  __$$BadgeInitialImplCopyWithImpl(
    _$BadgeInitialImpl _value,
    $Res Function(_$BadgeInitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of BadgeSystemState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$BadgeInitialImpl implements _BadgeInitial {
  const _$BadgeInitialImpl();

  @override
  String toString() {
    return 'BadgeSystemState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$BadgeInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_BadgeInitial value) initial,
    required TResult Function(_BadgeLoading value) loading,
    required TResult Function(_BadgeLoaded value) loaded,
    required TResult Function(_BadgeError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_BadgeInitial value)? initial,
    TResult? Function(_BadgeLoading value)? loading,
    TResult? Function(_BadgeLoaded value)? loaded,
    TResult? Function(_BadgeError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_BadgeInitial value)? initial,
    TResult Function(_BadgeLoading value)? loading,
    TResult Function(_BadgeLoaded value)? loaded,
    TResult Function(_BadgeError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _BadgeInitial implements BadgeSystemState {
  const factory _BadgeInitial() = _$BadgeInitialImpl;
}

/// @nodoc
abstract class _$$BadgeLoadingImplCopyWith<$Res> {
  factory _$$BadgeLoadingImplCopyWith(
    _$BadgeLoadingImpl value,
    $Res Function(_$BadgeLoadingImpl) then,
  ) = __$$BadgeLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$BadgeLoadingImplCopyWithImpl<$Res>
    extends _$BadgeSystemStateCopyWithImpl<$Res, _$BadgeLoadingImpl>
    implements _$$BadgeLoadingImplCopyWith<$Res> {
  __$$BadgeLoadingImplCopyWithImpl(
    _$BadgeLoadingImpl _value,
    $Res Function(_$BadgeLoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of BadgeSystemState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$BadgeLoadingImpl implements _BadgeLoading {
  const _$BadgeLoadingImpl();

  @override
  String toString() {
    return 'BadgeSystemState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$BadgeLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_BadgeInitial value) initial,
    required TResult Function(_BadgeLoading value) loading,
    required TResult Function(_BadgeLoaded value) loaded,
    required TResult Function(_BadgeError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_BadgeInitial value)? initial,
    TResult? Function(_BadgeLoading value)? loading,
    TResult? Function(_BadgeLoaded value)? loaded,
    TResult? Function(_BadgeError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_BadgeInitial value)? initial,
    TResult Function(_BadgeLoading value)? loading,
    TResult Function(_BadgeLoaded value)? loaded,
    TResult Function(_BadgeError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _BadgeLoading implements BadgeSystemState {
  const factory _BadgeLoading() = _$BadgeLoadingImpl;
}

/// @nodoc
abstract class _$$BadgeLoadedImplCopyWith<$Res> {
  factory _$$BadgeLoadedImplCopyWith(
    _$BadgeLoadedImpl value,
    $Res Function(_$BadgeLoadedImpl) then,
  ) = __$$BadgeLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({
    PlayerBadgeCollection playerBadges,
    TournamentStats stats,
    List<PlayerBadge> availableBadges,
    List<PlayerBadge> newlyUnlockedBadges,
  });

  $PlayerBadgeCollectionCopyWith<$Res> get playerBadges;
  $TournamentStatsCopyWith<$Res> get stats;
}

/// @nodoc
class __$$BadgeLoadedImplCopyWithImpl<$Res>
    extends _$BadgeSystemStateCopyWithImpl<$Res, _$BadgeLoadedImpl>
    implements _$$BadgeLoadedImplCopyWith<$Res> {
  __$$BadgeLoadedImplCopyWithImpl(
    _$BadgeLoadedImpl _value,
    $Res Function(_$BadgeLoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of BadgeSystemState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? playerBadges = null,
    Object? stats = null,
    Object? availableBadges = null,
    Object? newlyUnlockedBadges = null,
  }) {
    return _then(
      _$BadgeLoadedImpl(
        playerBadges:
            null == playerBadges
                ? _value.playerBadges
                : playerBadges // ignore: cast_nullable_to_non_nullable
                    as PlayerBadgeCollection,
        stats:
            null == stats
                ? _value.stats
                : stats // ignore: cast_nullable_to_non_nullable
                    as TournamentStats,
        availableBadges:
            null == availableBadges
                ? _value._availableBadges
                : availableBadges // ignore: cast_nullable_to_non_nullable
                    as List<PlayerBadge>,
        newlyUnlockedBadges:
            null == newlyUnlockedBadges
                ? _value._newlyUnlockedBadges
                : newlyUnlockedBadges // ignore: cast_nullable_to_non_nullable
                    as List<PlayerBadge>,
      ),
    );
  }

  /// Create a copy of BadgeSystemState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PlayerBadgeCollectionCopyWith<$Res> get playerBadges {
    return $PlayerBadgeCollectionCopyWith<$Res>(_value.playerBadges, (value) {
      return _then(_value.copyWith(playerBadges: value));
    });
  }

  /// Create a copy of BadgeSystemState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TournamentStatsCopyWith<$Res> get stats {
    return $TournamentStatsCopyWith<$Res>(_value.stats, (value) {
      return _then(_value.copyWith(stats: value));
    });
  }
}

/// @nodoc

class _$BadgeLoadedImpl implements _BadgeLoaded {
  const _$BadgeLoadedImpl({
    required this.playerBadges,
    required this.stats,
    required final List<PlayerBadge> availableBadges,
    final List<PlayerBadge> newlyUnlockedBadges = const [],
  }) : _availableBadges = availableBadges,
       _newlyUnlockedBadges = newlyUnlockedBadges;

  @override
  final PlayerBadgeCollection playerBadges;
  @override
  final TournamentStats stats;
  final List<PlayerBadge> _availableBadges;
  @override
  List<PlayerBadge> get availableBadges {
    if (_availableBadges is EqualUnmodifiableListView) return _availableBadges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableBadges);
  }

  final List<PlayerBadge> _newlyUnlockedBadges;
  @override
  @JsonKey()
  List<PlayerBadge> get newlyUnlockedBadges {
    if (_newlyUnlockedBadges is EqualUnmodifiableListView)
      return _newlyUnlockedBadges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_newlyUnlockedBadges);
  }

  @override
  String toString() {
    return 'BadgeSystemState.loaded(playerBadges: $playerBadges, stats: $stats, availableBadges: $availableBadges, newlyUnlockedBadges: $newlyUnlockedBadges)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BadgeLoadedImpl &&
            (identical(other.playerBadges, playerBadges) ||
                other.playerBadges == playerBadges) &&
            (identical(other.stats, stats) || other.stats == stats) &&
            const DeepCollectionEquality().equals(
              other._availableBadges,
              _availableBadges,
            ) &&
            const DeepCollectionEquality().equals(
              other._newlyUnlockedBadges,
              _newlyUnlockedBadges,
            ));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    playerBadges,
    stats,
    const DeepCollectionEquality().hash(_availableBadges),
    const DeepCollectionEquality().hash(_newlyUnlockedBadges),
  );

  /// Create a copy of BadgeSystemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BadgeLoadedImplCopyWith<_$BadgeLoadedImpl> get copyWith =>
      __$$BadgeLoadedImplCopyWithImpl<_$BadgeLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(playerBadges, stats, availableBadges, newlyUnlockedBadges);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(
      playerBadges,
      stats,
      availableBadges,
      newlyUnlockedBadges,
    );
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(playerBadges, stats, availableBadges, newlyUnlockedBadges);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_BadgeInitial value) initial,
    required TResult Function(_BadgeLoading value) loading,
    required TResult Function(_BadgeLoaded value) loaded,
    required TResult Function(_BadgeError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_BadgeInitial value)? initial,
    TResult? Function(_BadgeLoading value)? loading,
    TResult? Function(_BadgeLoaded value)? loaded,
    TResult? Function(_BadgeError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_BadgeInitial value)? initial,
    TResult Function(_BadgeLoading value)? loading,
    TResult Function(_BadgeLoaded value)? loaded,
    TResult Function(_BadgeError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _BadgeLoaded implements BadgeSystemState {
  const factory _BadgeLoaded({
    required final PlayerBadgeCollection playerBadges,
    required final TournamentStats stats,
    required final List<PlayerBadge> availableBadges,
    final List<PlayerBadge> newlyUnlockedBadges,
  }) = _$BadgeLoadedImpl;

  PlayerBadgeCollection get playerBadges;
  TournamentStats get stats;
  List<PlayerBadge> get availableBadges;
  List<PlayerBadge> get newlyUnlockedBadges;

  /// Create a copy of BadgeSystemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BadgeLoadedImplCopyWith<_$BadgeLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BadgeErrorImplCopyWith<$Res> {
  factory _$$BadgeErrorImplCopyWith(
    _$BadgeErrorImpl value,
    $Res Function(_$BadgeErrorImpl) then,
  ) = __$$BadgeErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$BadgeErrorImplCopyWithImpl<$Res>
    extends _$BadgeSystemStateCopyWithImpl<$Res, _$BadgeErrorImpl>
    implements _$$BadgeErrorImplCopyWith<$Res> {
  __$$BadgeErrorImplCopyWithImpl(
    _$BadgeErrorImpl _value,
    $Res Function(_$BadgeErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of BadgeSystemState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$BadgeErrorImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                as String,
      ),
    );
  }
}

/// @nodoc

class _$BadgeErrorImpl implements _BadgeError {
  const _$BadgeErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'BadgeSystemState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BadgeErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of BadgeSystemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BadgeErrorImplCopyWith<_$BadgeErrorImpl> get copyWith =>
      __$$BadgeErrorImplCopyWithImpl<_$BadgeErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      PlayerBadgeCollection playerBadges,
      TournamentStats stats,
      List<PlayerBadge> availableBadges,
      List<PlayerBadge> newlyUnlockedBadges,
    )?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_BadgeInitial value) initial,
    required TResult Function(_BadgeLoading value) loading,
    required TResult Function(_BadgeLoaded value) loaded,
    required TResult Function(_BadgeError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_BadgeInitial value)? initial,
    TResult? Function(_BadgeLoading value)? loading,
    TResult? Function(_BadgeLoaded value)? loaded,
    TResult? Function(_BadgeError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_BadgeInitial value)? initial,
    TResult Function(_BadgeLoading value)? loading,
    TResult Function(_BadgeLoaded value)? loaded,
    TResult Function(_BadgeError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _BadgeError implements BadgeSystemState {
  const factory _BadgeError(final String message) = _$BadgeErrorImpl;

  String get message;

  /// Create a copy of BadgeSystemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BadgeErrorImplCopyWith<_$BadgeErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$TournamentFilterState {
  TournamentFilter get filter => throw _privateConstructorUsedError;
  bool get isApplied => throw _privateConstructorUsedError;

  /// Create a copy of TournamentFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TournamentFilterStateCopyWith<TournamentFilterState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TournamentFilterStateCopyWith<$Res> {
  factory $TournamentFilterStateCopyWith(
    TournamentFilterState value,
    $Res Function(TournamentFilterState) then,
  ) = _$TournamentFilterStateCopyWithImpl<$Res, TournamentFilterState>;
  @useResult
  $Res call({TournamentFilter filter, bool isApplied});
}

/// @nodoc
class _$TournamentFilterStateCopyWithImpl<
  $Res,
  $Val extends TournamentFilterState
>
    implements $TournamentFilterStateCopyWith<$Res> {
  _$TournamentFilterStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TournamentFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? filter = null, Object? isApplied = null}) {
    return _then(
      _value.copyWith(
            filter:
                null == filter
                    ? _value.filter
                    : filter // ignore: cast_nullable_to_non_nullable
                        as TournamentFilter,
            isApplied:
                null == isApplied
                    ? _value.isApplied
                    : isApplied // ignore: cast_nullable_to_non_nullable
                        as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$TournamentFilterStateImplCopyWith<$Res>
    implements $TournamentFilterStateCopyWith<$Res> {
  factory _$$TournamentFilterStateImplCopyWith(
    _$TournamentFilterStateImpl value,
    $Res Function(_$TournamentFilterStateImpl) then,
  ) = __$$TournamentFilterStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({TournamentFilter filter, bool isApplied});
}

/// @nodoc
class __$$TournamentFilterStateImplCopyWithImpl<$Res>
    extends
        _$TournamentFilterStateCopyWithImpl<$Res, _$TournamentFilterStateImpl>
    implements _$$TournamentFilterStateImplCopyWith<$Res> {
  __$$TournamentFilterStateImplCopyWithImpl(
    _$TournamentFilterStateImpl _value,
    $Res Function(_$TournamentFilterStateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? filter = null, Object? isApplied = null}) {
    return _then(
      _$TournamentFilterStateImpl(
        filter:
            null == filter
                ? _value.filter
                : filter // ignore: cast_nullable_to_non_nullable
                    as TournamentFilter,
        isApplied:
            null == isApplied
                ? _value.isApplied
                : isApplied // ignore: cast_nullable_to_non_nullable
                    as bool,
      ),
    );
  }
}

/// @nodoc

class _$TournamentFilterStateImpl implements _TournamentFilterState {
  const _$TournamentFilterStateImpl({
    required this.filter,
    this.isApplied = false,
  });

  @override
  final TournamentFilter filter;
  @override
  @JsonKey()
  final bool isApplied;

  @override
  String toString() {
    return 'TournamentFilterState(filter: $filter, isApplied: $isApplied)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TournamentFilterStateImpl &&
            (identical(other.filter, filter) || other.filter == filter) &&
            (identical(other.isApplied, isApplied) ||
                other.isApplied == isApplied));
  }

  @override
  int get hashCode => Object.hash(runtimeType, filter, isApplied);

  /// Create a copy of TournamentFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TournamentFilterStateImplCopyWith<_$TournamentFilterStateImpl>
  get copyWith =>
      __$$TournamentFilterStateImplCopyWithImpl<_$TournamentFilterStateImpl>(
        this,
        _$identity,
      );
}

abstract class _TournamentFilterState implements TournamentFilterState {
  const factory _TournamentFilterState({
    required final TournamentFilter filter,
    final bool isApplied,
  }) = _$TournamentFilterStateImpl;

  @override
  TournamentFilter get filter;
  @override
  bool get isApplied;

  /// Create a copy of TournamentFilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TournamentFilterStateImplCopyWith<_$TournamentFilterStateImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$TournamentSearchState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() searching,
    required TResult Function(
      List<Tournament> results,
      String searchTerm,
      bool hasMore,
    )
    loaded,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? searching,
    TResult? Function(
      List<Tournament> results,
      String searchTerm,
      bool hasMore,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? searching,
    TResult Function(List<Tournament> results, String searchTerm, bool hasMore)?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SearchInitial value) initial,
    required TResult Function(_Searching value) searching,
    required TResult Function(_SearchLoaded value) loaded,
    required TResult Function(_SearchError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SearchInitial value)? initial,
    TResult? Function(_Searching value)? searching,
    TResult? Function(_SearchLoaded value)? loaded,
    TResult? Function(_SearchError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SearchInitial value)? initial,
    TResult Function(_Searching value)? searching,
    TResult Function(_SearchLoaded value)? loaded,
    TResult Function(_SearchError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TournamentSearchStateCopyWith<$Res> {
  factory $TournamentSearchStateCopyWith(
    TournamentSearchState value,
    $Res Function(TournamentSearchState) then,
  ) = _$TournamentSearchStateCopyWithImpl<$Res, TournamentSearchState>;
}

/// @nodoc
class _$TournamentSearchStateCopyWithImpl<
  $Res,
  $Val extends TournamentSearchState
>
    implements $TournamentSearchStateCopyWith<$Res> {
  _$TournamentSearchStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TournamentSearchState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$SearchInitialImplCopyWith<$Res> {
  factory _$$SearchInitialImplCopyWith(
    _$SearchInitialImpl value,
    $Res Function(_$SearchInitialImpl) then,
  ) = __$$SearchInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SearchInitialImplCopyWithImpl<$Res>
    extends _$TournamentSearchStateCopyWithImpl<$Res, _$SearchInitialImpl>
    implements _$$SearchInitialImplCopyWith<$Res> {
  __$$SearchInitialImplCopyWithImpl(
    _$SearchInitialImpl _value,
    $Res Function(_$SearchInitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentSearchState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SearchInitialImpl implements _SearchInitial {
  const _$SearchInitialImpl();

  @override
  String toString() {
    return 'TournamentSearchState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SearchInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() searching,
    required TResult Function(
      List<Tournament> results,
      String searchTerm,
      bool hasMore,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? searching,
    TResult? Function(
      List<Tournament> results,
      String searchTerm,
      bool hasMore,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? searching,
    TResult Function(List<Tournament> results, String searchTerm, bool hasMore)?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SearchInitial value) initial,
    required TResult Function(_Searching value) searching,
    required TResult Function(_SearchLoaded value) loaded,
    required TResult Function(_SearchError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SearchInitial value)? initial,
    TResult? Function(_Searching value)? searching,
    TResult? Function(_SearchLoaded value)? loaded,
    TResult? Function(_SearchError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SearchInitial value)? initial,
    TResult Function(_Searching value)? searching,
    TResult Function(_SearchLoaded value)? loaded,
    TResult Function(_SearchError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _SearchInitial implements TournamentSearchState {
  const factory _SearchInitial() = _$SearchInitialImpl;
}

/// @nodoc
abstract class _$$SearchingImplCopyWith<$Res> {
  factory _$$SearchingImplCopyWith(
    _$SearchingImpl value,
    $Res Function(_$SearchingImpl) then,
  ) = __$$SearchingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SearchingImplCopyWithImpl<$Res>
    extends _$TournamentSearchStateCopyWithImpl<$Res, _$SearchingImpl>
    implements _$$SearchingImplCopyWith<$Res> {
  __$$SearchingImplCopyWithImpl(
    _$SearchingImpl _value,
    $Res Function(_$SearchingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentSearchState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SearchingImpl implements _Searching {
  const _$SearchingImpl();

  @override
  String toString() {
    return 'TournamentSearchState.searching()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SearchingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() searching,
    required TResult Function(
      List<Tournament> results,
      String searchTerm,
      bool hasMore,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return searching();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? searching,
    TResult? Function(
      List<Tournament> results,
      String searchTerm,
      bool hasMore,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return searching?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? searching,
    TResult Function(List<Tournament> results, String searchTerm, bool hasMore)?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (searching != null) {
      return searching();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SearchInitial value) initial,
    required TResult Function(_Searching value) searching,
    required TResult Function(_SearchLoaded value) loaded,
    required TResult Function(_SearchError value) error,
  }) {
    return searching(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SearchInitial value)? initial,
    TResult? Function(_Searching value)? searching,
    TResult? Function(_SearchLoaded value)? loaded,
    TResult? Function(_SearchError value)? error,
  }) {
    return searching?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SearchInitial value)? initial,
    TResult Function(_Searching value)? searching,
    TResult Function(_SearchLoaded value)? loaded,
    TResult Function(_SearchError value)? error,
    required TResult orElse(),
  }) {
    if (searching != null) {
      return searching(this);
    }
    return orElse();
  }
}

abstract class _Searching implements TournamentSearchState {
  const factory _Searching() = _$SearchingImpl;
}

/// @nodoc
abstract class _$$SearchLoadedImplCopyWith<$Res> {
  factory _$$SearchLoadedImplCopyWith(
    _$SearchLoadedImpl value,
    $Res Function(_$SearchLoadedImpl) then,
  ) = __$$SearchLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Tournament> results, String searchTerm, bool hasMore});
}

/// @nodoc
class __$$SearchLoadedImplCopyWithImpl<$Res>
    extends _$TournamentSearchStateCopyWithImpl<$Res, _$SearchLoadedImpl>
    implements _$$SearchLoadedImplCopyWith<$Res> {
  __$$SearchLoadedImplCopyWithImpl(
    _$SearchLoadedImpl _value,
    $Res Function(_$SearchLoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentSearchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? results = null,
    Object? searchTerm = null,
    Object? hasMore = null,
  }) {
    return _then(
      _$SearchLoadedImpl(
        results:
            null == results
                ? _value._results
                : results // ignore: cast_nullable_to_non_nullable
                    as List<Tournament>,
        searchTerm:
            null == searchTerm
                ? _value.searchTerm
                : searchTerm // ignore: cast_nullable_to_non_nullable
                    as String,
        hasMore:
            null == hasMore
                ? _value.hasMore
                : hasMore // ignore: cast_nullable_to_non_nullable
                    as bool,
      ),
    );
  }
}

/// @nodoc

class _$SearchLoadedImpl implements _SearchLoaded {
  const _$SearchLoadedImpl({
    required final List<Tournament> results,
    required this.searchTerm,
    required this.hasMore,
  }) : _results = results;

  final List<Tournament> _results;
  @override
  List<Tournament> get results {
    if (_results is EqualUnmodifiableListView) return _results;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_results);
  }

  @override
  final String searchTerm;
  @override
  final bool hasMore;

  @override
  String toString() {
    return 'TournamentSearchState.loaded(results: $results, searchTerm: $searchTerm, hasMore: $hasMore)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchLoadedImpl &&
            const DeepCollectionEquality().equals(other._results, _results) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_results),
    searchTerm,
    hasMore,
  );

  /// Create a copy of TournamentSearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchLoadedImplCopyWith<_$SearchLoadedImpl> get copyWith =>
      __$$SearchLoadedImplCopyWithImpl<_$SearchLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() searching,
    required TResult Function(
      List<Tournament> results,
      String searchTerm,
      bool hasMore,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(results, searchTerm, hasMore);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? searching,
    TResult? Function(
      List<Tournament> results,
      String searchTerm,
      bool hasMore,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(results, searchTerm, hasMore);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? searching,
    TResult Function(List<Tournament> results, String searchTerm, bool hasMore)?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(results, searchTerm, hasMore);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SearchInitial value) initial,
    required TResult Function(_Searching value) searching,
    required TResult Function(_SearchLoaded value) loaded,
    required TResult Function(_SearchError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SearchInitial value)? initial,
    TResult? Function(_Searching value)? searching,
    TResult? Function(_SearchLoaded value)? loaded,
    TResult? Function(_SearchError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SearchInitial value)? initial,
    TResult Function(_Searching value)? searching,
    TResult Function(_SearchLoaded value)? loaded,
    TResult Function(_SearchError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _SearchLoaded implements TournamentSearchState {
  const factory _SearchLoaded({
    required final List<Tournament> results,
    required final String searchTerm,
    required final bool hasMore,
  }) = _$SearchLoadedImpl;

  List<Tournament> get results;
  String get searchTerm;
  bool get hasMore;

  /// Create a copy of TournamentSearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchLoadedImplCopyWith<_$SearchLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchErrorImplCopyWith<$Res> {
  factory _$$SearchErrorImplCopyWith(
    _$SearchErrorImpl value,
    $Res Function(_$SearchErrorImpl) then,
  ) = __$$SearchErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$SearchErrorImplCopyWithImpl<$Res>
    extends _$TournamentSearchStateCopyWithImpl<$Res, _$SearchErrorImpl>
    implements _$$SearchErrorImplCopyWith<$Res> {
  __$$SearchErrorImplCopyWithImpl(
    _$SearchErrorImpl _value,
    $Res Function(_$SearchErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TournamentSearchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$SearchErrorImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                as String,
      ),
    );
  }
}

/// @nodoc

class _$SearchErrorImpl implements _SearchError {
  const _$SearchErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'TournamentSearchState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of TournamentSearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchErrorImplCopyWith<_$SearchErrorImpl> get copyWith =>
      __$$SearchErrorImplCopyWithImpl<_$SearchErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() searching,
    required TResult Function(
      List<Tournament> results,
      String searchTerm,
      bool hasMore,
    )
    loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? searching,
    TResult? Function(
      List<Tournament> results,
      String searchTerm,
      bool hasMore,
    )?
    loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? searching,
    TResult Function(List<Tournament> results, String searchTerm, bool hasMore)?
    loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SearchInitial value) initial,
    required TResult Function(_Searching value) searching,
    required TResult Function(_SearchLoaded value) loaded,
    required TResult Function(_SearchError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SearchInitial value)? initial,
    TResult? Function(_Searching value)? searching,
    TResult? Function(_SearchLoaded value)? loaded,
    TResult? Function(_SearchError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SearchInitial value)? initial,
    TResult Function(_Searching value)? searching,
    TResult Function(_SearchLoaded value)? loaded,
    TResult Function(_SearchError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _SearchError implements TournamentSearchState {
  const factory _SearchError(final String message) = _$SearchErrorImpl;

  String get message;

  /// Create a copy of TournamentSearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchErrorImplCopyWith<_$SearchErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
