import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../../tournaments_providers.dart';
import '../logic/tournament_state.dart';
import '../widgets/tournament_card.dart';
import 'tournament_filter_screen.dart';
import 'tournament_search_screen.dart';

class TournamentListScreen extends ConsumerStatefulWidget {
  const TournamentListScreen({super.key});

  @override
  ConsumerState<TournamentListScreen> createState() =>
      _TournamentListScreenState();
}

class _TournamentListScreenState extends ConsumerState<TournamentListScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController.addListener(_onScroll);

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  void _loadInitialData() {
    final notifier = ref.read(tournamentListStateProvider.notifier);
    switch (_tabController.index) {
      case 0:
        notifier.loadTournaments();
        break;
      case 1:
        notifier.loadFeaturedTournaments();
        break;
      case 2:
        // Load nearby tournaments (requires location)
        _loadNearbyTournaments();
        break;
    }
  }

  void _loadNearbyTournaments() {
    // For demo purposes, using Kathmandu coordinates
    final notifier = ref.read(tournamentListStateProvider.notifier);
    notifier.loadNearbyTournaments(
      latitude: 27.7172,
      longitude: 85.3240,
      radiusKm: 50.0,
    );
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final notifier = ref.read(tournamentListStateProvider.notifier);
      notifier.loadMoreTournaments();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      body: NestedScrollView(
        headerSliverBuilder:
            (context, innerBoxIsScrolled) => [
              _buildSliverAppBar(context, theme),
              _buildTabBar(context, theme),
            ],
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildAllTournamentsTab(),
            _buildFeaturedTournamentsTab(),
            _buildNearbyTournamentsTab(),
          ],
        ),
      ),
      floatingActionButton: _buildFilterFAB(context),
    );
  }

  Widget _buildSliverAppBar(BuildContext context, ThemeData theme) {
    return SliverAppBar(
      expandedHeight: 120.h,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: theme.colorScheme.onPrimary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Tournaments',
          style: TextStyle(
            color: theme.colorScheme.onPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        titlePadding: EdgeInsets.only(left: 16.w, bottom: 16.h),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () => _openSearch(context),
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _refreshCurrentTab,
        ),
        SizedBox(width: 8.w),
      ],
    );
  }

  Widget _buildTabBar(BuildContext context, ThemeData theme) {
    return SliverPersistentHeader(
      pinned: true,
      delegate: _TabBarDelegate(
        TabBar(
          controller: _tabController,
          indicatorColor: theme.colorScheme.primary,
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.6),
          indicatorWeight: 3,
          onTap: (index) {
            if (_tabController.previousIndex != index) {
              _loadInitialData();
            }
          },
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Featured'),
            Tab(text: 'Nearby'),
          ],
        ),
      ),
    );
  }

  Widget _buildAllTournamentsTab() {
    return _buildTournamentList();
  }

  Widget _buildFeaturedTournamentsTab() {
    return _buildTournamentList();
  }

  Widget _buildNearbyTournamentsTab() {
    return _buildTournamentList();
  }

  Widget _buildTournamentList() {
    return Consumer(
      builder: (context, ref, child) {
        final state = ref.watch(tournamentListStateProvider);

        return RefreshIndicator(
          onRefresh: () async {
            await _refreshCurrentTab();
          },
          child: state.when(
            initial: () => _buildEmptyState(),
            loading: () => _buildLoadingState(),
            loadingMore: () => _buildLoadingMoreState(ref),
            loaded:
                (tournaments, hasMore, currentPage, query) =>
                    _buildLoadedState(tournaments, hasMore),
            error: (message) => _buildErrorState(message),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.sports_soccer, size: 64.sp, color: Colors.grey[400]),
          SizedBox(height: 16.h),
          Text(
            'No tournaments available',
            style: TextStyle(fontSize: 18.sp, color: Colors.grey[600]),
          ),
          SizedBox(height: 8.h),
          Text(
            'Check back later for new tournaments',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      itemCount: 6,
      itemBuilder: (context, index) => const TournamentCardShimmer(),
    );
  }

  Widget _buildLoadingMoreState(WidgetRef ref) {
    final state = ref.watch(tournamentListStateProvider);
    final tournaments = state.maybeWhen(
      loaded: (tournaments, _, __, ___) => tournaments,
      orElse: () => [],
    );

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(vertical: 16.h),
      itemCount: tournaments.length + 1,
      itemBuilder: (context, index) {
        if (index < tournaments.length) {
          return _buildTournamentCard(tournaments[index]);
        } else {
          return _buildLoadingIndicator();
        }
      },
    );
  }

  Widget _buildLoadedState(List<dynamic> tournaments, bool hasMore) {
    if (tournaments.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(vertical: 16.h),
      itemCount: tournaments.length + (hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < tournaments.length) {
          return _buildTournamentCard(tournaments[index]);
        } else {
          return _buildLoadingIndicator();
        }
      },
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red[400]),
          SizedBox(height: 16.h),
          Text(
            'Error loading tournaments',
            style: TextStyle(fontSize: 18.sp, color: Colors.grey[600]),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _refreshCurrentTab,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildTournamentCard(dynamic tournament) {
    return TournamentCard(
      tournament: tournament,
      onTap: () => _openTournamentDetails(tournament.id),
      onFavoriteTap: () => _toggleFavorite(tournament.id),
      isFavorite: false, // TODO: Check if tournament is favorite
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: EdgeInsets.all(16.w),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(),
    );
  }

  Widget _buildFilterFAB(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final filterState = ref.watch(tournamentFilterStateProvider);
        final hasActiveFilters = filterState.filter.hasActiveFilters;

        return FloatingActionButton.extended(
          onPressed: () => _openFilters(context),
          icon: Icon(
            Icons.filter_list,
            color: hasActiveFilters ? Colors.white : null,
          ),
          label: Text(
            hasActiveFilters
                ? 'Filters (${filterState.filter.activeFilterCount})'
                : 'Filters',
            style: TextStyle(color: hasActiveFilters ? Colors.white : null),
          ),
          backgroundColor:
              hasActiveFilters ? Theme.of(context).colorScheme.secondary : null,
        );
      },
    );
  }

  void _openSearch(BuildContext context) {
    // Keep as modal navigation for search since it's a temporary overlay
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const TournamentSearchScreen()),
    );
  }

  void _openFilters(BuildContext context) {
    // Keep as modal navigation for filters since it's a temporary overlay
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => const TournamentFilterScreen(),
          ),
        )
        .then((_) {
          // Refresh tournaments if filters were applied
          final filterState = ref.read(tournamentFilterStateProvider);
          if (filterState.isApplied) {
            final notifier = ref.read(tournamentListStateProvider.notifier);
            notifier.applyFilter(filterState.filter);
            ref.read(tournamentFilterStateProvider.notifier).resetApplied();
          }
        });
  }

  void _openTournamentDetails(String tournamentId) {
    context.push('/tournaments/$tournamentId');
  }

  void _toggleFavorite(String tournamentId) {
    // TODO: Implement favorite toggle
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Added to favorites!')));
  }

  Future<void> _refreshCurrentTab() async {
    final notifier = ref.read(tournamentListStateProvider.notifier);
    switch (_tabController.index) {
      case 0:
        await notifier.refreshTournaments();
        break;
      case 1:
        await notifier.loadFeaturedTournaments();
        break;
      case 2:
        _loadNearbyTournaments();
        break;
    }
  }
}

class _TabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _TabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(_TabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar;
  }
}
