import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../tournaments_providers.dart';
import '../logic/tournament_state.dart';
import '../widgets/tournament_participants_list.dart';
import '../widgets/tournament_rules_section.dart';
import '../widgets/tournament_organizer_info.dart';

class TournamentDetailsScreen extends ConsumerStatefulWidget {
  final String tournamentId;

  const TournamentDetailsScreen({super.key, required this.tournamentId});

  @override
  ConsumerState<TournamentDetailsScreen> createState() =>
      _TournamentDetailsScreenState();
}

class _TournamentDetailsScreenState
    extends ConsumerState<TournamentDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  GoogleMapController? _mapController;
  late AnimationController _favoriteAnimationController;
  late Animation<double> _favoriteAnimation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _favoriteAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _favoriteAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(
        parent: _favoriteAnimationController,
        curve: Curves.elasticOut,
      ),
    );

    // Load tournament details
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(tournamentDetailsStateProvider(widget.tournamentId).notifier)
          .loadTournamentDetails();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _favoriteAnimationController.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(
      tournamentDetailsStateProvider(widget.tournamentId),
    );

    return Scaffold(
      body: state.when(
        initial: () => _buildLoadingState(),
        loading: () => _buildLoadingState(),
        loaded:
            (tournament, isFavorite, isParticipant) =>
                _buildLoadedState(tournament, isFavorite, isParticipant),
        error: (message) => _buildErrorState(message),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Scaffold(body: Center(child: CircularProgressIndicator()));
  }

  Widget _buildErrorState(String message) {
    return Scaffold(
      appBar: AppBar(title: const Text('Tournament Details')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64.sp, color: Colors.red[400]),
            SizedBox(height: 16.h),
            Text(
              'Error loading tournament',
              style: TextStyle(fontSize: 18.sp, color: Colors.grey[600]),
            ),
            SizedBox(height: 8.h),
            Text(
              message,
              style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () {
                ref
                    .read(
                      tournamentDetailsStateProvider(
                        widget.tournamentId,
                      ).notifier,
                    )
                    .loadTournamentDetails();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadedState(
    dynamic tournament,
    bool isFavorite,
    bool isParticipant,
  ) {
    final theme = Theme.of(context);

    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder:
            (context, innerBoxIsScrolled) => [
              _buildSliverAppBar(tournament, isFavorite, theme),
              _buildTabBar(theme),
            ],
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildOverviewTab(tournament),
            _buildParticipantsTab(tournament),
            _buildLocationTab(tournament),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomBar(tournament, isParticipant, theme),
    );
  }

  Widget _buildSliverAppBar(
    dynamic tournament,
    bool isFavorite,
    ThemeData theme,
  ) {
    return SliverAppBar(
      expandedHeight: 300.h,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: theme.colorScheme.surface,
      foregroundColor: theme.colorScheme.onSurface,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            // Banner image
            if (tournament.bannerImage != null)
              CachedNetworkImage(
                imageUrl: tournament.bannerImage!,
                fit: BoxFit.cover,
                placeholder: (context, url) => _buildImagePlaceholder(),
                errorWidget: (context, url, error) => _buildImagePlaceholder(),
              )
            else
              _buildImagePlaceholder(),

            // Gradient overlay
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.transparent, Colors.black.withOpacity(0.7)],
                ),
              ),
            ),

            // Tournament info
            Positioned(
              bottom: 60.h,
              left: 16.w,
              right: 16.w,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    tournament.name,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      _buildInfoChip(
                        Icons.sports_soccer,
                        tournament.format.displayName,
                        Colors.white,
                      ),
                      SizedBox(width: 8.w),
                      _buildInfoChip(
                        Icons.location_on,
                        tournament.location,
                        Colors.white,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        AnimatedBuilder(
          animation: _favoriteAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _favoriteAnimation.value,
              child: IconButton(
                icon: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: isFavorite ? Colors.red : theme.colorScheme.onSurface,
                ),
                onPressed: () => _toggleFavorite(),
              ),
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.share),
          onPressed: () => _shareTournament(tournament),
        ),
      ],
    );
  }

  Widget _buildImagePlaceholder() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.green.shade400, Colors.green.shade600],
        ),
      ),
      child: Center(
        child: Icon(Icons.sports_soccer, size: 80.sp, color: Colors.white),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16.sp, color: color),
          SizedBox(width: 4.w),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(ThemeData theme) {
    return SliverPersistentHeader(
      pinned: true,
      delegate: _TabBarDelegate(
        TabBar(
          controller: _tabController,
          indicatorColor: theme.colorScheme.primary,
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.6),
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Participants'),
            Tab(text: 'Location'),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewTab(dynamic tournament) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTournamentStats(tournament),
          SizedBox(height: 24.h),
          _buildTournamentDescription(tournament),
          SizedBox(height: 24.h),
          TournamentOrganizerInfo(tournament: tournament),
          SizedBox(height: 24.h),
          TournamentRulesSection(tournament: tournament),
          SizedBox(height: 24.h),
          _buildPrizeBreakdown(tournament),
        ],
      ),
    );
  }

  Widget _buildParticipantsTab(dynamic tournament) {
    return TournamentParticipantsList(tournament: tournament);
  }

  Widget _buildLocationTab(dynamic tournament) {
    return Column(
      children: [
        Expanded(flex: 2, child: _buildGoogleMap(tournament)),
        Expanded(flex: 1, child: _buildLocationDetails(tournament)),
      ],
    );
  }

  Widget _buildTournamentStats(dynamic tournament) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Entry Fee',
                  tournament.entryFee > 0
                      ? '${tournament.currency} ${tournament.entryFee.toStringAsFixed(0)}'
                      : 'Free',
                  Icons.monetization_on,
                  theme.colorScheme.primary,
                ),
              ),
              Container(
                width: 1.w,
                height: 40.h,
                color: theme.colorScheme.outline.withOpacity(0.2),
              ),
              Expanded(
                child: _buildStatItem(
                  'Prize Pool',
                  '${tournament.prize.currency} ${tournament.prize.totalAmount.toStringAsFixed(0)}',
                  Icons.emoji_events,
                  Colors.orange,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Participants',
                  '${tournament.currentParticipants}/${tournament.maxParticipants}',
                  Icons.people,
                  Colors.blue,
                ),
              ),
              Container(
                width: 1.w,
                height: 40.h,
                color: theme.colorScheme.outline.withOpacity(0.2),
              ),
              Expanded(
                child: _buildStatItem(
                  'Available Slots',
                  '${tournament.availableSlots}',
                  Icons.event_seat,
                  tournament.hasAvailableSlots ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Icon(icon, color: color, size: 24.sp),
        SizedBox(height: 8.h),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildTournamentDescription(dynamic tournament) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Text(tournament.description, style: theme.textTheme.bodyMedium),
        SizedBox(height: 16.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children:
              tournament.tags
                  .map<Widget>(
                    (tag) => Chip(
                      label: Text(tag),
                      backgroundColor: theme.colorScheme.primary.withOpacity(
                        0.1,
                      ),
                      labelStyle: TextStyle(
                        color: theme.colorScheme.primary,
                        fontSize: 12.sp,
                      ),
                    ),
                  )
                  .toList(),
        ),
      ],
    );
  }

  Widget _buildPrizeBreakdown(dynamic tournament) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Prize Distribution',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        ...tournament.prize.distribution.map<Widget>((prize) {
          return Container(
            margin: EdgeInsets.only(bottom: 8.h),
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.2),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  prize.description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${tournament.prize.currency} ${prize.amount.toStringAsFixed(0)}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildGoogleMap(dynamic tournament) {
    final location = tournament.venueDetails;

    return GoogleMap(
      initialCameraPosition: CameraPosition(
        target: LatLng(location.latitude, location.longitude),
        zoom: 15,
      ),
      markers: {
        Marker(
          markerId: const MarkerId('venue'),
          position: LatLng(location.latitude, location.longitude),
          infoWindow: InfoWindow(
            title: location.name,
            snippet: location.address,
          ),
        ),
      },
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
      },
      mapType: MapType.normal,
      myLocationEnabled: true,
      myLocationButtonEnabled: true,
      zoomControlsEnabled: true,
    );
  }

  Widget _buildLocationDetails(dynamic tournament) {
    final theme = Theme.of(context);
    final location = tournament.venueDetails;

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            location.name,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: 16.sp,
                color: theme.colorScheme.primary,
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: Text(
                  location.address,
                  style: theme.textTheme.bodyMedium,
                ),
              ),
            ],
          ),
          if (location.contactNumber != null) ...[
            SizedBox(height: 8.h),
            Row(
              children: [
                Icon(
                  Icons.phone,
                  size: 16.sp,
                  color: theme.colorScheme.primary,
                ),
                SizedBox(width: 4.w),
                Text(
                  location.contactNumber!,
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
          ],
          if (location.facilities.isNotEmpty) ...[
            SizedBox(height: 12.h),
            Text(
              'Facilities',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 4.h),
            Wrap(
              spacing: 8.w,
              children:
                  location.facilities
                      .map<Widget>(
                        (facility) => Chip(
                          label: Text(facility),
                          backgroundColor: theme.colorScheme.primary
                              .withOpacity(0.1),
                          labelStyle: TextStyle(
                            color: theme.colorScheme.primary,
                            fontSize: 11.sp,
                          ),
                        ),
                      )
                      .toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBottomBar(
    dynamic tournament,
    bool isParticipant,
    ThemeData theme,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Tournament dates
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Tournament Date',
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                  Text(
                    _formatDateRange(tournament.startDate, tournament.endDate),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(width: 16.w),

            // Action button
            Expanded(
              child: _buildActionButton(tournament, isParticipant, theme),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    dynamic tournament,
    bool isParticipant,
    ThemeData theme,
  ) {
    String buttonText;
    VoidCallback? onPressed;
    Color? backgroundColor;

    if (isParticipant) {
      buttonText = 'Leave Tournament';
      backgroundColor = Colors.red;
      onPressed = () => _leaveTournament();
    } else if (!tournament.isRegistrationOpen) {
      buttonText = 'Registration Closed';
      onPressed = null;
    } else if (!tournament.hasAvailableSlots) {
      buttonText = 'Tournament Full';
      onPressed = null;
    } else {
      buttonText = 'Join Tournament';
      backgroundColor = theme.colorScheme.primary;
      onPressed = () => _joinTournament();
    }

    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
        padding: EdgeInsets.symmetric(vertical: 12.h),
      ),
      child: Text(
        buttonText,
        style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16.sp),
      ),
    );
  }

  void _toggleFavorite() {
    _favoriteAnimationController.forward().then((_) {
      _favoriteAnimationController.reverse();
    });

    ref
        .read(tournamentDetailsStateProvider(widget.tournamentId).notifier)
        .toggleFavourite();
  }

  void _shareTournament(dynamic tournament) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon!')),
    );
  }

  void _joinTournament() {
    ref
        .read(tournamentDetailsStateProvider(widget.tournamentId).notifier)
        .joinTournament();
  }

  void _leaveTournament() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Leave Tournament'),
            content: const Text(
              'Are you sure you want to leave this tournament?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ref
                      .read(
                        tournamentDetailsStateProvider(
                          widget.tournamentId,
                        ).notifier,
                      )
                      .leaveTournament();
                },
                child: const Text('Leave'),
              ),
            ],
          ),
    );
  }

  String _formatDateRange(DateTime start, DateTime end) {
    if (start.day == end.day &&
        start.month == end.month &&
        start.year == end.year) {
      return '${start.day}/${start.month}/${start.year}';
    } else {
      return '${start.day}/${start.month} - ${end.day}/${end.month}';
    }
  }
}

class _TabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _TabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(_TabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar;
  }
}

