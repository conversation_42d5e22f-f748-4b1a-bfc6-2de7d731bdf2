import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../tournaments_providers.dart';
import '../logic/tournament_state.dart';
import '../widgets/tournament_card.dart';

class TournamentSearchScreen extends ConsumerStatefulWidget {
  const TournamentSearchScreen({super.key});

  @override
  ConsumerState<TournamentSearchScreen> createState() =>
      _TournamentSearchScreenState();
}

class _TournamentSearchScreenState
    extends ConsumerState<TournamentSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();

  String _lastSearchTerm = '';

  @override
  void initState() {
    super.initState();
    _searchFocusNode.requestFocus();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final state = ref.read(tournamentSearchStateProvider);
      state.when(
        loaded: (results, searchTerm, hasMore) {
          if (hasMore && _lastSearchTerm.isNotEmpty) {
            ref
                .read(tournamentSearchStateProvider.notifier)
                .loadMore(_lastSearchTerm, results.length);
          }
        },
        initial: () {},
        searching: () {},
        error: (message) {},
      );
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: AppBar(
        title: _buildSearchField(theme),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
      ),
      body: Column(
        children: [
          _buildSearchSuggestions(theme),
          Expanded(child: _buildSearchResults()),
        ],
      ),
    );
  }

  Widget _buildSearchField(ThemeData theme) {
    return TextField(
      controller: _searchController,
      focusNode: _searchFocusNode,
      decoration: InputDecoration(
        hintText: 'Search tournaments...',
        hintStyle: TextStyle(
          color: theme.colorScheme.onSurface.withOpacity(0.6),
        ),
        border: InputBorder.none,
        suffixIcon: _searchController.text.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: _clearSearch,
              )
            : const Icon(Icons.search),
      ),
      onChanged: _onSearchChanged,
      onSubmitted: _performSearch,
      textInputAction: TextInputAction.search,
    );
  }

  Widget _buildSearchSuggestions(ThemeData theme) {
    return Container(
      color: theme.colorScheme.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Text(
              'Popular Searches',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ),
          Container(
            height: 50.h,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildSuggestionChip('5v5', theme),
                _buildSuggestionChip('Kathmandu', theme),
                _buildSuggestionChip('Free entry', theme),
                _buildSuggestionChip('This weekend', theme),
                _buildSuggestionChip('High prize', theme),
                _buildSuggestionChip('Football Academy', theme),
              ],
            ),
          ),
          Divider(height: 1.h),
        ],
      ),
    );
  }

  Widget _buildSuggestionChip(String suggestion, ThemeData theme) {
    return Container(
      margin: EdgeInsets.only(right: 8.w),
      child: ActionChip(
        label: Text(suggestion),
        onPressed: () => _applySuggestion(suggestion),
        backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
        labelStyle: TextStyle(
          color: theme.colorScheme.primary,
          fontSize: 12.sp,
        ),
      ),
    );
  }

  Widget _buildSearchResults() {
    return Consumer(
      builder: (context, ref, child) {
        final state = ref.watch(tournamentSearchStateProvider);

        return state.when(
          initial: () => _buildInitialState(),
          searching: () => _buildLoadingState(),
          loaded: (results, searchTerm, hasMore) =>
              _buildLoadedState(results, hasMore),
          error: (message) => _buildErrorState(message),
        );
      },
    );
  }

  Widget _buildInitialState() {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search, size: 64.sp, color: Colors.grey[400]),
          SizedBox(height: 16.h),
          Text(
            'Search for tournaments',
            style: TextStyle(fontSize: 18.sp, color: Colors.grey[600]),
          ),
          SizedBox(height: 8.h),
          Text(
            'Enter tournament name, location, or organizer',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          _buildQuickSearchButtons(theme),
        ],
      ),
    );
  }

  Widget _buildQuickSearchButtons(ThemeData theme) {
    final quickSearches = [
      {'label': 'Featured', 'icon': Icons.star},
      {'label': 'Free Entry', 'icon': Icons.money_off},
      {'label': 'This Week', 'icon': Icons.calendar_today},
      {'label': 'Nearby', 'icon': Icons.location_on},
    ];

    return Wrap(
      spacing: 12.w,
      runSpacing: 12.h,
      children: quickSearches.map((search) {
        return ElevatedButton.icon(
          onPressed: () => _performQuickSearch(search['label'] as String),
          icon: Icon(search['icon'] as IconData, size: 18.sp),
          label: Text(search['label'] as String),
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
            foregroundColor: theme.colorScheme.primary,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.r),
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildLoadedState(List<dynamic> results, bool hasMore) {
    if (results.isEmpty) {
      return _buildNoResultsState();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(vertical: 16.h),
      itemCount: results.length + (hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < results.length) {
          return TournamentCard(
            tournament: results[index],
            onTap: () => _openTournamentDetails(results[index].id),
            onFavoriteTap: () => _toggleFavorite(results[index].id),
            isFavorite: false, // TODO: Check if tournament is favorite
          );
        } else {
          return _buildLoadingIndicator();
        }
      },
    );
  }

  Widget _buildNoResultsState() {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 64.sp, color: Colors.grey[400]),
          SizedBox(height: 16.h),
          Text(
            'No tournaments found',
            style: TextStyle(fontSize: 18.sp, color: Colors.grey[600]),
          ),
          SizedBox(height: 8.h),
          Text(
            'Try adjusting your search terms',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
          ),
          SizedBox(height: 24.h),
          ElevatedButton(
            onPressed: _clearSearch,
            child: const Text('Clear Search'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red[400]),
          SizedBox(height: 16.h),
          Text(
            'Search Error',
            style: TextStyle(fontSize: 18.sp, color: Colors.grey[600]),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () => _performSearch(_lastSearchTerm),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: EdgeInsets.all(16.w),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(),
    );
  }

  void _onSearchChanged(String value) {
    setState(() {});

    // Debounce search
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_searchController.text == value && value.isNotEmpty) {
        _performSearch(value);
      }
    });
  }

  void _performSearch(String searchTerm) {
    if (searchTerm.trim().isEmpty) return;

    _lastSearchTerm = searchTerm;
    ref.read(tournamentSearchStateProvider.notifier).search(searchTerm);
  }

  void _performQuickSearch(String quickSearch) {
    switch (quickSearch) {
      case 'Featured':
        _searchController.text = 'featured';
        break;
      case 'Free Entry':
        _searchController.text = 'free';
        break;
      case 'This Week':
        _searchController.text = 'this week';
        break;
      case 'Nearby':
        _searchController.text = 'kathmandu'; // Default to Kathmandu for demo
        break;
    }
    _performSearch(_searchController.text);
  }

  void _applySuggestion(String suggestion) {
    _searchController.text = suggestion;
    _searchFocusNode.unfocus();
    _performSearch(suggestion);
  }

  void _clearSearch() {
    _searchController.clear();
    _lastSearchTerm = '';
    ref.read(tournamentSearchStateProvider.notifier).clearSearch();
    setState(() {});
  }

  void _openTournamentDetails(String tournamentId) {
    Navigator.of(context).pushNamed('/tournaments/$tournamentId');
  }

  void _toggleFavorite(String tournamentId) {
    // TODO: Implement favorite toggle
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Added to favorites!')));
  }
}
