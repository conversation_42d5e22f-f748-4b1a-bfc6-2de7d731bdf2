import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../tournaments_providers.dart';
import '../logic/tournament_state.dart';

class FavouriteTournamentsScreen extends ConsumerStatefulWidget {
  const FavouriteTournamentsScreen({super.key});

  @override
  ConsumerState<FavouriteTournamentsScreen> createState() =>
      _FavouriteTournamentsScreenState();
}

class _FavouriteTournamentsScreenState
    extends ConsumerState<FavouriteTournamentsScreen> {
  @override
  void initState() {
    super.initState();
    // Load favourite tournaments
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(favouriteTournamentsStateProvider.notifier).loadFavourites();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Favourites'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref
                  .read(favouriteTournamentsStateProvider.notifier)
                  .loadFavourites();
            },
          ),
        ],
      ),
      body: Consumer(
        builder: (context, ref, child) {
          final state = ref.watch(favouriteTournamentsStateProvider);

          return state.when(
            initial: () => _buildLoadingState(),
            loading: () => _buildLoadingState(),
            loaded: (favourites) => _buildLoadedState(favourites, theme),
            error: (message) => _buildErrorState(message, theme),
          );
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorState(String message, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red[400]),
          SizedBox(height: 16.h),
          Text(
            'Error loading favourites',
            style: TextStyle(fontSize: 18.sp, color: Colors.grey[600]),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () {
              ref
                  .read(favouriteTournamentsStateProvider.notifier)
                  .loadFavourites();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadedState(List<dynamic> favourites, ThemeData theme) {
    if (favourites.isEmpty) {
      return _buildEmptyState(theme);
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.read(favouriteTournamentsStateProvider.notifier).loadFavourites();
      },
      child: ListView.builder(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        itemCount: favourites.length,
        itemBuilder: (context, index) {
          final favourite = favourites[index];
          return _buildFavouriteCard(favourite, theme);
        },
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.favorite_border, size: 80.sp, color: Colors.grey[400]),
          SizedBox(height: 24.h),
          Text(
            'No Favourite Tournaments',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 12.h),
          Text(
            'Start exploring tournaments and\nadd them to your favourites!',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pushReplacementNamed('/tournaments');
            },
            icon: const Icon(Icons.explore),
            label: const Text('Explore Tournaments'),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25.r),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavouriteCard(dynamic favourite, ThemeData theme) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Material(
        elevation: 2,
        borderRadius: BorderRadius.circular(16.r),
        color: theme.colorScheme.surface,
        child: InkWell(
          onTap: () => _openTournamentDetails(favourite.favourite.tournamentId),
          borderRadius: BorderRadius.circular(16.r),
          child: Container(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                // Tournament image
                _buildTournamentImage(favourite, theme),

                SizedBox(width: 16.w),

                // Tournament info
                Expanded(child: _buildTournamentInfo(favourite, theme)),

                // Action buttons
                _buildActionButtons(favourite, theme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTournamentImage(dynamic favourite, ThemeData theme) {
    return Container(
      width: 80.w,
      height: 80.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withOpacity(0.8),
            theme.colorScheme.primary,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child:
            favourite.tournamentBannerImage != null
                ? CachedNetworkImage(
                  imageUrl: favourite.tournamentBannerImage!,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => _buildImagePlaceholder(theme),
                  errorWidget:
                      (context, url, error) => _buildImagePlaceholder(theme),
                )
                : _buildImagePlaceholder(theme),
      ),
    );
  }

  Widget _buildImagePlaceholder(ThemeData theme) {
    return Center(
      child: Icon(Icons.sports_soccer, size: 32.sp, color: Colors.white),
    );
  }

  Widget _buildTournamentInfo(dynamic favourite, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tournament name
        Text(
          favourite.tournamentName,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        SizedBox(height: 4.h),

        // Location
        Row(
          children: [
            Icon(
              Icons.location_on,
              size: 14.sp,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 4.w),
            Expanded(
              child: Text(
                favourite.tournamentLocation,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),

        SizedBox(height: 6.h),

        // Tournament date
        Row(
          children: [
            Icon(
              Icons.calendar_today,
              size: 14.sp,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 4.w),
            Text(
              _formatDate(favourite.tournamentStartDate),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),

        SizedBox(height: 6.h),

        // Added to favourites date
        Text(
          'Added ${_formatAddedDate(favourite.favourite.addedAt)}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.5),
            fontSize: 11.sp,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(dynamic favourite, ThemeData theme) {
    return Column(
      children: [
        // Favourite button
        IconButton(
          onPressed:
              () => _removeFromFavourites(favourite.favourite.tournamentId),
          icon: const Icon(Icons.favorite),
          color: Colors.red,
          style: IconButton.styleFrom(
            backgroundColor: Colors.red.withOpacity(0.1),
          ),
        ),

        SizedBox(height: 4.h),

        // Share button
        IconButton(
          onPressed: () => _shareTournament(favourite),
          icon: const Icon(Icons.share),
          color: theme.colorScheme.primary,
          style: IconButton.styleFrom(
            backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Tomorrow';
    } else if (difference > 0 && difference < 7) {
      return '$difference days away';
    } else if (difference < 0) {
      final pastDays = difference.abs();
      if (pastDays == 1) {
        return 'Yesterday';
      } else if (pastDays < 7) {
        return '$pastDays days ago';
      } else {
        return '${date.day}/${date.month}/${date.year}';
      }
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  String _formatAddedDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }

  void _openTournamentDetails(String tournamentId) {
    Navigator.of(context).pushNamed('/tournaments/$tournamentId');
  }

  void _removeFromFavourites(String tournamentId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Remove from Favourites'),
            content: const Text(
              'Are you sure you want to remove this tournament from your favourites?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ref
                      .read(favouriteTournamentsStateProvider.notifier)
                      .removeFavourite(tournamentId);

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Tournament removed from favourites'),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
                child: const Text('Remove'),
              ),
            ],
          ),
    );
  }

  void _shareTournament(dynamic favourite) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon!')),
    );
  }
}
