import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../tournaments_providers.dart';
import '../../domain/entities/tournament.dart';
import '../../domain/entities/tournament_filter.dart';

class TournamentFilterScreen extends ConsumerStatefulWidget {
  const TournamentFilterScreen({super.key});

  @override
  ConsumerState<TournamentFilterScreen> createState() =>
      _TournamentFilterScreenState();
}

class _TournamentFilterScreenState
    extends ConsumerState<TournamentFilterScreen> {
  late TournamentFilter _currentFilter;

  // Date range
  DateTimeRange? _selectedDateRange;

  // Price ranges
  RangeValues _entryFeeRange = const RangeValues(0, 5000);
  RangeValues _prizeRange = const RangeValues(0, 100000);

  // Selected values
  Set<TournamentType> _selectedFormats = {};
  Set<String> _selectedLocations = {};
  Set<TournamentStatus> _selectedStatuses = {};
  bool _availableSlotsOnly = false;
  TournamentSortBy _selectedSortBy = TournamentSortBy.soonestFirst;

  @override
  void initState() {
    super.initState();
    _currentFilter = ref.read(tournamentFilterStateProvider).filter;
    _initializeFilterValues();
  }

  void _initializeFilterValues() {
    _selectedFormats = Set.from(_currentFilter.formats);
    _selectedLocations = Set.from(_currentFilter.locations);
    _selectedStatuses = Set.from(_currentFilter.statuses);
    _availableSlotsOnly = _currentFilter.availableSlotsOnly;
    _selectedSortBy = _currentFilter.sortBy;

    if (_currentFilter.dateRange != null) {
      final dateRange = _currentFilter.dateRange!;
      if (dateRange.startDate != null && dateRange.endDate != null) {
        _selectedDateRange = DateTimeRange(
          start: dateRange.startDate!,
          end: dateRange.endDate!,
        );
      }
    }

    if (_currentFilter.entryFeeRange != null) {
      final feeRange = _currentFilter.entryFeeRange!;
      _entryFeeRange = RangeValues(
        feeRange.minAmount ?? 0,
        feeRange.maxAmount ?? 5000,
      );
    }

    if (_currentFilter.prizeRange != null) {
      final prizeRange = _currentFilter.prizeRange!;
      _prizeRange = RangeValues(
        prizeRange.minAmount ?? 0,
        prizeRange.maxAmount ?? 100000,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Filter Tournaments'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        actions: [
          TextButton(
            onPressed: _clearAllFilters,
            child: Text(
              'Clear All',
              style: TextStyle(color: theme.colorScheme.primary),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              padding: EdgeInsets.all(16.w),
              children: [
                _buildFormatFilter(theme),
                SizedBox(height: 24.h),
                _buildLocationFilter(theme),
                SizedBox(height: 24.h),
                _buildDateRangeFilter(theme),
                SizedBox(height: 24.h),
                _buildEntryFeeFilter(theme),
                SizedBox(height: 24.h),
                _buildPrizeRangeFilter(theme),
                SizedBox(height: 24.h),
                _buildStatusFilter(theme),
                SizedBox(height: 24.h),
                _buildAvailableSlotsFilter(theme),
                SizedBox(height: 24.h),
                _buildSortByFilter(theme),
              ],
            ),
          ),
          _buildBottomBar(theme),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title, ThemeData theme) {
    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
    );
  }

  Widget _buildFormatFilter(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Tournament Format', theme),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: [
            TournamentType.fiveVsFive,
            TournamentType.sevenVsSeven,
            TournamentType.elevenVsEleven
          ].map((format) {
            final isSelected = _selectedFormats.contains(format);
            return FilterChip(
              label: Text(format.displayName),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedFormats.add(format);
                  } else {
                    _selectedFormats.remove(format);
                  }
                });
              },
              selectedColor: theme.colorScheme.primary.withOpacity(0.2),
              checkmarkColor: theme.colorScheme.primary,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildLocationFilter(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Location', theme),
        SizedBox(height: 12.h),
        Consumer(
          builder: (context, ref, child) {
            final locationsAsync = ref.watch(popularLocationsProvider);
            return locationsAsync.when(
              data: (locations) => Wrap(
                spacing: 8.w,
                runSpacing: 8.h,
                children: locations.map((location) {
                  final isSelected = _selectedLocations.contains(
                    location,
                  );
                  return FilterChip(
                    label: Text(location),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedLocations.add(location);
                        } else {
                          _selectedLocations.remove(location);
                        }
                      });
                    },
                    selectedColor: theme.colorScheme.primary.withOpacity(0.2),
                    checkmarkColor: theme.colorScheme.primary,
                  );
                }).toList(),
              ),
              loading: () => const CircularProgressIndicator(),
              error: (error, stack) => Text('Error: $error'),
            );
          },
        ),
      ],
    );
  }

  Widget _buildDateRangeFilter(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Date Range', theme),
        SizedBox(height: 12.h),
        InkWell(
          onTap: _selectDateRange,
          child: Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.3),
              ),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              children: [
                Icon(Icons.date_range, color: theme.colorScheme.primary),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    _selectedDateRange != null
                        ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                        : 'Select date range',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: _selectedDateRange != null
                          ? theme.colorScheme.onSurface
                          : theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ),
                if (_selectedDateRange != null)
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _selectedDateRange = null;
                      });
                    },
                    icon: const Icon(Icons.clear),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEntryFeeFilter(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Entry Fee Range (NPR)', theme),
        SizedBox(height: 12.h),
        RangeSlider(
          values: _entryFeeRange,
          min: 0,
          max: 5000,
          divisions: 20,
          labels: RangeLabels(
            _entryFeeRange.start.round().toString(),
            _entryFeeRange.end.round().toString(),
          ),
          onChanged: (values) {
            setState(() {
              _entryFeeRange = values;
            });
          },
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'NPR ${_entryFeeRange.start.round()}',
              style: theme.textTheme.bodySmall,
            ),
            Text(
              'NPR ${_entryFeeRange.end.round()}',
              style: theme.textTheme.bodySmall,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPrizeRangeFilter(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Prize Range (NPR)', theme),
        SizedBox(height: 12.h),
        RangeSlider(
          values: _prizeRange,
          min: 0,
          max: 100000,
          divisions: 20,
          labels: RangeLabels(
            _prizeRange.start.round().toString(),
            _prizeRange.end.round().toString(),
          ),
          onChanged: (values) {
            setState(() {
              _prizeRange = values;
            });
          },
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'NPR ${_prizeRange.start.round()}',
              style: theme.textTheme.bodySmall,
            ),
            Text(
              'NPR ${_prizeRange.end.round()}',
              style: theme.textTheme.bodySmall,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusFilter(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Tournament Status', theme),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: [
            TournamentStatus.registrationOpen,
            TournamentStatus.inProgress,
            TournamentStatus.completed,
          ].map((status) {
            final isSelected = _selectedStatuses.contains(status);
            return FilterChip(
              label: Text(_getStatusDisplayName(status)),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedStatuses.add(status);
                  } else {
                    _selectedStatuses.remove(status);
                  }
                });
              },
              selectedColor: theme.colorScheme.primary.withOpacity(0.2),
              checkmarkColor: theme.colorScheme.primary,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildAvailableSlotsFilter(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: Text(
            'Only show tournaments with available slots',
            style: theme.textTheme.bodyMedium,
          ),
        ),
        Switch(
          value: _availableSlotsOnly,
          onChanged: (value) {
            setState(() {
              _availableSlotsOnly = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildSortByFilter(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Sort By', theme),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: [
            TournamentSortBy.soonestFirst,
            TournamentSortBy.lowestFee,
            TournamentSortBy.highestPrize,
            TournamentSortBy.mostPopular,
            TournamentSortBy.availableSlots,
          ].map((sortBy) {
            final isSelected = _selectedSortBy == sortBy;
            return ChoiceChip(
              label: Text(sortBy.displayName),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedSortBy = sortBy;
                  });
                }
              },
              selectedColor: theme.colorScheme.primary.withOpacity(0.2),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildBottomBar(ThemeData theme) {
    final activeFilterCount = _getActiveFilterCount();

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _clearAllFilters,
                child: const Text('Clear All'),
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _applyFilters,
                child: Text(
                  activeFilterCount > 0
                      ? 'Apply ($activeFilterCount) Filters'
                      : 'Apply Filters',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _selectDateRange() async {
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDateRange,
    );

    if (dateRange != null) {
      setState(() {
        _selectedDateRange = dateRange;
      });
    }
  }

  void _clearAllFilters() {
    setState(() {
      _selectedFormats.clear();
      _selectedLocations.clear();
      _selectedStatuses.clear();
      _selectedDateRange = null;
      _entryFeeRange = const RangeValues(0, 5000);
      _prizeRange = const RangeValues(0, 100000);
      _availableSlotsOnly = false;
      _selectedSortBy = TournamentSortBy.soonestFirst;
    });
  }

  void _applyFilters() {
    final filter = TournamentFilter(
      formats: _selectedFormats.toList(),
      locations: _selectedLocations.toList(),
      statuses: _selectedStatuses.toList(),
      dateRange: _selectedDateRange != null
          ? DateRangeFilter(
              startDate: _selectedDateRange!.start,
              endDate: _selectedDateRange!.end,
            )
          : null,
      entryFeeRange: PriceRangeFilter(
        minAmount: _entryFeeRange.start,
        maxAmount: _entryFeeRange.end,
      ),
      prizeRange: PriceRangeFilter(
        minAmount: _prizeRange.start,
        maxAmount: _prizeRange.end,
      ),
      availableSlotsOnly: _availableSlotsOnly,
      sortBy: _selectedSortBy,
    );

    ref.read(tournamentFilterStateProvider.notifier).updateFilter(filter);
    ref.read(tournamentFilterStateProvider.notifier).applyFilter();

    Navigator.of(context).pop();
  }

  int _getActiveFilterCount() {
    int count = 0;

    if (_selectedFormats.isNotEmpty) count++;
    if (_selectedLocations.isNotEmpty) count++;
    if (_selectedStatuses.isNotEmpty) count++;
    if (_selectedDateRange != null) count++;
    if (_entryFeeRange.start > 0 || _entryFeeRange.end < 5000) count++;
    if (_prizeRange.start > 0 || _prizeRange.end < 100000) count++;
    if (_availableSlotsOnly) count++;
    if (_selectedSortBy != TournamentSortBy.soonestFirst) count++;

    return count;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getStatusDisplayName(TournamentStatus status) {
    switch (status) {
      case TournamentStatus.registrationOpen:
        return 'Registration Open';
      case TournamentStatus.registrationClosed:
        return 'Registration Closed';
      case TournamentStatus.inProgress:
        return 'In Progress';
      default:
        return status.toString();
    }
  }
}
