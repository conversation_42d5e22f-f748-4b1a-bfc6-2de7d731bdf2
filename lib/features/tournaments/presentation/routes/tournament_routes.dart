import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import '../screens/tournament_list_screen.dart';
import '../screens/tournament_details_screen.dart';
import '../screens/tournament_search_screen.dart';
import '../screens/tournament_filter_screen.dart';
import '../screens/favourite_tournaments_screen.dart';

class TournamentRoutes {
  static const String tournamentList = '/tournaments';
  static const String tournamentDetails = '/tournaments/:id';
  static const String tournamentSearch = '/tournaments/search';
  static const String tournamentFilter = '/tournaments/filter';
  static const String favouriteTournaments = '/tournaments/favourites';

  static List<RouteBase> getRoutes() {
    return [
      GoRoute(
        path: tournamentList,
        name: 'tournaments',
        builder: (context, state) => const TournamentListScreen(),
        routes: [
          GoRoute(
            path: 'search',
            name: 'tournament-search',
            builder: (context, state) => const TournamentSearchScreen(),
          ),
          GoRoute(
            path: 'filter',
            name: 'tournament-filter',
            builder: (context, state) => const TournamentFilterScreen(),
          ),
          GoRoute(
            path: 'favourites',
            name: 'favourite-tournaments',
            builder: (context, state) => const FavouriteTournamentsScreen(),
          ),
          GoRoute(
            path: ':id',
            name: 'tournament-details',
            builder: (context, state) {
              final tournamentId = state.pathParameters['id']!;
              return TournamentDetailsScreen(tournamentId: tournamentId);
            },
          ),
        ],
      ),
    ];
  }

  // Helper methods for navigation
  static void goToTournamentList(BuildContext context) {
    context.go(tournamentList);
  }

  static void goToTournamentDetails(BuildContext context, String tournamentId) {
    context.go('/tournaments/$tournamentId');
  }

  static void goToTournamentSearch(BuildContext context) {
    context.go('/tournaments/search');
  }

  static void goToTournamentFilter(BuildContext context) {
    context.go('/tournaments/filter');
  }

  static void goToFavouriteTournaments(BuildContext context) {
    context.go('/tournaments/favourites');
  }

  // Push methods for modal navigation
  static Future<T?> pushTournamentSearch<T extends Object?>(
    BuildContext context,
  ) {
    return context.push<T>('/tournaments/search');
  }

  static Future<T?> pushTournamentFilter<T extends Object?>(
    BuildContext context,
  ) {
    return context.push<T>('/tournaments/filter');
  }

  static Future<T?> pushTournamentDetails<T extends Object?>(
    BuildContext context,
    String tournamentId,
  ) {
    return context.push<T>('/tournaments/$tournamentId');
  }
}
