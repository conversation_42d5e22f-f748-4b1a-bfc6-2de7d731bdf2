import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'domain/entities/venue.dart';
import 'data/repositories/venue_repository.dart';
import 'domain/usecases/venue_usecases.dart';

// Repository provider
final venueRepositoryProvider = Provider<VenueRepository>((ref) {
  return VenueRepositoryImpl();
});

// Use case providers
final getNearbyVenuesUseCaseProvider = Provider<GetNearbyVenuesUseCase>((ref) {
  final repository = ref.watch(venueRepositoryProvider);
  return GetNearbyVenuesUseCase(repository);
});

final searchVenuesUseCaseProvider = Provider<SearchVenuesUseCase>((ref) {
  final repository = ref.watch(venueRepositoryProvider);
  return SearchVenuesUseCase(repository);
});

final getVenueByIdUseCaseProvider = Provider<GetVenueByIdUseCase>((ref) {
  final repository = ref.watch(venueRepositoryProvider);
  return GetVenueByIdUseCase(repository);
});

final getVenueSuggestionsUseCaseProvider =
    Provider<GetVenueSuggestionsUseCase>((ref) {
  final repository = ref.watch(venueRepositoryProvider);
  return GetVenueSuggestionsUseCase(repository);
});

// State providers
final nearbyVenuesProvider =
    FutureProvider.autoDispose<List<Venue>>((ref) async {
  final useCase = ref.watch(getNearbyVenuesUseCaseProvider);
  return await useCase(
    latitude: 27.7172, // Default to Kathmandu center
    longitude: 85.3240,
    radiusInKm: 10.0,
  );
});

final venueSearchProvider =
    FutureProvider.family<List<Venue>, String>((ref, query) async {
  final useCase = ref.watch(searchVenuesUseCaseProvider);
  return await useCase(query: query);
});

final venueSuggestionsProvider =
    FutureProvider.family<List<Venue>, String>((ref, query) async {
  final useCase = ref.watch(getVenueSuggestionsUseCaseProvider);
  return await useCase(query: query);
});

// Legacy provider for backward compatibility
final venuesProvider = FutureProvider.autoDispose<List<dynamic>>((ref) async {
  final useCase = ref.watch(getNearbyVenuesUseCaseProvider);
  final venues = await useCase(
    latitude: 27.7172,
    longitude: 85.3240,
    radiusInKm: 10.0,
  );
  return venues.map((venue) => venue.toJson()).toList();
});
