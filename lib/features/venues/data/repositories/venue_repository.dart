import '../../domain/entities/venue.dart';

abstract class VenueRepository {
  Future<List<Venue>> getNearbyVenues({
    required double latitude,
    required double longitude,
    double radiusInKm = 10.0,
  });

  Future<List<Venue>> searchVenues({
    required String query,
    VenueFilters? filters,
  });

  Future<Venue?> getVenueById(String id);
}

class VenueRepositoryImpl implements VenueRepository {
  // Mock data for nearby venues
  static final List<Venue> _mockVenues = [
    Venue(
      id: 'venue-1',
      name: 'Dhumbarahi Futsal Center',
      address: 'Dhumbarahi, Kathmandu',
      latitude: 27.7172,
      longitude: 85.3240,
      description: 'Premium indoor futsal facility with professional turf',
      sports: ['soccer', 'futsal'],
      facilities: ['indoor', 'parking', 'shower', 'equipment'],
      rating: 4.8,
      reviewCount: 127,
      imageUrl: 'https://example.com/dhumbarahi.jpg',
      pricePerHour: 2500.0,
      contactNumber: '+977-1-4444444',
      distance: 0.5,
      availableTimeSlots: [
        '09:00',
        '10:00',
        '11:00',
        '14:00',
        '15:00',
        '16:00'
      ],
    ),
    Venue(
      id: 'venue-2',
      name: 'Bhatbhateni Futsal',
      address: 'Bhatbhateni, Kathmandu',
      latitude: 27.7200,
      longitude: 85.3300,
      description: 'Popular futsal venue with multiple courts',
      sports: ['soccer', 'futsal'],
      facilities: ['indoor', 'parking', 'equipment'],
      rating: 4.5,
      reviewCount: 89,
      imageUrl: 'https://example.com/bhatbhateni.jpg',
      pricePerHour: 2000.0,
      contactNumber: '+977-1-5555555',
      distance: 1.2,
      availableTimeSlots: [
        '08:00',
        '09:00',
        '10:00',
        '13:00',
        '14:00',
        '15:00',
        '16:00'
      ],
    ),
    Venue(
      id: 'venue-3',
      name: 'Kupondole Sports Complex',
      address: 'Kupondole, Lalitpur',
      latitude: 27.6800,
      longitude: 85.3100,
      description: 'Multi-sport complex with outdoor soccer field',
      sports: ['soccer', 'football', 'basketball'],
      facilities: ['outdoor', 'indoor', 'parking', 'shower', 'equipment'],
      rating: 4.2,
      reviewCount: 156,
      imageUrl: 'https://example.com/kupondole.jpg',
      pricePerHour: 1800.0,
      contactNumber: '+977-1-6666666',
      distance: 2.8,
      availableTimeSlots: [
        '06:00',
        '07:00',
        '08:00',
        '16:00',
        '17:00',
        '18:00'
      ],
    ),
    Venue(
      id: 'venue-4',
      name: 'Thamel Futsal Arena',
      address: 'Thamel, Kathmandu',
      latitude: 27.7150,
      longitude: 85.3150,
      description: 'Modern futsal arena in the heart of Thamel',
      sports: ['soccer', 'futsal'],
      facilities: ['indoor', 'parking', 'shower', 'equipment', 'cafe'],
      rating: 4.6,
      reviewCount: 203,
      imageUrl: 'https://example.com/thamel.jpg',
      pricePerHour: 3000.0,
      contactNumber: '+977-1-7777777',
      distance: 0.8,
      availableTimeSlots: [
        '09:00',
        '10:00',
        '11:00',
        '14:00',
        '15:00',
        '16:00',
        '17:00'
      ],
    ),
    Venue(
      id: 'venue-5',
      name: 'Baneshwor Sports Ground',
      address: 'Baneshwor, Kathmandu',
      latitude: 27.7000,
      longitude: 85.3400,
      description: 'Large outdoor soccer field for 11v11 matches',
      sports: ['soccer', 'football'],
      facilities: ['outdoor', 'parking', 'equipment'],
      rating: 4.0,
      reviewCount: 78,
      imageUrl: 'https://example.com/baneshwor.jpg',
      pricePerHour: 1500.0,
      contactNumber: '+977-1-8888888',
      distance: 3.5,
      availableTimeSlots: [
        '06:00',
        '07:00',
        '08:00',
        '16:00',
        '17:00',
        '18:00'
      ],
    ),
    Venue(
      id: 'venue-6',
      name: 'Patan Futsal Center',
      address: 'Patan, Lalitpur',
      latitude: 27.6700,
      longitude: 85.3200,
      description: 'Professional futsal facility with tournament standards',
      sports: ['soccer', 'futsal'],
      facilities: ['indoor', 'parking', 'shower', 'equipment', 'locker'],
      rating: 4.7,
      reviewCount: 134,
      imageUrl: 'https://example.com/patan.jpg',
      pricePerHour: 2800.0,
      contactNumber: '+977-1-9999999',
      distance: 4.2,
      availableTimeSlots: [
        '08:00',
        '09:00',
        '10:00',
        '11:00',
        '14:00',
        '15:00',
        '16:00'
      ],
    ),
    Venue(
      id: 'venue-7',
      name: 'Gongabu Sports Complex',
      address: 'Gongabu, Kathmandu',
      latitude: 27.7300,
      longitude: 85.3500,
      description: 'Multi-court futsal complex with excellent facilities',
      sports: ['soccer', 'futsal', 'basketball'],
      facilities: ['indoor', 'outdoor', 'parking', 'shower', 'equipment'],
      rating: 4.4,
      reviewCount: 95,
      imageUrl: 'https://example.com/gongabu.jpg',
      pricePerHour: 2200.0,
      contactNumber: '+977-1-1010101',
      distance: 5.1,
      availableTimeSlots: [
        '07:00',
        '08:00',
        '09:00',
        '10:00',
        '15:00',
        '16:00',
        '17:00'
      ],
    ),
    Venue(
      id: 'venue-8',
      name: 'Jawalakhel Ground',
      address: 'Jawalakhel, Lalitpur',
      latitude: 27.6800,
      longitude: 85.3000,
      description: 'Historic sports ground with natural grass field',
      sports: ['soccer', 'football'],
      facilities: ['outdoor', 'parking'],
      rating: 3.8,
      reviewCount: 67,
      imageUrl: 'https://example.com/jawalakhel.jpg',
      pricePerHour: 1200.0,
      contactNumber: '+977-1-2020202',
      distance: 3.8,
      availableTimeSlots: [
        '06:00',
        '07:00',
        '08:00',
        '16:00',
        '17:00',
        '18:00'
      ],
    ),
  ];

  @override
  Future<List<Venue>> getNearbyVenues({
    required double latitude,
    required double longitude,
    double radiusInKm = 10.0,
  }) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 800));

    // Filter venues within radius and sort by distance
    final nearbyVenues = _mockVenues
        .where((venue) => venue.distance <= radiusInKm)
        .toList()
      ..sort((a, b) => a.distance.compareTo(b.distance));

    return nearbyVenues;
  }

  @override
  Future<List<Venue>> searchVenues({
    required String query,
    VenueFilters? filters,
  }) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 600));

    var results = _mockVenues.where((venue) {
      // Search by name, address, or description
      final searchText = query.toLowerCase();
      final matchesSearch = venue.name.toLowerCase().contains(searchText) ||
          venue.address.toLowerCase().contains(searchText) ||
          (venue.description?.toLowerCase().contains(searchText) ?? false);

      if (!matchesSearch) return false;

      // Apply filters
      if (filters != null) {
        if (filters.sports.isNotEmpty) {
          final hasMatchingSport =
              filters.sports.any((sport) => venue.sports.contains(sport));
          if (!hasMatchingSport) return false;
        }

        if (filters.facilities.isNotEmpty) {
          final hasMatchingFacility = filters.facilities
              .any((facility) => venue.facilities.contains(facility));
          if (!hasMatchingFacility) return false;
        }

        if (filters.maxPrice != null && venue.pricePerHour != null) {
          if (venue.pricePerHour! > filters.maxPrice!) return false;
        }

        if (filters.minRating != null) {
          if (venue.rating < filters.minRating!) return false;
        }

        if (filters.maxDistance != null) {
          if (venue.distance > filters.maxDistance!) return false;
        }

        if (filters.isAvailable != null) {
          if (venue.isAvailable != filters.isAvailable!) return false;
        }
      }

      return true;
    }).toList();

    // Apply sorting
    if (filters?.sortBy != null) {
      switch (filters!.sortBy) {
        case 'distance':
          results.sort((a, b) => a.distance.compareTo(b.distance));
          break;
        case 'rating':
          results.sort((a, b) => b.rating.compareTo(a.rating));
          break;
        case 'price':
          results.sort(
              (a, b) => (a.pricePerHour ?? 0).compareTo(b.pricePerHour ?? 0));
          break;
        case 'name':
          results.sort((a, b) => a.name.compareTo(b.name));
          break;
      }
    }

    return results;
  }

  @override
  Future<Venue?> getVenueById(String id) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 400));

    try {
      return _mockVenues.firstWhere((venue) => venue.id == id);
    } catch (e) {
      return null;
    }
  }
}
