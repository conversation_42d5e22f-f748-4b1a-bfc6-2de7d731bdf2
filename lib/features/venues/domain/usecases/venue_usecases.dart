import '../entities/venue.dart';
import '../../data/repositories/venue_repository.dart';

class GetNearbyVenuesUseCase {
  final VenueRepository repository;

  GetNearbyVenuesUseCase(this.repository);

  Future<List<Venue>> call({
    required double latitude,
    required double longitude,
    double radiusInKm = 10.0,
  }) async {
    return await repository.getNearbyVenues(
      latitude: latitude,
      longitude: longitude,
      radiusInKm: radiusInKm,
    );
  }
}

class SearchVenuesUseCase {
  final VenueRepository repository;

  SearchVenuesUseCase(this.repository);

  Future<List<Venue>> call({
    required String query,
    VenueFilters? filters,
  }) async {
    return await repository.searchVenues(
      query: query,
      filters: filters,
    );
  }
}

class GetVenueByIdUseCase {
  final VenueRepository repository;

  GetVenueByIdUseCase(this.repository);

  Future<Venue?> call(String id) async {
    return await repository.getVenueById(id);
  }
}

class GetVenueSuggestionsUseCase {
  final VenueRepository repository;

  GetVenueSuggestionsUseCase(this.repository);

  Future<List<Venue>> call({
    required String query,
    int limit = 5,
  }) async {
    if (query.isEmpty) {
      // Return nearby venues as suggestions when query is empty
      return await repository.getNearbyVenues(
        latitude: 27.7172, // Default to Kathmandu center
        longitude: 85.3240,
        radiusInKm: 5.0,
      );
    }

    final results = await repository.searchVenues(
      query: query,
      filters: null,
    );

    return results.take(limit).toList();
  }
}
