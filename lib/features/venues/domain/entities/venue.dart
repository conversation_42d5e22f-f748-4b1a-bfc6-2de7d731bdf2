import 'package:flutter/material.dart';

/// Venue entity representing a sports venue
class Venue {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final String? description;
  final List<String> sports;
  final List<String> facilities;
  final double rating;
  final int reviewCount;
  final String? imageUrl;
  final double? pricePerHour;
  final String? contactNumber;
  final String? website;
  final bool isAvailable;
  final double distance; // Distance from user's location in km
  final List<String> availableTimeSlots;
  final Map<String, dynamic>? amenities;

  const Venue({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    this.description,
    required this.sports,
    required this.facilities,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.imageUrl,
    this.pricePerHour,
    this.contactNumber,
    this.website,
    this.isAvailable = true,
    this.distance = 0.0,
    this.availableTimeSlots = const [],
    this.amenities,
  });

  factory Venue.fromJson(Map<String, dynamic> json) {
    return Venue(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      description: json['description'] as String?,
      sports: List<String>.from(json['sports'] ?? []),
      facilities: List<String>.from(json['facilities'] ?? []),
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: json['reviewCount'] as int? ?? 0,
      imageUrl: json['imageUrl'] as String?,
      pricePerHour: (json['pricePerHour'] as num?)?.toDouble(),
      contactNumber: json['contactNumber'] as String?,
      website: json['website'] as String?,
      isAvailable: json['isAvailable'] as bool? ?? true,
      distance: (json['distance'] as num?)?.toDouble() ?? 0.0,
      availableTimeSlots: List<String>.from(json['availableTimeSlots'] ?? []),
      amenities: json['amenities'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'description': description,
      'sports': sports,
      'facilities': facilities,
      'rating': rating,
      'reviewCount': reviewCount,
      'imageUrl': imageUrl,
      'pricePerHour': pricePerHour,
      'contactNumber': contactNumber,
      'website': website,
      'isAvailable': isAvailable,
      'distance': distance,
      'availableTimeSlots': availableTimeSlots,
      'amenities': amenities,
    };
  }

  Venue copyWith({
    String? id,
    String? name,
    String? address,
    double? latitude,
    double? longitude,
    String? description,
    List<String>? sports,
    List<String>? facilities,
    double? rating,
    int? reviewCount,
    String? imageUrl,
    double? pricePerHour,
    String? contactNumber,
    String? website,
    bool? isAvailable,
    double? distance,
    List<String>? availableTimeSlots,
    Map<String, dynamic>? amenities,
  }) {
    return Venue(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      description: description ?? this.description,
      sports: sports ?? this.sports,
      facilities: facilities ?? this.facilities,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      imageUrl: imageUrl ?? this.imageUrl,
      pricePerHour: pricePerHour ?? this.pricePerHour,
      contactNumber: contactNumber ?? this.contactNumber,
      website: website ?? this.website,
      isAvailable: isAvailable ?? this.isAvailable,
      distance: distance ?? this.distance,
      availableTimeSlots: availableTimeSlots ?? this.availableTimeSlots,
      amenities: amenities ?? this.amenities,
    );
  }

  // Helper methods
  String get displayDistance {
    if (distance < 1) {
      return '${(distance * 1000).round()}m away';
    }
    return '${distance.toStringAsFixed(1)}km away';
  }

  String get displayPrice {
    if (pricePerHour == null) {
      return 'Price not available';
    }
    return '₹${pricePerHour!.toStringAsFixed(0)}/hour';
  }

  String get displayRating {
    return '${rating.toStringAsFixed(1)} (${reviewCount} reviews)';
  }

  bool get hasSoccer =>
      sports.contains('soccer') || sports.contains('football');
  bool get hasFutsal => sports.contains('futsal');
  bool get hasIndoor => facilities.contains('indoor');
  bool get hasOutdoor => facilities.contains('outdoor');
  bool get hasParking => facilities.contains('parking');
  bool get hasShower => facilities.contains('shower');
  bool get hasEquipment => facilities.contains('equipment');

  // Get venue icon based on primary sport
  IconData get venueIcon {
    if (hasSoccer || hasFutsal) {
      return Icons.sports_soccer;
    }
    if (sports.contains('basketball')) {
      return Icons.sports_basketball;
    }
    if (sports.contains('tennis')) {
      return Icons.sports_tennis;
    }
    if (sports.contains('cricket')) {
      return Icons.sports_cricket;
    }
    return Icons.sports;
  }

  // Get venue color based on rating
  Color get ratingColor {
    if (rating >= 4.5) return Colors.green;
    if (rating >= 4.0) return Colors.orange;
    if (rating >= 3.0) return Colors.yellow;
    return Colors.red;
  }
}

/// Venue search filters
class VenueFilters {
  final String? searchQuery;
  final List<String> sports;
  final List<String> facilities;
  final double? maxPrice;
  final double? minRating;
  final double? maxDistance;
  final bool? isAvailable;
  final String? sortBy; // 'distance', 'rating', 'price', 'name'

  const VenueFilters({
    this.searchQuery,
    this.sports = const [],
    this.facilities = const [],
    this.maxPrice,
    this.minRating,
    this.maxDistance,
    this.isAvailable,
    this.sortBy,
  });

  VenueFilters copyWith({
    String? searchQuery,
    List<String>? sports,
    List<String>? facilities,
    double? maxPrice,
    double? minRating,
    double? maxDistance,
    bool? isAvailable,
    String? sortBy,
  }) {
    return VenueFilters(
      searchQuery: searchQuery ?? this.searchQuery,
      sports: sports ?? this.sports,
      facilities: facilities ?? this.facilities,
      maxPrice: maxPrice ?? this.maxPrice,
      minRating: minRating ?? this.minRating,
      maxDistance: maxDistance ?? this.maxDistance,
      isAvailable: isAvailable ?? this.isAvailable,
      sortBy: sortBy ?? this.sortBy,
    );
  }
}
