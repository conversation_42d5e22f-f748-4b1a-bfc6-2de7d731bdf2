import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/venue.dart';
import '../../venues_providers.dart';
import '../../../../utils/color.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;

class SelectedVenuesDisplay extends ConsumerWidget {
  final List<String> selectedVenueIds;
  final VoidCallback? onTap;
  final bool showRemoveButton;
  final Function(String)? onRemoveVenue;

  const SelectedVenuesDisplay({
    Key? key,
    required this.selectedVenueIds,
    this.onTap,
    this.showRemoveButton = false,
    this.onRemoveVenue,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (selectedVenueIds.isEmpty) {
      return _buildEmptyState(ref);
    }

    return Consumer(
      builder: (context, ref, child) {
        final venuesAsync = ref.watch(
          venueSearchProvider('all'),
        );

        return venuesAsync.when(
          data: (allVenues) {
            final selectedVenues = allVenues
                .where((venue) => selectedVenueIds.contains(venue.id))
                .toList();

            if (selectedVenues.isEmpty) {
              return _buildEmptyState(ref);
            }

            return _buildVenuesList(selectedVenues, ref);
          },
          loading: () => _buildLoadingState(ref),
          error: (_, __) => _buildErrorState(ref),
        );
      },
    );
  }

  Widget _buildEmptyState(WidgetRef ref) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
            style: BorderStyle.solid,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: accentColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.location_on_outlined,
                color: accentColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Venues',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 14,
                      fontFamily: 'Gilroy_Medium',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Choose one or more venues for your match',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.5),
                      fontSize: 12,
                      fontFamily: 'Gilroy_Medium',
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.white.withOpacity(0.3),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVenuesList(List<Venue> venues, WidgetRef ref) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Icon(
              Icons.location_on,
              color: accentColor,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              'Selected Venues (${venues.length})',
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 14,
                fontFamily: 'Gilroy_Medium',
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            if (onTap != null)
              GestureDetector(
                onTap: onTap,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: accentColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Edit',
                    style: TextStyle(
                      color: accentColor,
                      fontSize: 12,
                      fontFamily: 'Gilroy_Medium',
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),

        // Venues list
        ...venues.map((venue) => _buildVenueItem(venue, ref)).toList(),
      ],
    );
  }

  Widget _buildVenueItem(Venue venue, WidgetRef ref) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Venue icon
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: accentColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                venue.venueIcon,
                color: accentColor,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),

            // Venue details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    venue.name,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontFamily: 'Gilroy_Medium',
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    venue.address,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.6),
                      fontSize: 12,
                      fontFamily: 'Gilroy_Medium',
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      // Rating
                      Flexible(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.star,
                              color: venue.ratingColor,
                              size: 12,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              venue.rating.toStringAsFixed(1),
                              style: TextStyle(
                                color: venue.ratingColor,
                                fontSize: 11,
                                fontFamily: 'Gilroy_Medium',
                                fontWeight: FontWeight.w600,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Distance
                      Flexible(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.location_on,
                              color: Colors.white.withOpacity(0.4),
                              size: 12,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              venue.displayDistance,
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.6),
                                fontSize: 11,
                                fontFamily: 'Gilroy_Medium',
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Price
                      if (venue.pricePerHour != null)
                        Flexible(
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.attach_money,
                                color: Colors.white.withOpacity(0.4),
                                size: 12,
                              ),
                              const SizedBox(width: 2),
                              Text(
                                venue.displayPrice,
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.6),
                                  fontSize: 11,
                                  fontFamily: 'Gilroy_Medium',
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),

            // Remove button
            if (showRemoveButton && onRemoveVenue != null)
              GestureDetector(
                onTap: () => onRemoveVenue!(venue.id),
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.red,
                    size: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(WidgetRef ref) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: CircularProgressIndicator(
              color: accentColor,
              strokeWidth: 2,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Loading venues...',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 14,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.red.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Failed to load venues',
              style: TextStyle(
                color: Colors.red.withOpacity(0.8),
                fontSize: 14,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
