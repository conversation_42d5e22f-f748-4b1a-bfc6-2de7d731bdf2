import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/venue.dart';
import '../../venues_providers.dart';
import '../../../../utils/color.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;

class VenueSelector extends ConsumerStatefulWidget {
  final List<String> selectedVenueIds;
  final Function(List<String>) onVenuesChanged;
  final String? hint;
  final bool showNearbyVenues;

  const VenueSelector({
    Key? key,
    required this.selectedVenueIds,
    required this.onVenuesChanged,
    this.hint,
    this.showNearbyVenues = true,
  }) : super(key: key);

  @override
  ConsumerState<VenueSelector> createState() => _VenueSelectorState();
}

class _VenueSelectorState extends ConsumerState<VenueSelector>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _isSearching = false;
  String _searchQuery = '';
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, -0.1), end: Offset.zero).animate(
            CurvedAnimation(
                parent: _animationController, curve: Curves.easeOut));

    _animationController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _toggleVenueSelection(String venueId) {
    final newSelection = List<String>.from(widget.selectedVenueIds);
    if (newSelection.contains(venueId)) {
      newSelection.remove(venueId);
    } else {
      newSelection.add(venueId);
    }
    widget.onVenuesChanged(newSelection);
  }

  void _clearSelection() {
    widget.onVenuesChanged([]);
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with selected count
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: accentColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Venues',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontFamily: 'Gilroy_Bold',
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (widget.selectedVenueIds.isNotEmpty)
                  TextButton(
                    onPressed: _clearSelection,
                    child: Text(
                      'Clear All',
                      style: TextStyle(
                        color: accentColor,
                        fontSize: 12,
                        fontFamily: 'Gilroy_Medium',
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),

            // Selected venues display
            if (widget.selectedVenueIds.isNotEmpty) ...[
              _buildSelectedVenuesDisplay(),
              const SizedBox(height: 16),
            ],

            // Search field
            _buildSearchField(),
            const SizedBox(height: 16),

            // Venues list
            Expanded(
              child: _buildVenuesList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchField() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.08),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isSearching
              ? accentColor.withOpacity(0.5)
              : Colors.white.withOpacity(0.1),
        ),
      ),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        onTap: () {
          setState(() {
            _isSearching = true;
          });
        },
        onSubmitted: (_) {
          setState(() {
            _isSearching = false;
          });
          _searchFocusNode.unfocus();
        },
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          hintText: widget.hint ?? 'Search venues...',
          hintStyle: TextStyle(
            color: Colors.white.withOpacity(0.5),
            fontSize: 14,
            fontFamily: 'Gilroy_Medium',
          ),
          prefixIcon: Icon(
            Icons.search,
            color: _isSearching ? accentColor : Colors.white.withOpacity(0.5),
          ),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: Colors.white.withOpacity(0.5),
                  ),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedVenuesDisplay() {
    return Consumer(
      builder: (context, ref, child) {
        final selectedVenuesAsync = ref.watch(
          venueSearchProvider(_searchQuery.isEmpty ? 'all' : _searchQuery),
        );

        return selectedVenuesAsync.when(
          data: (venues) {
            final selectedVenues = venues
                .where((venue) => widget.selectedVenueIds.contains(venue.id))
                .toList();

            if (selectedVenues.isEmpty) return const SizedBox.shrink();

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Venues',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 12,
                    fontFamily: 'Gilroy_Medium',
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: selectedVenues.map((venue) {
                    return _buildVenueChip(venue, true);
                  }).toList(),
                ),
              ],
            );
          },
          loading: () => const SizedBox.shrink(),
          error: (_, __) => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildVenuesList() {
    if (_searchQuery.isEmpty && widget.showNearbyVenues) {
      return Consumer(
        builder: (context, ref, child) {
          final nearbyVenuesAsync = ref.watch(nearbyVenuesProvider);

          return nearbyVenuesAsync.when(
            data: (venues) => _buildVenuesGrid(venues, 'Nearby Venues'),
            loading: () => _buildLoadingState(),
            error: (error, stack) => _buildErrorState(error.toString()),
          );
        },
      );
    } else {
      return Consumer(
        builder: (context, ref, child) {
          final searchResultsAsync = ref.watch(
            venueSearchProvider(_searchQuery.isEmpty ? 'all' : _searchQuery),
          );

          return searchResultsAsync.when(
            data: (venues) => _buildVenuesGrid(
              venues,
              _searchQuery.isEmpty ? 'All Venues' : 'Search Results',
            ),
            loading: () => _buildLoadingState(),
            error: (error, stack) => _buildErrorState(error.toString()),
          );
        },
      );
    }
  }

  Widget _buildVenuesGrid(List<Venue> venues, String title) {
    if (venues.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 14,
            fontFamily: 'Gilroy_Medium',
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Expanded(
          child: ListView.builder(
            itemCount: venues.length,
            itemBuilder: (context, index) {
              final venue = venues[index];
              final isSelected = widget.selectedVenueIds.contains(venue.id);

              return _buildVenueCard(venue, isSelected);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildVenueCard(Venue venue, bool isSelected) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isSelected
            ? accentColor.withOpacity(0.15)
            : Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected
              ? accentColor.withOpacity(0.3)
              : Colors.white.withOpacity(0.1),
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _toggleVenueSelection(venue.id),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Selection indicator
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? accentColor
                          : Colors.white.withOpacity(0.3),
                      width: 2,
                    ),
                    color: isSelected ? accentColor : Colors.transparent,
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      : null,
                ),
                const SizedBox(width: 16),

                // Venue icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: accentColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    venue.venueIcon,
                    color: accentColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // Venue details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        venue.name,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontFamily: 'Gilroy_Bold',
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        venue.address,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 12,
                          fontFamily: 'Gilroy_Medium',
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          // Rating
                          Flexible(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.star,
                                  color: venue.ratingColor,
                                  size: 14,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  venue.rating.toStringAsFixed(1),
                                  style: TextStyle(
                                    color: venue.ratingColor,
                                    fontSize: 12,
                                    fontFamily: 'Gilroy_Medium',
                                    fontWeight: FontWeight.w600,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 16),
                          // Distance
                          Flexible(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.location_on,
                                  color: Colors.white.withOpacity(0.5),
                                  size: 14,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  venue.displayDistance,
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.7),
                                    fontSize: 12,
                                    fontFamily: 'Gilroy_Medium',
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 16),
                          // Price
                          if (venue.pricePerHour != null)
                            Flexible(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.attach_money,
                                    color: Colors.white.withOpacity(0.5),
                                    size: 14,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    venue.displayPrice,
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.7),
                                      fontSize: 12,
                                      fontFamily: 'Gilroy_Medium',
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVenueChip(Venue venue, bool isSelected) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isSelected
            ? accentColor.withOpacity(0.2)
            : Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isSelected ? accentColor : Colors.white.withOpacity(0.2),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            venue.venueIcon,
            color: isSelected ? accentColor : Colors.white.withOpacity(0.7),
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            venue.name,
            style: TextStyle(
              color: isSelected ? accentColor : Colors.white.withOpacity(0.8),
              fontSize: 12,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: () => _toggleVenueSelection(venue.id),
            child: Icon(
              Icons.close,
              color: isSelected ? accentColor : Colors.white.withOpacity(0.5),
              size: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: accentColor,
            strokeWidth: 2,
          ),
          const SizedBox(height: 16),
          Text(
            'Finding venues near you...',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.withOpacity(0.7),
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load venues',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 16,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              color: Colors.white.withOpacity(0.5),
              fontSize: 12,
              fontFamily: 'Gilroy_Medium',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            color: Colors.white.withOpacity(0.3),
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty
                ? 'No venues found nearby'
                : 'No venues match your search',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 16,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty
                ? 'Try expanding your search radius'
                : 'Try different keywords or filters',
            style: TextStyle(
              color: Colors.white.withOpacity(0.5),
              fontSize: 12,
              fontFamily: 'Gilroy_Medium',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
