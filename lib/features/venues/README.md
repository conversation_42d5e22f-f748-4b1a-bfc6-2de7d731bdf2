# Venue Selection Feature

A comprehensive multi-option venue selection system for the NextSportz app, featuring search functionality, nearby venue discovery, and beautiful UI components.

## 🎯 Features

### ✨ Multi-Venue Selection

- Select multiple venues for a single match
- Toggle selection with visual feedback
- Clear all selections with one tap
- Real-time selection count display

### 🔍 Smart Search

- Search venues by name, address, or description
- Real-time search results with debouncing
- Search suggestions and autocomplete
- Filter by sports, facilities, price, and rating

### 📍 Nearby Venues

- Automatic location-based venue discovery
- Distance calculation and sorting
- Default nearby venues when no search query
- Configurable search radius

### 🎨 Beautiful UI

- Modern, animated interface
- Smooth transitions and micro-interactions
- Consistent design language with the app
- Responsive layout for different screen sizes

### 📊 Rich Information Display

- Venue ratings and reviews
- Price per hour information
- Distance from user location
- Available facilities and sports
- Contact information and website links

## 🏗️ Architecture

### Domain Layer

```
lib/features/venues/domain/
├── entities/
│   └── venue.dart              # Venue entity with business logic
├── repositories/
│   └── venue_repository.dart   # Abstract repository interface
└── usecases/
    └── venue_usecases.dart     # Business logic use cases
```

### Data Layer

```
lib/features/venues/data/
└── repositories/
    └── venue_repository.dart   # Repository implementation with mock data
```

### Presentation Layer

```
lib/features/venues/presentation/
├── screens/
│   ├── venues_screen.dart      # Main venues screen
│   └── venue_demo_screen.dart  # Demo screen for showcasing features
└── widgets/
    ├── venue_selector.dart     # Main venue selection widget
    ├── venue_selection_modal.dart # Modal for venue selection
    └── selected_venues_display.dart # Display selected venues
```

## 🚀 Usage

### Basic Venue Selection

```dart
// In your challenge creation form
SelectedVenuesDisplay(
  selectedVenueIds: _selectedVenueIds,
  onTap: () async {
    final selectedVenues = await showVenueSelectionModal(
      context: context,
      selectedVenueIds: _selectedVenueIds,
      title: 'Select Venues',
      hint: 'Search and select venues for your match',
    );
    setState(() {
      _selectedVenueIds = selectedVenues;
    });
  },
)
```

### Advanced Venue Selector

```dart
VenueSelector(
  selectedVenueIds: _selectedVenueIds,
  onVenuesChanged: (venues) {
    setState(() {
      _selectedVenueIds = venues;
    });
  },
  hint: 'Search venues...',
  showNearbyVenues: true,
)
```

### Integration with Challenge Creation

```dart
// Update your CreateChallengeParams
final params = CreateChallengeParams(
  // ... other parameters
  selectedVenueIds: _selectedVenueIds,
  // ... rest of parameters
);
```

## 📱 UI Components

### VenueSelector

The main venue selection widget that provides:

- Search functionality
- Nearby venues display
- Multi-selection with visual feedback
- Loading and error states
- Empty state handling

### VenueSelectionModal

A full-screen modal for venue selection featuring:

- Animated entrance and exit
- Search bar with clear functionality
- Selected venues display
- Confirm/cancel actions
- Responsive design

### SelectedVenuesDisplay

A compact widget for displaying selected venues:

- Venue cards with rich information
- Remove individual venues
- Edit functionality
- Empty state with call-to-action

## 🎨 Design System

### Colors

- Primary: `#DA22FF` (Purple)
- Secondary: `#1A1A1A` (Dark background)
- Accent: `lightblue` (Blue accent)
- Text: White with opacity variations
- Borders: White with low opacity

### Typography

- Font Family: `Gilroy_Bold`, `Gilroy_Medium`
- Headings: 18-22px, Bold weight
- Body: 14-16px, Medium weight
- Captions: 12px, Medium weight

### Spacing

- Consistent 8px grid system
- 12px, 16px, 24px spacing units
- 12px border radius for cards
- 20px border radius for modals

## 🔧 Configuration

### Mock Data

The system includes comprehensive mock data for testing:

- 8 different venues across Kathmandu and Lalitpur
- Various sports facilities (soccer, futsal, basketball)
- Different price ranges and ratings
- Realistic addresses and contact information

### Search Filters

Available filter options:

- Sports: soccer, futsal, basketball, tennis, cricket
- Facilities: indoor, outdoor, parking, shower, equipment
- Price range: configurable maximum price
- Rating: minimum rating filter
- Distance: maximum distance filter
- Availability: available venues only

### Sorting Options

- Distance (nearest first)
- Rating (highest first)
- Price (lowest first)
- Name (alphabetical)

## 🧪 Testing

### Demo Screen

Access the demo screen to test all features:

1. Navigate to Venues screen
2. Tap the "Demo" button
3. Explore all venue selection features
4. Test search, selection, and display functionality

### Integration Testing

The venue selection is integrated into:

- Challenge creation form
- Venues screen
- Demo screen for showcasing

## 🔄 State Management

### Riverpod Providers

```dart
// Repository provider
final venueRepositoryProvider = Provider<VenueRepository>

// Use case providers
final getNearbyVenuesUseCaseProvider = Provider<GetNearbyVenuesUseCase>
final searchVenuesUseCaseProvider = Provider<SearchVenuesUseCase>
final getVenueSuggestionsUseCaseProvider = Provider<GetVenueSuggestionsUseCase>

// State providers
final nearbyVenuesProvider = FutureProvider.autoDispose<List<Venue>>
final venueSearchProvider = FutureProvider.family<List<Venue>, String>
final venueSuggestionsProvider = FutureProvider.family<List<Venue>, String>
```

## 🚀 Future Enhancements

### Planned Features

- [ ] Real API integration
- [ ] Location services integration
- [ ] Venue booking functionality
- [ ] User reviews and ratings
- [ ] Venue photos and galleries
- [ ] Advanced filtering options
- [ ] Venue recommendations
- [ ] Offline support

### Performance Optimizations

- [ ] Image caching for venue photos
- [ ] Lazy loading for large venue lists
- [ ] Search result pagination
- [ ] Optimized distance calculations

## 📝 API Integration

When ready to integrate with real APIs, update the repository implementation:

```dart
class VenueRepositoryImpl implements VenueRepository {
  final ApiClient apiClient;

  VenueRepositoryImpl(this.apiClient);

  @override
  Future<List<Venue>> getNearbyVenues({
    required double latitude,
    required double longitude,
    double radiusInKm = 10.0,
  }) async {
    final response = await apiClient.get('/venues/nearby', queryParameters: {
      'lat': latitude,
      'lng': longitude,
      'radius': radiusInKm,
    });

    return (response.data as List)
        .map((json) => Venue.fromJson(json))
        .toList();
  }

  // ... other methods
}
```

## 🤝 Contributing

When contributing to the venue selection feature:

1. Follow the existing architecture patterns
2. Maintain consistent UI/UX design
3. Add comprehensive tests
4. Update documentation
5. Ensure backward compatibility

## 📄 License

This feature is part of the NextSportz app and follows the same licensing terms.
