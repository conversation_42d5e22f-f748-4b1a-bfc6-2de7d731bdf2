import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/networking/app_error.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/login_with_phone_usecase.dart';
import '../../domain/usecases/register_usecase.dart';
import '../../domain/usecases/verify_otp_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/is_authenticated_usecase.dart';
import '../../domain/usecases/forgot_password_usecase.dart';
import '../../domain/usecases/reset_password_usecase.dart';
import '../../domain/usecases/update_profile_usecase.dart';
import '../../data/repositories/auth_repository_provider.dart';
import 'auth_state.dart';

class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _authRepository;
  final LoginWithPhoneUseCase _loginWithPhoneUseCase;
  final RegisterUseCase _registerUseCase;
  final VerifyOtpUseCase _verifyOtpUseCase;
  final LogoutUseCase _logoutUseCase;
  final IsAuthenticatedUseCase _isAuthenticatedUseCase;
  final ForgotPasswordUseCase _forgotPasswordUseCase;
  final ResetPasswordUseCase _resetPasswordUseCase;
  final UpdateProfileUseCase _updateProfileUseCase;

  AuthNotifier(this._authRepository)
      : _loginWithPhoneUseCase = LoginWithPhoneUseCase(_authRepository),
        _registerUseCase = RegisterUseCase(_authRepository),
        _verifyOtpUseCase = VerifyOtpUseCase(_authRepository),
        _logoutUseCase = LogoutUseCase(_authRepository),
        _isAuthenticatedUseCase = IsAuthenticatedUseCase(_authRepository),
        _forgotPasswordUseCase = ForgotPasswordUseCase(_authRepository),
        _resetPasswordUseCase = ResetPasswordUseCase(_authRepository),
        _updateProfileUseCase = UpdateProfileUseCase(_authRepository),
        super(AuthState.initial());

  Future<bool> loginWithPhone({
    required String phoneNumber,
    required String password,
    required String role,
  }) async {
    state = AuthState.loading();

    final result = await _loginWithPhoneUseCase(
      phoneNumber: phoneNumber,
      password: password,
      role: role,
    );

    return result.fold(
      (error) {
        state = AuthState.error(error.message);
        return false;
      },
      (user) {
        state = AuthState.authenticated(user);
        return true;
      },
    );
  }

  Future<void> logout() async {
    state = AuthState.loading();

    final result = await _logoutUseCase();

    result.fold(
      (error) => state = AuthState.error(error.message),
      (_) => state = AuthState.unauthenticated(),
    );
  }

  Future<void> checkAuthStatus() async {
    state = AuthState.loading();

    final result = await _authRepository.getCurrentUser();

    result.fold(
      (error) {
        state = AuthState.error(error.message);
      },
      (user) {
        if (user != null) {
          state = AuthState.authenticated(user);
        } else {
          state = AuthState.unauthenticated();
        }
      },
    );
  }

  Future<void> checkTokenStatus() async {
    state = AuthState.loading();

    final isAuthenticated = await _isAuthenticatedUseCase();

    if (isAuthenticated.isLeft()) {
      final error =
          isAuthenticated.swap().getOrElse((r) => AppError('Unknown error'));
      state = AuthState.error(error.message);
    } else {
      final hasToken = isAuthenticated.getOrElse((l) => false);
      if (hasToken) {
        // If we have tokens, try to get current user
        await checkAuthStatus();
      } else {
        state = AuthState.unauthenticated();
      }
    }
  }

  Future<bool> register({
    required String name,
    required String email,
    required String phoneNumber,
    required String password,
    required String role,
  }) async {
    state = AuthState.loading();

    final result = await _registerUseCase(
      name: name,
      email: email,
      phoneNumber: phoneNumber,
      password: password,
      role: role,
    );

    return result.fold(
      (error) {
        debugPrint('Registration failed with error: ${error.message}');
        state = AuthState.error(error.message);
        return false;
      },
      (user) {
        debugPrint('Registration successful, navigating to OTP verification');
        // Don't set as authenticated yet - user needs to verify OTP
        state = AuthState.unauthenticated();
        return true;
      },
    );
  }

  Future<bool> verifyOtp({
    required String phoneNumber,
    required String otp,
  }) async {
    state = AuthState.loading();

    final result = await _verifyOtpUseCase(
      phoneNumber: phoneNumber,
      otp: otp,
    );

    return result.fold(
      (error) {
        state = AuthState.error(error.message);
        return false;
      },
      (user) {
        // Don't set as authenticated - user needs to login separately
        state = AuthState.unauthenticated();
        return true;
      },
    );
  }

  Future<bool> forgotPassword({required String email}) async {
    state = AuthState.loading();

    final result = await _forgotPasswordUseCase(email: email);

    return result.fold(
      (error) {
        state = AuthState.error(error.message);
        return false;
      },
      (_) {
        state = AuthState.unauthenticated();
        return true;
      },
    );
  }

  void clearError() {
    if (state.status == AuthStatus.error) {
      state = AuthState.unauthenticated();
    }
  }
}

final authNotifierProvider = StateNotifierProvider<AuthNotifier, AuthState>((
  ref,
) {
  final authRepository = ref.read(authRepositoryProvider);
  return AuthNotifier(authRepository);
});
