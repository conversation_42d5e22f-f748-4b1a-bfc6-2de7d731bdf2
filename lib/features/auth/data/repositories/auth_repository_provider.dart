import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../datasources/auth_datasource.dart';

import '../datasources/auth_remote_datasource.dart';
import '../../../../core/networking/api_client.dart';
import '../../../../core/local/token_storage.dart';
import 'auth_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';

/// Provider for the auth datasource
final authDatasourceProvider = Provider<AuthDatasource>((ref) {
  // For now, use local datasource for development
  // In production, you can switch to remote datasource
  // return AuthLocalDataSource();

  // Uncomment the following lines to use remote datasource
  final apiClient = ref.read(apiClientProvider);
  return AuthRemoteDataSource(apiClient);
});

/// Provider for the auth repository
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final authDatasource = ref.read(authDatasourceProvider);
  final tokenStorage = ref.read(tokenStorageProvider);
  return AuthRepositoryImpl(authDatasource, tokenStorage);
});
