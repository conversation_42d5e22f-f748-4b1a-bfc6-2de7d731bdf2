/// Generic API response models for the auth feature

/// Base API response model
class ApiResponse<T> {
  final bool? success;
  final String? message;
  final T? data;
  final int? statusCode;
  final Map<String, dynamic>? errors;

  const ApiResponse({
    this.success,
    this.message,
    this.data,
    this.statusCode,
    this.errors,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
      'statusCode': statusCode,
      'errors': errors,
    };
  }

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] as bool?,
      message: json['message'] as String?,
      data:
          json['data'] != null
              ? fromJsonT(json['data'] as Map<String, dynamic>)
              : null,
      statusCode: json['statusCode'] as int?,
      errors: json['errors'] as Map<String, dynamic>?,
    );
  }

  ApiResponse<T> copyWith({
    bool? success,
    String? message,
    T? data,
    int? statusCode,
    Map<String, dynamic>? errors,
  }) {
    return ApiResponse<T>(
      success: success ?? this.success,
      message: message ?? this.message,
      data: data ?? this.data,
      statusCode: statusCode ?? this.statusCode,
      errors: errors ?? this.errors,
    );
  }
}

/// Paginated API response model
class PaginatedResponse<T> {
  final List<T>? data;
  final int? currentPage;
  final int? totalPages;
  final int? totalItems;
  final int? itemsPerPage;
  final bool? hasNextPage;
  final bool? hasPreviousPage;

  const PaginatedResponse({
    this.data,
    this.currentPage,
    this.totalPages,
    this.totalItems,
    this.itemsPerPage,
    this.hasNextPage,
    this.hasPreviousPage,
  });

  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'currentPage': currentPage,
      'totalPages': totalPages,
      'totalItems': totalItems,
      'itemsPerPage': itemsPerPage,
      'hasNextPage': hasNextPage,
      'hasPreviousPage': hasPreviousPage,
    };
  }

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return PaginatedResponse<T>(
      data:
          json['data'] != null
              ? (json['data'] as List<dynamic>)
                  .map((item) => fromJsonT(item as Map<String, dynamic>))
                  .toList()
              : null,
      currentPage: json['currentPage'] as int?,
      totalPages: json['totalPages'] as int?,
      totalItems: json['totalItems'] as int?,
      itemsPerPage: json['itemsPerPage'] as int?,
      hasNextPage: json['hasNextPage'] as bool?,
      hasPreviousPage: json['hasPreviousPage'] as bool?,
    );
  }

  PaginatedResponse<T> copyWith({
    List<T>? data,
    int? currentPage,
    int? totalPages,
    int? totalItems,
    int? itemsPerPage,
    bool? hasNextPage,
    bool? hasPreviousPage,
  }) {
    return PaginatedResponse<T>(
      data: data ?? this.data,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalItems: totalItems ?? this.totalItems,
      itemsPerPage: itemsPerPage ?? this.itemsPerPage,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      hasPreviousPage: hasPreviousPage ?? this.hasPreviousPage,
    );
  }
}

/// Validation error response model
class ValidationErrorResponse {
  final bool? success;
  final String? message;
  final Map<String, List<String>>? errors;

  const ValidationErrorResponse({this.success, this.message, this.errors});

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message, 'errors': errors};
  }

  factory ValidationErrorResponse.fromJson(Map<String, dynamic> json) {
    Map<String, List<String>>? errors;
    if (json['errors'] != null) {
      errors = <String, List<String>>{};
      (json['errors'] as Map<String, dynamic>).forEach((key, value) {
        if (value is List) {
          errors![key] = value.cast<String>();
        }
      });
    }

    return ValidationErrorResponse(
      success: json['success'] as bool?,
      message: json['message'] as String?,
      errors: errors,
    );
  }

  ValidationErrorResponse copyWith({
    bool? success,
    String? message,
    Map<String, List<String>>? errors,
  }) {
    return ValidationErrorResponse(
      success: success ?? this.success,
      message: message ?? this.message,
      errors: errors ?? this.errors,
    );
  }
}

/// Network error response model
class NetworkErrorResponse {
  final bool? success;
  final String? message;
  final String? error;
  final int? statusCode;
  final String? requestId;

  const NetworkErrorResponse({
    this.success,
    this.message,
    this.error,
    this.statusCode,
    this.requestId,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'error': error,
      'statusCode': statusCode,
      'requestId': requestId,
    };
  }

  factory NetworkErrorResponse.fromJson(Map<String, dynamic> json) {
    return NetworkErrorResponse(
      success: json['success'] as bool?,
      message: json['message'] as String?,
      error: json['error'] as String?,
      statusCode: json['statusCode'] as int?,
      requestId: json['requestId'] as String?,
    );
  }

  NetworkErrorResponse copyWith({
    bool? success,
    String? message,
    String? error,
    int? statusCode,
    String? requestId,
  }) {
    return NetworkErrorResponse(
      success: success ?? this.success,
      message: message ?? this.message,
      error: error ?? this.error,
      statusCode: statusCode ?? this.statusCode,
      requestId: requestId ?? this.requestId,
    );
  }
}
