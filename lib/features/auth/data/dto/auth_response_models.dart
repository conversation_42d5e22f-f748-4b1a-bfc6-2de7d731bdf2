/// Authentication response models for the auth feature

/// Player ID model for user profile
class PlayerId {
  final String gameType;
  final String id;

  const PlayerId({
    required this.gameType,
    required this.id,
  });

  Map<String, dynamic> toJson() {
    return {
      'gameType': gameType,
      'id': id,
    };
  }

  factory PlayerId.fromJson(Map<String, dynamic> json) {
    return PlayerId(
      gameType: json['gameType'] as String,
      id: json['id'] as String,
    );
  }

  PlayerId copyWith({
    String? gameType,
    String? id,
  }) {
    return PlayerId(
      gameType: gameType ?? this.gameType,
      id: id ?? this.id,
    );
  }
}

/// User profile response model
class UserProfileResponse {
  final String? userId;
  final String? email;
  final String? phoneNumber;
  final String? fullName;
  final String? role;
  final DateTime? dateOfBirth;
  final List<PlayerId>? playerIds;
  final List<String>? roles;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserProfileResponse({
    this.userId,
    this.email,
    this.phoneNumber,
    this.fullName,
    this.role,
    this.dateOfBirth,
    this.playerIds,
    this.roles,
    this.createdAt,
    this.updatedAt,
  });

  /// Get the Football player ID if available
  String? get footballPlayerId {
    if (playerIds == null) return null;
    final footballPlayer = playerIds!.firstWhere(
      (player) => player.gameType == 'Football',
      orElse: () => const PlayerId(gameType: '', id: ''),
    );
    return footballPlayer.gameType.isNotEmpty ? footballPlayer.id : null;
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'email': email,
      'phoneNumber': phoneNumber,
      'fullName': fullName,
      'role': role,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'playerIds': playerIds?.map((e) => e.toJson()).toList(),
      'roles': roles,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory UserProfileResponse.fromJson(Map<String, dynamic> json) {
    // Handle the case where user data is nested in 'data' field
    Map<String, dynamic> userData = json;
    if (json['data'] != null && json['data'] is Map<String, dynamic>) {
      userData = json['data'] as Map<String, dynamic>;
    }

    // Handle UUID string as userId (don't try to parse as int)
    final userId = userData['id']?.toString();

    // Parse playerIds
    List<PlayerId>? playerIds;
    if (userData['playerIds'] != null && userData['playerIds'] is List) {
      playerIds = (userData['playerIds'] as List)
          .map((e) => PlayerId.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    // Parse roles
    List<String>? roles;
    if (userData['roles'] != null && userData['roles'] is List) {
      roles = (userData['roles'] as List).cast<String>();
    }

    return UserProfileResponse(
      userId: userId,
      email: userData['email'] as String?,
      phoneNumber: userData['phoneNumber'] as String?,
      fullName: userData['fullName'] as String?,
      role: userData['selectedRole'] as String? ?? userData['role'] as String?,
      dateOfBirth: userData['dateOfBirth'] != null
          ? DateTime.parse(userData['dateOfBirth'] as String)
          : null,
      playerIds: playerIds,
      roles: roles,
      createdAt: userData['createdAt'] != null
          ? DateTime.parse(userData['createdAt'] as String)
          : null,
      updatedAt: userData['updatedAt'] != null
          ? DateTime.parse(userData['updatedAt'] as String)
          : null,
    );
  }

  UserProfileResponse copyWith({
    String? userId,
    String? email,
    String? phoneNumber,
    String? fullName,
    String? role,
    DateTime? dateOfBirth,
    List<PlayerId>? playerIds,
    List<String>? roles,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfileResponse(
      userId: userId ?? this.userId,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      fullName: fullName ?? this.fullName,
      role: role ?? this.role,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      playerIds: playerIds ?? this.playerIds,
      roles: roles ?? this.roles,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Authentication token response model
class AuthTokenResponse {
  final String? accessToken;
  final String? refreshToken;

  const AuthTokenResponse({this.accessToken, this.refreshToken});

  Map<String, dynamic> toJson() {
    return {'accessToken': accessToken, 'refreshToken': refreshToken};
  }

  factory AuthTokenResponse.fromJson(Map<String, dynamic> json) {
    return AuthTokenResponse(
      accessToken: json['accessToken'] as String?,
      refreshToken: json['refreshToken'] as String?,
    );
  }

  AuthTokenResponse copyWith({String? accessToken, String? refreshToken}) {
    return AuthTokenResponse(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
    );
  }
}

/// Login response model
class LoginResponse {
  final AuthTokenResponse? tokens;
  final UserProfileResponse? user;

  const LoginResponse({this.tokens, this.user});

  Map<String, dynamic> toJson() {
    return {'tokens': tokens?.toJson(), 'user': user?.toJson()};
  }

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    // Handle the actual API response structure
    Map<String, dynamic>? tokensData;
    Map<String, dynamic>? userData;

    // Check if tokens are in the data field (actual API response)
    if (json['data'] != null && json['data'] is Map<String, dynamic>) {
      final data = json['data'] as Map<String, dynamic>;
      if (data['accessToken'] != null || data['refreshToken'] != null) {
        tokensData = data;
      }
      // Check if user data is also in the data field (though login typically doesn't return user data)
      if (data['userId'] != null || data['fullName'] != null) {
        userData = data;
      }
    }

    // Fallback to the expected structure
    if (tokensData == null && json['tokens'] != null) {
      tokensData = json['tokens'] as Map<String, dynamic>;
    }
    if (userData == null && json['user'] != null) {
      userData = json['user'] as Map<String, dynamic>;
    }

    // For login, we typically only get tokens, not user data
    return LoginResponse(
      tokens:
          tokensData != null ? AuthTokenResponse.fromJson(tokensData) : null,
      user: userData != null ? UserProfileResponse.fromJson(userData) : null,
    );
  }

  LoginResponse copyWith({
    AuthTokenResponse? tokens,
    UserProfileResponse? user,
  }) {
    return LoginResponse(
      tokens: tokens ?? this.tokens,
      user: user ?? this.user,
    );
  }
}

/// Success response model
class SuccessResponse {
  final bool? success;
  final String? message;
  final dynamic data; // Changed to dynamic to handle both string and map
  final String? error; // Added error field to match API response
  final dynamic
      validationErrors; // Added validationErrors field to match API response

  const SuccessResponse(
      {this.success,
      this.message,
      this.data,
      this.error,
      this.validationErrors});

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
      'error': error,
      'validationErrors': validationErrors
    };
  }

  factory SuccessResponse.fromJson(Map<String, dynamic> json) {
    return SuccessResponse(
      success: json['success'] as bool?,
      message: json['message'] as String?,
      data: json['data'], // Don't cast, keep as dynamic
      error: json['error'] as String?,
      validationErrors: json['validationErrors'],
    );
  }

  SuccessResponse copyWith({
    bool? success,
    String? message,
    dynamic data,
    String? error,
    dynamic validationErrors,
  }) {
    return SuccessResponse(
      success: success ?? this.success,
      message: message ?? this.message,
      data: data ?? this.data,
      error: error ?? this.error,
      validationErrors: validationErrors ?? this.validationErrors,
    );
  }
}

/// Error response model
class ErrorResponse {
  final bool? success;
  final String? message;
  final String? error;
  final int? statusCode;

  const ErrorResponse({
    this.success,
    this.message,
    this.error,
    this.statusCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'error': error,
      'statusCode': statusCode,
    };
  }

  factory ErrorResponse.fromJson(Map<String, dynamic> json) {
    return ErrorResponse(
      success: json['success'] as bool?,
      message: json['message'] as String?,
      error: json['error'] as String?,
      statusCode: json['statusCode'] as int?,
    );
  }

  ErrorResponse copyWith({
    bool? success,
    String? message,
    String? error,
    int? statusCode,
  }) {
    return ErrorResponse(
      success: success ?? this.success,
      message: message ?? this.message,
      error: error ?? this.error,
      statusCode: statusCode ?? this.statusCode,
    );
  }
}
