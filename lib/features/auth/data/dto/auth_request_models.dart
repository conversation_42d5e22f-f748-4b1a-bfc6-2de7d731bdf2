/// Authentication request models for the auth feature

/// Register request model
class RegisterRequest {
  final String? fullName;
  final String? email;
  final String? password;
  final String? role;
  final String? phoneNumber;

  const RegisterRequest({
    this.fullName,
    this.email,
    this.password,
    this.role,
    this.phoneNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'email': email,
      'password': password,
      'role': role,
      'phoneNumber': phoneNumber,
    };
  }

  factory RegisterRequest.fromJson(Map<String, dynamic> json) {
    return RegisterRequest(
      fullName: json['fullName'] as String?,
      email: json['email'] as String?,
      password: json['password'] as String?,
      role: json['role'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
    );
  }

  RegisterRequest copyWith({
    String? fullName,
    String? email,
    String? password,
    String? role,
    String? phoneNumber,
  }) {
    return RegisterRequest(
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      password: password ?? this.password,
      role: role ?? this.role,
      phoneNumber: phoneNumber ?? this.phoneNumber,
    );
  }
}

/// Login request model
class LoginRequest {
  final String? phoneNumber;
  final String? password;
  final String? role;

  const LoginRequest({this.phoneNumber, this.password, this.role});

  Map<String, dynamic> toJson() {
    return {'phoneNumber': phoneNumber, 'password': password, 'role': role};
  }

  factory LoginRequest.fromJson(Map<String, dynamic> json) {
    return LoginRequest(
      phoneNumber: json['phoneNumber'] as String?,
      password: json['password'] as String?,
      role: json['role'] as String?,
    );
  }

  LoginRequest copyWith({String? phoneNumber, String? password, String? role}) {
    return LoginRequest(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      password: password ?? this.password,
      role: role ?? this.role,
    );
  }
}

/// Register verify request model (for OTP verification)
class RegisterVerifyRequest {
  final String? phoneNumber;
  final String? otp;

  const RegisterVerifyRequest({this.phoneNumber, this.otp});

  Map<String, dynamic> toJson() {
    return {'phoneNumber': phoneNumber, 'otp': otp};
  }

  factory RegisterVerifyRequest.fromJson(Map<String, dynamic> json) {
    return RegisterVerifyRequest(
      phoneNumber: json['phoneNumber'] as String?,
      otp: json['otp'] as String?,
    );
  }

  RegisterVerifyRequest copyWith({String? phoneNumber, String? otp}) {
    return RegisterVerifyRequest(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      otp: otp ?? this.otp,
    );
  }
}

/// Forgot password request model
class ForgotPasswordRequest {
  final String? email;

  const ForgotPasswordRequest({this.email});

  Map<String, dynamic> toJson() {
    return {'email': email};
  }

  factory ForgotPasswordRequest.fromJson(Map<String, dynamic> json) {
    return ForgotPasswordRequest(email: json['email'] as String?);
  }

  ForgotPasswordRequest copyWith({String? email}) {
    return ForgotPasswordRequest(email: email ?? this.email);
  }
}

/// Reset password request model
class ResetPasswordRequest {
  final String? email;
  final String? newPassword;
  final String? confirmPassword;
  final String? token;

  const ResetPasswordRequest({
    this.email,
    this.newPassword,
    this.confirmPassword,
    this.token,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'newPassword': newPassword,
      'confirmPassword': confirmPassword,
      'token': token,
    };
  }

  factory ResetPasswordRequest.fromJson(Map<String, dynamic> json) {
    return ResetPasswordRequest(
      email: json['email'] as String?,
      newPassword: json['newPassword'] as String?,
      confirmPassword: json['confirmPassword'] as String?,
      token: json['token'] as String?,
    );
  }

  ResetPasswordRequest copyWith({
    String? email,
    String? newPassword,
    String? confirmPassword,
    String? token,
  }) {
    return ResetPasswordRequest(
      email: email ?? this.email,
      newPassword: newPassword ?? this.newPassword,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      token: token ?? this.token,
    );
  }
}

/// Update profile request model
class UpdateProfileRequest {
  final String? fullName;
  final String? email;
  final String? photoUrl;

  const UpdateProfileRequest({this.fullName, this.email, this.photoUrl});

  Map<String, dynamic> toJson() {
    return {'fullName': fullName, 'email': email, 'profileImage': photoUrl};
  }

  factory UpdateProfileRequest.fromJson(Map<String, dynamic> json) {
    return UpdateProfileRequest(
      fullName: json['fullName'] as String?,
      email: json['email'] as String?,
      photoUrl: json['profileImage'] as String?,
    );
  }

  UpdateProfileRequest copyWith({
    String? name,
    String? email,
    String? profileImage,
  }) {
    return UpdateProfileRequest(
      fullName: name ?? this.fullName,
      email: email ?? this.email,
      photoUrl: profileImage ?? this.photoUrl,
    );
  }
}
