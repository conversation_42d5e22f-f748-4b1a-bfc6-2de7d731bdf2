import '../dto/auth_request_models.dart';
import '../dto/auth_response_models.dart';
import 'auth_datasource.dart';

/// Local data source implementation for authentication operations (mock data)
class AuthLocalDataSource implements AuthDatasource {
  @override
  Future<SuccessResponse> register(RegisterRequest request) async {
    await Future.delayed(const Duration(seconds: 1));
    return const SuccessResponse(
      success: true,
      message: 'Registration successful. Please verify your phone number.',
    );
  }

  @override
  Future<SuccessResponse> verifyOtp(RegisterVerifyRequest request) async {
    await Future.delayed(const Duration(seconds: 1));
    return const SuccessResponse(
      success: true,
      message: 'Phone number verified successfully.',
    );
  }

  @override
  Future<LoginResponse> login(LoginRequest request) async {
    await Future.delayed(const Duration(seconds: 1));

    // Mock successful login response
    return LoginResponse(
      tokens: const AuthTokenResponse(
        accessToken:
            'DEV_TOKEN_68787A18473D4CB207D3983929CDB4D8D16C9C80573549CEF3EC83C847CACD3BE9E5FED0C3A55C257E5D1B9A28D1C9019C72DF84AD457EDB70A48A5412FEF766',
        refreshToken: 'DEV_REFRESH_TOKEN_123456789',
      ),
      user: UserProfileResponse(
        userId: "123",
        email: '<EMAIL>',
        phoneNumber: request.phoneNumber ?? '9876543210',
        fullName: 'Local Dev User',
        role: request.role ?? 'PLAYER',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
      ),
    );
  }

  @override
  Future<UserProfileResponse> getCurrentUser() async {
    await Future.delayed(const Duration(seconds: 1));

    return UserProfileResponse(
      userId: "123",
      email: '<EMAIL>',
      phoneNumber: '9876543210',
      fullName: 'Local Dev User',
      role: 'PLAYER',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    );
  }

  @override
  Future<AuthTokenResponse> refreshToken(String refreshToken) async {
    await Future.delayed(const Duration(seconds: 1));
    return const AuthTokenResponse(
      accessToken: 'NEW_DEV_TOKEN_987654321',
      refreshToken: 'NEW_DEV_REFRESH_TOKEN_987654321',
    );
  }

  @override
  Future<SuccessResponse> logout({String? refreshToken}) async {
    await Future.delayed(const Duration(seconds: 1));
    return const SuccessResponse(
      success: true,
      message: 'Logged out successfully.',
    );
  }

  @override
  Future<SuccessResponse> forgotPassword(ForgotPasswordRequest request) async {
    await Future.delayed(const Duration(seconds: 1));
    return const SuccessResponse(
      success: true,
      message: 'Password reset link sent to your email.',
    );
  }

  @override
  Future<SuccessResponse> resetPassword(ResetPasswordRequest request) async {
    await Future.delayed(const Duration(seconds: 1));
    return const SuccessResponse(
      success: true,
      message: 'Password reset successfully.',
    );
  }

  @override
  Future<SuccessResponse> updateProfile(UpdateProfileRequest request) async {
    await Future.delayed(const Duration(seconds: 1));
    return const SuccessResponse(
      success: true,
      message: 'Profile updated successfully.',
    );
  }
}
