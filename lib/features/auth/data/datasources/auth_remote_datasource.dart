import 'package:nextsportz_v2/core/networking/api_client.dart';
import 'package:nextsportz_v2/core/networking/api_const.dart';

import '../dto/auth_request_models.dart';
import '../dto/auth_response_models.dart';
import 'auth_datasource.dart';

/// Remote data source implementation for authentication operations
class AuthRemoteDataSource implements AuthDatasource {
  final ApiClient apiClient;

  AuthRemoteDataSource(this.apiClient);

  @override
  Future<SuccessResponse> register(RegisterRequest request) async {
    final response = await apiClient.post(
      ApiConst.registerEndpoint,
      data: request.toJson(),
    );
    return _handleSuccessResponse(response);
  }

  @override
  Future<SuccessResponse> verifyOtp(RegisterVerifyRequest request) async {
    final response = await apiClient.post(
      ApiConst.verifyOtpEndpoint,
      data: request.toJson(),
    );
    return _handleSuccessResponse(response);
  }

  @override
  Future<LoginResponse> login(LoginRequest request) async {
    final response = await apiClient.post(
      ApiConst.loginEndpoint,
      data: request.toJson(),
    );
    return _handleLoginResponse(response);
  }

  @override
  Future<UserProfileResponse> getCurrentUser() async {
    final response = await apiClient.get(ApiConst.meEndpoint);
    return _handleUserProfileResponse(response);
  }

  @override
  Future<AuthTokenResponse> refreshToken(String refreshToken) async {
    final response = await apiClient.post(
      ApiConst.refreshTokenEndpoint,
      data: {'refreshToken': refreshToken},
    );
    return _handleTokenResponse(response);
  }

  @override
  Future<SuccessResponse> logout({String? refreshToken}) async {
    final data = refreshToken != null ? {'refreshToken': refreshToken} : null;
    final response = await apiClient.post(
      ApiConst.logoutEndpoint,
      data: data,
    );

    return _handleSuccessResponse(response);
  }

  @override
  Future<SuccessResponse> forgotPassword(ForgotPasswordRequest request) async {
    final response = await apiClient.post(
      ApiConst.forgotPasswordEndpoint,
      data: request.toJson(),
    );

    return _handleSuccessResponse(response);
  }

  @override
  Future<SuccessResponse> resetPassword(ResetPasswordRequest request) async {
    final response = await apiClient.post(
      ApiConst.resetPasswordEndpoint,
      data: request.toJson(),
    );

    return _handleSuccessResponse(response);
  }

  @override
  Future<SuccessResponse> updateProfile(UpdateProfileRequest request) async {
    final response = await apiClient.put(
      ApiConst.updateProfileEndpoint,
      data: request.toJson(),
    );

    return _handleSuccessResponse(response);
  }

  // Helper methods for handling responses
  SuccessResponse _handleSuccessResponse(Map<String, dynamic> response) {
    return SuccessResponse.fromJson(response);
  }

  LoginResponse _handleLoginResponse(Map<String, dynamic> response) {
    return LoginResponse.fromJson(response);
  }

  UserProfileResponse _handleUserProfileResponse(
    Map<String, dynamic> response,
  ) {
    return UserProfileResponse.fromJson(response);
  }

  AuthTokenResponse _handleTokenResponse(Map<String, dynamic> response) {
    return AuthTokenResponse.fromJson(response);
  }
}
