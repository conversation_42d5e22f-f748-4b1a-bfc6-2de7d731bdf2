import '../dto/auth_request_models.dart';
import '../dto/auth_response_models.dart';

/// Abstract class defining the contract for authentication data operations
abstract class AuthDatasource {
  /// Register a new user
  Future<SuccessResponse> register(RegisterRequest request);

  /// Verify OTP after registration
  Future<SuccessResponse> verifyOtp(RegisterVerifyRequest request);

  /// Login with phone number, password, and role
  Future<LoginResponse> login(LoginRequest request);

  /// Get current user details
  Future<UserProfileResponse> getCurrentUser();

  /// Refresh authentication token
  Future<AuthTokenResponse> refreshToken(String refreshToken);

  /// Logout user
  Future<SuccessResponse> logout({String? refreshToken});

  /// Send forgot password email
  Future<SuccessResponse> forgotPassword(ForgotPasswordRequest request);

  /// Reset password using token from email
  Future<SuccessResponse> resetPassword(ResetPasswordRequest request);

  /// Update user profile
  Future<SuccessResponse> updateProfile(UpdateProfileRequest request);
}
