import 'package:fpdart/fpdart.dart';
import '../repositories/auth_repository.dart';
import '../../../../core/networking/app_error.dart';

class VerifyOtpUseCase {
  final AuthRepository _authRepository;

  VerifyOtpUseCase(this._authRepository);

  Future<Either<AppError, void>> call({
    required String phoneNumber,
    required String otp,
  }) async {
    return await _authRepository.verifyOtp(
      phoneNumber: phoneNumber,
      otp: otp,
    );
  }
}
