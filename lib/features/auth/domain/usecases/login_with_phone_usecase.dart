import 'package:fpdart/fpdart.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';
import '../../../../core/networking/app_error.dart';

class LoginWithPhoneUseCase {
  final AuthRepository _authRepository;

  LoginWithPhoneUseCase(this._authRepository);

  Future<Either<AppError, User>> call({
    required String phoneNumber,
    required String password,
    required String role,
  }) async {
    return await _authRepository.loginWithPhone(
      phoneNumber: phoneNumber,
      password: password,
      role: role,
    );
  }
}
