import 'package:fpdart/fpdart.dart';
import '../repositories/auth_repository.dart';
import '../../../../core/networking/app_error.dart';

class UpdateProfileUseCase {
  final AuthRepository _authRepository;

  UpdateProfileUseCase(this._authRepository);

  Future<Either<AppError, void>> call({
    required String name,
    required String email,
    String? profileImage,
  }) async {
    return await _authRepository.updateProfile(
      name: name,
      email: email,
      profileImage: profileImage,
    );
  }
}
