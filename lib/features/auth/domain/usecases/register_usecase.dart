import 'package:fpdart/fpdart.dart';
import '../repositories/auth_repository.dart';
import '../../../../core/networking/app_error.dart';

class RegisterUseCase {
  final AuthRepository _authRepository;

  RegisterUseCase(this._authRepository);

  Future<Either<AppError, void>> call({
    required String name,
    required String email,
    required String phoneNumber,
    required String password,
    required String role,
  }) async {
    return await _authRepository.register(
      name: name,
      email: email,
      phoneNumber: phoneNumber,
      password: password,
      role: role,
    );
  }
}
