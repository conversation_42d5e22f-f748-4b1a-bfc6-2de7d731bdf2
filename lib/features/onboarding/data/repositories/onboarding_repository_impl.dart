import 'package:fpdart/fpdart.dart';
import '../../domain/entities/onboarding_data.dart';
import '../../domain/repositories/onboarding_repository.dart';

class MockOnboardingRepositoryImpl implements OnboardingRepository {
  bool _isCompleted = false;
  int _currentPage = 0;

  @override
  Future<Either<String, bool>> isOnboardingCompleted() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return Right(_isCompleted);
  }

  @override
  Future<Either<String, void>> markOnboardingCompleted() async {
    await Future.delayed(const Duration(milliseconds: 500));
    _isCompleted = true;
    return const Right(null);
  }

  @override
  Future<Either<String, OnboardingState>> getOnboardingState() async {
    await Future.delayed(const Duration(milliseconds: 500));

    final state = OnboardingState.initial().copyWith(
      currentPage: _currentPage,
      isCompleted: _isCompleted,
    );

    return Right(state);
  }

  @override
  Future<Either<String, void>> updateOnboardingProgress(int currentPage) async {
    await Future.delayed(const Duration(milliseconds: 300));
    _currentPage = currentPage;
    return const Right(null);
  }
}

