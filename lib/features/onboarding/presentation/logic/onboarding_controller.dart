import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/onboarding_data.dart';
import '../../domain/repositories/onboarding_repository.dart';
import '../../domain/usecases/check_onboarding_status_usecase.dart';
import '../../data/repositories/onboarding_repository_impl.dart';

class OnboardingNotifier extends StateNotifier<OnboardingState> {
  final OnboardingRepository _onboardingRepository;
  final CheckOnboardingStatusUseCase _checkOnboardingStatusUseCase;

  OnboardingNotifier(this._onboardingRepository)
    : _checkOnboardingStatusUseCase = CheckOnboardingStatusUseCase(
        _onboardingRepository,
      ),
      super(OnboardingState.initial());

  Future<void> checkOnboardingStatus() async {
    final result = await _checkOnboardingStatusUseCase();

    result.fold(
      (error) => state = state.copyWith(isCompleted: false),
      (isCompleted) => state = state.copyWith(isCompleted: isCompleted),
    );
  }

  Future<void> nextPage() async {
    if (state.currentPage < state.pages.length - 1) {
      final newPage = state.currentPage + 1;
      await _onboardingRepository.updateOnboardingProgress(newPage);
      state = state.copyWith(currentPage: newPage);
    }
  }

  Future<void> previousPage() async {
    if (state.currentPage > 0) {
      final newPage = state.currentPage - 1;
      await _onboardingRepository.updateOnboardingProgress(newPage);
      state = state.copyWith(currentPage: newPage);
    }
  }

  Future<void> completeOnboarding() async {
    final result = await _onboardingRepository.markOnboardingCompleted();

    result.fold(
      (error) => null, // Handle error if needed
      (_) => state = state.copyWith(isCompleted: true),
    );
  }

  void goToPage(int page) {
    if (page >= 0 && page < state.pages.length) {
      state = state.copyWith(currentPage: page);
    }
  }

  bool get isLastPage => state.currentPage == state.pages.length - 1;
  bool get isFirstPage => state.currentPage == 0;
  OnboardingData get currentPageData => state.pages[state.currentPage];
}

final onboardingNotifierProvider =
    StateNotifierProvider<OnboardingNotifier, OnboardingState>((ref) {
      return OnboardingNotifier(MockOnboardingRepositoryImpl());
    });

