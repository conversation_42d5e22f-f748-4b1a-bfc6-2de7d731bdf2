class OnboardingData {
  final String title;
  final String description;
  final String imagePath;
  final bool isLastPage;

  const OnboardingData({
    required this.title,
    required this.description,
    required this.imagePath,
    this.isLastPage = false,
  });
}

class OnboardingState {
  final int currentPage;
  final bool isCompleted;
  final List<OnboardingData> pages;

  const OnboardingState({
    required this.currentPage,
    required this.isCompleted,
    required this.pages,
  });

  factory OnboardingState.initial() {
    return OnboardingState(
      currentPage: 0,
      isCompleted: false,
      pages: [
        const OnboardingData(
          title: 'Welcome to NextSportz',
          description:
              'Connect with soccer players, join tournaments, and find the perfect venue for your next match.',
          imagePath: 'assets/images/onboarding_1.png',
        ),
        const OnboardingData(
          title: 'Find Your Perfect Match',
          description:
              'Challenge other players, join teams, and participate in exciting tournaments.',
          imagePath: 'assets/images/onboarding_2.png',
        ),
        const OnboardingData(
          title: 'Book Venues Easily',
          description:
              'Discover and book soccer venues near you with just a few taps.',
          imagePath: 'assets/images/onboarding_3.png',
          isLastPage: true,
        ),
      ],
    );
  }

  OnboardingState copyWith({
    int? currentPage,
    bool? isCompleted,
    List<OnboardingData>? pages,
  }) {
    return OnboardingState(
      currentPage: currentPage ?? this.currentPage,
      isCompleted: isCompleted ?? this.isCompleted,
      pages: pages ?? this.pages,
    );
  }
}

