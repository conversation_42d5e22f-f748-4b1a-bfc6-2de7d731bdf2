import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/foundation.dart';

class ResponsiveUtils {
  // Spacing constants
  static const double xsSpacing = 4.0;
  static const double smSpacing = 8.0;
  static const double mdSpacing = 16.0;
  static const double lgSpacing = 24.0;
  static const double xlSpacing = 32.0;

  // Font sizes
  static const double xsFontSize = 12.0;
  static const double smFontSize = 14.0;
  static const double mdFontSize = 16.0;
  static const double lgFontSize = 18.0;
  static const double xlFontSize = 20.0;
  static const double xxlFontSize = 24.0;

  // Responsive spacing getters
  static double get responsiveXsSpacing => kIsWeb ? xsSpacing : xsSpacing.w;
  static double get responsiveSmSpacing => kIsWeb ? smSpacing : smSpacing.w;
  static double get responsiveMdSpacing => kIsWeb ? mdSpacing : mdSpacing.w;
  static double get responsiveLgSpacing => kIsWeb ? lgSpacing : lgSpacing.w;
  static double get responsiveXlSpacing => kIsWeb ? xlSpacing : xlSpacing.w;

  // Responsive font size getters
  static double get responsiveXsFontSize => kIsWeb ? xsFontSize : xsFontSize.sp;
  static double get responsiveSmFontSize => kIsWeb ? smFontSize : smFontSize.sp;
  static double get responsiveMdFontSize => kIsWeb ? mdFontSize : mdFontSize.sp;
  static double get responsiveLgFontSize => kIsWeb ? lgFontSize : lgFontSize.sp;
  static double get responsiveXlFontSize => kIsWeb ? xlFontSize : xlFontSize.sp;
  static double get responsiveXxlFontSize =>
      kIsWeb ? xxlFontSize : xxlFontSize.sp;

  // Responsive methods
  static double getResponsiveSafeAreaTop() {
    return kIsWeb ? 20.0 : 20.0.h;
  }

  static double getResponsiveBorderRadius() {
    return kIsWeb ? 12.0 : 12.0.r;
  }

  static double getResponsiveIconSize(double size) {
    return kIsWeb ? size : size.sp;
  }

  static double getResponsiveBadgeSize() {
    return kIsWeb ? 40.0 : 40.0.w;
  }

  static Offset getResponsiveShadowOffset() {
    return kIsWeb ? const Offset(0, 4) : Offset(0, 4.h);
  }

  static double getResponsiveShadowBlur() {
    return kIsWeb ? 8.0 : 8.0.r;
  }

  // Text styles
  static TextStyle getResponsiveHeadingTextStyle({
    double? fontSize,
    Color? color,
    String? fontFamily,
    FontWeight? fontWeight,
  }) {
    return TextStyle(
      fontSize: fontSize ?? lgFontSize,
      color: color ?? Colors.white,
      fontFamily: fontFamily ?? 'Gilroy_Bold',
      fontWeight: fontWeight ?? FontWeight.bold,
    );
  }

  static TextStyle getResponsiveBodyTextStyle({
    double? fontSize,
    Color? color,
    String? fontFamily,
    FontWeight? fontWeight,
  }) {
    return TextStyle(
      fontSize: fontSize ?? mdFontSize,
      color: color ?? Colors.white,
      fontFamily: fontFamily ?? 'Gilroy_Medium',
      fontWeight: fontWeight ?? FontWeight.w500,
    );
  }

  static TextStyle getResponsiveCaptionTextStyle({
    double? fontSize,
    Color? color,
    String? fontFamily,
    FontWeight? fontWeight,
  }) {
    return TextStyle(
      fontSize: fontSize ?? smFontSize,
      color: color ?? Colors.white70,
      fontFamily: fontFamily ?? 'Gilroy_Regular',
      fontWeight: fontWeight ?? FontWeight.normal,
    );
  }

  // Responsive dimensions
  static double getResponsiveWidth(double width) {
    return kIsWeb ? width : width.w;
  }

  static double getResponsiveHeight(double height) {
    return kIsWeb ? height : height.h;
  }

  static double getResponsiveRadius(double radius) {
    return kIsWeb ? radius : radius.r;
  }

  static EdgeInsets getResponsivePadding({
    double horizontal = 0,
    double vertical = 0,
    double all = 0,
  }) {
    if (all > 0) {
      return kIsWeb ? EdgeInsets.all(all) : EdgeInsets.all(all.w);
    }
    return kIsWeb
        ? EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical)
        : EdgeInsets.symmetric(horizontal: horizontal.w, vertical: vertical.h);
  }

  static EdgeInsets getResponsiveMargin({
    double horizontal = 0,
    double vertical = 0,
    double all = 0,
  }) {
    if (all > 0) {
      return kIsWeb ? EdgeInsets.all(all) : EdgeInsets.all(all.w);
    }
    return kIsWeb
        ? EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical)
        : EdgeInsets.symmetric(horizontal: horizontal.w, vertical: vertical.h);
  }
}
