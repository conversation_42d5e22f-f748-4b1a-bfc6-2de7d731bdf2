import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

class MobileWrapper extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;

  const MobileWrapper({
    super.key,
    required this.child,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      return child;
    }

    return Container(
      padding: padding ?? const EdgeInsets.all(16.0),
      child: child,
    );
  }
}
