import 'package:flutter/cupertino.dart';
import 'color.dart';

class ColorNotifire with ChangeNotifier {
  bool IsDark = false;

  set isDark(value) {
    IsDark = value;
    notifyListeners();
  }

  get getprimerycolor => IsDark ? darkPrimeryColor : PrimeryColor;
  get getlightblue => IsDark ? darkblue : lightblue;
  get getmediumblue => IsDark ? darkmediumblue : mediumblue;
  get getwhite => IsDark ? darkwhite : white;
  get getlightgrey => IsDark ? darkgrey : lightgrey;
  get getgrey => IsDark ? darkg : grey;

  // Method to toggle theme
  void toggleTheme() {
    isDark = !IsDark;
  }
}
