/// Debug utilities for authentication flow
/// 
/// This file provides debugging tools to help identify and resolve
/// authentication flow issues, particularly infinite loops.

import 'dart:developer' as developer;

class AuthDebugger {
  static bool _isEnabled = false;
  static final Map<String, int> _callCounts = {};
  static final Map<String, DateTime> _lastCallTimes = {};
  
  /// Enable debug logging for authentication flow
  static void enable() {
    _isEnabled = true;
    log('AuthDebugger enabled');
  }
  
  /// Disable debug logging
  static void disable() {
    _isEnabled = false;
    _callCounts.clear();
    _lastCallTimes.clear();
  }
  
  /// Log a method call with frequency tracking
  static void logCall(String methodName, [Map<String, dynamic>? params]) {
    if (!_isEnabled) return;
    
    final now = DateTime.now();
    _callCounts[methodName] = (_callCounts[methodName] ?? 0) + 1;
    
    final lastCall = _lastCallTimes[methodName];
    final timeSinceLastCall = lastCall != null 
        ? now.difference(lastCall).inMilliseconds 
        : 0;
    
    _lastCallTimes[methodName] = now;
    
    final count = _callCounts[methodName]!;
    final paramsStr = params != null ? ' with $params' : '';
    
    log('[$count] $methodName$paramsStr (${timeSinceLastCall}ms since last)');
    
    // Warn about potential infinite loops
    if (count > 10 && timeSinceLastCall < 100) {
      log('⚠️  WARNING: Potential infinite loop detected in $methodName');
      log('   Called $count times, last call was ${timeSinceLastCall}ms ago');
    }
  }
  
  /// Log authentication state changes
  static void logAuthStateChange(String from, String to, [String? reason]) {
    if (!_isEnabled) return;
    
    final reasonStr = reason != null ? ' ($reason)' : '';
    log('🔐 Auth state: $from → $to$reasonStr');
  }
  
  /// Log navigation events
  static void logNavigation(String from, String to, [String? reason]) {
    if (!_isEnabled) return;
    
    final reasonStr = reason != null ? ' - $reason' : '';
    log('🧭 Navigation: $from → $to$reasonStr');
  }
  
  /// Log session events
  static void logSessionEvent(String eventType, [Map<String, dynamic>? data]) {
    if (!_isEnabled) return;
    
    final dataStr = data != null ? ' $data' : '';
    log('📡 Session event: $eventType$dataStr');
  }
  
  /// Get call statistics
  static Map<String, int> getCallCounts() {
    return Map.from(_callCounts);
  }
  
  /// Reset call statistics
  static void resetStats() {
    _callCounts.clear();
    _lastCallTimes.clear();
    log('📊 Call statistics reset');
  }
  
  /// Print current statistics
  static void printStats() {
    if (!_isEnabled) return;
    
    log('📊 Authentication Flow Statistics:');
    if (_callCounts.isEmpty) {
      log('   No method calls recorded');
      return;
    }
    
    final sortedEntries = _callCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    for (final entry in sortedEntries) {
      final lastCall = _lastCallTimes[entry.key];
      final timeSinceLastCall = lastCall != null 
          ? DateTime.now().difference(lastCall).inSeconds 
          : 0;
      
      log('   ${entry.key}: ${entry.value} calls (last: ${timeSinceLastCall}s ago)');
    }
  }
  
  /// Check for potential issues
  static List<String> checkForIssues() {
    final issues = <String>[];
    
    // Check for excessive calls
    for (final entry in _callCounts.entries) {
      if (entry.value > 50) {
        issues.add('${entry.key} called ${entry.value} times - possible infinite loop');
      }
    }
    
    // Check for rapid successive calls
    for (final entry in _lastCallTimes.entries) {
      final count = _callCounts[entry.key] ?? 0;
      final timeSinceLastCall = DateTime.now().difference(entry.value).inMilliseconds;
      
      if (count > 5 && timeSinceLastCall < 1000) {
        issues.add('${entry.key} called $count times recently - check for loops');
      }
    }
    
    return issues;
  }
  
  /// Log a message with timestamp
  static void log(String message) {
    if (!_isEnabled) return;
    
    final timestamp = DateTime.now().toIso8601String().substring(11, 23);
    developer.log('[$timestamp] $message', name: 'AuthFlow');
  }
}

/// Extension to add debug logging to AuthGuardService
extension AuthGuardDebugExtension on Object {
  void debugLog(String methodName, [Map<String, dynamic>? params]) {
    AuthDebugger.logCall('${runtimeType}.$methodName', params);
  }
}

/// Usage example:
/// 
/// ```dart
/// // Enable debugging
/// AuthDebugger.enable();
/// 
/// // In your AuthGuardService methods:
/// Future<String?> getRedirectPath(String currentPath) async {
///   debugLog('getRedirectPath', {'currentPath': currentPath});
///   // ... rest of method
/// }
/// 
/// // Check for issues
/// final issues = AuthDebugger.checkForIssues();
/// if (issues.isNotEmpty) {
///   print('Auth flow issues detected: $issues');
/// }
/// 
/// // Print statistics
/// AuthDebugger.printStats();
/// ```
