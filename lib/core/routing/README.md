# Authentication Flow with go_router

This module implements comprehensive authentication flow handling using go_router with proper route protection, session management, and deep linking support.

## Features

### 🔐 Route Protection
- **Automatic Redirects**: Unauthenticated users are automatically redirected to login when accessing protected routes
- **Public Routes**: Login, register, forgot password, onboarding, and OTP verification are accessible without authentication
- **Protected Routes**: All other routes require authentication

### 🔄 Session Management Integration
- **Token Refresh Handling**: Automatically handles 401 responses and token refresh failures
- **Session Events**: Listens to session events from the existing SessionManager
- **Automatic Logout**: Forces logout and redirects to login when token refresh fails after max retries

### 🔗 Deep Linking Support
- **Intended Destination Storage**: Stores the intended destination when redirecting unauthenticated users
- **Query Parameter Preservation**: Maintains query parameters and fragments in deep links
- **Post-Login Redirect**: Automatically redirects to intended destination after successful login

### 🚀 Navigation Stack Management
- **Proper Navigation**: Uses `context.go()` for navigation to replace the current route
- **No Back Navigation**: Prevents users from navigating back to protected content after logout

## Architecture

### Core Components

#### 1. AuthGuardService (`auth_guard.dart`)
The main service responsible for authentication guards and redirects.

```dart
class AuthGuardService {
  // Check if user is authenticated
  Future<bool> isAuthenticated();
  
  // Determine redirect path based on auth status
  Future<String?> getRedirectPath(String currentPath);
  
  // Handle deep linking with full URI support
  Future<String?> getRedirectPathForUri(Uri uri);
  
  // Manage intended destinations
  String? getAndClearIntendedDestination();
}
```

#### 2. App Router (`app_router.dart`)
Creates the go_router configuration with authentication guards.

```dart
GoRouter createAppRouter(WidgetRef ref) {
  final authGuard = ref.read(authGuardServiceProvider);
  
  return GoRouter(
    redirect: (context, state) async {
      return await authGuard.getRedirectPath(state.uri.path);
    },
    routes: [...],
  );
}
```

### Integration Points

#### 1. Session Manager Integration
The AuthGuardService listens to session events from the existing SessionManager:

```dart
void _handleSessionEvent(SessionEvent event) {
  switch (event.type) {
    case SessionEventType.tokenRefreshFailed:
    case SessionEventType.sessionExpired:
      _forceLogout(); // Redirect to login
      break;
    // ... other cases
  }
}
```

#### 2. Auth Interceptor Integration
The existing AuthInterceptor works seamlessly with the new flow:
- Handles 401 responses and token refresh
- Uses SessionManager to clear sessions and notify about failures
- AuthGuardService automatically handles the session events

#### 3. Login Screen Integration
Updated to handle intended destinations after successful login:

```dart
ref.listen<AuthState>(authNotifierProvider, (previous, next) {
  if (mounted && next.status == AuthStatus.authenticated) {
    final authGuard = ref.read(authGuardServiceProvider);
    final intendedDestination = authGuard.getAndClearIntendedDestination();
    
    if (intendedDestination != null) {
      context.go(intendedDestination);
    } else {
      context.go('/home');
    }
  }
});
```

## Usage

### 1. Setup
The router is automatically configured in `main.dart`:

```dart
class NextSportzApp extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);
    
    return MaterialApp.router(
      routerConfig: router,
    );
  }
}
```

### 2. Route Definitions
Routes are automatically protected based on their path:

```dart
// Public routes (no authentication required)
- /
- /login
- /register
- /forgot-password
- /onboarding
- /otp-verification

// Protected routes (authentication required)
- /home
- /challenges
- /venues
- /teams
- /my-teams
- /profile-settings
- etc.
```

### 3. Deep Linking
Deep links work automatically:

```
https://app.nextsportz.com/teams/123?tab=members
```

If the user is not authenticated:
1. Redirects to `/login`
2. Stores `/teams/123?tab=members` as intended destination
3. After successful login, redirects to the stored destination

## Testing

Comprehensive tests are provided:

### Unit Tests (`auth_guard_test.dart`)
- Authentication status checking
- Redirect logic
- Intended destination management
- Session event handling

### Integration Tests (`app_router_test.dart`)
- End-to-end routing behavior
- Deep linking scenarios
- Authentication flow integration

### Running Tests
```bash
flutter test test/core/routing/
```

## Error Handling

### Token Refresh Failures
1. AuthInterceptor detects 401 response
2. Attempts token refresh up to 3 times
3. On failure, clears session via SessionManager
4. SessionManager emits `tokenRefreshFailed` event
5. AuthGuardService handles event and redirects to login

### Session Expiry
1. SessionManager detects session expiry
2. Emits `sessionExpired` event
3. AuthGuardService handles event and redirects to login

### Network Errors
- Handled by existing error handling in repositories
- Auth errors result in session clearing and redirect to login

## Best Practices

### 1. Route Protection
- All new routes are protected by default unless explicitly added to public routes
- Use meaningful route names for better debugging

### 2. Navigation
- Always use `context.go()` for navigation to maintain proper routing state
- Avoid `Navigator.push()` as it bypasses the router guards

### 3. Deep Linking
- Design routes to be deep-linkable with meaningful parameters
- Use query parameters for optional state (filters, tabs, etc.)

### 4. Testing
- Test both authenticated and unauthenticated scenarios
- Verify intended destination storage and retrieval
- Test session event handling

## Migration Notes

### From Previous Implementation
The new authentication flow is backward compatible:
- Existing AuthNotifier and repository patterns remain unchanged
- Session management continues to work as before
- Only navigation logic has been updated to use go_router

### Breaking Changes
- Removed manual router configuration from `main.dart`
- Updated login screen to handle intended destinations
- Navigation now uses `context.go()` instead of `Navigator.push()`
