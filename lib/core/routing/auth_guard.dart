import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../networking/session_manager.dart';
import '../../features/auth/presentation/logic/controller.dart';
import '../../features/auth/presentation/logic/auth_state.dart';

/// Service responsible for handling authentication guards and redirects
class AuthGuardService {
  final Ref _ref;
  StreamSubscription<SessionEvent>? _sessionSubscription;
  GoRouter? _router;
  bool _isHandlingLogout = false; // Prevent infinite logout loops

  AuthGuardService(this._ref);

  /// Initialize the auth guard service with router instance
  void initialize(GoRouter router) {
    _router = router;
    _startListeningToSessionEvents();
  }

  /// Check if user is authenticated for route protection
  Future<bool> isAuthenticated() async {
    try {
      final authState = _ref.read(authNotifierProvider);

      // If already authenticated in state, return true
      if (authState.status == AuthStatus.authenticated) {
        return true;
      }

      // If unauthenticated or error, return false
      if (authState.status == AuthStatus.unauthenticated ||
          authState.status == AuthStatus.error) {
        return false;
      }

      // For initial/loading states, check token status
      final sessionManager = _ref.read(sessionManagerProvider);
      return await sessionManager.isAuthenticated();
    } catch (e) {
      return false;
    }
  }

  /// Determine redirect path based on authentication status
  Future<String?> getRedirectPath(String currentPath) async {
    // Don't redirect if we're already handling a logout
    if (_isHandlingLogout) return null;

    final isAuth = await isAuthenticated();

    // Define public routes that don't require authentication
    final publicRoutes = {
      '/',
      '/login',
      '/register',
      '/forgot-password',
      '/onboarding',
      '/otp-verification',
    };

    // Check if current path is public
    final isPublicRoute =
        publicRoutes.contains(currentPath) ||
        currentPath.startsWith('/otp-verification');

    if (!isAuth && !isPublicRoute) {
      // User is not authenticated and trying to access protected route
      // Store the intended destination for after login
      _storeIntendedDestination(currentPath);
      return '/login';
    }

    if (isAuth && (currentPath == '/login' || currentPath == '/register')) {
      // User is authenticated but on auth pages, redirect to home
      return '/home';
    }

    // No redirect needed
    return null;
  }

  /// Handle deep linking with full URI support (including query parameters)
  Future<String?> getRedirectPathForUri(Uri uri) async {
    final fullPath = uri.toString();
    final pathOnly = uri.path;

    final isAuth = await isAuthenticated();

    // Define public routes that don't require authentication
    final publicRoutes = {
      '/',
      '/login',
      '/register',
      '/forgot-password',
      '/onboarding',
      '/otp-verification',
    };

    // Check if current path is public
    final isPublicRoute =
        publicRoutes.contains(pathOnly) ||
        pathOnly.startsWith('/otp-verification');

    if (!isAuth && !isPublicRoute) {
      // User is not authenticated and trying to access protected route
      // Store the full URI for after login (including query parameters)
      _storeIntendedDestination(fullPath);
      return '/login';
    }

    if (isAuth && (pathOnly == '/login' || pathOnly == '/register')) {
      // User is authenticated but on auth pages, redirect to home
      return '/home';
    }

    // No redirect needed
    return null;
  }

  /// Store intended destination for redirect after login
  void _storeIntendedDestination(String path) {
    // Only store meaningful destinations (not auth pages or root)
    if (path != '/' &&
        path != '/login' &&
        path != '/register' &&
        path != '/forgot-password' &&
        !path.startsWith('/otp-verification')) {
      _ref.read(_intendedDestinationProvider.notifier).state = path;
    }
  }

  /// Get and clear stored intended destination
  String? getAndClearIntendedDestination() {
    final destination = _ref.read(_intendedDestinationProvider);
    if (destination != null) {
      _ref.read(_intendedDestinationProvider.notifier).state = null;
      return destination;
    }
    return null;
  }

  /// Start listening to session events for automatic logout
  void _startListeningToSessionEvents() {
    final sessionManager = _ref.read(sessionManagerProvider);
    _sessionSubscription = sessionManager.sessionEvents.listen(
      _handleSessionEvent,
    );
  }

  /// Handle session events (token refresh failures, etc.)
  void _handleSessionEvent(SessionEvent event) {
    if (_router == null || _isHandlingLogout) return;

    switch (event.type) {
      case SessionEventType.tokenRefreshFailed:
      case SessionEventType.sessionExpired:
        // Force logout and redirect to login
        _forceLogout();
        break;
      case SessionEventType.sessionCleared:
        // Session was cleared, redirect to login if not already there
        // Only redirect if we're not already handling a logout
        if (!_isHandlingLogout) {
          final currentLocation =
              _router!.routerDelegate.currentConfiguration.uri.path;
          if (currentLocation != '/login') {
            _router!.go('/login');
          }
        }
        break;
      case SessionEventType.sessionClearFailed:
        // Handle session clear failure - still redirect to login for safety
        if (!_isHandlingLogout) {
          _router!.go('/login');
        }
        break;
    }
  }

  /// Force logout and redirect to login
  void _forceLogout() {
    if (_router == null || _isHandlingLogout) return;

    // Set flag to prevent infinite loops
    _isHandlingLogout = true;

    try {
      // Clear any stored intended destination since this is a forced logout
      _ref.read(_intendedDestinationProvider.notifier).state = null;

      // Navigate to login first to avoid triggering more events
      final currentLocation =
          _router!.routerDelegate.currentConfiguration.uri.path;
      if (currentLocation != '/login') {
        _router!.go('/login');
      }

      // Update auth state to unauthenticated (this might trigger session events)
      // We do this after navigation to avoid redirect loops
      _ref.read(authNotifierProvider.notifier).logout();
    } finally {
      // Reset the flag after a delay to allow the logout process to complete
      Future.delayed(const Duration(milliseconds: 500), () {
        _isHandlingLogout = false;
      });
    }
  }

  /// Dispose resources
  void dispose() {
    _sessionSubscription?.cancel();
    _sessionSubscription = null;
    _router = null;
    _isHandlingLogout = false; // Reset logout flag
  }
}

/// Provider for intended destination storage
final _intendedDestinationProvider = StateProvider<String?>((ref) => null);

/// Provider for auth guard service
final authGuardServiceProvider = Provider<AuthGuardService>((ref) {
  final service = AuthGuardService(ref);

  // Dispose when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
