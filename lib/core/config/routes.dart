class Routes {
  // Auth routes
  static const String login = '/login';
  static const String register = '/register';
  static const String otpVerification = '/otp-verification';
  static const String forgotPassword = '/forgot-password';
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';

  // Main app routes
  static const String home = '/home';
  static const String dashboard = '/dashboard';
  static const String profile = '/profile';
  static const String matches = '/matches';
  static const String tournaments = '/tournaments';
  static const String venues = '/venues';
  static const String challenges = '/challenges';
  static const String performance = '/performance';
  static const String teams = '/teams';

  // Feature routes
  static const String playerProfile = '/player-profile';
  static const String teamDetails = '/team-details';
  static const String teamList = '/teams';
  static const String teamDetail = '/team-detail';
  static const String joinTeam = '/join-team';

  // Dynamic team routes
  static String teamDetailWithId(String teamId) => '/teams/$teamId';
  static String myTeamDetailWithId(String teamId) => '/my-teams/$teamId';

  // Profile and settings routes
  static const String profileSettings = '/profile-settings';
  static const String myMatches = '/my-matches';

  // Tournament routes
  static const String tournamentDetails = '/tournament-details';
  static const String tournamentSearch = '/tournament-search';
  static const String tournamentFilter = '/tournament-filter';

  // Other feature routes
  static const String venueDetails = '/venue-details';
  static const String challengeDetails = '/challenge-details';
  static const String matchDetails = '/match-details';
  static const String settings = '/settings';
  static const String performanceAnalysis = '/performance-analysis';

  static const String notifications = '/notifications';
}
