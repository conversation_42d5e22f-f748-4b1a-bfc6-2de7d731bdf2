import 'package:flutter/material.dart';

class NextSportzTheme {
  // Light theme colors
  static const Color lightPrimary = Color(0xff202342);
  static const Color lightSecondary = Color(0xff2d325a);
  static const Color lightAccent =
      Color(0xff4CAF50); // Softer, more professional green
  static const Color lightAccentSecondary =
      Color(0xffFF6B35); // Changed from purple to energetic orange
  static const Color lightBlue = Color(0xff2d325a);
  static const Color lightMediumBlue = Color(0xff272b4e);
  static const Color lightGrey = Color(0xff767d97);
  static const Color lightLightGrey = Color(0xff373e58);

  // Dark theme colors
  static const Color darkPrimary = Color(0xff000000);
  static const Color darkSecondary = Color(0xff121212);
  static const Color darkAccent =
      Color(0xff4CAF50); // Softer, more professional green
  static const Color darkAccentSecondary =
      Color(0xffFF6B35); // Changed from purple to energetic orange
  static const Color darkBlue = Color(0xff1e1e1e);
  static const Color darkMediumBlue = Color(0xff2a2a2a);
  static const Color darkGrey = Color(0xffbbbbbb);
  static const Color darkLightGrey = Color(0xff333333);

  // Legacy color references (for backward compatibility)
  static const Color primary = lightPrimary;
  static const Color secondary = lightSecondary;
  static const Color accent = lightAccent;
  static const Color accentSecondary = lightAccentSecondary;
  static const Color mediumBlue = lightMediumBlue;
  static const Color grey = lightGrey;

  // Gradient definitions matching existing design
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [
      Color(0xff4CAF50),
      Color(0xffFF6B35),
      Color(0xff4CAF50)
    ], // Updated to softer, professional colors
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0x40FFFFFF), Color(0x20FFFFFF)],
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xff202342), Color(0xff2d325a)],
  );

  // Text styles
  static const TextStyle titleStyle = TextStyle(
    fontFamily: 'Gilroy_Medium',
    color: Colors.white,
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle bodyStyle = TextStyle(
    fontFamily: 'Gilroy_Medium',
    color: Colors.white,
    fontSize: 14,
  );

  static const TextStyle hintStyle = TextStyle(
    fontFamily: 'Gilroy_Medium',
    color: Color(0xff767d97),
    fontSize: 12,
  );

  static const TextStyle buttonStyle = TextStyle(
    fontFamily: 'Gilroy_Medium',
    color: Colors.white,
    fontSize: 15,
    fontWeight: FontWeight.bold,
  );

  // Input decoration theme
  static InputDecoration getInputDecoration({
    required String hintText,
    required IconData prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixTap,
  }) {
    return InputDecoration(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(30),
        borderSide: const BorderSide(width: 0, style: BorderStyle.none),
      ),
      fillColor: lightBlue,
      hintStyle: hintStyle,
      hintText: hintText,
      filled: true,
      prefixIcon: Icon(prefixIcon, color: Colors.white, size: 20),
      suffixIcon: suffixIcon != null
          ? GestureDetector(
              onTap: onSuffixTap,
              child: Icon(suffixIcon, color: grey, size: 20),
            )
          : null,
    );
  }

  // Button decoration
  static BoxDecoration getButtonDecoration() {
    return const BoxDecoration(
      borderRadius: BorderRadius.all(Radius.circular(20)),
      gradient: primaryGradient,
    );
  }

  // Card decoration
  static BoxDecoration getCardDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(15),
      gradient: cardGradient,
      border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
    );
  }

  // Light theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: false,
      brightness: Brightness.light,
      primaryColor: lightPrimary,
      scaffoldBackgroundColor: lightPrimary,
      fontFamily: 'Gilroy_Medium',
      colorScheme: const ColorScheme.light(
        primary: lightAccent,
        secondary: lightAccentSecondary,
        surface: lightSecondary,
        background: lightPrimary,
        error: Colors.red,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: Colors.white,
        onBackground: Colors.white,
        onError: Colors.white,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: lightPrimary,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontFamily: 'Gilroy_Bold',
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      shadowColor: Colors.transparent,
      splashColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      dividerColor: Colors.transparent,
    );
  }

  // Dark theme
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: false,
      brightness: Brightness.dark,
      primaryColor: darkPrimary,
      scaffoldBackgroundColor: darkPrimary,
      fontFamily: 'Gilroy_Medium',
      colorScheme: const ColorScheme.dark(
        primary: darkAccent,
        secondary: darkAccentSecondary,
        surface: darkSecondary,
        background: darkPrimary,
        error: Colors.red,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: Colors.white,
        onBackground: Colors.white,
        onError: Colors.white,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: darkPrimary,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontFamily: 'Gilroy_Bold',
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      shadowColor: Colors.transparent,
      splashColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      dividerColor: Colors.transparent,
    );
  }

  // Get current colors based on theme mode
  static ColorScheme getColors(bool isDark) {
    return isDark ? darkTheme.colorScheme : lightTheme.colorScheme;
  }

  static Color getPrimaryColor(bool isDark) {
    return isDark ? darkPrimary : lightPrimary;
  }

  static Color getSecondaryColor(bool isDark) {
    return isDark ? darkSecondary : lightSecondary;
  }

  static Color getAccentColor(bool isDark) {
    return isDark ? darkAccent : lightAccent;
  }

  static Color getGreyColor(bool isDark) {
    return isDark ? darkGrey : lightGrey;
  }

  static Color getLightGreyColor(bool isDark) {
    return isDark ? darkLightGrey : lightLightGrey;
  }
}
