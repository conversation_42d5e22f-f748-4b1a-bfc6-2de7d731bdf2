import 'dart:io';
import 'dart:typed_data';
import '../../../models/image_upload_models.dart';

/// Abstract datasource interface for image upload operations
abstract class ImageUploadDatasource {
  /// Get presigned upload and download URLs for an image
  /// 
  /// Takes an [ImageUploadRequest] and returns an [ImageUploadResponse]
  /// containing the presigned upload URL and the final download URL
  Future<ImageUploadResponse> getUploadUrls(ImageUploadRequest request);

  /// Upload file to the presigned URL
  /// 
  /// Takes the [uploadUrl] from getUploadUrls response and the [file] to upload
  /// Returns true if upload was successful
  Future<bool> uploadFileToUrl(String uploadUrl, File file);

  /// Upload bytes to the presigned URL (for web support)
  /// 
  /// Takes the [uploadUrl] from getUploadUrls response and the [bytes] to upload
  /// along with the [fileName] for proper content type detection
  /// Returns true if upload was successful
  Future<bool> uploadBytesToUrl(String uploadUrl, Uint8List bytes, String fileName);
}
