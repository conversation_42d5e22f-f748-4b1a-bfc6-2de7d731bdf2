import 'dart:io';
import 'dart:typed_data';
import '../../../models/image_upload_models.dart';
import 'image_upload_datasource.dart';

/// Mock implementation of image upload datasource for testing
class ImageUploadMockDatasource implements ImageUploadDatasource {
  final Duration delay;
  final bool shouldFail;
  final String? errorMessage;

  const ImageUploadMockDatasource({
    this.delay = const Duration(milliseconds: 500),
    this.shouldFail = false,
    this.errorMessage,
  });

  @override
  Future<ImageUploadResponse> getUploadUrls(ImageUploadRequest request) async {
    await Future.delayed(delay);
    
    if (shouldFail) {
      throw Exception(errorMessage ?? 'Mock upload URL request failed');
    }

    // Generate mock URLs based on the request
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final entityId = request.entityId ?? 'mock-entity';
    final fileName = request.fileName ?? 'image.jpg';
    
    return ImageUploadResponse(
      uploadUrl: 'https://mock-storage.example.com/upload/$timestamp/$fileName',
      downloadUrl: 'https://mock-cdn.example.com/${request.type.value}/$entityId/$fileName',
    );
  }

  @override
  Future<bool> uploadFileToUrl(String uploadUrl, File file) async {
    await Future.delayed(delay);
    
    if (shouldFail) {
      throw Exception(errorMessage ?? 'Mock file upload failed');
    }

    // Simulate successful upload
    return true;
  }

  @override
  Future<bool> uploadBytesToUrl(String uploadUrl, Uint8List bytes, String fileName) async {
    await Future.delayed(delay);
    
    if (shouldFail) {
      throw Exception(errorMessage ?? 'Mock bytes upload failed');
    }

    // Simulate successful upload
    return true;
  }
}
