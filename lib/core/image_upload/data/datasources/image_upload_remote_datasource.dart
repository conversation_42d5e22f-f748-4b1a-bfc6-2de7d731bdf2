import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:mime/mime.dart';
import '../../../networking/api_client.dart';
import '../../../networking/api_const.dart';
import '../../../models/image_upload_models.dart';
import 'image_upload_datasource.dart';

/// Remote implementation of image upload datasource
class ImageUploadRemoteDatasource implements ImageUploadDatasource {
  final ApiClient _apiClient;
  final Dio _dio;

  ImageUploadRemoteDatasource(this._apiClient) : _dio = Dio();

  @override
  Future<ImageUploadResponse> getUploadUrls(ImageUploadRequest request) async {
    final response = await _apiClient.post(
      ApiConst.imageUploadUrlEndpoint,
      data: request.toJson(),
    );

    return ImageUploadResponse.fromJson(
      response['data'] as Map<String, dynamic>,
    );
  }

  @override
  Future<bool> uploadFileToUrl(String uploadUrl, File file) async {
    try {
      // Read file as bytes
      final bytes = await file.readAsBytes();
      final fileName = file.path.split('/').last;

      return await uploadBytesToUrl(uploadUrl, bytes, fileName);
    } catch (e) {
      throw Exception('Failed to upload file: $e');
    }
  }

  @override
  Future<bool> uploadBytesToUrl(
    String uploadUrl,
    Uint8List bytes,
    String fileName,
  ) async {
    try {
      // Determine content type from file extension
      final mimeType = lookupMimeType(fileName) ?? 'application/octet-stream';

      final response = await _dio.put(
        uploadUrl,
        data: bytes,
        options: Options(
          headers: {'Content-Type': mimeType, 'Content-Length': bytes.length},
          // Don't follow redirects for presigned URLs
          followRedirects: false,
          validateStatus: (status) {
            // Accept 200-299 status codes as successful
            return status != null && status >= 200 && status < 300;
          },
        ),
      );

      return response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! < 300;
    } on DioException catch (e) {
      throw Exception('Failed to upload to presigned URL: ${e.message}');
    } catch (e) {
      throw Exception('Failed to upload to presigned URL: $e');
    }
  }
}
