import 'dart:io';
import 'dart:typed_data';
import 'package:fpdart/fpdart.dart';
import '../../../models/image_upload_models.dart';
import '../../domain/repositories/image_upload_repository.dart';
import '../datasources/image_upload_datasource.dart';

/// Implementation of image upload repository
class ImageUploadRepositoryImpl implements ImageUploadRepository {
  final ImageUploadDatasource _datasource;

  ImageUploadRepositoryImpl(this._datasource);

  @override
  Future<Either<String, String>> uploadImageFile({
    required ImageType imageType,
    required File file,
    required String entityId,
    String? fileName,
  }) async {
    try {
      // Use provided fileName or extract from file path
      final actualFileName = fileName ?? file.path.split('/').last;
      
      // Get upload URLs
      final request = ImageUploadRequest(
        type: imageType,
        fileName: actualFileName,
        entityId: entityId,
      );

      final uploadResponse = await _datasource.getUploadUrls(request);
      
      if (uploadResponse.uploadUrl == null || uploadResponse.downloadUrl == null) {
        return const Left('Failed to get upload URLs from server');
      }

      // Upload file to presigned URL
      final uploadSuccess = await _datasource.uploadFileToUrl(
        uploadResponse.uploadUrl!,
        file,
      );

      if (!uploadSuccess) {
        return const Left('Failed to upload file to storage');
      }

      return Right(uploadResponse.downloadUrl!);
    } catch (e) {
      return Left('Image upload failed: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, String>> uploadImageBytes({
    required ImageType imageType,
    required Uint8List bytes,
    required String entityId,
    required String fileName,
  }) async {
    try {
      // Get upload URLs
      final request = ImageUploadRequest(
        type: imageType,
        fileName: fileName,
        entityId: entityId,
      );

      final uploadResponse = await _datasource.getUploadUrls(request);
      
      if (uploadResponse.uploadUrl == null || uploadResponse.downloadUrl == null) {
        return const Left('Failed to get upload URLs from server');
      }

      // Upload bytes to presigned URL
      final uploadSuccess = await _datasource.uploadBytesToUrl(
        uploadResponse.uploadUrl!,
        bytes,
        fileName,
      );

      if (!uploadSuccess) {
        return const Left('Failed to upload bytes to storage');
      }

      return Right(uploadResponse.downloadUrl!);
    } catch (e) {
      return Left('Image upload failed: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, ImageUploadResponse>> getUploadUrls({
    required ImageType imageType,
    required String entityId,
    String? fileName,
  }) async {
    try {
      final request = ImageUploadRequest(
        type: imageType,
        fileName: fileName,
        entityId: entityId,
      );

      final uploadResponse = await _datasource.getUploadUrls(request);
      return Right(uploadResponse);
    } catch (e) {
      return Left('Failed to get upload URLs: ${e.toString()}');
    }
  }
}
