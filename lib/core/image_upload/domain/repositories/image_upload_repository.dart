import 'dart:io';
import 'dart:typed_data';
import 'package:fpdart/fpdart.dart';
import '../../../models/image_upload_models.dart';

/// Abstract repository interface for image upload operations
abstract class ImageUploadRepository {
  /// Upload an image file for a specific entity type
  /// 
  /// Takes [imageType], [file], [entityId], and optional [fileName]
  /// Returns Either<String, String> where Right contains the download URL
  /// and Left contains error message
  Future<Either<String, String>> uploadImageFile({
    required ImageType imageType,
    required File file,
    required String entityId,
    String? fileName,
  });

  /// Upload image bytes for a specific entity type (web support)
  /// 
  /// Takes [imageType], [bytes], [entityId], and [fileName]
  /// Returns Either<String, String> where Right contains the download URL
  /// and Left contains error message
  Future<Either<String, String>> uploadImageBytes({
    required ImageType imageType,
    required Uint8List bytes,
    required String entityId,
    required String fileName,
  });

  /// Get presigned upload URLs without uploading
  /// 
  /// Takes [imageType], [entityId], and optional [fileName]
  /// Returns Either<String, ImageUploadResponse> where Right contains the URLs
  /// and Left contains error message
  Future<Either<String, ImageUploadResponse>> getUploadUrls({
    required ImageType imageType,
    required String entityId,
    String? fileName,
  });
}
