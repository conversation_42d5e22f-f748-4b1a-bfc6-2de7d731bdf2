import 'dart:io';
import 'dart:typed_data';
import 'package:fpdart/fpdart.dart';
import 'package:flutter/foundation.dart';
import '../../../models/image_upload_models.dart';
import '../repositories/image_upload_repository.dart';

/// Generic image upload helper that abstracts the complete upload process
class ImageUploadHelper {
  final ImageUploadRepository _repository;

  ImageUploadHelper(this._repository);

  /// Upload an image for a user profile
  /// 
  /// Takes [file] or [bytes] and [userId]
  /// Returns Either<String, String> where Right contains the download URL
  Future<Either<String, String>> uploadUserProfileImage({
    File? file,
    Uint8List? bytes,
    required String userId,
    String? fileName,
  }) async {
    return _uploadImage(
      imageType: ImageType.userProfile,
      file: file,
      bytes: bytes,
      entityId: userId,
      fileName: fileName,
    );
  }

  /// Upload an image for a team logo
  /// 
  /// Takes [file] or [bytes] and [teamId]
  /// Returns Either<String, String> where Right contains the download URL
  Future<Either<String, String>> uploadTeamLogo({
    File? file,
    Uint8List? bytes,
    required String teamId,
    String? fileName,
  }) async {
    return _uploadImage(
      imageType: ImageType.teamLogo,
      file: file,
      bytes: bytes,
      entityId: teamId,
      fileName: fileName,
    );
  }

  /// Upload an advertisement banner
  /// 
  /// Takes [file] or [bytes] and [advertisementId]
  /// Returns Either<String, String> where Right contains the download URL
  Future<Either<String, String>> uploadAdvertisementBanner({
    File? file,
    Uint8List? bytes,
    required String advertisementId,
    String? fileName,
  }) async {
    return _uploadImage(
      imageType: ImageType.advertisementBanner,
      file: file,
      bytes: bytes,
      entityId: advertisementId,
      fileName: fileName,
    );
  }

  /// Upload a tournament banner
  /// 
  /// Takes [file] or [bytes] and [tournamentId]
  /// Returns Either<String, String> where Right contains the download URL
  Future<Either<String, String>> uploadTournamentBanner({
    File? file,
    Uint8List? bytes,
    required String tournamentId,
    String? fileName,
  }) async {
    return _uploadImage(
      imageType: ImageType.tournamentBanner,
      file: file,
      bytes: bytes,
      entityId: tournamentId,
      fileName: fileName,
    );
  }

  /// Generic upload method for any image type
  /// 
  /// Takes [imageType], [file] or [bytes], [entityId], and optional [fileName]
  /// Returns Either<String, String> where Right contains the download URL
  Future<Either<String, String>> uploadImage({
    required ImageType imageType,
    File? file,
    Uint8List? bytes,
    required String entityId,
    String? fileName,
  }) async {
    return _uploadImage(
      imageType: imageType,
      file: file,
      bytes: bytes,
      entityId: entityId,
      fileName: fileName,
    );
  }

  /// Private helper method that handles the actual upload logic
  Future<Either<String, String>> _uploadImage({
    required ImageType imageType,
    File? file,
    Uint8List? bytes,
    required String entityId,
    String? fileName,
  }) async {
    // Validate input parameters
    if (file == null && bytes == null) {
      return const Left('Either file or bytes must be provided');
    }

    if (file != null && bytes != null) {
      return const Left('Cannot provide both file and bytes');
    }

    if (bytes != null && fileName == null) {
      return const Left('fileName is required when uploading bytes');
    }

    try {
      // Upload based on platform and input type
      if (file != null) {
        return await _repository.uploadImageFile(
          imageType: imageType,
          file: file,
          entityId: entityId,
          fileName: fileName,
        );
      } else {
        return await _repository.uploadImageBytes(
          imageType: imageType,
          bytes: bytes!,
          entityId: entityId,
          fileName: fileName!,
        );
      }
    } catch (e) {
      return Left('Upload failed: ${e.toString()}');
    }
  }

  /// Get presigned upload URLs without uploading
  /// 
  /// Useful for advanced use cases where you want to handle the upload manually
  Future<Either<String, ImageUploadResponse>> getUploadUrls({
    required ImageType imageType,
    required String entityId,
    String? fileName,
  }) async {
    return await _repository.getUploadUrls(
      imageType: imageType,
      entityId: entityId,
      fileName: fileName,
    );
  }
}
