# Generic Image Upload System

This module provides a generic image upload system that abstracts the complete image upload process for different entity types (UserProfile, TeamProfile, etc.) using presigned URLs.

## Features

- **Generic Upload Helper**: Abstracts the complete image upload process
- **Multiple Entity Types**: Supports UserProfile, TeamLogo, AdvertisementBanner, TournamentBanner
- **Platform Support**: Works with both File (mobile) and Uint8List (web)
- **Presigned URLs**: Uses `/api/image/upload-url` endpoint for secure uploads
- **Error Handling**: Comprehensive error handling with Either types
- **Testing**: Full test coverage with unit and integration tests

## Architecture

```
Domain Layer:
├── helpers/
│   └── image_upload_helper.dart          # Main helper with convenience methods
└── repositories/
    └── image_upload_repository.dart       # Repository interface

Data Layer:
├── datasources/
│   ├── image_upload_datasource.dart      # Datasource interface
│   ├── image_upload_remote_datasource.dart # Remote implementation
│   └── image_upload_mock_datasource.dart # Mock implementation
└── repositories/
    └── image_upload_repository_impl.dart # Repository implementation

Models:
└── image_upload_models.dart               # Request/Response models

Providers:
└── image_upload_providers.dart            # Riverpod providers
```

## Usage

### Basic Usage

```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nextsportz_v2/core/image_upload/image_upload_providers.dart';

// Get the image upload helper
final imageUploadHelper = ref.read(imageUploadHelperProvider);

// Upload user profile image
final result = await imageUploadHelper.uploadUserProfileImage(
  file: selectedFile,        // For mobile
  // bytes: selectedBytes,   // For web
  // fileName: 'profile.jpg', // Required for bytes
  userId: 'user-123',
);

result.fold(
  (error) => print('Upload failed: $error'),
  (downloadUrl) => print('Upload successful: $downloadUrl'),
);
```

### Profile Integration

```dart
// Upload and update profile photo
final profileRepository = ref.read(profileRepositoryProvider);

final result = await profileRepository.updateProfilePhoto(
  userId: 'user-123',
  file: selectedFile,
  fileName: 'profile.jpg',
);

result.fold(
  (error) => print('Profile update failed: $error'),
  (updatedProfile) => print('Profile updated with new photo: ${updatedProfile.photoUrl}'),
);
```

### Different Entity Types

```dart
// Upload team logo
final teamResult = await imageUploadHelper.uploadTeamLogo(
  file: logoFile,
  teamId: 'team-456',
);

// Upload advertisement banner
final adResult = await imageUploadHelper.uploadAdvertisementBanner(
  bytes: bannerBytes,
  fileName: 'banner.png',
  advertisementId: 'ad-789',
);

// Upload tournament banner
final tournamentResult = await imageUploadHelper.uploadTournamentBanner(
  file: bannerFile,
  tournamentId: 'tournament-123',
);
```

### Generic Upload

```dart
// Generic upload for any image type
final result = await imageUploadHelper.uploadImage(
  imageType: ImageType.userProfile,
  file: selectedFile,
  entityId: 'entity-123',
  fileName: 'image.jpg', // Optional for files, required for bytes
);
```

### Advanced Usage - Get Upload URLs Only

```dart
// Get presigned URLs without uploading
final urlsResult = await imageUploadHelper.getUploadUrls(
  imageType: ImageType.teamLogo,
  entityId: 'team-123',
  fileName: 'logo.png',
);

urlsResult.fold(
  (error) => print('Failed to get URLs: $error'),
  (response) {
    print('Upload URL: ${response.uploadUrl}');
    print('Download URL: ${response.downloadUrl}');
    // Handle upload manually if needed
  },
);
```

## Testing

### Using Mock Datasource

```dart
// Override with mock datasource for testing
final container = ProviderContainer(
  overrides: [
    imageUploadDatasourceProvider.overrideWithValue(
      const ImageUploadMockDatasource(),
    ),
  ],
);

final helper = container.read(imageUploadHelperProvider);
```

### Mock with Failure

```dart
// Test error scenarios
final failingContainer = ProviderContainer(
  overrides: [
    imageUploadDatasourceProvider.overrideWithValue(
      const ImageUploadMockDatasource(
        shouldFail: true,
        errorMessage: 'Network error',
      ),
    ),
  ],
);
```

## API Endpoints

### Upload URL Request
```
POST /api/image/upload-url
Content-Type: application/json

{
  "type": "UserProfile",
  "fileName": "profile.jpg",
  "entityId": "user-123"
}
```

### Upload URL Response
```json
{
  "uploadUrl": "https://storage.example.com/upload/...",
  "downloadUrl": "https://cdn.example.com/UserProfile/user-123/profile.jpg"
}
```

## Supported Image Types

- `UserProfile`: User profile pictures
- `TeamLogo`: Team logos
- `AdvertisementBanner`: Advertisement banners
- `TournamentBanner`: Tournament banners

## Error Handling

All methods return `Either<String, T>` where:
- `Left(error)`: Contains error message
- `Right(result)`: Contains successful result

Common error scenarios:
- Network failures
- Invalid file types
- Missing required parameters
- Upload failures to presigned URLs

## Dependencies

- `fpdart`: For functional error handling
- `dio`: For HTTP requests
- `mime`: For content type detection
- `flutter_riverpod`: For dependency injection
