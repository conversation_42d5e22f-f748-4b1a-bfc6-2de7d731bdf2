import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../networking/api_client.dart';
import 'data/datasources/image_upload_datasource.dart';
import 'data/datasources/image_upload_remote_datasource.dart';
import 'data/datasources/image_upload_mock_datasource.dart';
import 'data/repositories/image_upload_repository_impl.dart';
import 'domain/repositories/image_upload_repository.dart';
import 'domain/helpers/image_upload_helper.dart';

/// Provider for image upload datasource
/// 
/// In production, this uses the remote datasource
/// For testing, you can override this with the mock datasource
final imageUploadDatasourceProvider = Provider<ImageUploadDatasource>((ref) {
  final apiClient = ref.read(apiClientProvider);
  return ImageUploadRemoteDatasource(apiClient);
});

/// Provider for mock image upload datasource
/// 
/// Useful for testing and development
final imageUploadMockDatasourceProvider = Provider<ImageUploadDatasource>((ref) {
  return const ImageUploadMockDatasource();
});

/// Provider for image upload repository
final imageUploadRepositoryProvider = Provider<ImageUploadRepository>((ref) {
  final datasource = ref.read(imageUploadDatasourceProvider);
  return ImageUploadRepositoryImpl(datasource);
});

/// Provider for image upload helper
/// 
/// This is the main entry point for image upload operations
final imageUploadHelperProvider = Provider<ImageUploadHelper>((ref) {
  final repository = ref.read(imageUploadRepositoryProvider);
  return ImageUploadHelper(repository);
});

/// Provider for mock image upload helper
/// 
/// Useful for testing
final imageUploadMockHelperProvider = Provider<ImageUploadHelper>((ref) {
  final mockDatasource = ref.read(imageUploadMockDatasourceProvider);
  final repository = ImageUploadRepositoryImpl(mockDatasource);
  return ImageUploadHelper(repository);
});
