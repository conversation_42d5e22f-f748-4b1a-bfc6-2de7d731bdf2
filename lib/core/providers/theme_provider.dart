import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../local/key_value_storage.dart';
import '../providers.dart';

class ThemeNotifier extends StateNotifier<ThemeMode> {
  final KeyValueStorageService _storage;
  static const String _themeKey = 'theme_mode';

  ThemeNotifier(this._storage) : super(ThemeMode.light) {
    _loadTheme();
  }

  void _loadTheme() {
    try {
      final themeIndex = _storage.getValue<int>(_themeKey);
      if (themeIndex != null && themeIndex < ThemeMode.values.length) {
        state = ThemeMode.values[themeIndex];
      }
    } catch (e) {
      // Default to light mode if loading fails
      state = ThemeMode.light;
    }
  }

  Future<void> setThemeMode(ThemeMode themeMode) async {
    try {
      state = themeMode;
      await _storage.setKeyValue(_themeKey, themeMode.index);
      print('Theme saved: ${themeMode.name} (index: ${themeMode.index})');
    } catch (e) {
      print('Error saving theme: $e');
    }
  }

  Future<void> toggleTheme() async {
    final newTheme =
        state == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    print('Toggling theme from ${state.name} to ${newTheme.name}');
    await setThemeMode(newTheme);
  }

  bool get isDarkMode => state == ThemeMode.dark;
}

final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeMode>((ref) {
  final storage = ref.watch(keyValueStorageProvider);
  return ThemeNotifier(storage);
});

final isDarkModeProvider = Provider<bool>((ref) {
  return ref.watch(themeProvider) == ThemeMode.dark;
});
