import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/theme_provider.dart';

class DebugThemeToggle extends ConsumerWidget {
  const DebugThemeToggle({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeProvider);
    final isDark = ref.watch(isDarkModeProvider);
    final themeNotifier = ref.read(themeProvider.notifier);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.black : Colors.blue[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? Colors.white : Colors.blue,
          width: 2,
        ),
      ),
      child: Column(
        children: [
          Text(
            'Current Theme: ${themeMode.name}',
            style: TextStyle(
              color: isDark ? Colors.white : Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'Is Dark: $isDark',
            style: TextStyle(
              color: isDark ? Colors.green : Colors.orange,
              fontSize: 14,
            ),
          ),
          Text(
            'Theme Index: ${themeMode.index}',
            style: const TextStyle(color: Colors.grey, fontSize: 12),
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: () {
              print('Button pressed - current theme: ${themeMode.name}');
              themeNotifier.toggleTheme();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isDark
                  ? Colors.purple
                  : const Color(0xff00D4AA), // Updated to sporty green
            ),
            child: Text(
              'Toggle to ${isDark ? 'Light' : 'Dark'} Mode',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
