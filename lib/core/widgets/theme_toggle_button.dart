import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/theme_provider.dart';

class ThemeToggleButton extends ConsumerWidget {
  final bool showLabel;
  final double? iconSize;

  const ThemeToggleButton({Key? key, this.showLabel = false, this.iconSize})
      : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDark = ref.watch(isDarkModeProvider);
    final themeNotifier = ref.read(themeProvider.notifier);

    if (showLabel) {
      return ListTile(
        leading: Icon(
          isDark ? Icons.light_mode : Icons.dark_mode,
          color: Colors.white,
          size: iconSize ?? 24,
        ),
        title: Text(
          isDark ? 'Light Mode' : 'Dark Mode',
          style: const TextStyle(
            color: Colors.white,
            fontFamily: 'Gilroy_Medium',
            fontSize: 16,
          ),
        ),
        trailing: Switch(
          value: isDark,
          onChanged: (_) => themeNotifier.toggleTheme(),
          activeColor: const Color(0xff00D4AA), // Updated to sporty green
          inactiveThumbColor: Colors.white,
          inactiveTrackColor: Colors.grey,
        ),
        onTap: () => themeNotifier.toggleTheme(),
      );
    }

    return IconButton(
      icon: Icon(
        isDark ? Icons.light_mode : Icons.dark_mode,
        color: Colors.white,
        size: iconSize ?? 24,
      ),
      onPressed: () => themeNotifier.toggleTheme(),
      tooltip: isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode',
    );
  }
}

class AnimatedThemeToggleButton extends ConsumerStatefulWidget {
  final double? size;

  const AnimatedThemeToggleButton({Key? key, this.size}) : super(key: key);

  @override
  ConsumerState<AnimatedThemeToggleButton> createState() =>
      _AnimatedThemeToggleButtonState();
}

class _AnimatedThemeToggleButtonState
    extends ConsumerState<AnimatedThemeToggleButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(isDarkModeProvider);
    final themeNotifier = ref.read(themeProvider.notifier);

    return GestureDetector(
      onTap: () {
        _animationController.forward().then((_) {
          themeNotifier.toggleTheme();
          _animationController.reset();
        });
      },
      child: Container(
        width: widget.size ?? 50,
        height: widget.size ?? 50,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: const LinearGradient(
            colors: [
              Color(0xff00D4AA),
              Color(0xffFF6B35)
            ], // Updated to sporty colors
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xff00D4AA)
                  .withOpacity(0.3), // Updated to sporty green
              blurRadius: 10,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _animation.value * 3.14159, // 180 degrees
              child: Icon(
                isDark ? Icons.light_mode : Icons.dark_mode,
                color: Colors.white,
                size: (widget.size ?? 50) * 0.5,
              ),
            );
          },
        ),
      ),
    );
  }
}
