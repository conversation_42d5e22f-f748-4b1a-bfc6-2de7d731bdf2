import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/paginated_response.dart';

/// A reusable widget for displaying paginated data with automatic loading
/// 
/// This widget handles:
/// - Loading states (initial, loading more, refreshing)
/// - Error handling with retry functionality
/// - Automatic pagination when scrolling to bottom
/// - Pull-to-refresh functionality
/// - Empty state display
class PaginatedListView<T> extends ConsumerStatefulWidget {
  /// Function to fetch data for a specific page
  final Future<PaginatedResponse<T>> Function(int page) onLoadPage;
  
  /// Widget builder for each item
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  
  /// Widget to display when the list is empty
  final Widget? emptyWidget;
  
  /// Widget to display when there's an error
  final Widget Function(String error, VoidCallback onRetry)? errorBuilder;
  
  /// Widget to display while loading initial data
  final Widget? loadingWidget;
  
  /// Widget to display while loading more data
  final Widget? loadingMoreWidget;
  
  /// Number of items per page
  final int pageSize;
  
  /// Whether to enable pull-to-refresh
  final bool enableRefresh;
  
  /// Scroll controller (optional)
  final ScrollController? scrollController;
  
  /// Padding for the list
  final EdgeInsetsGeometry? padding;
  
  /// Whether to show a separator between items
  final bool showSeparator;
  
  /// Custom separator widget
  final Widget? separator;

  const PaginatedListView({
    Key? key,
    required this.onLoadPage,
    required this.itemBuilder,
    this.emptyWidget,
    this.errorBuilder,
    this.loadingWidget,
    this.loadingMoreWidget,
    this.pageSize = 20,
    this.enableRefresh = true,
    this.scrollController,
    this.padding,
    this.showSeparator = false,
    this.separator,
  }) : super(key: key);

  @override
  ConsumerState<PaginatedListView<T>> createState() => _PaginatedListViewState<T>();
}

class _PaginatedListViewState<T> extends ConsumerState<PaginatedListView<T>> {
  late ScrollController _scrollController;
  List<T> _items = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasError = false;
  String _errorMessage = '';
  int _currentPage = 1;
  bool _hasNextPage = true;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreData();
    }
  }

  Future<void> _loadInitialData() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final response = await widget.onLoadPage(1);
      if (mounted) {
        setState(() {
          _items = response.data.items;
          _currentPage = response.data.page;
          _hasNextPage = response.data.hasNextPage;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore || !_hasNextPage || _hasError) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final nextPage = _currentPage + 1;
      final response = await widget.onLoadPage(nextPage);
      if (mounted) {
        setState(() {
          _items.addAll(response.data.items);
          _currentPage = response.data.page;
          _hasNextPage = response.data.hasNextPage;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
          // Don't set error state for load more failures
          // Just show a snackbar or toast
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to load more items: ${e.toString()}'),
              action: SnackBarAction(
                label: 'Retry',
                onPressed: _loadMoreData,
              ),
            ),
          );
        });
      }
    }
  }

  Future<void> _refresh() async {
    setState(() {
      _currentPage = 1;
      _hasNextPage = true;
      _items.clear();
    });
    await _loadInitialData();
  }

  Widget _buildLoadingWidget() {
    return widget.loadingWidget ?? 
        const Center(
          child: Padding(
            padding: EdgeInsets.all(20.0),
            child: CircularProgressIndicator(),
          ),
        );
  }

  Widget _buildEmptyWidget() {
    return widget.emptyWidget ?? 
        const Center(
          child: Padding(
            padding: EdgeInsets.all(20.0),
            child: Text(
              'No items found',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ),
        );
  }

  Widget _buildErrorWidget() {
    if (widget.errorBuilder != null) {
      return widget.errorBuilder!(_errorMessage, _loadInitialData);
    }
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Error: $_errorMessage',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadInitialData,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingMoreWidget() {
    return widget.loadingMoreWidget ?? 
        const Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && _items.isEmpty) {
      return _buildLoadingWidget();
    }

    if (_hasError && _items.isEmpty) {
      return _buildErrorWidget();
    }

    if (_items.isEmpty) {
      return widget.enableRefresh
          ? RefreshIndicator(
              onRefresh: _refresh,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: SizedBox(
                  height: MediaQuery.of(context).size.height * 0.7,
                  child: _buildEmptyWidget(),
                ),
              ),
            )
          : _buildEmptyWidget();
    }

    final listView = ListView.builder(
      controller: _scrollController,
      padding: widget.padding,
      itemCount: _items.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _items.length) {
          return _buildLoadingMoreWidget();
        }

        final item = _items[index];
        final itemWidget = widget.itemBuilder(context, item, index);

        if (widget.showSeparator && index < _items.length - 1) {
          return Column(
            children: [
              itemWidget,
              widget.separator ?? const Divider(height: 1),
            ],
          );
        }

        return itemWidget;
      },
    );

    return widget.enableRefresh
        ? RefreshIndicator(
            onRefresh: _refresh,
            child: listView,
          )
        : listView;
  }
}
