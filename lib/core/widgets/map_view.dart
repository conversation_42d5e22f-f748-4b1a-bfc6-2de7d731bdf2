import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Placeholder Map widget that can be swapped for Google Maps / Flutter Map
/// This avoids MapLibre which isn't iOS-compatible in your setup.
class MapView extends StatelessWidget {
  final double? initialLat;
  final double? initialLng;
  final double initialZoom;

  const MapView({super.key, this.initialLat, this.initialLng, this.initialZoom = 14});

  @override
  Widget build(BuildContext context) {
    // For now, render a styled placeholder ensuring UI continuity
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0x40FFFFFF), Color(0x20FFFFFF)],
        ),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Center(
        child: Text(
          kIsWeb
              ? 'Map (Web Placeholder)'
              : 'Map Placeholder (Replace with GoogleMap)',
          style: const TextStyle(color: Colors.white70),
        ),
      ),
    );
  }
}

