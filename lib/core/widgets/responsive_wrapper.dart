import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:ui'; // Added for ImageFilter

class ResponsiveWrapper extends StatelessWidget {
  final Widget child;
  final Color? backgroundColor;
  final double? maxWidth;
  final double? maxHeight;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final BoxShadow? shadow;
  final bool enableGlassEffect;
  final List<Color>? gradientColors;
  final double? blurIntensity;

  const ResponsiveWrapper({
    super.key,
    required this.child,
    this.backgroundColor,
    this.maxWidth,
    this.maxHeight,
    this.padding,
    this.borderRadius,
    this.shadow,
    this.enableGlassEffect = false,
    this.gradientColors,
    this.blurIntensity,
  });

  @override
  Widget build(BuildContext context) {
    // Only apply responsive wrapper on web
    if (!kIsWeb) {
      return child;
    }

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors ??
              [
                backgroundColor ?? const Color(0xFF201f22),
                const Color(0xFF2a2a2d),
                const Color(0xFF1a1a1d),
              ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: maxWidth ?? 450,
            maxHeight: maxHeight ?? 950,
          ),
          margin: const EdgeInsets.all(16),
          child: enableGlassEffect
              ? ClipRRect(
                  borderRadius: borderRadius ?? BorderRadius.circular(24),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: blurIntensity ?? 10,
                      sigmaY: blurIntensity ?? 10,
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: borderRadius ?? BorderRadius.circular(24),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1.5,
                        ),
                        color: Colors.white.withOpacity(0.1),
                      ),
                      child: child,
                    ),
                  ),
                )
              : Container(
                  decoration: BoxDecoration(
                    borderRadius: borderRadius ?? BorderRadius.circular(24),
                    boxShadow: shadow != null
                        ? [shadow!]
                        : [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 40,
                              offset: const Offset(0, 20),
                            ),
                          ],
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        const Color(0xFF2a2a2d),
                        const Color(0xFF1f1f22),
                        const Color(0xFF252528),
                      ],
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: borderRadius ?? BorderRadius.circular(24),
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.white.withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                      child: child,
                    ),
                  ),
                ),
        ),
      ),
    );
  }
}

// Simple wrapper matching the original request
class SimpleResponsiveWrapper extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final double? maxHeight;
  final Color? backgroundColor;

  const SimpleResponsiveWrapper({
    super.key,
    required this.child,
    this.maxWidth,
    this.maxHeight,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb) {
      return child;
    }

    return Container(
      color: backgroundColor ?? const Color(0xFF201f22),
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: maxWidth ?? 450,
            maxHeight: maxHeight ?? 950,
          ),
          child: child,
        ),
      ),
    );
  }
}

// Alternative wrapper with glass morphism effect
class GlassMorphismWrapper extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final double? maxHeight;
  final double blur;
  final double opacity;

  const GlassMorphismWrapper({
    super.key,
    required this.child,
    this.maxWidth,
    this.maxHeight,
    this.blur = 10,
    this.opacity = 0.1,
  });

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb) {
      return child;
    }

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF667eea),
            const Color(0xFF764ba2),
            const Color(0xFFf093fb),
          ],
        ),
      ),
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: maxWidth ?? 450,
            maxHeight: maxHeight ?? 950,
          ),
          margin: const EdgeInsets.all(16),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1.5,
                  ),
                  color: Colors.white.withOpacity(opacity),
                ),
                child: child,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
