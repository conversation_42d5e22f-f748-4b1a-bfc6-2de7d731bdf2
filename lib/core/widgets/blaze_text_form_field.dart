import 'package:flutter/material.dart';
import '../config/theme.dart';

class BlazeTextFormField extends StatefulWidget {
  final String hintText;
  final IconData prefixIcon;
  final IconData? suffixIcon;
  final bool isPassword;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final VoidCallback? onSuffixTap;
  final bool enabled;
  final int? maxLines;
  final int? maxLength;
  final void Function(String)? onFieldSubmitted;

  const BlazeTextFormField({
    super.key,
    required this.hintText,
    required this.prefixIcon,
    this.suffixIcon,
    this.isPassword = false,
    this.controller,
    this.validator,
    this.keyboardType,
    this.onSuffixTap,
    this.enabled = true,
    this.maxLines = 1,
    this.maxLength,
    this.onFieldSubmitted,
  });

  @override
  State<BlazeTextFormField> createState() => _BlazeTextFormFieldState();
}

class _BlazeTextFormFieldState extends State<BlazeTextFormField> {
  bool _obscureText = true;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width / 1.1,
      child: TextFormField(
        controller: widget.controller,
        validator: widget.validator,
        keyboardType: widget.keyboardType,
        enabled: widget.enabled,
        maxLines: widget.maxLines,
        maxLength: widget.maxLength,
        onFieldSubmitted: widget.onFieldSubmitted,
        obscureText: widget.isPassword ? _obscureText : false,
        style: const TextStyle(
          color: Colors.white,
          fontFamily: 'Gilroy_Medium',
        ),
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30),
            borderSide: const BorderSide(width: 0, style: BorderStyle.none),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30),
            borderSide: const BorderSide(width: 0, style: BorderStyle.none),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30),
            borderSide: const BorderSide(width: 0, style: BorderStyle.none),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30),
            borderSide: const BorderSide(color: Colors.red, width: 1),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30),
            borderSide: const BorderSide(color: Colors.red, width: 1),
          ),
          fillColor: NextSportzTheme.lightBlue,
          hintStyle: const TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: NextSportzTheme.grey,
            fontSize: 12,
          ),
          hintText: widget.hintText,
          filled: true,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
          prefixIcon: Icon(widget.prefixIcon, color: Colors.white, size: 20),
          suffixIcon:
              widget.isPassword
                  ? GestureDetector(
                    onTap: () {
                      setState(() {
                        _obscureText = !_obscureText;
                      });
                    },
                    child: Icon(
                      _obscureText
                          ? Icons.remove_red_eye_outlined
                          : Icons.remove_red_eye,
                      color: NextSportzTheme.grey,
                      size: 20,
                    ),
                  )
                  : widget.suffixIcon != null
                  ? GestureDetector(
                    onTap: widget.onSuffixTap,
                    child: Icon(
                      widget.suffixIcon,
                      color: NextSportzTheme.grey,
                      size: 20,
                    ),
                  )
                  : null,
        ),
      ),
    );
  }
}
