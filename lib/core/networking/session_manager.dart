import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../local/token_storage.dart';

/// Service to manage authentication session state and cleanup
class SessionManager {
  final TokenStorageService _tokenStorage;
  final StreamController<SessionEvent> _sessionController =
      StreamController<SessionEvent>.broadcast();

  SessionManager(this._tokenStorage);

  /// Stream of session events
  Stream<SessionEvent> get sessionEvents => _sessionController.stream;

  /// Clear the current session and notify listeners
  Future<void> clearSession({
    SessionClearReason reason = SessionClearReason.userLogout,
  }) async {
    try {
      await _tokenStorage.clearTokens();
      _sessionController.add(
        SessionEvent(
          type: SessionEventType.sessionCleared,
          reason: reason,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      _sessionController.add(
        SessionEvent(
          type: SessionEventType.sessionClearFailed,
          reason: reason,
          timestamp: DateTime.now(),
          error: e.toString(),
        ),
      );
    }
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    try {
      final tokens = await _tokenStorage.getTokens();
      return tokens != null && tokens['accessToken'] != null;
    } catch (e) {
      return false;
    }
  }

  /// Notify that token refresh failed after max retries
  void notifyTokenRefreshFailed() {
    _sessionController.add(
      SessionEvent(
        type: SessionEventType.tokenRefreshFailed,
        reason: SessionClearReason.tokenRefreshFailed,
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Notify that session expired
  void notifySessionExpired() {
    _sessionController.add(
      SessionEvent(
        type: SessionEventType.sessionExpired,
        reason: SessionClearReason.sessionExpired,
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Dispose resources
  void dispose() {
    _sessionController.close();
  }
}

/// Session event types
enum SessionEventType {
  sessionCleared,
  sessionClearFailed,
  tokenRefreshFailed,
  sessionExpired,
}

/// Reasons for session clearing
enum SessionClearReason {
  userLogout,
  tokenRefreshFailed,
  sessionExpired,
  authenticationError,
}

/// Session event model
class SessionEvent {
  final SessionEventType type;
  final SessionClearReason reason;
  final DateTime timestamp;
  final String? error;

  const SessionEvent({
    required this.type,
    required this.reason,
    required this.timestamp,
    this.error,
  });

  @override
  String toString() {
    return 'SessionEvent(type: $type, reason: $reason, timestamp: $timestamp, error: $error)';
  }
}

/// Provider for session manager
final sessionManagerProvider = Provider<SessionManager>((ref) {
  final tokenStorage = ref.read(tokenStorageProvider);
  return SessionManager(tokenStorage);
});
