# Token Refresh Middleware

This module implements an automatic token refresh mechanism that handles 401 authentication errors by automatically refreshing the access token and retrying failed requests.

## Features

- **Automatic Token Refresh**: Automatically refreshes expired access tokens when receiving 401 responses
- **Retry Mechanism**: Retries failed requests up to 3 times with exponential backoff
- **Session Management**: Manages authentication session state and cleanup
- **Concurrent Request Handling**: Prevents multiple simultaneous token refresh requests
- **Session Event Notifications**: Provides real-time session state notifications

## Components

### 1. AuthInterceptor

The main interceptor that handles:

- Adding Authorization headers to requests
- Detecting 401 responses and triggering token refresh
- Retrying requests after successful token refresh
- Session cleanup after max retries

```dart
// The interceptor is automatically added to the API client
final apiClient = ApiClient(ref);
```

### 2. SessionManager

Manages authentication session state and provides event notifications:

```dart
final sessionManager = ref.read(sessionManagerProvider);

// Listen to session events
sessionManager.sessionEvents.listen((event) {
  switch (event.type) {
    case SessionEventType.sessionExpired:
      // Navigate to login screen
      break;
    case SessionEventType.tokenRefreshFailed:
      // Show error message and navigate to login
      break;
    case SessionEventType.sessionCleared:
      // Handle session cleanup
      break;
  }
});
```

### 3. SessionListener Widget

A convenient widget that listens to session events and handles UI updates:

```dart
MaterialApp(
  home: SessionListener(
    onSessionExpired: () {
      // Navigate to login
      Navigator.pushReplacementNamed(context, '/login');
    },
    onTokenRefreshFailed: () {
      // Show error and navigate to login
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please log in again')),
      );
    },
    child: YourAppContent(),
  ),
)
```

## How It Works

### Token Refresh Flow

1. **Request Made**: API request is made with current access token
2. **401 Received**: Server responds with 401 (token expired)
3. **Refresh Check**: Interceptor checks if refresh token exists and retry count < 3
4. **Token Refresh**: Makes refresh token request to `/auth/refresh` endpoint
5. **Save New Tokens**: Saves new access and refresh tokens to storage
6. **Retry Request**: Retries original request with new access token
7. **Success/Failure**: Request succeeds or fails after max retries

### Session Cleanup

If token refresh fails after 3 attempts:

1. Clear all stored tokens
2. Emit session events to notify app components
3. Allow app to navigate to login screen

### Preventing Concurrent Refreshes

The interceptor uses a completer map to ensure only one refresh operation per token occurs simultaneously:

```dart
static final Map<String, Completer<bool>> _refreshTokenCompleterMap = {};
```

## Configuration

### Max Retries

You can adjust the maximum number of retry attempts:

```dart
class AuthInterceptor extends Interceptor {
  static const int _maxRetries = 3; // Change this value
}
```

### API Endpoints

Ensure your API endpoints are correctly configured in `api_const.dart`:

```dart
static String refreshTokenEndpoint = "/auth/refresh";
```

### Expected Refresh Response Format

The token refresh endpoint should return:

```json
{
  "data": {
    "accessToken": "new_access_token",
    "refreshToken": "new_refresh_token"
  }
}
```

## Usage Examples

### Basic Setup

```dart
// In main.dart or app initialization
void main() {
  runApp(
    ProviderScope(
      child: SessionListener(
        onTokenRefreshFailed: () {
          // Handle auth failure globally
        },
        child: MyApp(),
      ),
    ),
  );
}
```

### Manual Session Management

```dart
class AuthController {
  final SessionManager sessionManager;

  AuthController(this.sessionManager);

  Future<void> logout() async {
    await sessionManager.clearSession(
      reason: SessionClearReason.userLogout,
    );
  }

  Future<bool> checkAuthStatus() async {
    return await sessionManager.isAuthenticated();
  }
}
```

### Listening to Session Events with Riverpod

```dart
class AuthStatusWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<SessionEvent?>(sessionEventProvider, (previous, current) {
      if (current != null && current.type == SessionEventType.tokenRefreshFailed) {
        // Navigate to login
        Navigator.pushReplacementNamed(context, '/login');
      }
    });

    return YourWidget();
  }
}
```

## Testing

The implementation includes comprehensive tests:

- `auth_interceptor_test.dart`: Tests token refresh logic and retry mechanism
- `session_manager_test.dart`: Tests session management and event notifications

Run tests with:

```bash
flutter test test/core/networking/
```

## Error Handling

The middleware handles various error scenarios:

1. **No Refresh Token**: Immediately clears session
2. **Refresh Request Fails**: Clears session after max retries
3. **Invalid Refresh Response**: Treats as refresh failure
4. **Storage Errors**: Gracefully handles storage failures
5. **Network Errors**: Retries with exponential backoff

## Security Considerations

- Refresh tokens are stored securely using the `TokenStorageService`
- Failed refresh attempts are limited to prevent abuse
- Session is automatically cleared when refresh fails
- All token operations are performed asynchronously to avoid blocking UI
