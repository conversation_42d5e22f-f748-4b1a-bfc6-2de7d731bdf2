import 'package:dio/dio.dart';

class DioException<PERSON>andle implements Exception {
  final String message;

  DioExceptionHandle._(this.message);

  factory DioExceptionHandle.fromDioError(DioException dioError) {
    switch (dioError.type) {
      case DioExceptionType.connectionError:
        return DioExceptionHandle._(
            "Connection failed due to internet connection");

      case DioExceptionType.badResponse:
        final statusCode = dioError.response?.statusCode ?? 0;
        final errorData = dioError.response?.data;
        return DioExceptionHandle._(_handleError(statusCode, errorData));

      default:
        return DioExceptionHandle._("Something went wrong");
    }
  }

  static String _handleError(int statusCode, dynamic error) {
    switch (statusCode) {
      case 500:
        return "Internal server error";
      case 401:
        return error["data"]?.toString() ?? "Unauthorized Access";
      case 400:
        // Handle API response format: {"message":"Success","data":"Invalid Phonenumber or Password.","error":null,"validationErrors":null}
        if (error is Map<String, dynamic>) {
          return error["data"]?.toString() ?? "Bad Request";
        }
        return "Bad Request";

      default:
        return "Something went wrong";
    }
  }

  @override
  String toString() => message;
}
