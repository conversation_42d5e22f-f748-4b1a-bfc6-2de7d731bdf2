import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'session_manager.dart';

/// A widget that listens to session events and handles navigation/UI updates
class SessionListener extends ConsumerStatefulWidget {
  final Widget child;
  final VoidCallback? onSessionExpired;
  final VoidCallback? onTokenRefreshFailed;
  final VoidCallback? onSessionCleared;

  const SessionListener({
    super.key,
    required this.child,
    this.onSessionExpired,
    this.onTokenRefreshFailed,
    this.onSessionCleared,
  });

  @override
  ConsumerState<SessionListener> createState() => _SessionListenerState();
}

class _SessionListenerState extends ConsumerState<SessionListener> {
  StreamSubscription<SessionEvent>? _sessionSubscription;

  @override
  void initState() {
    super.initState();
    _startListening();
  }

  @override
  void dispose() {
    _sessionSubscription?.cancel();
    super.dispose();
  }

  void _startListening() {
    final sessionManager = ref.read(sessionManagerProvider);
    _sessionSubscription = sessionManager.sessionEvents.listen(
      _handleSessionEvent,
    );
  }

  void _handleSessionEvent(SessionEvent event) {
    if (!mounted) return;

    switch (event.type) {
      case SessionEventType.sessionExpired:
        _showSessionExpiredDialog();
        widget.onSessionExpired?.call();
        break;
      case SessionEventType.tokenRefreshFailed:
        _showTokenRefreshFailedDialog();
        widget.onTokenRefreshFailed?.call();
        break;
      case SessionEventType.sessionCleared:
        // Session was cleared, you might want to navigate to login
        widget.onSessionCleared?.call();
        break;
      case SessionEventType.sessionClearFailed:
        // Handle session clear failure if needed
        debugPrint('Session clear failed: ${event.error}');
        break;
    }
  }

  void _showSessionExpiredDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('Session Expired'),
            content: const Text(
              'Your session has expired. Please log in again.',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _navigateToLogin();
                },
                child: const Text('Login'),
              ),
            ],
          ),
    );
  }

  void _showTokenRefreshFailedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('Authentication Failed'),
            content: const Text(
              'Unable to refresh your authentication. Please log in again.',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _navigateToLogin();
                },
                child: const Text('Login'),
              ),
            ],
          ),
    );
  }

  void _navigateToLogin() {
    // Replace with your actual login route
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// A provider-based approach for listening to session events
class SessionEventNotifier extends StateNotifier<SessionEvent?> {
  final SessionManager _sessionManager;
  StreamSubscription<SessionEvent>? _subscription;

  SessionEventNotifier(this._sessionManager) : super(null) {
    _subscription = _sessionManager.sessionEvents.listen((event) {
      state = event;
    });
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }
}

final sessionEventProvider =
    StateNotifierProvider<SessionEventNotifier, SessionEvent?>((ref) {
      final sessionManager = ref.read(sessionManagerProvider);
      return SessionEventNotifier(sessionManager);
    });

/// A consumer widget that reacts to session events
class SessionEventConsumer extends ConsumerWidget {
  final Widget child;
  final Function(SessionEvent)? onSessionEvent;

  const SessionEventConsumer({
    super.key,
    required this.child,
    this.onSessionEvent,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<SessionEvent?>(sessionEventProvider, (previous, current) {
      if (current != null) {
        onSessionEvent?.call(current);
      }
    });

    return child;
  }
}
