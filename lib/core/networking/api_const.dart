/// API constants for the NextSportz application

class ApiConst {
  static String baseUrl = "https://apidev.nextsportz.com";

  // Authentication endpoints
  static String registerEndpoint = "/auth/register";
  static String verifyOtpEndpoint = "/auth/verify-otp";
  static String loginEndpoint = "/auth/login";
  static String meEndpoint = "/auth/me";
  static String refreshTokenEndpoint = "/auth/refresh";
  static String logoutEndpoint = "/auth/logout";
  static String forgotPasswordEndpoint = "/auth/forgot-password";
  static String resetPasswordEndpoint = "/auth/reset-password";
  static String updateProfileEndpoint = "/auth/update-profile";

  // Player endpoints
  static String playersEndpoint = "/api/players";
  static String playerProfileEndpoint = "/api/players/{playerId}";
  static String playerRatingEndpoint = "/api/players/{playerId}/rating";

  // Challenge endpoints
  static String challengesEndpoint = "/api/challenges";
  static String challengeDetailEndpoint = "/api/challenges/{challengeId}";
  static String respondToChallengeEndpoint = "/api/challenges/respond";
  static String submitMatchResultEndpoint = "/api/challenges/result";
  static String disputeMatchResultEndpoint = "/api/challenges/dispute";
  static String challengeStatsEndpoint = "/api/challenges/stats";
  static String challengeSuggestionsEndpoint = "/api/challenges/suggestions";
  static String myChallengesEndpoint = "/api/challenges/my-challenges";
  static String matchRequestsEndpoint = "/api/challenges/match-requests";
  static String myMatchRequestsEndpoint = "/api/challenges/my-match-requests";

  // Teams endpoints
  static String teamsEndpoint = "/api/teams";
  static String teamDetailEndpoint = "/api/teams/{teamId}";
  static String myTeamsEndpoint = "/api/teams/me";
  static String teamLeaderboardEndpoint = "/api/teams/leaderboard";
  static String teamSearchEndpoint = "/api/teams/search";
  static String allTeamsEndpoint = "/api/teams";
  static String searchTeamsEndpoint = "/api/teams";
  static String joinTeamByCodeEndpoint = "/api/teams/{teamCode}/members";

  // Team Members endpoints (separated)
  static String teamMembersEndpoint = "/api/teams/{teamId}/members";
  static String teamMemberDetailEndpoint =
      "/api/teams/{teamId}/members/{memberId}";
  static String teamMemberRoleEndpoint =
      "/api/teams/{teamId}/members/{memberId}/role";

  // Team Invitations endpoints (based on swagger.json)
  static String teamInvitationsEndpoint = "/api/teams/invitations";
  static String teamInvitationDetailEndpoint =
      "/api/teams/invitations/{invitationId}";
  static String teamInvitationDeleteEndpoint =
      "/api/teams/invitations/{invitationId}";
  static String teamInvitationAcceptEndpoint =
      "/api/teams/{teamId}/invitations/{invitationId}/accept";
  static String teamInvitationDeclineEndpoint =
      "/api/teams/{teamId}/invitations/{invitationId}/decline";

  // Team member invitation endpoints (based on swagger.json)
  static String teamInvitePlayerEndpoint = "/api/teams/{teamId}/invitations";
  static String teamMemberInviteDeleteEndpoint =
      "/api/teams/{teamId}/members/{memberId}/invite";
  static String teamMemberAcceptInviteEndpoint =
      "/api/teams/{teamId}/members/{memberId}/acceptinvite";

  // File upload endpoints
  static String fileUploadEndpoint = "/api/upload";

  // Image upload endpoints
  static String imageUploadUrlEndpoint = "/api/image/upload-url";
}
