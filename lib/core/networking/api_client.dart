import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'api_const.dart';
import 'auth_interceptor.dart';
import 'exception.dart';

class ApiClient {
  final Dio _dio;
  final Ref _ref;
  bool _authInterceptorAdded = false;

  ApiClient(this._ref, {Dio? dio})
      : _dio = dio ??
            Dio(
              BaseOptions(
                baseUrl: ApiConst.baseUrl,
                headers: {'Content-Type': 'application/json'},
              ),
            );

  void _ensureAuthInterceptor() {
    if (_authInterceptorAdded) return;

    // Add auth interceptor
    _dio.interceptors.add(AuthInterceptor(_ref));
    _authInterceptorAdded = true;

    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(requestBody: true, responseBody: true),
      );
    }
  }

  Future<dynamic> get(String path, {Map<String, dynamic>? query}) async {
    _ensureAuthInterceptor();
    try {
      final response = await _dio.get(path, queryParameters: query);
      return response.data;
    } on DioException catch (e) {
      throw DioExceptionHandle.fromDioError(e);
    }
  }

  Future<dynamic> post(
    String path, {
    dynamic data,
    bool isFormData = false,
  }) async {
    _ensureAuthInterceptor();
    try {
      final response = await _dio.post(
        path,
        data: isFormData ? FormData.fromMap(data) : data,
      );
      return response.data;
    } on DioException catch (e) {
      throw DioExceptionHandle.fromDioError(e);
    }
  }

  Future<dynamic> put(String path, {dynamic data}) async {
    _ensureAuthInterceptor();
    try {
      final response = await _dio.put(path, data: data);
      return response.data;
    } on DioException catch (e) {
      throw DioExceptionHandle.fromDioError(e);
    }
  }

  Future<dynamic> patch(
    String path, {
    dynamic data,
    bool isFormData = false,
  }) async {
    _ensureAuthInterceptor();
    try {
      final response = await _dio.patch(
        path,
        data: isFormData ? FormData.fromMap(data) : data,
      );
      return response.data;
    } on DioException catch (e) {
      throw DioExceptionHandle.fromDioError(e);
    }
  }

  Future<dynamic> delete(String path) async {
    _ensureAuthInterceptor();
    try {
      final response = await _dio.delete(path);
      return response.data;
    } on DioException catch (e) {
      throw DioExceptionHandle.fromDioError(e);
    }
  }
}

final apiClientProvider = Provider<ApiClient>((ref) => ApiClient(ref));
