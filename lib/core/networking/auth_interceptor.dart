import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../local/token_storage.dart';
import 'api_const.dart';
import 'session_manager.dart';

class AuthInterceptor extends Interceptor {
  final Ref _ref;
  static final Map<String, Completer<bool>> _refreshTokenCompleterMap = {};
  static const int _maxRetries = 3;

  AuthInterceptor(this._ref);

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      final tokenStorage = _ref.read(tokenStorageProvider);
      final tokens = await tokenStorage.getTokens();

      if (tokens != null && tokens['accessToken'] != null) {
        final token = tokens['accessToken'] as String;
        options.headers['Authorization'] = 'Bearer $token';
      }
    } catch (e) {
      // If token retrieval fails, continue without token
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // Check retry count for this request
      final retryCount = err.requestOptions.extra['retry_count'] as int? ?? 0;

      if (retryCount >= _maxRetries) {
        // Max retries reached, clear session and fail
        await _clearSessionAndFail(err, handler);
        return;
      }

      // Attempt token refresh
      final refreshSuccess = await _attemptTokenRefresh();

      if (refreshSuccess) {
        // Retry the original request with new token
        await _retryRequest(err, handler, retryCount + 1);
      } else {
        // Refresh failed, clear session and fail
        await _clearSessionAndFail(err, handler);
      }
    } else {
      handler.next(err);
    }
  }

  Future<bool> _attemptTokenRefresh() async {
    try {
      final tokenStorage = _ref.read(tokenStorageProvider);
      final tokens = await tokenStorage.getTokens();

      if (tokens == null || tokens['refreshToken'] == null) {
        return false;
      }

      final refreshToken = tokens['refreshToken'] as String;
      final refreshKey = refreshToken.hashCode.toString();

      // Check if refresh is already in progress for this token
      if (_refreshTokenCompleterMap.containsKey(refreshKey)) {
        return await _refreshTokenCompleterMap[refreshKey]!.future;
      }

      // Create a new completer for this refresh operation
      final completer = Completer<bool>();
      _refreshTokenCompleterMap[refreshKey] = completer;

      try {
        // Create a new Dio instance without auth interceptor to avoid recursion
        final dio = Dio(
          BaseOptions(
            baseUrl: ApiConst.baseUrl,
            headers: {'Content-Type': 'application/json'},
          ),
        );

        final response = await dio.post(
          ApiConst.refreshTokenEndpoint,
          data: {'refreshToken': refreshToken},
        );

        if (response.statusCode == 200 && response.data != null) {
          final data = response.data as Map<String, dynamic>;
          final newAccessToken = data['data']?['accessToken'] as String?;
          final newRefreshToken = data['data']?['refreshToken'] as String?;

          if (newAccessToken != null && newRefreshToken != null) {
            // Save new tokens
            await tokenStorage.saveTokens(
              accessToken: newAccessToken,
              refreshToken: newRefreshToken,
            );

            completer.complete(true);
            return true;
          }
        }

        completer.complete(false);
        return false;
      } catch (e) {
        completer.complete(false);
        return false;
      } finally {
        // Remove completer from map
        _refreshTokenCompleterMap.remove(refreshKey);
      }
    } catch (e) {
      return false;
    }
  }

  Future<void> _retryRequest(
    DioException err,
    ErrorInterceptorHandler handler,
    int retryCount,
  ) async {
    try {
      final tokenStorage = _ref.read(tokenStorageProvider);
      final tokens = await tokenStorage.getTokens();

      // Update request with new token and retry count
      if (tokens != null && tokens['accessToken'] != null) {
        final newToken = tokens['accessToken'] as String;
        err.requestOptions.headers['Authorization'] = 'Bearer $newToken';
      }

      err.requestOptions.extra['retry_count'] = retryCount;

      // Create a new Dio instance to retry the request
      final dio = Dio();
      final response = await dio.fetch(err.requestOptions);
      handler.resolve(response);
    } catch (e) {
      // If retry fails, continue with original error
      handler.next(err);
    }
  }

  Future<void> _clearSessionAndFail(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    try {
      final sessionManager = _ref.read(sessionManagerProvider);

      // Clear session and notify listeners
      await sessionManager.clearSession(
        reason: SessionClearReason.tokenRefreshFailed,
      );

      // Notify that token refresh failed after max retries
      sessionManager.notifyTokenRefreshFailed();
    } catch (e) {
      // Ignore cleanup errors
    }

    handler.next(err);
  }
}
