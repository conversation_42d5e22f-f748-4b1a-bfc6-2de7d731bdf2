/// Generic paginated response model that matches the API response structure
/// 
/// This model handles the standard API response format:
/// {
///   "message": "Success",
///   "data": {
///     "items": [...],
///     "total": 1,
///     "page": 1,
///     "pageSize": 20,
///     "totalPages": 1
///   },
///   "error": null,
///   "validationErrors": null
/// }
class PaginatedResponse<T> {
  final String message;
  final PaginatedData<T> data;
  final String? error;
  final dynamic validationErrors;

  const PaginatedResponse({
    required this.message,
    required this.data,
    this.error,
    this.validationErrors,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return PaginatedResponse<T>(
      message: json['message'] as String? ?? '',
      data: PaginatedData<T>.fromJson(
        json['data'] as Map<String, dynamic>? ?? {},
        fromJsonT,
      ),
      error: json['error'] as String?,
      validationErrors: json['validationErrors'],
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'message': message,
      'data': data.toJson(toJsonT),
      'error': error,
      'validationErrors': validationErrors,
    };
  }

  PaginatedResponse<T> copyWith({
    String? message,
    PaginatedData<T>? data,
    String? error,
    dynamic validationErrors,
  }) {
    return PaginatedResponse<T>(
      message: message ?? this.message,
      data: data ?? this.data,
      error: error ?? this.error,
      validationErrors: validationErrors ?? this.validationErrors,
    );
  }

  /// Check if the response is successful
  bool get isSuccess => error == null;

  /// Check if there are more pages available
  bool get hasNextPage => data.hasNextPage;

  /// Check if there are previous pages available
  bool get hasPreviousPage => data.hasPreviousPage;
}

/// Paginated data container
class PaginatedData<T> {
  final List<T> items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  const PaginatedData({
    required this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  factory PaginatedData.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    final itemsList = json['items'] as List<dynamic>? ?? [];
    return PaginatedData<T>(
      items: itemsList
          .map((item) => fromJsonT(item as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int? ?? 0,
      page: json['page'] as int? ?? 1,
      pageSize: json['pageSize'] as int? ?? 20,
      totalPages: json['totalPages'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'items': items.map((item) => toJsonT(item)).toList(),
      'total': total,
      'page': page,
      'pageSize': pageSize,
      'totalPages': totalPages,
    };
  }

  PaginatedData<T> copyWith({
    List<T>? items,
    int? total,
    int? page,
    int? pageSize,
    int? totalPages,
  }) {
    return PaginatedData<T>(
      items: items ?? this.items,
      total: total ?? this.total,
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
      totalPages: totalPages ?? this.totalPages,
    );
  }

  /// Check if there are more pages available
  bool get hasNextPage => page < totalPages;

  /// Check if there are previous pages available
  bool get hasPreviousPage => page > 1;

  /// Check if this is the first page
  bool get isFirstPage => page == 1;

  /// Check if this is the last page
  bool get isLastPage => page == totalPages;

  /// Check if the data is empty
  bool get isEmpty => items.isEmpty;

  /// Check if the data is not empty
  bool get isNotEmpty => items.isNotEmpty;

  /// Get the start index for the current page (1-based)
  int get startIndex => (page - 1) * pageSize + 1;

  /// Get the end index for the current page (1-based)
  int get endIndex {
    final calculatedEnd = page * pageSize;
    return calculatedEnd > total ? total : calculatedEnd;
  }

  /// Get a summary string like "1-20 of 100"
  String get rangeSummary {
    if (isEmpty) return '0 of $total';
    return '$startIndex-$endIndex of $total';
  }

  /// Append items from another page (useful for infinite scrolling)
  PaginatedData<T> appendPage(PaginatedData<T> nextPage) {
    return copyWith(
      items: [...items, ...nextPage.items],
      page: nextPage.page,
      total: nextPage.total,
      totalPages: nextPage.totalPages,
    );
  }
}

/// Empty paginated response for initial states
class EmptyPaginatedResponse<T> extends PaginatedResponse<T> {
  EmptyPaginatedResponse()
      : super(
          message: '',
          data: const PaginatedData(
            items: [],
            total: 0,
            page: 1,
            pageSize: 20,
            totalPages: 0,
          ),
        );
}
