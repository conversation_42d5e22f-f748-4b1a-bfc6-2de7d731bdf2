/// Image upload models based on swagger.json schema definitions

/// Enum for different image types that can be uploaded
enum ImageType {
  userProfile('UserProfile'),
  team<PERSON><PERSON>('TeamLogo'),
  advertisementBanner('AdvertisementBanner'),
  tournamentBanner('TournamentBanner');

  const ImageType(this.value);
  final String value;

  /// Convert from string value to enum
  static ImageType fromString(String value) {
    switch (value) {
      case 'UserProfile':
        return ImageType.userProfile;
      case 'TeamLogo':
        return ImageType.teamLogo;
      case 'AdvertisementBanner':
        return ImageType.advertisementBanner;
      case 'TournamentBanner':
        return ImageType.tournamentBanner;
      default:
        throw ArgumentError('Unknown ImageType: $value');
    }
  }
}

/// Request model for image upload URL endpoint
class ImageUploadRequest {
  final ImageType type;
  final String? fileName;
  final String? entityId;

  const ImageUploadRequest({
    required this.type,
    this.fileName,
    this.entityId,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'type': type.value,
      if (fileName != null) 'fileName': fileName,
      if (entityId != null) 'entityId': entityId,
    };
  }

  /// Create from JSON response
  factory ImageUploadRequest.fromJson(Map<String, dynamic> json) {
    return ImageUploadRequest(
      type: ImageType.fromString(json['type'] as String),
      fileName: json['fileName'] as String?,
      entityId: json['entityId'] as String?,
    );
  }

  @override
  String toString() {
    return 'ImageUploadRequest(type: $type, fileName: $fileName, entityId: $entityId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ImageUploadRequest &&
        other.type == type &&
        other.fileName == fileName &&
        other.entityId == entityId;
  }

  @override
  int get hashCode => Object.hash(type, fileName, entityId);
}

/// Response model for image upload URL endpoint
class ImageUploadResponse {
  final String? uploadUrl;
  final String? downloadUrl;

  const ImageUploadResponse({
    this.uploadUrl,
    this.downloadUrl,
  });

  /// Create from JSON response
  factory ImageUploadResponse.fromJson(Map<String, dynamic> json) {
    return ImageUploadResponse(
      uploadUrl: json['uploadUrl'] as String?,
      downloadUrl: json['downloadUrl'] as String?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'uploadUrl': uploadUrl,
      'downloadUrl': downloadUrl,
    };
  }

  @override
  String toString() {
    return 'ImageUploadResponse(uploadUrl: $uploadUrl, downloadUrl: $downloadUrl)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ImageUploadResponse &&
        other.uploadUrl == uploadUrl &&
        other.downloadUrl == downloadUrl;
  }

  @override
  int get hashCode => Object.hash(uploadUrl, downloadUrl);
}
