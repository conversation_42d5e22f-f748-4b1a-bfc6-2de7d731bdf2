import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'key_value_storage.dart';
import '../providers.dart';

abstract class TokenStorageService {
  Future<void> saveTokens({
    required String accessToken,
    required String refreshToken,
  });

  Future<Map<String, String>?> getTokens();

  Future<void> clearTokens();

  Future<bool> hasTokens();
}

class TokenStorageServiceImpl implements TokenStorageService {
  final KeyValueStorageService _storage;

  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';

  TokenStorageServiceImpl(this._storage);

  @override
  Future<void> saveTokens({
    required String accessToken,
    required String refreshToken,
  }) async {
    await _storage.setKeyValue(_accessTokenKey, accessToken);
    await _storage.setKeyValue(_refreshTokenKey, refreshToken);
  }

  @override
  Future<Map<String, String>?> getTokens() async {
    final accessToken = _storage.getValue<String>(_accessTokenKey);
    final refreshToken = _storage.getValue<String>(_refreshTokenKey);

    if (accessToken != null && refreshToken != null) {
      return {'accessToken': accessToken, 'refreshToken': refreshToken};
    }

    return null;
  }

  @override
  Future<void> clearTokens() async {
    await _storage.removeKey(_accessTokenKey);
    await _storage.removeKey(_refreshTokenKey);
  }

  @override
  Future<bool> hasTokens() async {
    final tokens = await getTokens();
    return tokens != null;
  }
}

// Provider for token storage
final tokenStorageProvider = Provider<TokenStorageService>((ref) {
  final keyValueStorage = ref.read(keyValueStorageProvider);
  return TokenStorageServiceImpl(keyValueStorage);
});
