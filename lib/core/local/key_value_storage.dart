import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

abstract class KeyValueStorageService {
  T? getValue<T>(String key);
  Future<void> setKeyValue<T>(String key, T? value);
  Future<bool> removeKey(String key);
  Future<void> clearAll();
}

class KeyValueStorageServiceImpl implements KeyValueStorageService {
  final SharedPreferences prefs;
  KeyValueStorageServiceImpl(this.prefs);

  @override
  T? getValue<T>(String key) {
    try {
      switch (T) {
        case const (String):
          return prefs.getString(key) as T?;
        case const (int):
          return prefs.getInt(key) as T?;
        case const (double):
          return prefs.getDouble(key) as T?;
        case const (bool):
          return prefs.getBool(key) as T?;
        case const (List<String>):
          return prefs.getStringList(key) as T?;
        case const (Map):
          final jsonString = prefs.getString(key);
          return jsonString != null ? jsonDecode(jsonString) as T : null;
        default:
          throw UnimplementedError('No implementation for type ${T.runtimeType}');
      }
    } catch (e) {
      debugPrint('Error retrieving key $key: $e');
      return null;
    }
  }

  @override
  Future<void> setKeyValue<T>(String key, T? value) async {
    try {
      switch (T) {
        case const (String):
          if (value == null) {
            await prefs.remove(key);
          } else {
            await prefs.setString(key, value as String);
          }
          break;
        case const (int):
          if (value == null) {
            await prefs.remove(key);
          } else {
            await prefs.setInt(key, value as int);
          }
          break;
        case const (double):
          if (value == null) {
            await prefs.remove(key);
          } else {
            await prefs.setDouble(key, value as double);
          }
          break;
        case const (bool):
          if (value == null) {
            await prefs.remove(key);
          } else {
            await prefs.setBool(key, value as bool);
          }
          break;
        case const (List<String>):
          if (value == null) {
            await prefs.remove(key);
          } else {
            await prefs.setStringList(key, value as List<String>);
          }
          break;
        case const (Map):
          if (value == null) {
            await prefs.remove(key);
          } else {
            await prefs.setString(key, jsonEncode(value));
          }
          break;
        default:
          throw UnimplementedError('No implementation for type ${T.runtimeType}');
      }
    } catch (e) {
      debugPrint('Error setting key $key: $e');
    }
  }

  @override
  Future<bool> removeKey(String key) async {
    try {
      return await prefs.remove(key);
    } catch (e) {
      debugPrint('Error removing key $key: $e');
      return false;
    }
  }

  @override
  Future<void> clearAll() async {
    try {
      await prefs.clear();
    } catch (e) {
      debugPrint('Error clearing all keys: $e');
    }
  }
}

