import 'package:flutter/material.dart';
import '../utils/color_notifire.dart';

import 'package:provider/provider.dart';

class Common {
  static ColorNotifire getColorNotifier(BuildContext context) {
    return Provider.of<ColorNotifire>(context, listen: false);
  }

  // Theme colors based on existing color scheme
  static Color getPrimaryColor(BuildContext context) {
    final notifier = getColorNotifier(context);
    return notifier.getprimerycolor;
  }

  static Color getLightBlue(BuildContext context) {
    final notifier = getColorNotifier(context);
    return notifier.getlightblue;
  }

  static Color getWhite(BuildContext context) {
    final notifier = getColorNotifier(context);
    return notifier.getwhite;
  }

  static Color getGrey(BuildContext context) {
    final notifier = getColorNotifier(context);
    return notifier.getgrey;
  }

  // Gradient definitions matching existing design
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [
      Color(0xff00D4AA),
      Color(0xffFF6B35),
      Color(0xff00D4AA)
    ], // Updated to sporty colors
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0x40FFFFFF), Color(0x20FFFFFF)],
  );

  // Common text styles
  static TextStyle getTitleStyle(BuildContext context) {
    return TextStyle(
      fontFamily: 'Gilroy_Medium',
      color: getWhite(context),
      fontSize: 18,
      fontWeight: FontWeight.bold,
    );
  }

  static TextStyle getBodyStyle(BuildContext context) {
    return TextStyle(
      fontFamily: 'Gilroy_Medium',
      color: getWhite(context),
      fontSize: 14,
    );
  }

  static TextStyle getHintStyle(BuildContext context) {
    return TextStyle(
      fontFamily: 'Gilroy_Medium',
      color: getGrey(context),
      fontSize: 12,
    );
  }

  // Common input decoration
  static InputDecoration getInputDecoration(
    BuildContext context, {
    required String hintText,
    required IconData prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixTap,
  }) {
    return InputDecoration(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(30),
        borderSide: const BorderSide(width: 0, style: BorderStyle.none),
      ),
      fillColor: getLightBlue(context),
      hintStyle: getHintStyle(context),
      hintText: hintText,
      filled: true,
      prefixIcon: Icon(prefixIcon, color: getWhite(context), size: 20),
      suffixIcon: suffixIcon != null
          ? GestureDetector(
              onTap: onSuffixTap,
              child: Icon(suffixIcon, color: getGrey(context), size: 20),
            )
          : null,
    );
  }

  // Common button style
  static BoxDecoration getButtonDecoration() {
    return const BoxDecoration(
      borderRadius: BorderRadius.all(Radius.circular(20)),
      gradient: primaryGradient,
    );
  }
}
