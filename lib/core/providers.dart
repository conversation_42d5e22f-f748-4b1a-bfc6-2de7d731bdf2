import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'local/key_value_storage.dart';

final sharedPrefsProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError(
    'sharedPrefsProvider must be overridden at app start',
  );
});

final keyValueStorageProvider = Provider<KeyValueStorageService>((ref) {
  final prefs = ref.read(sharedPrefsProvider);
  return KeyValueStorageServiceImpl(prefs);
});
