import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart' as legacy_provider;
import 'core/providers.dart' as core_providers;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'utils/color_notifire.dart';
import 'core/local/key_value_storage.dart';
import 'core/widgets/responsive_wrapper.dart';
import 'core/config/theme.dart';
import 'core/providers/theme_provider.dart' as theme_providers;
import 'package:flutter_web_plugins/url_strategy.dart';
import 'core/routing/app_router.dart';

late SharedPreferences sharedPref;

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  sharedPref = await SharedPreferences.getInstance();
  if (kIsWeb) {
    usePathUrlStrategy();
  }
  print('SharedPreferences initialized successfully');
  runApp(
    ProviderScope(
      overrides: [
        core_providers.sharedPrefsProvider.overrideWithValue(sharedPref),
        core_providers.keyValueStorageProvider.overrideWithValue(
          KeyValueStorageServiceImpl(sharedPref),
        ),
      ],
      child: legacy_provider.MultiProvider(
        providers: [
          legacy_provider.ChangeNotifierProvider(
            create: (_) => ColorNotifire(),
          ),
        ],
        child: const NextSportzApp(),
      ),
    ),
  );
}

class NextSportzApp extends ConsumerWidget {
  const NextSportzApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(theme_providers.themeProvider);
    final router = ref.watch(appRouterProvider);

    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      builder: (context, child) {
        return ResponsiveWrapper(
          enableGlassEffect: true,
          gradientColors: [
            const Color(0xFF667eea),
            const Color(0xFF764ba2),
            const Color(0xFFf093fb),
          ],
          blurIntensity: 15,
          child: MaterialApp.router(
            title: 'NextSportz',
            debugShowCheckedModeBanner: false,
            theme: NextSportzTheme.lightTheme,
            darkTheme: NextSportzTheme.darkTheme,
            themeMode: themeMode,
            routerConfig: router,
          ),
        );
      },
    );
  }
}
