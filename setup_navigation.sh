#!/bin/bash

echo "Setting up NextSportz Navigation..."

# Check if Flutter is available
if command -v flutter &> /dev/null; then
    echo "Flutter found! Installing dependencies..."
    flutter pub get
    echo "Dependencies installed successfully!"
    echo ""
    echo "To run the app:"
    echo "flutter run"
else
    echo "Flutter not found in PATH."
    echo "Please install Flutter or add it to your PATH."
    echo ""
    echo "Common Flutter installation paths:"
    echo "- ~/flutter/bin/flutter"
    echo "- /usr/local/bin/flutter"
    echo "- /opt/flutter/bin/flutter"
    echo ""
    echo "After installing Flutter, run:"
    echo "flutter pub get"
    echo "flutter run"
fi

echo ""
echo "Navigation Features:"
echo "- 6 tabs: Dashboard, Venues, Challenges, Tournaments, Teams, Profile"
echo "- Challenges is at the center (index 2) with notched FAB"
echo "- Ven<PERSON> is the 2nd tab (index 1)"
echo "- Teams tab added to make even number for center gap"
echo "- Center FAB navigates to Challenges"
echo "- Using animated_bottom_navigation_bar package"
