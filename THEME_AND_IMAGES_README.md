# NextSportz - Theme & Image Loading Updates

## Overview

This update adds comprehensive dark/light mode support and optimized network image loading using `cached_network_image`.

## Features Added

### 1. Dark/Light Mode System

#### Theme Configuration

- **Location**: `lib/core/config/theme.dart`
- **Features**:
  - Complete light and dark theme definitions
  - Consistent color schemes
  - Gradient support
  - Material 3 compatibility

#### Theme Provider

- **Location**: `lib/core/providers/theme_provider.dart`
- **Features**:
  - Riverpod-based state management
  - Persistent theme storage
  - Theme toggle functionality
  - Provider integration

#### Theme Toggle Widget

- **Location**: `lib/core/widgets/theme_toggle_button.dart`
- **Features**:
  - Simple icon button toggle
  - ListTile-style toggle for settings
  - Animated theme toggle button

### 2. Optimized Image Loading

#### Cached Network Image Integration

- **Dependency**: `cached_network_image: ^3.4.1` (already in pubspec.yaml)
- **Features**:
  - Automatic caching of network images
  - Loading and error state handling
  - Fade animations
  - Memory and disk caching

#### Custom Image Widgets

- **Location**: `lib/core/widgets/cached_image_widget.dart`
- **Features**:
  - `CachedImageWidget` - General purpose cached image widget
  - `CircularCachedImageWidget` - For profile pictures
  - `TeamLogoCachedImageWidget` - For team logos
  - Consistent error and loading states

#### Updated Screens

The following screens have been updated to use `CachedNetworkImage`:

- `lib/features/challenges/presentation/screens/my_matches_screen.dart`
- `lib/features/teams/presentation/screens/team_details_screen.dart`
- `lib/features/teams/presentation/screens/my_teams_screen.dart`
- `lib/features/home/<USER>/widgets/ad_carousel.dart` (already implemented)

## Usage

### Using Theme Toggle

#### In Settings Screen

```dart
import '../../../../core/providers/theme_provider.dart' as theme_providers;

// Watch theme state
final isDark = ref.watch(theme_providers.isDarkModeProvider);
final themeNotifier = ref.read(theme_providers.themeProvider.notifier);

// Toggle theme
themeNotifier.toggleTheme();
```

#### Using Theme Toggle Widget

```dart
import '../../../../core/widgets/theme_toggle_button.dart';

// Simple icon button
ThemeToggleButton()

// With label (for settings)
ThemeToggleButton(showLabel: true)

// Animated button
AnimatedThemeToggleButton()
```

### Using Cached Images

#### Basic Usage

```dart
import '../../../../core/widgets/cached_image_widget.dart';

CachedImageWidget(
  imageUrl: 'https://example.com/image.jpg',
  width: 100,
  height: 100,
  borderRadius: BorderRadius.circular(10),
)
```

#### Profile Picture

```dart
CircularCachedImageWidget(
  imageUrl: 'https://example.com/profile.jpg',
  radius: 30,
)
```

#### Team Logo

```dart
TeamLogoCachedImageWidget(
  imageUrl: 'https://example.com/team-logo.jpg',
  size: 60,
)
```

### Theme Colors

#### Getting Theme-Aware Colors

```dart
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;

// In a widget with ref
final isDark = ref.watch(theme_providers.isDarkModeProvider);
final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
final textColor = NextSportzTheme.getAccentColor(isDark);
```

## Implementation Details

### Theme Persistence

- Theme preference is stored using `SharedPreferences`
- Automatically loads saved theme on app startup
- Seamless switching between light and dark modes

### Image Caching

- Images are cached both in memory and on disk
- Automatic cache management (LRU policy)
- Network error handling with fallback UI
- Smooth loading transitions

### Performance Benefits

- **Images**: Reduced network requests, faster image loading, offline support
- **Themes**: Instant theme switching, persistent user preferences
- **Memory**: Efficient image caching, reduced memory usage

## Integration Points

### Main App Setup

The theme system is integrated at the app level in `lib/main.dart`:

```dart
MaterialApp(
  theme: NextSportzTheme.lightTheme,
  darkTheme: NextSportzTheme.darkTheme,
  themeMode: themeMode, // From provider
  // ...
)
```

### Profile Settings

A complete implementation example is available in:
`lib/features/profile/presentation/screens/profile_settings_screen.dart`

## Future Enhancements

- System theme detection (auto mode)
- Custom theme colors
- Image optimization settings
- Advanced caching configuration
- Theme transitions animations

## Testing

To test the theme functionality:

1. Open the Profile Settings screen
2. Toggle the Dark Mode switch
3. Observe immediate theme changes
4. Restart the app to verify persistence

To test image caching:

1. Load screens with network images
2. Go offline and return to the same screens
3. Images should load from cache
4. Test error states with invalid URLs
